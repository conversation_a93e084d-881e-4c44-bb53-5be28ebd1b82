package com.woxue.resourceservice.service.impl;

import com.woxue.common.model.redBook.PhoneticCompose;
import com.woxue.common.model.redBook.PhoneticExample;
import com.woxue.redbookresource.service.IPhoneticResourceService;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 音标
 * <AUTHOR>
 * @date 2022/7/20 11:37
 */
@Service("phoneticResourceService")
public class PhoneticResourceServiceImpl implements IPhoneticResourceService {
    @Override
    public List<Integer> getPhoneticIdList(int unitId) {
        return RedBookCourseManager.getPhoneticIdList(unitId);
    }

    @Override
    public List<Integer> getPhoneticComposeIdList(int unitId) {
        return RedBookCourseManager.getPhoneticComposeIdList(unitId);
    }

    @Override
    public PhoneticCompose getPhoneticCompose(int composeWordId) {
        return RedBookCourseManager.getPhoneticCompose(composeWordId);
    }

    @Override
    public List<Integer> getPhoneticExampleIdList(int unitId) {
        return RedBookCourseManager.getPhoneticExampleIdList(unitId);
    }

    @Override
    public PhoneticExample getPhoneticExample(int exampleWordId) {
        return RedBookCourseManager.getPhoneticExample(exampleWordId);
    }
}
