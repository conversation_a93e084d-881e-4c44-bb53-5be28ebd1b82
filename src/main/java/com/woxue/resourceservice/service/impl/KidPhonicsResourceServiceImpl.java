package com.woxue.resourceservice.service.impl;

import com.redbook.kid.common.model.phonics.*;
import com.redbook.kid.resource.KidPhonicsResourceService;
import com.woxue.resourceservice.util.KidPhonicsManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 少儿自然拼读资源服务实现
 * 提供Dubbo服务给客户端获取资源
 * 严格只从缓存中获取数据，确保客户端只能访问已发布的内容
 */
@Service("KidPhonicsResourceService")
public class KidPhonicsResourceServiceImpl implements KidPhonicsResourceService {

    private static final Logger logger = LoggerFactory.getLogger(KidPhonicsResourceServiceImpl.class);

    @Override
    public KidPhonicsCourse getCourseById(Integer courseId) {
        try {
            KidPhonicsCourse course = KidPhonicsManager.getCourseById(courseId);
            if (course == null) {
                logger.warn("缓存中未找到课程数据，课程ID: {}", courseId);
            }
            return course;
        } catch (Exception e) {
            logger.error("获取课程信息异常，课程ID: {}", courseId, e);
            return null;
        }
    }

    @Override
    public Integer getCourseBranch(Integer courseId) {
        try {
            KidPhonicsCourse course = KidPhonicsManager.getCourseById(courseId);
            if (course == null) {
                logger.warn("缓存中未找到课程数据，课程ID: {}", courseId);
                return null;
            }
            return course.getBranch();
        } catch (Exception e) {
            logger.error("获取课程分支号异常，课程ID: {}", courseId, e);
            return null;
        }
    }

    @Override
    public KidPhonicsCourse getCourseWithAllResources(Integer courseId) {
        try {
            KidPhonicsCourse course = KidPhonicsManager.getCourseWithAllResources(courseId);
            if (course == null) {
                logger.warn("缓存中未找到课程完整资源，课程ID: {}", courseId);
            }
            return course;
        } catch (Exception e) {
            logger.error("获取课程完整资源异常，课程ID: {}", courseId, e);
            return null;
        }
    }

    @Override
    public List<KidPhonicsCourse> getAllCourses() {
        try {
            // 只从缓存获取所有课程，不从数据库回退
            List<KidPhonicsCourse> courses = KidPhonicsManager.getAllCourses();
            if (courses == null || courses.isEmpty()) {
                logger.warn("缓存中未找到课程列表或列表为空");
                return new ArrayList<>();
            }
            return courses;
        } catch (Exception e) {
            logger.error("获取所有课程列表异常", e);
            return new ArrayList<>();
        }
    }
} 