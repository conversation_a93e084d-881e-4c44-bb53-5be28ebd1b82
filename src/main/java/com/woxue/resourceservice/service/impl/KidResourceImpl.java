package com.woxue.resourceservice.service.impl;

import com.redbook.kid.common.model.*;
import com.redbook.kid.resource.KidCourseService;
import com.woxue.resourceservice.util.KidResourceManager;
import org.springframework.stereotype.Service;

import java.util.List;
@Service("KidCourseService")
public class KidResourceImpl implements KidCourseService {
    @Override
    public List<ResourceKidCourse> getKidCourses(KidConstant.CourseType courseType) {
        return KidResourceManager.getCourseList(courseType);
    }

    @Override
    public List<ResourceKidScene> getKidScenes(Integer integer) {
        return KidResourceManager.getSceneByCourseId(integer);
    }

    @Override
    public List<ResourceKidUnit> getUnitListBySceneId(Integer integer) {
        ResourceKidScene sceneById = KidResourceManager.getSceneById(integer);
        if (sceneById == null)return null;
        return sceneById.getUnitList();
    }

    @Override
    public List<ResourceKidUnit> getUnitListByCourseId(Integer integer) {
        return KidResourceManager.getUnitByCourseId(integer);
    }

    @Override
    public ResourceKidUnit getUnitByUnitId(Integer integer) {
        return KidResourceManager.getUnitById(integer);
    }

    @Override
    public ResourceKidWord getWordByWordId(Integer integer) {
        return KidResourceManager.getWordById(integer);
    }


    @Override
    public List<KidLetterUnit> getLetterUnitByCourseId(Integer integer) {
        return KidResourceManager.getLetterUnitByCourseId(integer);
    }

    @Override
    public ResourceKidCourse getKidCourseByCourseId(Integer integer) {
        return KidResourceManager.getCourse(integer);
    }


}
