package com.woxue.resourceservice.service.impl;

import com.woxue.common.model.redBook.readExpand.*;
import com.woxue.redbookresource.service.IReadExpandResourceService;
import com.woxue.resourceservice.util.ReadExpandReadResourceManager;
import org.springframework.stereotype.Service;

import java.util.List;
@Service("readExpandResourceService")
public class ReadExpandResourceServiceImpl implements IReadExpandResourceService {

    @Override
    public List<Integer> getReadExpandReadArticleIdList(int unitId) {
        return ReadExpandReadResourceManager.getReadExpandReadArticleIdList(unitId);
    }

    @Override
    public ReadExpandReadArticleBean getReadExpandReadArticle(int articleId) {
        return ReadExpandReadResourceManager.getReadExpandReadArticle(articleId);
    }

    @Override
    public List<ReadExpandReadArticleQuestionBean> getReadExpandReadArticleBasic(int articleId) {
        return ReadExpandReadResourceManager.getReadExpandReadArticleBasic(articleId);
    }

    @Override
    public List<ReadExpandReadArticleQuestionBean> getReadExpandReadArticleRaise(int articleId) {
        return ReadExpandReadResourceManager.getReadExpandReadArticleRaise(articleId);
    }

    @Override
    public List<ReadExpandReadArticleWordBean> getWordBeanList(int articleId) {
        return ReadExpandReadResourceManager.getWordBeanList(articleId);
    }

    @Override
    public ReadExpandReadArticleCorrelationBean getReadExpandReadArticleCorrelation(int articleId) {
        return ReadExpandReadResourceManager.getReadExpandReadArticleCorrelation(articleId);
    }

    @Override
    public List<ReadExpandKnowledgeQuestionBean> getReadExpandKnowledgeContent(int articleId) {
        return ReadExpandReadResourceManager.getReadExpandKnowledgeQuestionList(articleId);
    }

}
