package com.woxue.resourceservice.service.impl;

import java.util.List;

import com.woxue.resourceservice.util.WordUseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.woxue.common.model.DictionaryBean;
import com.woxue.resourceservice.dao.IDictionaryDao;
import com.woxue.redbookresource.service.IDictionaryService;
import com.woxue.resourceservice.util.DictionaryManager;

import javax.annotation.PostConstruct;

@Service("dictionaryService")
public class DictionaryServiceImpl implements IDictionaryService {
	@Autowired
	IDictionaryDao dictionaryDao;

	@PostConstruct
	public void init() {
		// 初始化监听
		DictionaryManager.processDictionaryQueue();
		WordUseManager.processWordUseQueue();
	}
	public DictionaryBean getDictionary(String spelling) {
		return DictionaryManager.getDictionary(spelling);
	}

	public List<DictionaryBean> getDictionaryListByLikeSpelling(String spelling, Integer limitSize) {
		return dictionaryDao.getDictionaryListByLikeSpelling(spelling,limitSize);
	}


	public int updateDictionary(DictionaryBean dictionaryBean) {
		DictionaryManager.deleteDictionary(dictionaryBean.getSpelling());
		return dictionaryDao.updateDictionary(dictionaryBean);
	}

	public int insertDictionary(DictionaryBean dictionaryBean) {
		return dictionaryDao.insertDictionary(dictionaryBean);
	}
}
