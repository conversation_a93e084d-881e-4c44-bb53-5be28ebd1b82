package com.woxue.resourceservice.service.impl;

import com.woxue.common.model.SimulationQuestionBean;
import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.*;
import com.woxue.common.model.redBook.listen.ResourceUnitListenDTO;
import com.woxue.common.model.redBook.read.ResourceReadArticleDTO;
import com.woxue.common.util.wordUse.WordUseBean;
import com.woxue.redbookresource.model.ResourcePublishRecords;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.dao.IResourcePublishRecordsDao;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import com.woxue.resourceservice.util.HomonymManager;
import com.woxue.resourceservice.util.RedBookCourseManager;
import com.woxue.resourceservice.util.SoundMarkManager;
import com.woxue.resourceservice.util.WordUseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("redBookCourseService")
public class RedBookCourseServiceImpl implements IRedBookCourseService {
    @Autowired
    IRedBookCourseDao redBookCourseDao;
    @Autowired
    IResourcePublishRecordsDao iResourcePublishRecordsDao;

    @Override
    public List<RedBookVersion> getVersionList(Integer stage, Integer versionType) {
        return RedBookCourseManager.getRedBookVersionList(versionType,stage);
    }
    @Override
    public RedBookVersion getVersionById(Integer versionId) {
        return RedBookCourseManager.getRedBookVersion(versionId);
    }

    @Override
    public RedBookVersion getVersionById(Integer versionId, Integer stage) {
        return RedBookCourseManager.getRedBookVersion(versionId,stage);
    }

    @Override
    public List<RedBookCourse> getCourseList(Integer versionId) {
        return RedBookCourseManager.getRedBookCourseList(versionId);
    }

    @Override
    public List<RedBookCourse> getCourseList(Integer versionId, Integer stage) {
        return RedBookCourseManager.getRedBookCourseList(versionId,stage);
    }

    @Override
    public Boolean checkCourseInStage(Integer courseId, Integer stage) {
        return RedBookCourseManager.checkCourseInStage(courseId,stage);
    }

    @Override
    public RedBookCourse getCourse(Integer courseId) {
        return RedBookCourseManager.getRedBookCourse(courseId);
    }

    @Override
    public RedBookCourse getCourse(Integer courseId, Integer stage) {
        return RedBookCourseManager.getRedBookCourse(courseId,stage);
    }

    @Override
    public List<RedBookUnit> getUnitList(Integer courseId) {
//        return RedBookCourseManager.getRedBookUnitList(courseId);
        List<RedBookUnit> redBookUnitList = RedBookCourseManager.getRedBookUnitList(courseId);
        try {
            List<RedBookUnit> uniqueSortedRedBookUnitList = redBookUnitList.stream()
                    .collect(Collectors.toMap(
                            RedBookUnit::getId,
                            unit -> unit,
                            (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(RedBookUnit::getDisplayOrder))
                    .collect(Collectors.toList());
            return uniqueSortedRedBookUnitList;
        } catch (Exception e) {
            return redBookUnitList;
        }

    }
    @Override
    public RedBookUnit getUnit(Integer unitId) {
        return RedBookCourseManager.getRedBookUnit(unitId);
    }

    @Override
    public List<Integer> getWordIdListByCourseId(Integer courseId) {
        RedBookCourse redBookCourse = RedBookCourseManager.getRedBookCourse(courseId);
        if(redBookCourse!=null){
            return redBookCourse.getWordIdList();
        }
        return null;
    }

    @Override
    public List<Integer> getSentenceIdListByCourseId(Integer courseId) {
        RedBookCourse redBookCourse = RedBookCourseManager.getRedBookCourse(courseId);
        if(redBookCourse!=null){
            return redBookCourse.getSentenceIdList();
        }
        return null;
    }



    @Override
    public List<WordBean> getWordBeanListByUnitId(Integer unitId, RedBookContentTypeEnum redBookContentTypeEnum) {
        if(redBookContentTypeEnum==null){
            redBookContentTypeEnum = RedBookContentTypeEnum.WORD;
        }
        List<WordBean> wordList = new ArrayList<>();
        RedBookUnit unit = RedBookCourseManager.getRedBookUnit(unitId);
        if(RedBookConstant.SENTENCE_CONTENT_TYPE_ENUM_LIST.contains(redBookContentTypeEnum)){
            if(unit!=null && unit.getSentenceIdList(redBookContentTypeEnum)!=null){
                for (Integer wid:unit.getSentenceIdList(redBookContentTypeEnum)) {
                    wordList.add(RedBookCourseManager.getWordBeanById(wid));
                }
            }
        }else {
            if(unit!=null && unit.getWordIdList(redBookContentTypeEnum)!=null){
                for (Integer wid:unit.getWordIdList(redBookContentTypeEnum)) {
                    wordList.add(RedBookCourseManager.getWordBeanById(wid));
                }
            }
        }

        return wordList;
    }

    @Override
    public List<Integer> getWordIdListByUnitId(Integer unitId, RedBookContentTypeEnum redBookContentTypeEnum) {
        RedBookUnit unit = RedBookCourseManager.getRedBookUnit(unitId);
        if(unit!=null){
            if(RedBookConstant.SENTENCE_CONTENT_TYPE_ENUM_LIST.contains(redBookContentTypeEnum)){
                return unit.getSentenceIdList(redBookContentTypeEnum);
            }
            return unit.getWordIdList(redBookContentTypeEnum);
        }
        return null;
    }


    @Override
    public WordBean getWordBeanById(Integer wordId) {
        return RedBookCourseManager.getWordBeanById(wordId);
    }

    @Override
    public String getSoundmark(String spelling) {
        String soundmark = SoundMarkManager.getSoundmark(spelling);
        if(soundmark == null || soundmark.equals("0")){
            soundmark = redBookCourseDao.getSoundMark(spelling);
            if(soundmark!=null && !soundmark.equals("")&&!soundmark.equals("0")){
                if(Character.isUpperCase(spelling.charAt(0))){//如果是大写字母开头
                    soundmark = soundmark.substring(0,1).toUpperCase()+soundmark.substring(1);
                }else{
                    soundmark = soundmark.substring(0,1).toLowerCase()+soundmark.substring(1);
                }
            }else{
                soundmark = spelling;
            }
            SoundMarkManager.addSoundmark(spelling, soundmark);
        }
        return soundmark;
    }

    @Override
    public Map<String, Object> getSentenceElementWordBean(int wordId) {
        return RedBookCourseManager.getSentenceElementWordBean(wordId);
    }

    @Override
    public boolean isHomonym(String spelling) {
        return HomonymManager.isHomonym(spelling);
    }

    @Override
    public String getWordMnemonics(int wordId) {
        Map<String,String> wordMnemonicsInfo = RedBookCourseManager.getWordMnemonics(wordId);
        if(wordMnemonicsInfo!=null){
            if(wordMnemonicsInfo.get(wordId+"")!=null){
                return wordMnemonicsInfo.get(wordId+"");
            }
            return wordMnemonicsInfo.get("mnemonics");
        }else{
            String result = null;
            WordBean word = RedBookCourseManager.getWordBeanById(wordId);
            if(result==null){
                Map<String, String> dbWordMnemonics = redBookCourseDao.getWordMnemonics(word.getSpelling());
                if (dbWordMnemonics==null){
                    return null;
                }else{
                    wordMnemonicsInfo = new HashMap<String, String>();
                    result = dbWordMnemonics.get("mnemonics");
                    wordMnemonicsInfo.put("mnemonics",result);
                    try{
                        String wordIdsStr1 = dbWordMnemonics.get("other_word_ids1");
                        if(wordIdsStr1!=null&&wordIdsStr1.length()>0){
                            String mnemonics1 = dbWordMnemonics.get("other_mnemonics1");
                            for(String wid:wordIdsStr1.split("\\|")){
                                if(wid.length()>0){
                                    if(Integer.valueOf(wid)==wordId){
                                        result = mnemonics1;
                                    }
                                    wordMnemonicsInfo.put(wid,mnemonics1);
                                }
                            }
                        }

                        String wordIdsStr2 = dbWordMnemonics.get("other_word_ids2");
                        if(wordIdsStr2!=null&&wordIdsStr2.length()>0){
                            String mnemonics2 = dbWordMnemonics.get("other_mnemonics2");
                            for(String wid:wordIdsStr2.split("\\|")){
                                if(wid.length()>0){
                                    if(Integer.valueOf(wid)==wordId){
                                        result = mnemonics2;
                                    }
                                    wordMnemonicsInfo.put(wid,mnemonics2);
                                }
                            }
                        }

                        String noMnemonicsWordIds = dbWordMnemonics.get("no_mnemonics_word_ids");
                        if(noMnemonicsWordIds!=null&&noMnemonicsWordIds.length()>0){
                            for(String wid:noMnemonicsWordIds.split("\\|")){
                                if(wid.length()>0){
                                    if(Integer.valueOf(wid)==wordId){
                                        result = "";
                                    }
                                    wordMnemonicsInfo.put(wid,"");
                                }
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    RedBookCourseManager.addWordMnemonics(word.getSpelling(),wordMnemonicsInfo);
                }
            }
            return result;
        }
    }

    @Override
    public List<WordBean> getWordDisturbList(String spelling) {
        List<Integer> wordDisturbWordIdList = RedBookCourseManager.getWordDisturbWordIdList(spelling);
        if(wordDisturbWordIdList==null){
            wordDisturbWordIdList = redBookCourseDao.getWordDisturbWordIdList(spelling);
            if(wordDisturbWordIdList==null){
                wordDisturbWordIdList = new ArrayList<>();
            }
            RedBookCourseManager.saveWordDisturbWordIdList(spelling,wordDisturbWordIdList);
        }
        if (wordDisturbWordIdList.size()<=0) {
            return null;
        }
        List<WordBean> beanList = new ArrayList<>();
        WordBean wordBean;
        for (Integer wid : wordDisturbWordIdList) {
            wordBean = RedBookCourseManager.getWordBeanById(wid);
            if (wordBean != null) {
                beanList.add(wordBean);
            }
        }
        return beanList;
    }


    @Override
    public RedBookArticle getArticle(Integer articleId) {
        return RedBookCourseManager.getRedBookArticle(articleId);
    }

    @Override
    public List<RedBookPhonicsLetter> getPhonicsLetterListByUnitId(Integer unitId) {
        return RedBookCourseManager.getPhonicsLetterListByUnitId(unitId);
    }

    @Override
    public List<RedBookPhonicsLetterPlus> getNewPhonicsLetterListByUnitId(Integer unitId) {
        return RedBookCourseManager.getNewPhonicsLetterListByUnitId(unitId);
    }

    @Override
    public RedBookPhonicsLetter getPhonicsLetterById(Integer letterId) {
        return RedBookCourseManager.getPhonicsLetterById(letterId);
    }

    @Override
    public RedBookPhonicsLetterPlus getNewPhonicsLetterById(Integer letterId) {
        return RedBookCourseManager.getNewPhonicsLetterById(letterId);
    }

    @Override
    public RedBookPhonicsLetter getPhonicsLetterByWordId(Integer wordId) {
        return RedBookCourseManager.getPhonicsLetterByWordId(wordId);
    }

    @Override
    public List<GrammarTitleBean> getGrammarTitleList(Integer unitId) {
        return RedBookCourseManager.getGrammarTitleList(unitId);
    }

    @Override
    public GrammarTitleBean getGrammarTitle(Integer titleId) {
        return RedBookCourseManager.getGrammarTitle(titleId);
    }

    @Override
    public List<GrammarQuestionBean> getGrammarQuestionList(Integer unitId) {
        return RedBookCourseManager.getGrammarQuestionList(unitId);
    }

    @Override
    public List<SimulationQuestionBean> getGrammarCourseQuestionList(Integer courseId) {
        return RedBookCourseManager.getGrammarCourseQuestionList(courseId);
    }

    @Override
    public List<ResourcePublishRecords> getResourcePublishRecordsList(Integer lastPublishId) {
        return iResourcePublishRecordsDao.getResourcePublishRecordsList(lastPublishId);
    }

    @Override
    public Integer getResourcePublishRecordsLastPublishId() {
        return RedBookCourseManager.getLastPublishId();
    }


    @Override
    public Map<String, Object> getPhonogramWordBean(int phonogramWordId) {
        return RedBookCourseManager.getPhonogramWordBean(phonogramWordId);
    }

    @Override
    public List<String> getPhonogramWordIdListbyPhonogramId(int phonogramWordId) {
        return RedBookCourseManager.getPhonogramWordIdListbyPhonogramId(phonogramWordId);
    }

    @Override
    public List<String> getPhonogramWordIdAllList() {
        return RedBookCourseManager.getPhonogramWordIdAllList();
    }

    @Override
    public List<WordAbbr> getWordAbbrList() {
        return RedBookCourseManager.getWordAbbrList();
    }

    @Override
    public String getWordImgUrl(String spelling) {
        String wordImgUrl = RedBookCourseManager.getWordImgUrl(spelling);
        if(null==wordImgUrl){
            wordImgUrl = redBookCourseDao.getWordImgUrl(spelling);
            if(null==wordImgUrl){
                wordImgUrl = "";
            }
            RedBookCourseManager.addWordImgUrl(spelling,wordImgUrl);
        }
        return wordImgUrl;
    }

    @Override
    public String[] getWordSpeakWordMeaningList(String spelling) {
        String wordMeaning = RedBookCourseManager.getWordSpeakWordMeaning(spelling);
        if(null==wordMeaning){
            wordMeaning = redBookCourseDao.getWordSpeakWordMeaning(spelling);
            if(null==wordMeaning){
                wordMeaning = "";
            }
            RedBookCourseManager.addWordSpeakWordMeaning(spelling,wordMeaning);
        }
        if(wordMeaning.equals("")||wordMeaning.length()<=0){
            return null;
        }
        return wordMeaning.split("\\|");
    }

    @Override
    public Boolean isSingleWordPhonicsCourse(Integer courseId) {
        return courseId!=1589;
    }

    @Override
    public WriteUnitTitleBean getSyncWriteMethodByUnitId(Integer unitId) {
        return RedBookCourseManager.getSyncWriteMethodByUnitId(unitId);
    }

    @Override
    public ResourceReadArticleDTO getResourceReadArticleDTO(Integer integer) {
        return RedBookCourseManager.getResourceReadArticleDTO(integer);
    }

    @Override
    public ResourceUnitListenDTO getResourceListenDTO(Integer unitId) {
        return RedBookCourseManager.getResourceListenDTO(unitId);
    }

    @Override
    public WordUseBean getWordUseBean(String s, String s1) {
        return WordUseManager.getWordUseBean(s,s1);
    }

}