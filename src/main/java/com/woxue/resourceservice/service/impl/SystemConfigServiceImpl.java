package com.woxue.resourceservice.service.impl;

import com.woxue.resourceservice.dao.ISystemConfigDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.woxue.common.model.SystemInfoBean;
import com.woxue.redbookresource.service.ISystemConfigService;
import com.woxue.resourceservice.util.SystemInfoManager;

@Service("systemConfigService")
public class SystemConfigServiceImpl implements ISystemConfigService {
	@Autowired
    ISystemConfigDao systemConfigDao;
	
	@Override
	public SystemInfoBean getSystemInfoBean(Integer cid) {
		SystemInfoBean systemInfoBean = SystemInfoManager.getSystemInfoBean(cid);
        if(systemInfoBean == null){
        	systemInfoBean = systemConfigDao.getSystemInfoBean(cid);
        	if(null!=systemInfoBean) {
        		SystemInfoManager.addSystemInfoBean(systemInfoBean);
        	}
		}
		return systemInfoBean;
	}

}
