package com.woxue.resourceservice.service.impl;

import com.woxue.common.model.redBook.PictureBook;
import com.woxue.common.model.redBook.PictureBookContent;
import com.woxue.common.model.redBook.PictureBookSentence;
import com.woxue.redbookresource.service.IPictureBookService;
import com.woxue.resourceservice.util.PictureBookManager;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("pictureBookService")
public class PictureBookServiceImpl implements IPictureBookService {
    @Override
    public List<PictureBook> getPictureBookList() {
        return PictureBookManager.getAllPictureBookList();
    }

    @Override
    public PictureBook getPictureBook(Integer integer) {
        return PictureBookManager.getPictureBook(integer);
    }

    @Override
    public List<PictureBookContent> getPictureBookContentList(Integer integer) {
        return PictureBookManager.getPictureBookContentList(integer);
    }

    @Override
    public PictureBookContent getPictureBookContent(Integer integer) {
        return PictureBookManager.getPictureBookContent(integer);
    }

    @Override
    public List<PictureBookSentence> getPictureBookSentenceList(Integer integer) {
        return PictureBookManager.getPictureBookSentenceList(integer);
    }

    @Override
    public PictureBookSentence getPictureBookSentence(Integer integer) {
        return PictureBookManager.getPictureBookSentence(integer);
    }

    @Override
    public Date getLastUpdateTime() {
        return null;
    }

    @Override
    public List<Map> getUpdateTimeList() {
        return PictureBookManager.getUpdateDate();
    }
}
