package com.woxue.resourceservice.service.impl;


import com.redbook.kid.common.model.NurserySongDO;
import com.redbook.kid.common.model.NurserySongSentenceDO;
import com.redbook.kid.common.model.NurserySongStatusEnum;
import com.redbook.kid.common.model.NurserySongWithSentencesDTO;
import com.redbook.kid.resource.NurserySongResourceService;
import com.woxue.resourcemanage.dao.INurserySongDao;
import com.woxue.resourcemanage.dao.INurserySongSentenceDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 儿歌资源服务实现类
 * 提供给其他模块通过 Dubbo 调用的服务
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
@Slf4j
@Service("NurserySongResourceService")
public class NurserySongResourceServiceImpl implements NurserySongResourceService {

    @Autowired
    private INurserySongDao nurserySongDao;

    @Autowired
    private INurserySongSentenceDao nurserySongSentenceDao;

    @Override
    public NurserySongDO getSongById(Integer songId) {
        if (songId == null) {
            return null;
        }
        
        try {
            return nurserySongDao.getSongById(songId);
        } catch (Exception e) {
            log.error("根据ID获取儿歌信息异常，songId: {}", songId, e);
            return null;
        }
    }

    @Override
    public List<NurserySongSentenceDO> getSentencesBySongId(Integer songId) {
        if (songId == null) {
            return null;
        }
        
        try {
            return nurserySongSentenceDao.getSentencesBySongId(songId);
        } catch (Exception e) {
            log.error("根据儿歌ID获取句子列表异常，songId: {}", songId, e);
            return null;
        }
    }

    @Override
    public NurserySongWithSentencesDTO getSongWithSentences(Integer songId) {
        if (songId == null) {
            return null;
        }
        
        try {
            NurserySongDO song = getSongById(songId);
            if (song == null) {
                return null;
            }
            
            List<NurserySongSentenceDO> sentences = getSentencesBySongId(songId);
            return new NurserySongWithSentencesDTO(song, sentences);
            
        } catch (Exception e) {
            log.error("获取儿歌完整信息异常，songId: {}", songId, e);
            return null;
        }
    }

    @Override
    public List<NurserySongDO> getAvailableSongs(NurserySongStatusEnum status) {
        try {
            if (status != null) {
                return nurserySongDao.getSongsByStatus(status.getCode());
            } else {
                // 默认返回上架状态的儿歌
                return nurserySongDao.getSongsByStatus(NurserySongStatusEnum.ONLINE.getCode());
            }
        } catch (Exception e) {
            log.error("获取可用儿歌列表异常，status: {}", status, e);
            return null;
        }
    }

    @Override
    public List<NurserySongDO> getSongsByDifficulty(Integer difficultyLevel) {
        if (difficultyLevel == null || difficultyLevel < 1 || difficultyLevel > 5) {
            return null;
        }
        
        try {
            return nurserySongDao.getSongsByDifficulty(difficultyLevel);
        } catch (Exception e) {
            log.error("根据难度等级获取儿歌列表异常，difficultyLevel: {}", difficultyLevel, e);
            return null;
        }
    }


}
