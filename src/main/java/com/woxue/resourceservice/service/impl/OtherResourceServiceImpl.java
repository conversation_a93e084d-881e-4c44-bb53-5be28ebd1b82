package com.woxue.resourceservice.service.impl;

import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopic;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent;
import com.woxue.redbookresource.service.IOtherResourceService;
import com.woxue.resourceservice.util.OtherResourceManager;
import com.woxue.resourceservice.util.ResourceSpokenManager;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Map.Entry;

@Service("otherResourceService")
public class OtherResourceServiceImpl implements IOtherResourceService {

	@Override
	public List<WordBean> getVocabularyNumQuizWordList(Integer level) {
		return OtherResourceManager.getVocabularyNumQuizWordList(level+1);
	}

	/**
	 * 获取一个随机数
	 * @param intSize 随机的范围
	 * @return 
	 */
	private static final Random random = new Random();
	public static Integer getRandomInt(Integer intSize){
		return random.nextInt(intSize);
	}

	/**
	 * 获取键盘熟练度单词
	 */
	@Override
	public List<List<Map<String, Object>>> getKeyBoardSkilledWordList(Integer isFirst,Map<String, Integer> wordMap) {
		
		Integer ranIndex=-1;
		List<Integer> useedIds = new ArrayList<Integer>(); 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		List<List<Map<String, Object>>> list2 = new ArrayList<List<Map<String, Object>>>();
		List<Integer> keyBoardSkilledWordIds = OtherResourceManager.gekeyBoardSkilledWordIdList();
		if(isFirst==1&&wordMap==null){
			while(list2.size()<10){
				list = new ArrayList<Map<String, Object>>();
				while(list.size()<10){
					ranIndex = keyBoardSkilledWordIds.get(getRandomInt(keyBoardSkilledWordIds.size()));
					if(!useedIds.contains(ranIndex)){
						useedIds.add(ranIndex);
						Map<String, Object> m =  OtherResourceManager.getKeyBoardSkilledWord(ranIndex);
						list.add(m);
					}
				}
				list2.add(list);
			}
			
		}else{
			 List<Entry<String,Integer>> lists = new ArrayList(wordMap.entrySet());
			 Collections.sort(lists, new Comparator<Entry<String, Integer>>(){
				@Override
				public int compare(Entry<String, Integer> o1,
						Entry<String, Integer> o2) {
	                int compare = (o1.getValue()).compareTo(o2.getValue());
	                return compare;
				}
	        });
			while(list.size()<10){
				for (int i = 0; i < 10; i++) {
					ranIndex = keyBoardSkilledWordIds.get(getRandomInt(keyBoardSkilledWordIds.size()));
					if(!useedIds.contains(ranIndex)){
						useedIds.add(ranIndex);
						Map<String, Object> m =  OtherResourceManager.getKeyBoardSkilledWord(ranIndex);
						if(m.get("spelling").toString().indexOf(lists.get(i).getKey())!=-1){
							list.add(m);
							break;
						}else{
							continue;
						}
					}
				}
			} 
		}
		return list2;	
	}

	@Override
	public List<ResourceSpokenTopic> getSpokenTopicList() {
		return ResourceSpokenManager.getAllResourceSpokenTopic();
	}

	@Override
	public List<ResourceSpokenTopicContent> getSpokenTopicContentList(Integer topicId) {
		return ResourceSpokenManager.getResourceSpokenTopicContentByTopicId(topicId);
	}

}
