package com.woxue.resourceservice.dao;

import com.redbook.kid.common.model.*;
import org.apache.ibatis.annotations.*;

import java.util.ArrayList;
import java.util.List;


public interface ResourceKidMapper {


    // resource_kid_course表操作
    @Insert("INSERT INTO resource_kid_course (type, name_cn, name_en, grade,branch) VALUES (#{type}, #{nameCn}, #{nameEn},#{grade}, #{branch})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCourse(ResourceKidCourse course);


    @Select("SELECT * FROM resource_kid_course WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "type", column = "type"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "grade", column = "grade"),
            @Result(property = "branchTime", column = "branch_time"),
            @Result(property = "branch", column = "branch")
    })
    ResourceKidCourse getCourseById(int id);

    @Update("UPDATE resource_kid_course SET type = #{type}, name_cn = #{nameCn}, name_en = #{nameEn},grade=#{grade}, branch = #{branch} WHERE id = #{id}")
    void updateCourse(ResourceKidCourse course);

    @Delete("DELETE FROM resource_kid_course WHERE id = #{id}")
    void deleteCourse(int id);

    @Select("SELECT * FROM resource_kid_course")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "type", column = "type"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "grade", column = "grade"),
            @Result(property = "branchTime", column = "branch_time"),
            @Result(property = "branch", column = "branch")
    })
    List<ResourceKidCourse> getAllCourses();

    // resource_kid_picture_book表操作
    @Insert("INSERT INTO resource_kid_picture_book (unit_id, title, en_title, thumbnail,title_sound, sort, level, desc_ch, desc_en) " +
            "VALUES (#{unitId}, #{title}, #{enTitle}, #{thumbnail}, #{titleSound} ,#{sort}, #{level}, #{descCh}, #{descEn})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPictureBook(ResourceKidPictureBook pictureBook);

    @Select("SELECT * FROM resource_kid_picture_book WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "enTitle", column = "en_title"),
            @Result(property = "thumbnail", column = "thumbnail"),
            @Result(property = "titleSound", column = "title_sound"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "level", column = "level"),
            @Result(property = "descCh", column = "desc_ch"),
            @Result(property = "descEn", column = "desc_en")
    })
    ResourceKidPictureBook getPictureBookById(int id);

    @Update("UPDATE resource_kid_picture_book SET unit_id = #{unitId}, title = #{title}, en_title = #{enTitle}, " +
            "thumbnail = #{thumbnail}, title_sound= #{titleSound} ,sort = #{sort}, level = #{level}, desc_ch = #{descCh}, desc_en = #{descEn} WHERE id = #{id}")
    void updatePictureBook(ResourceKidPictureBook pictureBook);

    @Delete("DELETE FROM resource_kid_picture_book WHERE id = #{id}")
    void deletePictureBook(int id);

    @Select("SELECT * FROM resource_kid_picture_book")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "enTitle", column = "en_title"),
            @Result(property = "titleSound", column = "title_sound"),
            @Result(property = "thumbnail", column = "thumbnail"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "level", column = "level"),
            @Result(property = "descCh", column = "desc_ch"),
            @Result(property = "descEn", column = "desc_en")
    })
    List<ResourceKidPictureBook> getAllPictureBooks();

    // resource_kid_picture_book_content表操作
    @Insert("INSERT INTO resource_kid_picture_book_content (picture_book_id, pic_url, sentence_num, sort, unit_id) " +
            "VALUES (#{pictureBookId}, #{picUrl}, #{sentenceNum}, #{sort}, #{unitId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPictureBookContent(ResourceKidPictureBookContent content);

    @Select("SELECT * FROM resource_kid_picture_book_content WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "picUrl", column = "pic_url"),
            @Result(property = "sentenceNum", column = "sentence_num"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "unitId", column = "unit_id")
    })
    ResourceKidPictureBookContent getPictureBookContentById(int id);

    @Update("UPDATE resource_kid_picture_book_content SET picture_book_id = #{pictureBookId}, pic_url = #{picUrl}, " +
            "sentence_num = #{sentenceNum}, sort = #{sort}, unit_id = #{unitId} WHERE id = #{id}")
    void updatePictureBookContent(ResourceKidPictureBookContent content);

    @Delete("DELETE FROM resource_kid_picture_book_content WHERE id = #{id}")
    void deletePictureBookContent(int id);

    @Select("SELECT * FROM resource_kid_picture_book_content")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "picUrl", column = "pic_url"),
            @Result(property = "sentenceNum", column = "sentence_num"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "unitId", column = "unit_id")
    })
    List<ResourceKidPictureBookContent> getAllPictureBookContents();

    // resource_kid_picture_book_sentence表操作
    @Insert("INSERT INTO resource_kid_picture_book_sentence (unit_id, picture_book_id, content_id, example_en_US, " +
            "example_en_US_element, example_zh_CN, example_zh_PY, speaker, sound_file, sort, last_update_time,core_sentence) " +
            "VALUES (#{unitId}, #{pictureBookId}, #{contentId}, #{exampleEnUS}, #{exampleEnUSElement}, #{exampleZhCN}, " +
            "#{exampleZhPY}, #{speaker}, #{soundFile}, #{sort}, #{lastUpdateTime},#{coreSentence})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPictureBookSentence(ResourceKidPictureBookSentence sentence);

    @Select("SELECT * FROM resource_kid_picture_book_sentence WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "exampleEnUS", column = "example_en_US"),
            @Result(property = "exampleEnUSElement", column = "example_en_US_element"),
            @Result(property = "exampleZhCN", column = "example_zh_CN"),
            @Result(property = "exampleZhPY", column = "example_zh_PY"),
            @Result(property = "speaker", column = "speaker"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "coreSentence", column = "core_sentence"),
            @Result(property = "lastUpdateTime", column = "last_update_time")
    })
    ResourceKidPictureBookSentence getPictureBookSentenceById(int id);

    @Update("UPDATE resource_kid_picture_book_sentence SET unit_id = #{unitId}, picture_book_id = #{pictureBookId}, " +
            "content_id = #{contentId}, example_en_US = #{exampleEnUS}, example_en_US_element = #{exampleEnUSElement}, " +
            "example_zh_CN = #{exampleZhCN}, example_zh_PY = #{exampleZhPY}, speaker = #{speaker}, sound_file = #{soundFile}, " +
            "sort = #{sort}, last_update_time = #{lastUpdateTime},core_sentence =#{coreSentence}  WHERE id = #{id}")
    void updatePictureBookSentence(ResourceKidPictureBookSentence sentence);

    @Delete("DELETE FROM resource_kid_picture_book_sentence WHERE id = #{id}")
    void deletePictureBookSentence(int id);

    @Select("SELECT * FROM resource_kid_picture_book_sentence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "exampleEnUS", column = "example_en_US"),
            @Result(property = "exampleEnUSElement", column = "example_en_US_element"),
            @Result(property = "exampleZhCN", column = "example_zh_CN"),
            @Result(property = "exampleZhPY", column = "example_zh_PY"),
            @Result(property = "speaker", column = "speaker"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "coreSentence", column = "core_sentence"),
            @Result(property = "lastUpdateTime", column = "last_update_time")
    })
    List<ResourceKidPictureBookSentence> getAllPictureBookSentences();

    // resource_kid_scene表操作
    @Insert("INSERT INTO resource_kid_scene (course_id, name_cn, name_en, sound_url, img_url, content) " +
            "VALUES (#{courseId}, #{nameCn}, #{nameEn}, #{soundUrl}, #{imgUrl}, #{content})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertScene(ResourceKidScene scene);

    @Select("SELECT * FROM resource_kid_scene WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "imgUrl", column = "img_url"),
            @Result(property = "content", column = "content")
    })
    ResourceKidScene getSceneById(int id);

    @Update("UPDATE resource_kid_scene SET course_id = #{courseId}, name_cn = #{nameCn}, name_en = #{nameEn}, " +
            "sound_url = #{soundUrl}, img_url = #{imgUrl}, content = #{content} WHERE id = #{id}")
    void updateScene(ResourceKidScene scene);

    @Delete("DELETE FROM resource_kid_scene WHERE id = #{id}")
    void deleteScene(int id);

    @Select("SELECT * FROM resource_kid_scene")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "imgUrl", column = "img_url"),
            @Result(property = "content", column = "content")
    })
    List<ResourceKidScene> getAllScenes();


    // resource_kid_unit表操作
    @Insert("INSERT INTO resource_kid_unit (id, course_id, scene_id, topic_name,topic_img, name_cn, name_en, level, display_order) " +
            "VALUES (#{id}, #{courseId}, #{sceneId}, #{topicName},#{topicImg}, #{nameCn}, #{nameEn}, #{level}, #{displayOrder})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertUnit(ResourceKidUnit unit);

    @Select("SELECT * FROM resource_kid_unit WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "sceneId", column = "scene_id"),
            @Result(property = "topicName", column = "topic_name"),
            @Result(property = "topicImg", column = "topic_img"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "level", column = "level"),
            @Result(property = "onLine", column = "on_line"),
            @Result(property = "displayOrder", column = "display_order")
    })
    ResourceKidUnit getUnitById(int id);

    @Update("UPDATE resource_kid_unit SET course_id = #{courseId}, scene_id = #{sceneId}, topic_name = #{topicName}, " +
            "name_cn = #{nameCn}, name_en = #{nameEn}, level = #{level}, display_order = #{displayOrder},topic_img = #{topicImg} WHERE id = #{id}")
    void updateUnit(ResourceKidUnit unit);

    @Delete("DELETE FROM resource_kid_unit WHERE id = #{id}")
    void deleteUnit(int id);

    @Select("SELECT * FROM resource_kid_unit")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "sceneId", column = "scene_id"),
            @Result(property = "topicName", column = "topic_name"),
            @Result(property = "topicImg", column = "topic_img"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "level", column = "level"),
            @Result(property = "displayOrder", column = "display_order")
    })
    List<ResourceKidUnit> getAllUnits();

    // resource_kid_video表操作
    @Insert("INSERT INTO resource_kid_video (id, unit_id, video_url, subtitle_en_url, subtitle_cn_url) " +
            "VALUES (#{id}, #{unitId}, #{videoUrl}, #{subtitleEnUrl}, #{subtitleCnUrl})")
    void insertVideo(ResourceKidVideo video);

    @Select("SELECT * FROM resource_kid_video WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "videoUrl", column = "video_url"),
            @Result(property = "subtitleEnUrl", column = "subtitle_en_url"),
            @Result(property = "subtitleCnUrl", column = "subtitle_cn_url")
    })
    ResourceKidVideo getVideoById(int id);

    @Update("UPDATE resource_kid_video SET unit_id = #{unitId}, video_url = #{videoUrl}, " +
            "subtitle_en_url = #{subtitleEnUrl}, subtitle_cn_url = #{subtitleCnUrl} WHERE id = #{id}")
    void updateVideo(ResourceKidVideo video);

    @Delete("DELETE FROM resource_kid_video WHERE id = #{id}")
    void deleteVideo(int id);

    @Select("SELECT * FROM resource_kid_video")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "videoUrl", column = "video_url"),
            @Result(property = "subtitleEnUrl", column = "subtitle_en_url"),
            @Result(property = "subtitleCnUrl", column = "subtitle_cn_url")
    })
    List<ResourceKidVideo> getAllVideos();

    // resource_kid_word表操作
    @Insert("INSERT INTO resource_kid_word (id, unit_id, spelling, syllable, meaning_en_US, meaning_zh_CN, sound_file, " +
            "img_file, display_order, utime) VALUES (#{id}, #{unitId}, #{spelling}, #{syllable}, #{meaningEnUS}, #{meaningZhCN}, " +
            "#{soundFile}, #{imgFile}, #{displayOrder}, #{utime})")
    void insertWord(ResourceKidWord word);


    // 通过绘本id获取绘本内容
    @Select("SELECT * FROM resource_kid_picture_book_content WHERE picture_book_id = #{pictureBookId}  order by sort")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "picUrl", column = "pic_url"),
            @Result(property = "sentenceNum", column = "sentence_num"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "unitId", column = "unit_id")
    })
    List<ResourceKidPictureBookContent> getPictureBookContentByPictureBookId(int pictureBookId);

    // 通过绘本id获取绘本句子
    @Select("SELECT * FROM resource_kid_picture_book_sentence WHERE picture_book_id = #{pictureBookId}  order by sort")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "exampleEnUS", column = "example_en_US"),
            @Result(property = "exampleEnUSElement", column = "example_en_US_element"),
            @Result(property = "exampleZhCN", column = "example_zh_CN"),
            @Result(property = "exampleZhPY", column = "example_zh_PY"),
            @Result(property = "speaker", column = "speaker"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "coreSentence", column = "core_sentence"),
            @Result(property = "lastUpdateTime", column = "last_update_time")
    })
    List<ResourceKidPictureBookSentence> getPictureBookSentenceByPictureBookId(int pictureBookId);
      // 通过绘本内容id获取绘本句子
    @Select("SELECT * FROM resource_kid_picture_book_sentence WHERE content_id = #{contentId}  order by sort ")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "exampleEnUS", column = "example_en_US"),
            @Result(property = "exampleEnUSElement", column = "example_en_US_element"),
            @Result(property = "exampleZhCN", column = "example_zh_CN"),
            @Result(property = "exampleZhPY", column = "example_zh_PY"),
            @Result(property = "speaker", column = "speaker"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "coreSentence", column = "core_sentence"),
            @Result(property = "lastUpdateTime", column = "last_update_time")
    })
    List<ResourceKidPictureBookSentence> getPictureBookSentenceByPictureBookContentIdId(int contentId);



    // 通过单元id获取单词
    @Select("SELECT * FROM resource_kid_word WHERE unit_id = #{unitId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "spelling", column = "spelling"),
            @Result(property = "syllable", column = "syllable"),
            @Result(property = "meaningEnUS", column = "meaning_en_US"),
            @Result(property = "meaningZhCN", column = "meaning_zh_CN"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "imgFile", column = "img_file"),
            @Result(property = "displayOrder", column = "display_order"),
            @Result(property = "utime", column = "utime")
    })
    ArrayList<ResourceKidWord> getWordByUnitId(int unitId);

    // 通过课程id获取场景
    @Select("SELECT * FROM resource_kid_scene WHERE course_id = #{courseId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "imgUrl", column = "img_url"),
            @Result(property = "content", column = "content")
    })
    List<ResourceKidScene> getSceneByCourseId(int courseId);

    // 通过单元id获取视频
    @Select("SELECT * FROM resource_kid_video WHERE unit_id = #{unitId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "videoUrl", column = "video_url"),
            @Result(property = "subtitleEnUrl", column = "subtitle_en_url"),
            @Result(property = "subtitleCnUrl", column = "subtitle_cn_url")
    })
    ResourceKidVideo getVideoByUnitId(int unitId);


    // resource_kid_word 表原有的根据id查询方法
    @Select("SELECT * FROM resource_kid_word WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "spelling", column = "spelling"),
            @Result(property = "syllable", column = "syllable"),
            @Result(property = "meaningEnUS", column = "meaning_en_US"),
            @Result(property = "meaningZhCN", column = "meaning_zh_CN"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "imgFile", column = "img_file"),
            @Result(property = "displayOrder", column = "display_order"),
            @Result(property = "utime", column = "utime")
    })
    ResourceKidWord getWordById(int id);

    // resource_kid_word 表原有的更新方法
    @Update("UPDATE resource_kid_word SET unit_id = #{unitId}, spelling = #{spelling}, syllable = #{syllable}, " +
            "meaning_en_US = #{meaningEnUS}, meaning_zh_CN = #{meaningZhCN}, sound_file = #{soundFile}, " +
            "img_file = #{imgFile}, display_order = #{displayOrder}, utime = #{utime} WHERE id = #{id}")
    void updateWord(ResourceKidWord word);

    // resource_kid_word 表原有的删除方法
    @Delete("DELETE FROM resource_kid_word WHERE id = #{id}")
    void deleteWord(int id);

    // resource_kid_word 表原有的获取所有单词方法
    @Select("SELECT * FROM resource_kid_word")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "spelling", column = "spelling"),
            @Result(property = "syllable", column = "syllable"),
            @Result(property = "meaningEnUS", column = "meaning_en_US"),
            @Result(property = "meaningZhCN", column = "meaning_zh_CN"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "imgFile", column = "img_file"),
            @Result(property = "displayOrder", column = "display_order"),
            @Result(property = "utime", column = "utime")
    })
    List<ResourceKidWord> getAllWords();

    @Select("SELECT * FROM resource_kid_unit WHERE course_id = #{courseId} order by display_order asc ")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "sceneId", column = "scene_id"),
            @Result(property = "topicName", column = "topic_name"),
            @Result(property = "topicImg", column = "topic_img"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "level", column = "level"),
            @Result(property = "onLine", column = "on_line"),
            @Result(property = "displayOrder", column = "display_order")
    })
    List<ResourceKidUnit> getUnitByCourseId(int courseId);
    @Select("SELECT * FROM resource_kid_unit WHERE scene_id = #{sceneId}  order by display_order asc ")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "sceneId", column = "scene_id"),
            @Result(property = "topicName", column = "topic_name"),
            @Result(property = "topicImg", column = "topic_img"),
            @Result(property = "nameCn", column = "name_cn"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "level", column = "level"),
            @Result(property = "onLine", column = "on_line"),
            @Result(property = "displayOrder", column = "display_order")
    })
    List<ResourceKidUnit> getUnitBySceneId(int sceneId);
    @Select("SELECT * FROM resource_kid_picture_book WHERE unit_id = #{unitId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "enTitle", column = "en_title"),
            @Result(property = "thumbnail", column = "thumbnail"),
            @Result(property = "titleSound", column = "title_sound"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "level", column = "level"),
            @Result(property = "descCh", column = "desc_ch"),
            @Result(property = "descEn", column = "desc_en")
    })
    ResourceKidPictureBook getPictureBookByUnitId(int unitId);
    @Select("SELECT * FROM resource_kid_letter WHERE unit_id = #{unitId}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "letter", column = "letter"),
    })
    ResourceKidLetter getLetterByUnitId(int unitId);

    @Insert("INSERT INTO resource_kid_letter (unit_id, letter) VALUES (#{unitId}, #{letter})")
    void insertLetter(@Param("unitId") Integer unitId, @Param("letter") String letter);
    @Update("UPDATE resource_kid_letter SET letter = #{letter} WHERE unit_id = #{unitId}")
    void updateLetter( @Param("unitId") Integer unitId, @Param("letter") String letter);

    /**
     * CREATE TABLE `resource_kid_letter_example` (
     *   `id` int NOT NULL,
     *   `letter_id` int NOT NULL COMMENT '字母 id',
     *   `spell` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '拼写',
     *   `meaning_zh_CN` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '中文释义',
     *   `sound_file` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
     *   `img_file` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
     *   `display_order` int NOT NULL DEFAULT '0' COMMENT '排序值',
     *   PRIMARY KEY (`id`)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
     * @param letterId
     * @return
     */
    @Select("SELECT * FROM resource_kid_letter_example WHERE letter_id = #{letterId} order by display_order asc")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "letterId", column = "letter_id"),
            @Result(property = "spell", column = "spell"),
            @Result(property = "meaningZhCN", column = "meaning_zh_CN"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "imgFile", column = "img_file"),
            @Result(property = "displayOrder", column = "display_order")
    })
    List<ResourceKidLetterExample> getLetterExampleByLetterId(int letterId);

    @Insert("INSERT INTO resource_kid_letter_example (letter_id, spell, meaning_zh_CN, sound_file, img_file, display_order) VALUES (#{letterId}, #{spell}, #{meaningZhCN}, #{soundFile}, #{imgFile}, #{displayOrder})")
    void insertLetterExample(ResourceKidLetterExample letterExample);

    @Update("UPDATE resource_kid_letter_example SET spell = #{spell}, meaning_zh_CN = #{meaningZhCN}, sound_file = #{soundFile}, img_file = #{imgFile}, display_order = #{displayOrder} WHERE id = #{id}")
    void updateLetterExample(ResourceKidLetterExample letterExample);

    @Delete("DELETE FROM resource_kid_letter_example WHERE id = #{id}")
    void deleteLetterExample(int id);

    @Update("UPDATE resource_kid_unit SET on_line = #{onLine} WHERE id = #{unitId}")
    void setUnitOnLine(@Param("unitId") Integer unitId, @Param("onLine") Boolean onLine);
}