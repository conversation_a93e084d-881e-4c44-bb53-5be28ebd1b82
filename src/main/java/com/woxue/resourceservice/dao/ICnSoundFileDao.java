package com.woxue.resourceservice.dao;

import org.apache.ibatis.annotations.Param;

/**
 * 中文语音文件DAO接口
 */
public interface ICnSoundFileDao {
    
    /**
     * 根据中文文本查询语音文件路径
     * 
     * @param cn 中文文本
     * @return 语音文件路径
     */
    String getSoundFileByCn(@Param("cn") String cn);
    
    /**
     * 保存中文文本和对应的语音文件路径
     * 
     * @param cn 中文文本
     * @param soundFile 语音文件路径
     * @return 影响的行数
     */
    int saveCnSoundFile(@Param("cn") String cn, @Param("soundFile") String soundFile);
} 