package com.woxue.resourceservice.dao;

import com.woxue.redbookquestionBank.model.SuitBean;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("questionSuitDao")
public interface IQuestionSuitDao {

    List<SuitBean> getUnitPaper(@Param("courseId") Integer courseId, @Param("unitId") Integer unitId);

    // 插入新的 suit 数据并返回主键 ID
    void insertSuit(@Param("originalSuitId") int originalSuitId, @Param("newSuit") Map<String, Object> newSuit);

    // 插入 suit_section 数据
    void insertSuitSections(@Param("originalSuitId") int originalSuitId, @Param("newSuitId") int newSuitId);

    // 查询新旧 section_id 映射
    List<Map<String, Integer>> getSectionIdMapping(@Param("newSuitId") int newSuitId);

    // 查询原始 suit_section_question 数据
    List<Map<String, Integer>> getOriginalSectionQuestions(@Param("originalSuitId") int originalSuitId);

    // 批量插入 suit_section_question 数据
    void batchInsertSectionQuestions(@Param("questions") List<Map<String, Integer>> questions);
}
