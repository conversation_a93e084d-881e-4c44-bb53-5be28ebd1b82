package com.woxue.resourceservice.dao;

import com.woxue.common.model.SimulationQuestionBean;
import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("redBookCourseDao")
public interface IRedBookCourseDao {
    /**
     * 获取版本资源
     * @return
     */
    RedBookVersion getVersion(Integer versionId);
    List<RedBookVersion> getVersionList();
    List<RedBookVersion> getVersionListByType(Integer versionType);
    /**
     * 获取版本下所有课程资源
     * @param versionId
     * @return
     */
    List<RedBookCourse> getCourseList(Integer versionId);
    RedBookCourse getCourse(Integer courseId);

    RedBookCourse getCourseByUnitId(Integer unitId);

    /**
     * 获取课程下所有单元资源
     * @param courseId
     * @return
     */
    List<RedBookUnit> getUnitList(Integer courseId);

    /**
     * 加载课文
     * @param articleId
     * @return
     */
    RedBookArticle getArticle(Integer articleId);

    /**
     * 加载所有课文
     * @return
     */
    List<RedBookArticle> getArticleList();

    /**
     * 加载自然拼读单元中所有发音字母
     * @param unitId
     * @return
     */
    List<RedBookPhonicsLetter> getPhonicsLetterList(@Param("unitId")Integer unitId);

    List<RedBookPhonicsLetterPlus> getNewPhonicsLetterList(@Param("unitId")Integer unitId);

    /**
     * 获取自然拼读中的发音字母例词Id列表
     * @param letterId
     * @param isQuizWord 是否是测试单词
     * @return
     */
    List<Integer> getWordIdListByPhonicsLetterId(@Param("letterId")Integer letterId,@Param("isQuizWord")boolean isQuizWord);
    /**
     * 获取自然拼读中的发音字母Id分段列表
     * @param letterId
     * @return
     */
    List<RedBookPhonicsLetterPart> getPhonicsLetterPartList(@Param("letterId")Integer letterId);
    /**
     * 获取单词Id列表 根据单元id
     * @param unitId
     * @param level 1、2、3用于区分词汇一、二、三  为空则是全部单词
     * @return
     */
    List<Integer> getWordIdListByUnitId(@Param("unitId")Integer unitId,@Param("level")Integer level);
    List<Integer> getSentenceWordIdListByUnitId(@Param("unitId")Integer unitId,@Param("level")Integer level);

    /***
     * 根据unitId和parentId查询
     * @param unitId
     * @param parentId
     * @return
     */
    List<GrammarTitleBean> getGrammarTitleList(@Param("unitId") Integer unitId,
                                                   @Param("parentId") Integer parentId);
    /***
     * 根据unitId和parentId查询子标题
     * @param unitId
     * @param titleId
     * @return
     */
    List<GrammarSubtitleBean> getGrammarSubtitleList(@Param("unitId") Integer unitId,
                                                   @Param("titleId") Integer titleId);
    /**
     * 根据unitId和SubtitleId查询对应的内容
     * @param unitId
     * @param subtitleId
     * @return
     */
    List<GrammarContentBean> getGrammarContentList(@Param("unitId") Integer unitId, @Param("subtitleId") Integer subtitleId);

    /**
     * 获取本节检测试题
     * @param unitId
     * @return
     */
    List<GrammarQuestionBean> getGrammarQuestionList(Integer unitId);
    List<GrammarQuestionContentBean> getGrammarQuestionContentList(Integer questionId);

    /**
     * 本章检测题
     * @param courseId
     * @return
     */
    List<SimulationQuestionBean> getGrammarCourseQuestionList(@Param("courseId") Integer courseId);

    /**
     * 获取词汇量测试单词列表
     * @param wordLevel
     * @return
     */
    List<WordBean> loadVocabularyNumQuizWordList(@Param("wordLevel") Integer wordLevel);
    /**
     获取键盘熟练度单词
     */
    List<Map<String,Object>> getKeyBoardSkilledWordList();

    String getSoundMark(String spelling);
    /**
     * 获取单词“助记”内容
     * @param spelling
     * @return
     */
    Map<String, String> getWordMnemonics(@Param("spelling")String spelling);

    /**
     * 获取对应拼写的干扰单词id列表
     * @param spelling
     * @return
     */
    List<Integer> getWordDisturbWordIdList(@Param("spelling")String spelling);

    /**
     * 获取单词信息
     * @param wordId
     * @return
     */
    WordBean loadWordBean(int wordId);

    /**
     * 同步句子句块
     * @return
     */
    Map<String, Object> getSentenceElement(int wordId);
    /**
     * 获取音标例词列表（旧版）
     * @return
     */
//    List<Map<String, Object>> getPhonogramWordList();

    /**
     * 获取音标id列表
     * @param unitId
     * @return
     */
    List<Integer> getPhoneticIdList(Integer unitId);

    /**
     * 获取音标组合
     * @param unitId
     * @return
     */
    List<PhoneticCompose> getPhoneticComposeList(Integer unitId);

    /**
     * 获取音标例词
     * @param unitId
     * @return
     */
    List<PhoneticExample> getPhoneticExampleList(Integer unitId);

    /**
     * 获取单词配图地址
     * @param spelling
     * @return
     */
    String getWordImgUrl(@Param("spelling")String spelling);

    /**
     * 获取手动编辑的“说词义”的单词词义
     * @param spelling
     * @return
     */
    String getWordSpeakWordMeaning(@Param("spelling")String spelling);


    boolean deleteVersionStage(@Param("versionId") Integer versionId);

    boolean insertVersionStage(@Param("versionId") Integer versionId, @Param("stage") Integer stage);

    List<RedBookVersionStage>getVersionStageList(@Param("versionId") Integer versionId);

    boolean deleteCourseStage(@Param("courseId") Integer courseId);

    boolean insertCourseStage(@Param("courseId") Integer courseId, @Param("stage") Integer stage);

    List<RedBookCourseStage>getCourseStageList(@Param("courseId") Integer courseId);

    List<RedBookPhonicsLetterWordInfo> gePhonicsLetterWordInfoList(Integer letterId);

    void updateCourseBranchIdByPrimaryKey(@Param("id") Integer id);

    void updateVersionBranchIdByPrimaryKey(@Param("id") int id);
}
