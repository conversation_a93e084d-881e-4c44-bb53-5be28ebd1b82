package com.woxue.resourceservice.dao;

import com.redbook.kid.common.model.phonics.*;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 少儿自然拼读数据访问接口
 */
public interface KidPhonicsMapper {

    // KidPhonicsCourse表操作
    @Insert("INSERT INTO kid_phonics_course (course_code, course_name_zh, course_name_en, description, cover_img, branch, publish_status, sequence) " +
            "VALUES (#{courseCode}, #{courseNameZh}, #{courseNameEn}, #{description}, #{coverImg}, #{branch}, #{publishStatus}, #{sequence})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertCourse(KidPhonicsCourse course);

    @Select("SELECT * FROM kid_phonics_course WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseCode", column = "course_code"),
            @Result(property = "courseNameZh", column = "course_name_zh"),
            @Result(property = "courseNameEn", column = "course_name_en"),
            @Result(property = "description", column = "description"),
            @Result(property = "coverImg", column = "cover_img"),
            @Result(property = "branch", column = "branch"),
            @Result(property = "publishStatus", column = "publish_status"),
            @Result(property = "publishTime", column = "publish_time"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsCourse getCourseById(Integer id);

    @Update("UPDATE kid_phonics_course SET course_code = #{courseCode}, course_name_zh = #{courseNameZh}, " +
            "course_name_en = #{courseNameEn}, description = #{description}, cover_img = #{coverImg}, " +
            "branch = #{branch}, publish_status = #{publishStatus}, sequence = #{sequence} WHERE id = #{id}")
    void updateCourse(KidPhonicsCourse course);

    @Delete("DELETE FROM kid_phonics_course WHERE id = #{id}")
    void deleteCourse(Integer id);

    @Select("SELECT * FROM kid_phonics_course ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseCode", column = "course_code"),
            @Result(property = "courseNameZh", column = "course_name_zh"),
            @Result(property = "courseNameEn", column = "course_name_en"),
            @Result(property = "description", column = "description"),
            @Result(property = "coverImg", column = "cover_img"),
            @Result(property = "branch", column = "branch"),
            @Result(property = "publishStatus", column = "publish_status"),
            @Result(property = "publishTime", column = "publish_time"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsCourse> getAllCourses();

    // KidPhonicsUnit表操作
    @Insert("INSERT INTO kid_phonics_unit (course_id, unit_code, unit_name, phoneme_type, description, sequence, status) " +
            "VALUES (#{courseId}, #{unitCode}, #{unitName}, #{phonemeType}, #{description}, #{sequence}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertUnit(KidPhonicsUnit unit);

    @Select("SELECT * FROM kid_phonics_unit WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "unitCode", column = "unit_code"),
            @Result(property = "unitName", column = "unit_name"),
            @Result(property = "phonemeType", column = "phoneme_type"),
            @Result(property = "description", column = "description"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsUnit getUnitById(Integer id);

    @Update("UPDATE kid_phonics_unit SET course_id = #{courseId}, unit_code = #{unitCode}, unit_name = #{unitName}, " +
            "phoneme_type = #{phonemeType}, description = #{description}, sequence = #{sequence}, status = #{status} WHERE id = #{id}")
    void updateUnit(KidPhonicsUnit unit);

    @Delete("DELETE FROM kid_phonics_unit WHERE id = #{id}")
    void deleteUnit(Integer id);

    @Select("SELECT * FROM kid_phonics_unit WHERE course_id = #{courseId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "unitCode", column = "unit_code"),
            @Result(property = "unitName", column = "unit_name"),
            @Result(property = "phonemeType", column = "phoneme_type"),
            @Result(property = "description", column = "description"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsUnit> getUnitsByCourseId(Integer courseId);

    // KidPhonicsLetter表操作
    @Insert("INSERT INTO kid_phonics_letter (unit_id, letter, ipa, is_combination, sound_url, sequence, status) " +
            "VALUES (#{unitId}, #{letter}, #{ipa}, #{isCombination}, #{soundUrl}, #{sequence}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertLetter(KidPhonicsLetter letter);

    @Select("SELECT * FROM kid_phonics_letter WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "letter", column = "letter"),
            @Result(property = "ipa", column = "ipa"),
            @Result(property = "isCombination", column = "is_combination"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsLetter getLetterById(Integer id);

    @Update("UPDATE kid_phonics_letter SET unit_id = #{unitId}, letter = #{letter}, ipa = #{ipa}, " +
            "is_combination = #{isCombination}, sound_url = #{soundUrl}, sequence = #{sequence}, status = #{status} WHERE id = #{id}")
    void updateLetter(KidPhonicsLetter letter);

    @Delete("DELETE FROM kid_phonics_letter WHERE id = #{id}")
    void deleteLetter(Integer id);

    @Select("SELECT * FROM kid_phonics_letter WHERE unit_id = #{unitId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "letter", column = "letter"),
            @Result(property = "ipa", column = "ipa"),
            @Result(property = "isCombination", column = "is_combination"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsLetter> getLettersByUnitId(Integer unitId);

    // KidPhonicsLetterComponent表操作
    @Insert("INSERT INTO kid_phonics_letter_component (letter_id, component, sound_url, sequence) " +
            "VALUES (#{letterId}, #{component}, #{soundUrl}, #{sequence})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertLetterComponent(KidPhonicsLetterComponent component);

    @Select("SELECT * FROM kid_phonics_letter_component WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "letterId", column = "letter_id"),
            @Result(property = "component", column = "component"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsLetterComponent getLetterComponentById(Integer id);

    @Update("UPDATE kid_phonics_letter_component SET letter_id = #{letterId}, component = #{component}, " +
            "sound_url = #{soundUrl}, sequence = #{sequence} WHERE id = #{id}")
    void updateLetterComponent(KidPhonicsLetterComponent component);

    @Delete("DELETE FROM kid_phonics_letter_component WHERE id = #{id}")
    void deleteLetterComponent(Integer id);

    @Select("SELECT * FROM kid_phonics_letter_component WHERE letter_id = #{letterId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "letterId", column = "letter_id"),
            @Result(property = "component", column = "component"),
            @Result(property = "soundUrl", column = "sound_url"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsLetterComponent> getComponentsByLetterId(Integer letterId);

    // KidPhonicsWord表操作
    @Insert("INSERT INTO kid_phonics_word (letter_id, word, phonetic, translation, full_sound_url, initial_sound_url, rhyme_sound_url, image_url, sequence, status) " +
            "VALUES (#{letterId}, #{word}, #{phonetic}, #{translation}, #{fullSoundUrl}, #{initialSoundUrl}, #{rhymeSoundUrl}, #{imageUrl}, #{sequence}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertWord(KidPhonicsWord word);

    @Select("SELECT * FROM kid_phonics_word WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "letterId", column = "letter_id"),
            @Result(property = "word", column = "word"),
            @Result(property = "phonetic", column = "phonetic"),
            @Result(property = "translation", column = "translation"),
            @Result(property = "fullSoundUrl", column = "full_sound_url"),
            @Result(property = "initialSoundUrl", column = "initial_sound_url"),
            @Result(property = "rhymeSoundUrl", column = "rhyme_sound_url"),
            @Result(property = "imageUrl", column = "image_url"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsWord getWordById(Integer id);

    @Update("UPDATE kid_phonics_word SET letter_id = #{letterId}, word = #{word}, phonetic = #{phonetic}, " +
            "translation = #{translation}, full_sound_url = #{fullSoundUrl}, initial_sound_url = #{initialSoundUrl}, " +
            "rhyme_sound_url = #{rhymeSoundUrl}, image_url = #{imageUrl}, sequence = #{sequence}, status = #{status} WHERE id = #{id}")
    void updateWord(KidPhonicsWord word);

    @Delete("DELETE FROM kid_phonics_word WHERE id = #{id}")
    void deleteWord(Integer id);

    @Select("SELECT * FROM kid_phonics_word WHERE letter_id = #{letterId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "letterId", column = "letter_id"),
            @Result(property = "word", column = "word"),
            @Result(property = "phonetic", column = "phonetic"),
            @Result(property = "translation", column = "translation"),
            @Result(property = "fullSoundUrl", column = "full_sound_url"),
            @Result(property = "initialSoundUrl", column = "initial_sound_url"),
            @Result(property = "rhymeSoundUrl", column = "rhyme_sound_url"),
            @Result(property = "imageUrl", column = "image_url"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsWord> getWordsByLetterId(Integer letterId);

    // KidPhonicsRhyme表操作
    @Insert("INSERT INTO kid_phonics_rhyme (unit_id, title, description, video_url, cover_img, duration, sequence, status) " +
            "VALUES (#{unitId}, #{title}, #{description}, #{videoUrl}, #{coverImg}, #{duration}, #{sequence}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertRhyme(KidPhonicsRhyme rhyme);

    @Select("SELECT * FROM kid_phonics_rhyme WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "description", column = "description"),
            @Result(property = "videoUrl", column = "video_url"),
            @Result(property = "coverImg", column = "cover_img"),
            @Result(property = "duration", column = "duration"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsRhyme getRhymeById(Integer id);

    @Update("UPDATE kid_phonics_rhyme SET unit_id = #{unitId}, title = #{title}, description = #{description}, " +
            "video_url = #{videoUrl}, cover_img = #{coverImg}, duration = #{duration}, sequence = #{sequence}, status = #{status} WHERE id = #{id}")
    void updateRhyme(KidPhonicsRhyme rhyme);

    @Delete("DELETE FROM kid_phonics_rhyme WHERE id = #{id}")
    void deleteRhyme(Integer id);

    @Select("SELECT * FROM kid_phonics_rhyme WHERE unit_id = #{unitId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "description", column = "description"),
            @Result(property = "videoUrl", column = "video_url"),
            @Result(property = "coverImg", column = "cover_img"),
            @Result(property = "duration", column = "duration"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsRhyme> getRhymesByUnitId(Integer unitId);

    // KidPhonicsPictureBook表操作
    @Insert("INSERT INTO kid_phonics_picture_book (unit_id, title, description, book_url, cover_img, page_count, sequence, status) " +
            "VALUES (#{unitId}, #{title}, #{description}, #{bookUrl}, #{coverImg}, #{pageCount}, #{sequence}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPictureBook(KidPhonicsPictureBook pictureBook);

    @Select("SELECT * FROM kid_phonics_picture_book WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "description", column = "description"),
            @Result(property = "bookUrl", column = "book_url"),
            @Result(property = "coverImg", column = "cover_img"),
            @Result(property = "pageCount", column = "page_count"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsPictureBook getPictureBookById(Integer id);

    @Update("UPDATE kid_phonics_picture_book SET unit_id = #{unitId}, title = #{title}, description = #{description}, " +
            "book_url = #{bookUrl}, cover_img = #{coverImg}, page_count = #{pageCount}, sequence = #{sequence}, status = #{status} WHERE id = #{id}")
    void updatePictureBook(KidPhonicsPictureBook pictureBook);

    @Delete("DELETE FROM kid_phonics_picture_book WHERE id = #{id}")
    void deletePictureBook(Integer id);

    @Select("SELECT * FROM kid_phonics_picture_book WHERE unit_id = #{unitId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "title", column = "title"),
            @Result(property = "description", column = "description"),
            @Result(property = "bookUrl", column = "book_url"),
            @Result(property = "coverImg", column = "cover_img"),
            @Result(property = "pageCount", column = "page_count"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "status", column = "status"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsPictureBook> getPictureBooksByUnitId(Integer unitId);

    // KidPhonicsPictureBookContent表操作
    @Insert("INSERT INTO kid_phonics_picture_book_content (picture_book_id, unit_id, pic_url, sentence_num, sequence) " +
            "VALUES (#{pictureBookId}, #{unitId}, #{picUrl}, #{sentenceNum}, #{sequence})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPictureBookContent(KidPhonicsPictureBookContent content);

    @Select("SELECT * FROM kid_phonics_picture_book_content WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "picUrl", column = "pic_url"),
            @Result(property = "sentenceNum", column = "sentence_num"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsPictureBookContent getPictureBookContentById(Integer id);

    @Update("UPDATE kid_phonics_picture_book_content SET picture_book_id = #{pictureBookId}, unit_id = #{unitId}, " +
            "pic_url = #{picUrl}, sentence_num = #{sentenceNum}, sequence = #{sequence} WHERE id = #{id}")
    void updatePictureBookContent(KidPhonicsPictureBookContent content);

    @Delete("DELETE FROM kid_phonics_picture_book_content WHERE id = #{id}")
    void deletePictureBookContent(Integer id);

    @Select("SELECT * FROM kid_phonics_picture_book_content WHERE picture_book_id = #{pictureBookId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "picUrl", column = "pic_url"),
            @Result(property = "sentenceNum", column = "sentence_num"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsPictureBookContent> getPictureBookContentsByPictureBookId(Integer pictureBookId);

    // KidPhonicsPictureBookSentence表操作
    @Insert("INSERT INTO kid_phonics_picture_book_sentence (content_id, picture_book_id, unit_id, example_en_us, example_en_us_element, " +
            "example_zh_cn, example_zh_py, speaker, sound_file, cn_sound_file, core_sentence, sequence) " +
            "VALUES (#{contentId}, #{pictureBookId}, #{unitId}, #{exampleEnUs}, #{exampleEnUsElement}, #{exampleZhCn}, " +
            "#{exampleZhPy}, #{speaker}, #{soundFile}, #{cnSoundFile}, #{coreSentence}, #{sequence})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPictureBookSentence(KidPhonicsPictureBookSentence sentence);

    @Select("SELECT * FROM kid_phonics_picture_book_sentence WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "exampleEnUs", column = "example_en_us"),
            @Result(property = "exampleEnUsElement", column = "example_en_us_element"),
            @Result(property = "exampleZhCn", column = "example_zh_cn"),
            @Result(property = "exampleZhPy", column = "example_zh_py"),
            @Result(property = "speaker", column = "speaker"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "cnSoundFile", column = "cn_sound_file"),
            @Result(property = "coreSentence", column = "core_sentence"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KidPhonicsPictureBookSentence getPictureBookSentenceById(Integer id);

    @Update("UPDATE kid_phonics_picture_book_sentence SET content_id = #{contentId}, picture_book_id = #{pictureBookId}, " +
            "unit_id = #{unitId}, example_en_us = #{exampleEnUs}, example_en_us_element = #{exampleEnUsElement}, " +
            "example_zh_cn = #{exampleZhCn}, example_zh_py = #{exampleZhPy}, speaker = #{speaker}, " +
            "sound_file = #{soundFile}, cn_sound_file = #{cnSoundFile}, core_sentence = #{coreSentence}, sequence = #{sequence} WHERE id = #{id}")
    void updatePictureBookSentence(KidPhonicsPictureBookSentence sentence);

    @Delete("DELETE FROM kid_phonics_picture_book_sentence WHERE id = #{id}")
    void deletePictureBookSentence(Integer id);

    @Select("SELECT * FROM kid_phonics_picture_book_sentence WHERE content_id = #{contentId} ORDER BY sequence")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "contentId", column = "content_id"),
            @Result(property = "pictureBookId", column = "picture_book_id"),
            @Result(property = "unitId", column = "unit_id"),
            @Result(property = "exampleEnUs", column = "example_en_us"),
            @Result(property = "exampleEnUsElement", column = "example_en_us_element"),
            @Result(property = "exampleZhCn", column = "example_zh_cn"),
            @Result(property = "exampleZhPy", column = "example_zh_py"),
            @Result(property = "speaker", column = "speaker"),
            @Result(property = "soundFile", column = "sound_file"),
            @Result(property = "cnSoundFile", column = "cn_sound_file"),
            @Result(property = "coreSentence", column = "core_sentence"),
            @Result(property = "sequence", column = "sequence"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KidPhonicsPictureBookSentence> getPictureBookSentencesByContentId(Integer contentId);

    // KidPhonicsPublishRecord表操作
    @Insert("INSERT INTO kid_phonics_publish_record (course_id, branch, publish_time, publisher, remark) " +
            "VALUES (#{courseId}, #{branch}, #{publishTime}, #{publisher}, #{remark})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertPublishRecord(KidPhonicsPublishRecord record);

    @Select("SELECT * FROM kid_phonics_publish_record WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "branch", column = "branch"),
            @Result(property = "publishTime", column = "publish_time"),
            @Result(property = "publisher", column = "publisher"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "createTime", column = "create_time")
    })
    KidPhonicsPublishRecord getPublishRecordById(Integer id);

    @Select("SELECT * FROM kid_phonics_publish_record WHERE course_id = #{courseId} ORDER BY publish_time DESC")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "courseId", column = "course_id"),
            @Result(property = "branch", column = "branch"),
            @Result(property = "publishTime", column = "publish_time"),
            @Result(property = "publisher", column = "publisher"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "createTime", column = "create_time")
    })
    List<KidPhonicsPublishRecord> getPublishRecordsByCourseId(Integer courseId);

    // 课程发布时更新课程分支号和发布状态
    @Update("UPDATE kid_phonics_course SET branch = #{branch}, publish_status = 1, publish_time = #{publishTime} WHERE id = #{id}")
    void updateCoursePublishInfo(@Param("id") Integer id, @Param("branch") Integer branch, @Param("publishTime") java.util.Date publishTime);

    // 更新单元状态
    @Update("UPDATE kid_phonics_unit SET status = #{status} WHERE id = #{id}")
    void updateUnitStatus(@Param("id") Integer id, @Param("status") Integer status);
} 