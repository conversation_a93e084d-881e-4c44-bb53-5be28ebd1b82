package com.woxue.resourceservice.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 同音词
 * <AUTHOR>
 *
 */
public interface IHomonymDao {
	List<String> loadHomonymList();


	List<Map> list(@Param("spelling") String spelling, @Param("index") int index, @Param("pageSize") Integer pageSize);

	Boolean add(@Param("spelling") String spelling, @Param("meaning") String meaning);

	Boolean update(@Param("spelling") String spelling, @Param("meaning") String meaning);

	Boolean delete(@Param("spelling") String spelling);

	Integer count(@Param("spelling") String spelling);
}
