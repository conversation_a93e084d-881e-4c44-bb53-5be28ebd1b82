package com.woxue.resourceservice.dao;

import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.PictureBook;
import com.woxue.common.model.redBook.PictureBookContent;
import com.woxue.common.model.redBook.PictureBookSentence;
import com.woxue.common.model.redBook.PictureBookWord;
import org.springframework.stereotype.Component;

import java.util.List;
@Component("pictureBookDao")
public interface IPictureBookDao {

    /**
     * 获取绘本列表
     * @param courseId
     * @return
     */
    List<PictureBook> getPictureBookList(Integer courseId);
    List<PictureBook> getAllPictureBookList();

    /**
     * 获取绘本内容
     * @param pictureBookId
     * @return
     */
    PictureBook getPictureBook(Integer pictureBookId);

    /**
     * 获取绘本内容列表
     * @param pictureBookId
     * @return
     */
    List<PictureBookContent> getPictureBookContentList(Integer pictureBookId);

    /**
     * 获取绘本内容
     * @param pictureBookContentId
     * @return
     */
    PictureBookContent getPictureBookContent(Integer pictureBookContentId);
    /**
     * 获取绘本内容的句子列表
     * @param pictureBookContentId
     * @return
     */
    List<PictureBookSentence> getPictureBookSentenceList(Integer pictureBookContentId);

    /**
     * 获取绘本内容的句子
     * @param pictureBookSentenceId
     * @return
     */
    PictureBookSentence getPictureBookSentence(Integer pictureBookSentenceId);

    /**
     * 获取绘本内容的单词列表
     * @param pictureBookId
     * @return
     */
    List<PictureBookWord> getWordListByPictureBookId(Integer pictureBookId);

}
