package com.woxue.resourceservice.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.woxue.common.model.DictionaryBean;

public interface IDictionaryDao {
	List<String> getWordSpellingList();
	List<String> getSpellingList();
	List<DictionaryBean> getDictionaryList();
	
	List<DictionaryBean> getDictionaryListByLikeSpelling(@Param("spelling")String spelling,@Param("limitSize")Integer limitSize);
	
	int insertDictionary(DictionaryBean dictionaryBean);
	
	DictionaryBean getDictionary(String spelling);

	int updateDictionary(DictionaryBean dictionaryBean);
}
