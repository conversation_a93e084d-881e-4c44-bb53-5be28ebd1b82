//package com.woxue.resourceservice.util;
//
//import com.woxue.common.util.wordUse.WordUseBean;
//import com.woxue.common.util.wordUse.WordUseUtil;
////import com.woxue.common.util.wordUse.WordUseUtil;
//
//import java.util.Arrays;
//
//public class WordUseExamples {
//    public static void main(String[] args) {
//        // 1. 基本动词变化示例
//        testBasicVerb();
//
//        // 2. 不规则动词示例
//        testIrregularVerb();
//
//        // 3. 名词单复数示例
//        testNounPlural();
//
//        // 4. 缩写形式示例
//        testContraction();
//
//        // 5. 词组示例 - 连续
//        testContinuousPhrase();
//
//        // 6. 词组示例 - 分离
//        testSeparatedPhrase();
//
//        // 7. 带标点符号示例
//        testWithPunctuation();
//
//        // 8. 词组变形示例
//        testPhraseTransformation();
//    }
//
//    private static void testBasicVerb() {
//        System.out.println("===== 测试基本动词变化 =====");
//        // 原形：play
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "He plays football every day.",  // 例句
//                "play",                         // 目标词
//                "玩"                            // 含义
//        );
//        printResult("基本动词变化", result);
//        result = WordUseUtilNLP.getUserShowData(
//                "He plays football every day.",  // 例句
//                "play",                         // 目标词
//                "玩"                            // 含义
//        );
//        printResult("基本动词变化nlp", result);
//    }
//
//    private static void testIrregularVerb() {
//        System.out.println("\n===== 测试不规则动词 =====");
//        // 不规则动词：go
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "She went to school yesterday.", // 例句
//                "go",                           // 目标词
//                "去"                            // 含义
//        );
//        printResult("不规则动词", result);
//        result = WordUseUtilNLP.getUserShowData(
//                "She went to school yesterday.", // 例句
//                "go",                           // 目标词
//                "去"                            // 含义
//        );
//        printResult("不规则动词nlp", result);
//    }
//
//    private static void testNounPlural() {
//        System.out.println("\n===== 测试名词单复数 =====");
//        // 名词单复数：child -> children
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "There are many children in the park.", // 例句
//                "child",                               // 目标词
//                "孩子"                                 // 含义
//        );
//
//        printResult("名词单复数", result);
//
//        result = WordUseUtilNLP.getUserShowData(
//                "There are many children in the park.", // 例句
//                "child",                               // 目标词
//                "孩子"                                 // 含义
//        );
//        printResult("名词单复数nlp", result);
//    }
//
//    private static void testContraction() {
//        System.out.println("\n===== 测试缩写形式 =====");
//        // 缩写：is -> 's
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "He's a good student.",    // 例句
//                "is",                      // 目标词
//                "是"                       // 含义
//        );
//        printResult("缩写形式", result);
//
//        result = WordUseUtilNLP.getUserShowData(
//                "He's a good student.",    // 例句
//                "is",                      // 目标词
//                "是"                       // 含义
//        );
//        printResult("缩写形式nlp", result);
//    }
//
//    private static void testContinuousPhrase() {
//        System.out.println("\n===== 测试连续词组 =====");
//        // 连续词组
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "We need to look after the children.", // 例句
//                "look after",                         // 目标词组
//                "照顾"                                // 含义
//        );
//        printResult("连续词组", result);
//
//        result = WordUseUtilNLP.getUserShowData(
//                "We need to look after the children.", // 例句
//                "look after",                         // 目标词组
//                "照顾"                                // 含义
//        );
//        printResult("连续词组nlp", result);
//    }
//
//    private static void testSeparatedPhrase() {
//        System.out.println("\n===== 测试分离词组 =====");
//        // 分离词组
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "Please turn the light off.",  // 例句
//                "turn off",                    // 目标词组
//                "关闭"                         // 含义
//        );
//        printResult("分离词组", result);
//
//        result = WordUseUtilNLP.getUserShowData(
//                "Please turn the light off.",  // 例句
//                "turn off",                    // 目标词组
//                "关闭"                         // 含义
//        );
//        printResult("分离词组nlp", result);
//    }
//
//    private static void testWithPunctuation() {
//        System.out.println("\n===== 测试带标点符号 =====");
//        // 带标点符号
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "Do you know how to swim?",    // 例句
//                "know",                        // 目标词
//                "知道"                         // 含义
//        );
//        printResult("带标点符号", result);
//
//        result = WordUseUtilNLP.getUserShowData(
//                "Do you know how to swim?",    // 例句
//                "know",                        // 目标词
//                "知道"                         // 含义
//        );
//        printResult("带标点符号nlp", result);
//    }
//
//    private static void testPhraseTransformation() {
//        System.out.println("\n===== 测试词组变形 =====");
//        // 词组变形：go to the store -> go shopping
//        WordUseBean result = WordUseUtil.getUserShowData(
//                "He went to the store to buy groceries.", // 例句
//                "go to the store",                         // 目标词组
//                "去商店"                                 // 含义
//        );
//        printResult("词组变形", result);
//
//        result = WordUseUtilNLP.getUserShowData(
//                "He went to the store to buy groceries.", // 例句
//                "go to the store",                         // 目标词组
//                "去商店"                                 // 含义
//        );
//        printResult("词组变形nlp", result);
//    }
//
//    private static void printResult(String testName, WordUseBean result) {
//        System.out.println("测试类型: " + testName);
//        System.out.println("原句: " + result.getSentence());
//        System.out.println("目标词/词组: " + result.getSpell());
//        System.out.println("分词结果: " + result.getFinalList());
//        System.out.println("正确答案: " + result.getCorrectWords());
//        System.out.println("挖空位置: " + Arrays.toString(result.getInputPos()));
//        System.out.println("是否词组: " + result.isHasSpace());
//        System.out.println("是否连续: " + result.isConnect());
//        System.out.println("是否找到正确位置: " + result.isCorrectPos());
//        System.out.println("-------------");
//    }
//}