package com.woxue.resourceservice.util;

import com.alibaba.fastjson.JSON;
import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.util.GsonManager;
import com.woxue.common.util.wordUse.WordUseBean;
import com.woxue.common.util.wordUse.WordUseUtil;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class WordUseManager {
    private static final Logger logger = LoggerFactory.getLogger(WordUseManager.class);
    private static final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private static final Lock processLock = new ReentrantLock();

    public static final String WORD_USE_QUEUE = "wordUse:queue";
    public static final String WORD_USE_HASH = "wordUse:hash";
    public static final String WORD_USE_HASH_RESOURCE = "wordUse:hash:resource";

    private static final String URL = "http://region-45.autodl.pro:17319/word_related/word_using_in_blank/";
    private static final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

    public static void processWordUseQueue() {
        processLock.lock();
        try {
            executorService.submit(() -> {
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connectionManager).build();
                ResponseHandler<String> responseHandler = new BasicResponseHandler();
                try (Jedis jedis = RedBookRedisManager.getPool().getResource()) {
                    while (true) {
                        String json = jedis.lpop(WORD_USE_QUEUE);
                        if (json != null) {
                            WordUseBean wordUseBean = GsonManager.fromJson(json, WordUseBean.class);
                            if (wordUseBean == null) {
                                continue;
                            }
                            String spelling = wordUseBean.getSpell();
                            String sentence = wordUseBean.getSentence();
                            String meaning = wordUseBean.getMeaning();
                            wordUseBean=WordUseUtil.getUserShowData(sentence,spelling,meaning);
                            String key = spelling + ":" + sentence;
                            if (!jedis.hexists(WORD_USE_HASH, key)) {
                                jedis.hset(WORD_USE_HASH, key, "1");
                                List<String> correctWords = wordUseBean.getCorrectWords();
                                StringBuilder contentBuilder = new StringBuilder();
                                for (String word : correctWords) {
                                    if (contentBuilder.length() > 0) {
                                        contentBuilder.append(" ");
                                    }
                                    contentBuilder.append(word);
                                }
                                String content = contentBuilder.toString();
                                if (!content.equalsIgnoreCase(wordUseBean.getSpell())) {
                                    String reason = getReason(JSON.toJSONString(wordUseBean), httpClient, responseHandler);
                                    if (StringUtils.isNotEmpty(reason)){
                                        wordUseBean.setExplanation(reason);
                                        IResourceWordDao resourceWordDao = SpringActionSupport.getSpringBean("resourceWordDao", null, IResourceWordDao.class);
                                        resourceWordDao.insertWordUse(wordUseBean.getSpell(), wordUseBean.getSentence(), JSON.toJSONString(wordUseBean));
                                    }
                                }else {
                                    logger.warn("WordUseBean with spelling '{}' and sentence '{}' is not in correct position.", spelling, sentence);
                                }
                            }

                        } else {
                            Thread.sleep(50);
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error processing word use queue", e);
                    System.out.println(e.getMessage());
                } finally {
                    processLock.unlock();
                }
            });
        } catch (Exception e) {
            logger.error("Failed to submit task to executor service", e);
        }
    }

    public static WordUseBean getWordUseBean(String spelling, String sentence) {
        String key = spelling + ":" + sentence;
        String resourceHashString = RedBookRedisManager.getResourceHashString(WORD_USE_HASH_RESOURCE, key);
        if (StringUtils.isNotEmpty(resourceHashString)) {
            return GsonManager.fromJson(resourceHashString, WordUseBean.class);
        }
        return null;
    }

    public static void addWordUseBean(WordUseBean wordUseBean) {
        String sentence = wordUseBean.getSentence();
        String spell = wordUseBean.getSpell();
        String key = spell + ":" + sentence;
        RedBookRedisManager.setResourceHashString(WORD_USE_HASH_RESOURCE, key, GsonManager.toJson(wordUseBean));
        IResourceWordDao resourceWordDao = SpringActionSupport.getSpringBean("resourceWordDao", null, IResourceWordDao.class);
        resourceWordDao.updateWordUseStatus(spell, sentence, 1);
    }

    private static String getReason(String paramBody, CloseableHttpClient httpClient, ResponseHandler<String> responseHandler) {
        String result = "";
        try {
            System.out.println(paramBody);
            HttpPost httpPost = new HttpPost(URL);
            StringEntity requestEntity = new StringEntity(paramBody, "utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            result = httpClient.execute(httpPost, responseHandler);
            return JSON.parseObject(result).getString("explanation");
        } catch (Exception e) {
            logger.error("Error getting reason from AI service", e);
        }
        return result;
    }


}
