package com.woxue.resourceservice.util;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.redBook.RedBookConstant;
import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.common.model.redBook.readExpand.*;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.dao.IResourceVersionDao;
import com.woxue.resourcemanage.enums.ReadExpandArticleQuestionEnum;
import com.woxue.resourcemanage.service.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ReadExpandReadResourceManager {

    private final static String READ_EXPAND_ARTICLE_LIST_PREFIX = "readexpandReadArticleIdList:unitId:";
    private final static String READ_EXPAND_ARTICLE_PREFIX = "readexpandReadArticle:articleId:";
    private final static String READ_EXPAND_ARTICLE_QUESTION_PREFIX = "readexpandReadArticleQuestion:articleId:trainPhase:";
    private final static String READ_EXPAND_WORD_LIST_PREFIX = "readexpandReadWordList:articleId:";
    private final static String READ_EXPAND_ARTICLE_CORRELATION_PREFIX = "readexpandReadArticleCorrelation:articleId:";
    private final static String READ_EXPAND_ARTICLE_KNOWLEDGE_QUESTION_PREFIX = "readexpandReadKnowledgeQuestion:articleId:";

    /**
     * 加载部署
     */
    public static void reloadReadExpandReadResource() {
        IResourceVersionDao resourceVersionDao = SpringActionSupport.getSpringBean("resourceVersionDao", null, IResourceVersionDao.class);
        IResourceCourseDao resourceCourseDao = SpringActionSupport.getSpringBean("resourceCourseDao", null, IResourceCourseDao.class);
        IResourceUnitDao resourceUnitDao = SpringActionSupport.getSpringBean("resourceUnitDao", null, IResourceUnitDao.class);

        List<Map<String, Object>> versionList = resourceVersionDao.getVersionList(RedBookConstant.VersionType.READ_EXPAND.value, null, null, null, null);
        Integer versionId  = (Integer) versionList.get(0).get("id");

        RedBookCourse redBookCourse = resourceCourseDao.getCourseList(versionId, null).get(0);
        Integer courseId = redBookCourse.getId();
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, null, null);

        IReadExpandReadArticleService articleService = SpringActionSupport.getSpringBean("articleService", null, IReadExpandReadArticleService.class);
        IReadExpandReadArticleQuestionService articleQuestionService = SpringActionSupport.getSpringBean("articleQuestionService", null, IReadExpandReadArticleQuestionService.class);
        IReadExpandReadArticleWordService wordService = SpringActionSupport.getSpringBean("wordService", null, IReadExpandReadArticleWordService.class);
        IReadExpandReadArticleCorrelationService correlationService = SpringActionSupport.getSpringBean("correlationService", null, IReadExpandReadArticleCorrelationService.class);
        IReadExpandKnowledgeQuestionService knowledgeQuestionService = SpringActionSupport.getSpringBean("knowledgeQuestionService", null, IReadExpandKnowledgeQuestionService.class);

        unitList.forEach(item -> {
            Integer unitId = (Integer) item.get("id");
            List<ReadExpandReadArticleBean> articleBeanList = articleService.list(unitId);

            if(articleBeanList.isEmpty()){
                return;
            }

            articleBeanList.forEach(article -> {

                List<ReadExpandReadArticleQuestionBean> articleQuestionBeanList = articleQuestionService.list(article.getArticleId());

                List<ReadExpandReadArticleWordBean> wordBeanList = wordService.list(article.getArticleId());

                ReadExpandReadArticleCorrelationBean correlationBean = correlationService.editByArticleId(article.getArticleId());

                List<ReadExpandKnowledgeQuestionBean> knowledgeQuestionBeanList = knowledgeQuestionService.getListByArticleId(article.getArticleId());

                //加载到缓存
                addCacheArticleBean(article);
                addCacheQuestionList(articleQuestionBeanList,article.getArticleId());
                addCacheWordList(wordBeanList,article.getArticleId());
                addCacheCorrelationBean(correlationBean,article.getArticleId());
                addCacheKnowledgeContentBean(knowledgeQuestionBeanList,article.getArticleId());

            });

            //清除文章id列表缓存
            RedBookRedisManager.delResourceBean(READ_EXPAND_ARTICLE_LIST_PREFIX + unitId);

            //将文章id列表加载到缓存
            List<Integer> articleIdList = articleBeanList.stream().map(ReadExpandReadArticleBean::getArticleId).collect(Collectors.toList());
            addCacheArticleIdList(articleIdList,unitId);
        });

    }


    /**
     * 加载部署
     */
    public static void reloadReadExpandReadResource(Integer unitId) {
        IReadExpandReadArticleService articleService = SpringActionSupport.getSpringBean("articleService", null, IReadExpandReadArticleService.class);
        IReadExpandReadArticleQuestionService articleQuestionService = SpringActionSupport.getSpringBean("articleQuestionService", null, IReadExpandReadArticleQuestionService.class);
        IReadExpandReadArticleWordService wordService = SpringActionSupport.getSpringBean("wordService", null, IReadExpandReadArticleWordService.class);
        IReadExpandReadArticleCorrelationService correlationService = SpringActionSupport.getSpringBean("correlationService", null, IReadExpandReadArticleCorrelationService.class);
        IReadExpandKnowledgeQuestionService knowledgeQuestionService = SpringActionSupport.getSpringBean("knowledgeQuestionService", null, IReadExpandKnowledgeQuestionService.class);

        List<ReadExpandReadArticleBean> articleBeanList = articleService.list(unitId);

        if(articleBeanList.isEmpty()){
            return;
        }

        articleBeanList.forEach(article -> {
            List<ReadExpandReadArticleQuestionBean> articleQuestionBeanList = articleQuestionService.list(article.getArticleId());

            List<ReadExpandReadArticleWordBean> wordBeanList = wordService.list(article.getArticleId());

            ReadExpandReadArticleCorrelationBean correlationBean = correlationService.editByArticleId(article.getArticleId());

            List<ReadExpandKnowledgeQuestionBean> knowledgeQuestionBeanList = knowledgeQuestionService.getListByArticleId(article.getArticleId());

            //加载到缓存
            addCacheArticleBean(article);
            addCacheQuestionList(articleQuestionBeanList,article.getArticleId());
            addCacheWordList(wordBeanList,article.getArticleId());
            addCacheCorrelationBean(correlationBean,article.getArticleId());
            if(knowledgeQuestionBeanList != null && !knowledgeQuestionBeanList.isEmpty()){
                addCacheKnowledgeContentBean(knowledgeQuestionBeanList,article.getArticleId());
            }
        });

        //将文章id列表 加载到缓存
        List<Integer> articleIdList = articleBeanList.stream().map(ReadExpandReadArticleBean::getArticleId).collect(Collectors.toList());
        addCacheArticleIdList(articleIdList,unitId);

    }


    /**
     * 将文章id列表加载到缓存
     * @param articleIdList
     * @param unitId
     */
    public static void addCacheArticleIdList(List<Integer> articleIdList,Integer unitId) {
        RedBookRedisManager.setResourceBean((READ_EXPAND_ARTICLE_LIST_PREFIX + unitId), articleIdList);
    }

    /**
     * 将问题选项列表加载到缓存
     * @param articleQuestionBeanList
     * @param articleId
     */
    public static void addCacheQuestionList(List<ReadExpandReadArticleQuestionBean> articleQuestionBeanList,Integer articleId) {
        List<ReadExpandReadArticleQuestionBean> basicsQuestionBeanList = new ArrayList<>();
        List<ReadExpandReadArticleQuestionBean> raiseQuestionBeanList = new ArrayList<>();
        articleQuestionBeanList.forEach(questionBean ->{
            if(ReadExpandArticleQuestionEnum.BASIC_TRAIN.getCode().equals(questionBean.getTrainPhase())){
                basicsQuestionBeanList.add(questionBean);
            }
            if(ReadExpandArticleQuestionEnum.RAISE_TRAIN.getCode().equals(questionBean.getTrainPhase())){
                raiseQuestionBeanList.add(questionBean);
            }
        });
        RedBookRedisManager.setResourceBean((READ_EXPAND_ARTICLE_QUESTION_PREFIX + articleId + ":" + ReadExpandArticleQuestionEnum.BASIC_TRAIN.getCode()), basicsQuestionBeanList);
        RedBookRedisManager.setResourceBean((READ_EXPAND_ARTICLE_QUESTION_PREFIX + articleId + ":" + ReadExpandArticleQuestionEnum.RAISE_TRAIN.getCode()), raiseQuestionBeanList);
    }

    /**
     * 将重点词汇列表加载到缓存
     * @param wordBeanList
     * @param articleId
     */
    public static void addCacheWordList(List<ReadExpandReadArticleWordBean> wordBeanList,Integer articleId) {
        RedBookRedisManager.setResourceBean((READ_EXPAND_WORD_LIST_PREFIX + articleId), wordBeanList);
    }

    /**
     * 将句句互译加载到缓存
     * @param correlationBean
     * @param articleId
     */
    public static void addCacheCorrelationBean(ReadExpandReadArticleCorrelationBean correlationBean,Integer articleId) {
        RedBookRedisManager.setResourceBean((READ_EXPAND_ARTICLE_CORRELATION_PREFIX + articleId), correlationBean);
    }

    /**
     * 将知识重点加载到缓存
     * @param knowledgeQuestionBeanList
     * @param articleId
     */
    public static void addCacheKnowledgeContentBean(List<ReadExpandKnowledgeQuestionBean> knowledgeQuestionBeanList,Integer articleId) {
        RedBookRedisManager.setResourceBean((READ_EXPAND_ARTICLE_KNOWLEDGE_QUESTION_PREFIX + articleId), knowledgeQuestionBeanList);
    }
    /**
     * 将文章加载到缓存
     * @param articleBean
     */
    public static void addCacheArticleBean(ReadExpandReadArticleBean articleBean) {
        RedBookRedisManager.setResourceBean((READ_EXPAND_ARTICLE_PREFIX + articleBean.getArticleId()), articleBean);
    }


    /**
     * 获取所有文章id
     * @param unitId
     * @return
     */
    public static List<Integer> getReadExpandReadArticleIdList(int unitId) {
        return (List<Integer>) RedBookRedisManager.getResourceBean(READ_EXPAND_ARTICLE_LIST_PREFIX + unitId);
    }
    /**
     * 获取文章信息
     * @param articleId
     * @return
     */
    public static ReadExpandReadArticleBean getReadExpandReadArticle(int articleId) {
        return (ReadExpandReadArticleBean) RedBookRedisManager.getResourceBean(READ_EXPAND_ARTICLE_PREFIX + articleId);
    }
    /**
     * 获取基础训练信息
     * @param articleId
     * @return
     */
    public static List<ReadExpandReadArticleQuestionBean> getReadExpandReadArticleBasic(int articleId) {
        return (List<ReadExpandReadArticleQuestionBean>) RedBookRedisManager.getResourceBean(READ_EXPAND_ARTICLE_QUESTION_PREFIX
                + articleId + ":" + ReadExpandArticleQuestionEnum.BASIC_TRAIN.getCode());
    }
    /**
     * 获取拔高训练信息
     * @param articleId
     * @return
     */
    public static List<ReadExpandReadArticleQuestionBean> getReadExpandReadArticleRaise(int articleId) {
        return (List<ReadExpandReadArticleQuestionBean>) RedBookRedisManager.getResourceBean(READ_EXPAND_ARTICLE_QUESTION_PREFIX
                + articleId + ":" + ReadExpandArticleQuestionEnum.RAISE_TRAIN.getCode());
    }
    /**
     * 获取重点词汇列表信息
     * @param articleId
     * @return
     */
    public static List<ReadExpandReadArticleWordBean> getWordBeanList(int articleId){
        return (List<ReadExpandReadArticleWordBean>) RedBookRedisManager.getResourceBean(READ_EXPAND_WORD_LIST_PREFIX + articleId);
    }
    /**
     * 获取句句互译信息
     * @param articleId
     * @return
     */
    public static ReadExpandReadArticleCorrelationBean getReadExpandReadArticleCorrelation(int articleId){
        return (ReadExpandReadArticleCorrelationBean) RedBookRedisManager.getResourceBean(READ_EXPAND_ARTICLE_CORRELATION_PREFIX + articleId);
    }
    /**
     * 获取知识要点信息
     * @param articleId
     * @return
     */
    public static List<ReadExpandKnowledgeQuestionBean> getReadExpandKnowledgeQuestionList(int articleId) {
        return (List<ReadExpandKnowledgeQuestionBean>) RedBookRedisManager.getResourceBean(READ_EXPAND_ARTICLE_KNOWLEDGE_QUESTION_PREFIX + articleId);
    }

}
