package com.woxue.resourceservice.util;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.SimulationQuestionBean;
import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.*;
import com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean;
import com.woxue.common.model.redBook.listen.ResourceUnitListenDTO;
import com.woxue.common.model.redBook.read.*;
import com.woxue.redbookresource.model.ResourcePublishRecords;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.dao.*;
import com.woxue.resourcemanage.entity.dto.read.ResourceReadArticleNewDTO;
import com.woxue.resourcemanage.entity.read.ResourceUnitContentReadBean;
import com.woxue.resourcemanage.service.IResourceListenService;
import com.woxue.resourcemanage.service.IResourceReadArticleService;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.*;

/**
 * @description: 小红本课程资源类
 * @author: TT
 * @create: 2021-07-03
 */
public class RedBookCourseManager {

    private final static String VERSION_IDS_PREFIX = "redBookVersionIdList:versionType:";
    private final static String VERSION_IDS_STAGE_PREFIX = "redBookVersionIdList:versionType:stage:";
    private final static String VERSION_BEAN_PREFIX = "redBookVersionBean:versionId:";
    private final static String COURSE_BEAN_PREFIX = "redBookCourseBean:courseId:";

    private final static String COURSE_STAGE_LIST_PREFIX = "redBookCourseStage:courseId:";

    private final static String VERSION_STAGE_LIST_PREFIX = "redBookVersionStage:versionId:";
    private final static String UNIT_BEAN_PREFIX = "redBookUnitBean:unitId:";
    private final static String ARTICLE_BEAN_PREFIX = "redBookArticle:articleId:";

    private final static String WORDBEAN_PREFIX = "redBookWordBean:wid:";
    private final static String TONGBUWORDBEAN_PREFIX = "sentenceElementWord:";
    //助记信息
    private final static String WORD_MNEMONICS_PREFIX = "WORD_MNEMONICS:";
    //单词配图地址
    private final static String WORD_IMGURL_PREFIX = "WORD_IMG_URL:";
    //单词干扰项
    private final static String WORD_DISTURB_WORDIDS_PREFIX = "redbook_wordDisturb_wordIdList:";
    //说词义：手动编辑的单词词义列表
    private final static String WORD_SPEAK_WORD_MEANING_PREFIX = "WORD_SPEAK_WORD_MEANING:";

    //音标
    private final static String PHONOGRAM_WORD_PREFIX = "phonogramWord:phonogramId:";
    private final static String PHONOGRAM_WORDIDLIST_BY_PHONOGRAMID_PREFIX = "phonogramWordIdList:phonogramId:";
    private final static String PHONOGRAM_ALL_WORDIDLIST_PREFIX = "phonogramWordIdAllList";
    private final static String PHONETIC_ID_LIST_BY_UNIT_ID_PREFIX = "redBookUnitPhoneticIdList:unitId:";//每个单元音标id列表
    private final static String PHONETIC_COMPOSE_ID_LIST_BY_UNIT_ID_PREFIX = "redBookUnitPhoneticComposeIdList:unitId:";//每个单元 中 音标组合id列表
    private final static String PHONETIC_COMPOSE_BEAN_PREFIX = "redBookUnitPhoneticCompose:composeWordId:";//音标组合 key=composeWordId
    private final static String PHONETIC_EXAMPLE_ID_LIST_BY_UNIT_ID_PREFIX = "redBookUnitPhoneticExampleIdList:unitId:";//每个单元 中 音标例词id列表
    private final static String PHONETIC_EXAMPLE_BEAN_PREFIX = "redBookUnitPhoneticExample:exampleWordId:";//音标例词 key=exampleWordId

    //语法
    private final static String GRAMMAR_TITLELIST_PREFIX = "redBook-grammar:titleList:unitId:";
    private final static String GRAMMAR_TITLE_BEAN_PREFIX = "redBook-grammar:titleBean:titleId:";
    private final static String GRAMMAR_QUESTIONLIST_PREFIX = "redBook-grammarQuestionList:unitId:";
    private final static String GRAMMAR_COURSE_QUESTIONLIST_PREFIX = "redBook-grammarCourseQuestionList:courseId:";

    //自然拼读
    private final static String PHONICS_LETTER_ID_LIST_BY_UNITID_PREFIX = "redBookUnitPhonicsLetterIdList:unitId:";//自然拼读中发音字母ID列表
    private final static String PHONICS_LETTER_BEAN_PREFIX = "redBookPhonicsLetter:letterId:";//自然拼读中发音字母 bean
    private final static String PHONICS_LETTER_ID_BY_WORD_ID_PREFIX = "redBookLetterId:wordId:";//自然拼读中单词 对应的发音字母是什么
    //自然拼读 2024版本
    private final static String PHONICS_NEW_LETTER_ID_LIST_BY_UNITID_PREFIX = "redBookUnitNewPhonicsLetterIdList:unitId:";//自然拼读中发音字母ID列表
    private final static String PHONICS_NEW_LETTER_BEAN_PREFIX = "redBookNewPhonicsLetter:letterId:";//自然拼读中发音字母 bean
    private final static String PHONICS_NEW_LETTER_ID_BY_WORD_ID_PREFIX = "redBookNewLetterId:wordId:";//自然拼读中单词 对应的发音字母是什么

    private final static String WORD_ABBR_LIST_BEAN_PREFIX = "redBookWordAbbr:";//单词缩写列表
    //单元写作方法
    private final static String UNITCONTENTWRITEMETHOD = "unitContentWriteMethod:unitId";
    //单元阅读缓存key
    private final static String RESOURCE_READ_ARTICLE_PREFIX = "resourceReadArticle:articleId:";
    //单元听力
    private final static String RESOURCE_LISTEN_UNIT_PREFIX = "unitContentListen:unitId:";

    //版本可以传入配置，添加到其它学段中
    public static boolean addVersionBean(RedBookVersion redBookVersion, List<RedBookVersionStage> stageList) {
        RedBookRedisManager.setResourceBean((VERSION_BEAN_PREFIX + redBookVersion.getId()), redBookVersion);
        List<String> idList = RedBookRedisManager.getResourceListRange(VERSION_IDS_PREFIX + redBookVersion.getVersionType(), 0, -1);
        if (!idList.contains(redBookVersion.getId() + "")) {
            RedBookRedisManager.addResourceToList(VERSION_IDS_PREFIX + redBookVersion.getVersionType(), redBookVersion.getId() + "");
        }
        idList = RedBookRedisManager.getResourceListRange(VERSION_IDS_STAGE_PREFIX + redBookVersion.getVersionType() + ":" + redBookVersion.getStage(), 0, -1);
        if (!idList.contains(redBookVersion.getId() + "")) {
            RedBookRedisManager.addResourceToList(VERSION_IDS_STAGE_PREFIX + redBookVersion.getVersionType() + ":" + redBookVersion.getStage(), redBookVersion.getId() + "");
        }
        //重新加载的时候，先删除原来的
        List<RedBookVersionStage> redisStageList = getRedBookVersionStageList(redBookVersion.getId());
        if (redisStageList != null && !redisStageList.isEmpty()) {
            redisStageList.forEach(stage -> {
                RedBookRedisManager.removeResourceFromList(VERSION_IDS_STAGE_PREFIX + redBookVersion.getVersionType() + ":" + stage.getStage(), stage.getVersionId() + "");
            });
        }
        if (stageList != null && !stageList.isEmpty()) {
            for (RedBookVersionStage stage : stageList) {
                idList = RedBookRedisManager.getResourceListRange(VERSION_IDS_STAGE_PREFIX + redBookVersion.getVersionType() + ":" + stage.getStage(), 0, -1);
                if (!idList.contains(stage.getVersionId() + "")) {
                    RedBookRedisManager.addResourceToList(VERSION_IDS_STAGE_PREFIX + redBookVersion.getVersionType() + ":" + stage.getStage(), stage.getVersionId() + "");
                }
            }
            addRedBookVersionStageList(redBookVersion.getId(), stageList);
        }
        return true;
    }

    public static boolean addCourseBean(RedBookCourse redBookCourse) {
        RedBookRedisManager.setResourceBean((COURSE_BEAN_PREFIX + redBookCourse.getId()), redBookCourse);
        return true;
    }

    public static boolean addCourseStageBean(RedBookCourse course, List<RedBookCourseStage> list) {
        if (list == null || list.isEmpty()) {
            RedBookRedisManager.delResourceBean(COURSE_STAGE_LIST_PREFIX + course.getId());
            return false;
        }
        RedBookRedisManager.setResourceBean((COURSE_STAGE_LIST_PREFIX + course.getId()), list);
        return true;
    }

    //获取课程学段
    public static List<RedBookCourseStage> getCourseStageList(Integer courseId) {
        return (List<RedBookCourseStage>) RedBookRedisManager.getResourceBean(COURSE_STAGE_LIST_PREFIX + courseId);
    }

    public static boolean addUnitBean(RedBookUnit redBookUnit) {
        RedBookRedisManager.setResourceBean((UNIT_BEAN_PREFIX + redBookUnit.getId()), redBookUnit);
        return true;
    }

    public static boolean addArticleBean(RedBookArticle article) {
        RedBookRedisManager.setResourceBean((ARTICLE_BEAN_PREFIX + article.getId()), article);
        return true;
    }

    public static List<RedBookVersion> getRedBookVersionList(Integer versionType, Integer stage) {
        List<String> idList = null;
        if (stage == null) {
            idList = RedBookRedisManager.getResourceListRange(VERSION_IDS_PREFIX + versionType, 0, -1);
        } else {
            idList = RedBookRedisManager.getResourceListRange(VERSION_IDS_STAGE_PREFIX + versionType + ":" + stage, 0, -1);
        }
        List<RedBookVersion> versionBeanList = new ArrayList<>();
        for (String id : idList) {
            versionBeanList.add(getRedBookVersion(Integer.parseInt(id)));
        }
        return versionBeanList;
    }

    public static RedBookVersion getRedBookVersion(Integer versionId) {
        return (RedBookVersion) RedBookRedisManager.getResourceBean(VERSION_BEAN_PREFIX + versionId);
    }

    public static RedBookVersion getRedBookVersion(Integer versionId, Integer stage) {
        // 从Redis中尝试获取版本信息
        RedBookVersion bean = (RedBookVersion) RedBookRedisManager.getResourceBean(VERSION_BEAN_PREFIX + versionId);

        if (bean != null && Objects.equals(bean.getStage(), stage)) {
            return bean; // 如果阶段匹配，则直接返回版本信息
        }

        // 阶段不匹配，从数据库中获取版本的所有阶段
        List<RedBookVersionStage> stageList = getRedBookVersionStageList(versionId);
        if (stageList != null && !stageList.isEmpty()) {
            // 使用Stream API查找匹配的版本
            return stageList.stream()
                    .filter(versionStage -> Objects.equals(versionStage.getStage(), stage))
                    .map(stageVersion -> getRedBookVersion(stageVersion.getVersionId()))
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null); // 如果找到匹配的版本，则返回，否则返回null
        }

        // 如果没有匹配的版本，则返回null
        return null;
    }

    private static List<RedBookVersionStage> getRedBookVersionStageList(Integer versionId) {
        return (List<RedBookVersionStage>) RedBookRedisManager.getResourceBean(VERSION_STAGE_LIST_PREFIX + versionId);
    }

    private static boolean addRedBookVersionStageList(Integer versionId, List<RedBookVersionStage> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        RedBookRedisManager.setResourceBean(VERSION_STAGE_LIST_PREFIX + versionId, list);
        return true;
    }


    public static List<RedBookCourse> getRedBookCourseList(Integer versionId) {
        RedBookVersion redBookVersion = getRedBookVersion(versionId);
        if (redBookVersion != null) {
            List<Integer> idList = redBookVersion.getCourseIdList();
            List<RedBookCourse> beanList = new ArrayList<>();
            for (Integer id : idList) {
                beanList.add(getRedBookCourse(id));
            }
            return beanList;
        }
        return null;
    }

    public static List<RedBookCourse> getRedBookCourseList(Integer versionId, Integer stage) {
        RedBookVersion redBookVersion = getRedBookVersion(versionId);
        if (redBookVersion != null) {
            List<Integer> idList = redBookVersion.getCourseIdList();
            List<RedBookCourse> beanList = new ArrayList<>();
            for (Integer id : idList) {
                if (redBookVersion.getStage() != stage) {
                    List<RedBookCourseStage> stageList = getCourseStageList(id);
                    //获取课程学段
                    if (stageList != null && !stageList.isEmpty()) {
                        //判断课程学段是否包含
                        for (RedBookCourseStage courseStage : stageList) {
                            if (Objects.equals(courseStage.getStage(), stage)) {
                                beanList.add(getRedBookCourse(id));
                            }
                        }
                    }
                } else {
                    beanList.add(getRedBookCourse(id));
                }
            }
            return beanList;
        }
        return null;
    }

    public static RedBookCourse getRedBookCourse(Integer courseId) {
        return (RedBookCourse) RedBookRedisManager.getResourceBean(COURSE_BEAN_PREFIX + courseId);
    }

    public static RedBookCourse getRedBookCourse(Integer courseId, Integer stage) {
        RedBookCourse redBookCourse = (RedBookCourse) RedBookRedisManager.getResourceBean(COURSE_BEAN_PREFIX + courseId);
        if (redBookCourse != null && redBookCourse.getStage() != stage) {
            List<RedBookCourseStage> stageList = getCourseStageList(courseId);
            //获取课程学段
            if (stageList != null && !stageList.isEmpty()) {
                //判断课程学段是否包含
                for (RedBookCourseStage courseStage : stageList) {
                    if (Objects.equals(courseStage.getStage(), stage)) {
                        return redBookCourse;
                    }
                }
            }
            //匹配不上返回空
            return null;
        }
        return redBookCourse;
    }

    public static List<RedBookUnit> getRedBookUnitList(Integer courseId) {
        RedBookCourse redBookCourse = RedBookCourseManager.getRedBookCourse(courseId);
        if (redBookCourse != null) {
            List<Integer> idList = redBookCourse.getUnitIdList();
            List<RedBookUnit> beanList = new ArrayList<>();
            for (Integer id : idList) {
                beanList.add(getRedBookUnit(id));
            }
            return beanList;
        }
        return null;
    }

    public static RedBookUnit getRedBookUnit(Integer unitId) {
        return (RedBookUnit) RedBookRedisManager.getResourceBean(UNIT_BEAN_PREFIX + unitId);
    }

    public static RedBookArticle getRedBookArticle(Integer articleId) {
        return (RedBookArticle) RedBookRedisManager.getResourceBean(ARTICLE_BEAN_PREFIX + articleId);
    }


    /*public static boolean addWord(RedBookWord word) {
        RedBookRedisManager.setResourceBean(WORD_BEAN_PREFIX + word.getId(), word);
        return true;
    }*/

    /*public static RedBookWord getWordById(Integer wordId) {
        return (RedBookWord) RedBookRedisManager.getResourceBean(WORD_BEAN_PREFIX + wordId);
    }*/


    public static WordBean getWordBeanById(Integer wordId) {
        WordBean wordBean = (WordBean) RedBookRedisManager.getResourceBean(WORDBEAN_PREFIX + wordId);
        if (wordBean == null) {
            return reloadWordBean(wordId);
        }
        return wordBean;
    }

    public static WordBean reloadWordBean(int wordId) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        WordBean wordBean = redBookCourseDao.loadWordBean(wordId);
        if (wordBean != null) {
            List<Map<Integer, String>> meaningList = new ArrayList<>();
            if (wordBean.getMeaning_zh_CN() != null && !wordBean.getMeaning_zh_CN().isEmpty() && (wordBean.getMeaning_zh_CN().contains(".") ||
                    wordBean.getMeaning_zh_CN().contains("[") && wordBean.getMeaning_zh_CN().contains("]"))) {
                Map<Integer, String> meaningMap;
                String meaningStr = wordBean.getMeaning_zh_CN();
                try {
                    if (meaningStr.contains(".")) {
                        meaningStr = meaningStr.replaceAll("\\s+", "");
                        meaningStr = meaningStr.replaceAll("modalverb", "modalv.");//情态动词原形处理
                        meaningStr = meaningStr.replaceAll("modal\\.", "modalv.");//情态动词异常处理
                        meaningStr = meaningStr.replaceAll("v\\.modal", "v.modalv.");//动词和情态动词特殊情况处理
                        meaningStr = meaningStr.replaceAll("v\\.v\\.", "v.");//将2个v.处理成1个
                        meaningStr = meaningStr.replaceAll("v\\.", "v. ");//现将v.后加空格,用于区分 动词和副词
                        meaningStr = meaningStr.replaceAll("n\\.", "n. ");//现将v.后加空格,用于区分 代词和名词
                        meaningStr = meaningStr.replaceAll("aux\\.", "aux. ");

                        meaningStr = meaningStr.replaceAll("aux\\. v\\. ", "||aux.v.| ");//aux. v.是指助动词
                        meaningStr = meaningStr.replaceAll("v\\. aux\\. ", "||v.aux.| ");//aux. v.是指助动词
                        meaningStr = meaningStr.replaceAll("pron\\. ", "||pron.| ");//pron. 代词
                        meaningStr = meaningStr.replaceAll("n\\. ", "||n.| ");//n. 名词
                        meaningStr = meaningStr.replaceAll("adv\\. ", "||adv.| ");//adv. 副词
                        meaningStr = meaningStr.replaceAll("modalv\\. ", "||m. v.| ");//modal v. 情态动词
                        meaningStr = meaningStr.replaceAll("v\\. ", "||v.| ");//v. 动词
                        meaningStr = meaningStr.replaceAll("vt\\.", "||vt.| ");//vt. 及物动词
                        meaningStr = meaningStr.replaceAll("vi\\.", "||vi.| ");//vi.不及物动词
                        meaningStr = meaningStr.replaceAll("adj\\.", "||adj.| ");//adj.形容词
                        meaningStr = meaningStr.replaceAll("num\\.", "||num.| ");// num.数词
                        meaningStr = meaningStr.replaceAll("art\\.", "||art.| ");//art. 冠词
                        meaningStr = meaningStr.replaceAll("prep\\.", "||prep.| ");//prep. 介词
                        meaningStr = meaningStr.replaceAll("conj\\.", "||conj.| ");// conj. 连词
                        meaningStr = meaningStr.replaceAll("int\\.", "||int.| ");//int. 感叹词
                        meaningStr = meaningStr.replaceAll("interj\\.", "||interj.| ");//int. 感叹词
                        meaningStr = meaningStr.replaceAll("aux\\. ", "||aux.| ");//aux. 助动词
                    } else {
                        meaningStr = meaningStr.replaceAll("\\s+", "");
                        meaningStr = meaningStr.replaceAll("\\[", "||[");
                        meaningStr = meaningStr.replaceAll("\\]", "]| ");
                    }
                    if (meaningStr.contains("|")) {
                        for (String mstr : meaningStr.split("\\|\\|")) {
                            if (mstr != null && !mstr.trim().isEmpty()) {
                                meaningMap = new HashMap<>();
                                if (!mstr.contains("|")) {
                                    meaningMap.put(0, "");
                                    meaningMap.put(1, mstr.trim());
                                } else {
                                    meaningMap.put(0, mstr.split("\\| ")[0].trim());
                                    meaningMap.put(1, mstr.split("\\| ")[1].trim());
                                }
                                meaningList.add(meaningMap);
                            }
                        }
                    }
                    if (meaningList.size() > 1) {
                        String firstMeaning;
                        for (int i = 0; i < meaningList.size(); i++) {
                            if (i < (meaningList.size() - 1)) {
                                firstMeaning = meaningList.get(i).get(1);
                                if (firstMeaning.isEmpty() || firstMeaning.equals("&") || firstMeaning.equals("&amp;") || firstMeaning.equals("/")) {
                                    meaningList.get(i).put(1, meaningList.get(i + 1).get(1));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            wordBean.setMeaningList(meaningList);
            RedBookRedisManager.setResourceBean((WORDBEAN_PREFIX + wordBean.getWordId()), wordBean);
            //句子成分
            Map<String, Object> sentenceElement = redBookCourseDao.getSentenceElement(wordBean.getWordId());
            if (sentenceElement != null) {
                RedBookRedisManager.setResourceBean(TONGBUWORDBEAN_PREFIX + wordBean.getWordId(), sentenceElement);
            }
        }
        return wordBean;
    }

    public static Map<String, Object> getSentenceElementWordBean(int wordId) {
        Map<String, Object> map = (Map<String, Object>) RedBookRedisManager.getResourceBean(TONGBUWORDBEAN_PREFIX + wordId);
        return map;
    }

    /**
     * 助记
     *
     * @param wordId
     * @return
     */
    public static Map<String, String> getWordMnemonics(int wordId) {
        WordBean word = getWordBeanById(wordId);
        Object obj = RedBookRedisManager.getResourceBean(WORD_MNEMONICS_PREFIX + word.getSpelling().replaceAll(" ", "_").replaceAll("'", "-"));
        if (obj == null) {
            return null;
        }
        return (Map<String, String>) obj;
    }

    public static Map<String, String> getWordMnemonics(String spelling) {
        Object obj = RedBookRedisManager.getResourceBean(WORD_MNEMONICS_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"));
        if (obj == null) {
            return null;
        }
        return (Map<String, String>) obj;
    }

    public static void addWordMnemonics(String spelling, Map<String, String> wordMnemonicsInfo) {
        RedBookRedisManager.setResourceBean(WORD_MNEMONICS_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"), wordMnemonicsInfo);
    }

    public static void deleteWordMnemonics(String spelling) {
        if (getWordMnemonics(spelling) != null) {
            RedBookRedisManager.delResourceBean(WORD_MNEMONICS_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"));
            addPublishRecord(ResourcePublishRecords.PublishType.MNEMONICS_PUBLISH, "spelling", spelling);
        }
    }

    public static List<Integer> getWordDisturbWordIdList(String spelling) {
        Object obj = RedBookRedisManager.getResourceBean(WORD_DISTURB_WORDIDS_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"));
        if (obj == null) {
            return null;
        }
        return (List<Integer>) obj;
    }

    public static void saveWordDisturbWordIdList(String spelling, List<Integer> wordIdList) {
        RedBookRedisManager.setResourceBean(WORD_DISTURB_WORDIDS_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"), wordIdList);
    }

    public static void deleteWordDisturb(String spelling) {
        if (getWordDisturbWordIdList(spelling) != null) {
            RedBookRedisManager.delResourceBean(WORD_DISTURB_WORDIDS_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"));
            addPublishRecord(ResourcePublishRecords.PublishType.DISTURB_PUBLISH, "spelling", spelling);
        }
    }
//    public static void addPhonogramWordBean(Map<String, Object> map) {
//        RedBookRedisManager.setResourceBean(PHONOGRAM_WORD_PREFIX+map.get("id"),map);
//    }
//    public static void addPhonogramWordIdList(int phonogramId,int phonogramWordId) {
//        RedBookRedisManager.addResourceToList(PHONOGRAM_WORDIDLIST_BY_PHONOGRAMID_PREFIX+phonogramId,phonogramWordId+"");
//    }
//    public static void addPhonogramWordIdAllList(int phonogramWordId) {
//        RedBookRedisManager.addResourceToList(PHONOGRAM_ALL_WORDIDLIST_PREFIX,phonogramWordId+"");
//    }
//    public static Map<String, Object> getPhonogramWordBean(int phonogramWordId) {
//        return (Map<String, Object>) RedBookRedisManager.getResourceBean(PHONOGRAM_WORD_PREFIX + phonogramWordId);
//    }
//    public static List<String> getPhonogramWordIdListbyPhonogramId(int phonogramWordId) {
//        return RedBookRedisManager.getResourceListRange(PHONOGRAM_WORDIDLIST_BY_PHONOGRAMID_PREFIX + phonogramWordId, 0, -1);
//    }
//    public static List<String> getPhonogramWordIdAllList() {
//        return RedBookRedisManager.getResourceListRange(PHONOGRAM_ALL_WORDIDLIST_PREFIX, 0, -1);
//    }


    public static List<Integer> getPhoneticIdList(int unitId) {
        List<String> list = RedBookRedisManager.getResourceListRange(PHONETIC_ID_LIST_BY_UNIT_ID_PREFIX + unitId, 0, -1);
        if (list == null || list.size() <= 0) {
            return null;
        }
        List<Integer> idList = new ArrayList<>();
        list.forEach(id -> idList.add(Integer.parseInt(id)));
        return idList;
    }

    public static List<Integer> getPhoneticComposeIdList(int unitId) {
        List<String> list = RedBookRedisManager.getResourceListRange(PHONETIC_COMPOSE_ID_LIST_BY_UNIT_ID_PREFIX + unitId, 0, -1);
        if (list == null || list.size() <= 0) {
            return null;
        }
        List<Integer> idList = new ArrayList<>();
        list.forEach(id -> idList.add(Integer.parseInt(id)));
        return idList;
    }

    public static PhoneticCompose getPhoneticCompose(int composeWordId) {
        return (PhoneticCompose) RedBookRedisManager.getResourceBean(PHONETIC_COMPOSE_BEAN_PREFIX + composeWordId);
    }

    public static List<Integer> getPhoneticExampleIdList(int unitId) {
        List<String> list = RedBookRedisManager.getResourceListRange(PHONETIC_EXAMPLE_ID_LIST_BY_UNIT_ID_PREFIX + unitId, 0, -1);
        if (list == null || list.size() <= 0) {
            return null;
        }
        List<Integer> idList = new ArrayList<>();
        list.forEach(id -> idList.add(Integer.parseInt(id)));
        return idList;
    }

    public static PhoneticExample getPhoneticExample(int exampleWordId) {
        return (PhoneticExample) RedBookRedisManager.getResourceBean(PHONETIC_EXAMPLE_BEAN_PREFIX + exampleWordId);
    }


    public static boolean addPhonicsLetter(RedBookPhonicsLetter letter) {
        List letterIdList = getPhonicsLetterIdListByUnitId(letter.getResourceUnitId());
        if (letterIdList == null || !letterIdList.contains(letter.getId())) {
            RedBookRedisManager.addResourceToList(PHONICS_LETTER_ID_LIST_BY_UNITID_PREFIX + letter.getResourceUnitId(), letter.getId() + "");
        }
        RedBookRedisManager.setResourceBean(PHONICS_LETTER_BEAN_PREFIX + letter.getId(), letter);
        for (Integer wid : letter.getStudyWordIdList()) {
            RedBookRedisManager.setResourceString(PHONICS_LETTER_ID_BY_WORD_ID_PREFIX + wid, letter.getId() + "");
        }
        for (Integer wid : letter.getQuizWordIdList()) {
            RedBookRedisManager.setResourceString(PHONICS_LETTER_ID_BY_WORD_ID_PREFIX + wid, letter.getId() + "");
        }
        return true;
    }

    public static List<String> getPhonicsLetterIdListByUnitId(Integer unitId) {
        return RedBookRedisManager.getResourceListRange(PHONICS_LETTER_ID_LIST_BY_UNITID_PREFIX + unitId, 0, -1);
    }

    public static void destoryPhonicsLetterIdListByUnitId(Integer unitId) {
        RedBookRedisManager.delResourceBean(PHONICS_LETTER_ID_LIST_BY_UNITID_PREFIX + unitId);
    }

    public static RedBookPhonicsLetter getPhonicsLetterByWordId(Integer wordId) {
        String letterId = RedBookRedisManager.getResourceString(PHONICS_LETTER_ID_BY_WORD_ID_PREFIX + wordId);
        if (letterId == null) {
            return null;
        }
        return (RedBookPhonicsLetter) RedBookRedisManager.getResourceBean(PHONICS_LETTER_BEAN_PREFIX + letterId);
    }

    public static RedBookPhonicsLetter getPhonicsLetterById(Integer letterId) {
        return (RedBookPhonicsLetter) RedBookRedisManager.getResourceBean(PHONICS_LETTER_BEAN_PREFIX + letterId);
    }

    public static List<RedBookPhonicsLetter> getPhonicsLetterListByUnitId(Integer unitId) {
        List<RedBookPhonicsLetter> wordList = new ArrayList<>();
        List<String> letterIdList = getPhonicsLetterIdListByUnitId(unitId);
        if (letterIdList != null) {
            for (String lidStr : letterIdList) {
                wordList.add(getPhonicsLetterById(Integer.valueOf(lidStr)));
            }
        }
        return wordList;
    }

    public static RedBookPhonicsLetterPlus getNewPhonicsLetterById(Integer letterId) {
        return (RedBookPhonicsLetterPlus) RedBookRedisManager.getResourceBean(PHONICS_NEW_LETTER_BEAN_PREFIX + letterId);
    }

    public static List<RedBookPhonicsLetterPlus> getNewPhonicsLetterListByUnitId(Integer unitId) {
        List<RedBookPhonicsLetterPlus> wordList = new ArrayList<>();
        List<String> letterIdList = getNewPhonicsLetterIdListByUnitId(unitId);
        if (letterIdList != null) {
            for (String lidStr : letterIdList) {
                wordList.add(getNewPhonicsLetterById(Integer.valueOf(lidStr)));
            }
        }
        return wordList;
    }

    public static boolean addNewPhonicsLetter(RedBookPhonicsLetterPlus letter) {
        List letterIdList = getNewPhonicsLetterIdListByUnitId(letter.getResourceUnitId());
        if (letterIdList == null || !letterIdList.contains(letter.getId())) {
            RedBookRedisManager.addResourceToList(PHONICS_NEW_LETTER_ID_LIST_BY_UNITID_PREFIX + letter.getResourceUnitId(), letter.getId() + "");
        }
        RedBookRedisManager.setResourceBean(PHONICS_NEW_LETTER_BEAN_PREFIX + letter.getId(), letter);
        return true;
    }

    public static List<String> getNewPhonicsLetterIdListByUnitId(Integer unitId) {
        return RedBookRedisManager.getResourceListRange(PHONICS_NEW_LETTER_ID_LIST_BY_UNITID_PREFIX + unitId, 0, -1);
    }

    public static void destoryNewPhonicsLetterIdListByUnitId(Integer unitId) {
        RedBookRedisManager.delResourceBean(PHONICS_NEW_LETTER_ID_LIST_BY_UNITID_PREFIX + unitId);
    }

    public static RedBookPhonicsLetter getNewPhonicsLetterByWordId(Integer wordId) {
        String letterId = RedBookRedisManager.getResourceString(PHONICS_NEW_LETTER_ID_BY_WORD_ID_PREFIX + wordId);
        if (letterId == null) {
            return null;
        }
        return (RedBookPhonicsLetter) RedBookRedisManager.getResourceBean(PHONICS_NEW_LETTER_BEAN_PREFIX + letterId);
    }


    /**
     * 更新redis中版本信息(不重新加载版本下的课程)
     *
     * @param versionId
     * @return
     */
    public static boolean updateVersionInfo(Integer versionId) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        RedBookVersion version = redBookCourseDao.getVersion(versionId);
        if (version == null) {
            return false;
        }
        RedBookVersion redBookVersion = RedBookCourseManager.getRedBookVersion(versionId);
        redBookVersion.setNameCn(version.getNameCn());
        redBookVersion.setNameEn(version.getNameEn());
        redBookVersion.setPrice(version.getPrice());
        redBookVersion.setRelationType(version.getRelationType());
        redBookVersion.setVersionType(version.getVersionType());
        redBookVersion.setStage(version.getStage());
        redBookVersion.setBriefIntroduction(version.getBriefIntroduction());
        redBookVersion.setDisplayOrder(version.getDisplayOrder());
        //获取版本下的配置的学段
        List<RedBookVersionStage> stageList = redBookCourseDao.getVersionStageList(versionId);
        RedBookCourseManager.addVersionBean(redBookVersion, stageList);
        RedBookCourseManager.addPublishRecord(ResourcePublishRecords.PublishType.VERSION_PUBLISH, "versionId", versionId);
        return true;
    }

    public static boolean reloadVersion(Integer versionId) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        RedBookVersion version = redBookCourseDao.getVersion(versionId);
        if (version == null) {
            return false;
        }
        redBookCourseDao.updateVersionBranchIdByPrimaryKey(version.getId());
        return reloadVersion(version);
    }

    public static boolean reloadVersion(RedBookVersion version) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        List<RedBookVersionStage> stageList = redBookCourseDao.getVersionStageList(version.getId());
        if (version.getVersionType() == RedBookConstant.VersionType.SYNC.value
                || version.getVersionType() == RedBookConstant.VersionType.WORD.value
                || version.getVersionType() == RedBookConstant.VersionType.SENTENCE.value
                || version.getVersionType() == RedBookConstant.VersionType.GRAMMAR.value
                || version.getVersionType() == RedBookConstant.VersionType.PHONETIC.value//音标训练
                || version.getVersionType() == RedBookConstant.VersionType.COMPREHENSIVE.value//1：同步 2：词汇 3：句子 4：语法 10：综合 才有课程信息
                || version.getVersionType() == RedBookConstant.VersionType.LETTER.value//认字母
                || version.getVersionType() == RedBookConstant.VersionType.PHONICS_NEW.value//新版自然拼读
                || version.getVersionType() == RedBookConstant.VersionType.PICTURE_BOOK.value//绘本
                || version.getVersionType() == RedBookConstant.VersionType.PHONICS_NEW_V2.value//自然拼读 2024版
                || version.getVersionType() == RedBookConstant.VersionType.READ_EXPAND.value//新版扩展
                || version.getVersionType() == RedBookConstant.VersionType.READ_EXPAND_SKILL.value//新版扩展
        ) {
            for (RedBookCourse course : redBookCourseDao.getCourseList(version.getId())) {
                reloadCourse(version, course);
                version.addCourseId(course.getId());
            }
        }
        RedBookCourseManager.addVersionBean(version, stageList);
        return true;
    }

    public static boolean reloadCourse(Integer courseId) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        RedBookCourse course = redBookCourseDao.getCourse(courseId);
        if (course == null) {
            return false;
        }
        RedBookVersion version = RedBookCourseManager.getRedBookVersion(course.getVersionId());
        if (version == null) {
            return false;
        }
        if (!version.getCourseIdList().contains(courseId)) {
            version.addCourseId(courseId);
        }
        List<RedBookVersionStage> stageList = redBookCourseDao.getVersionStageList(version.getId());
        RedBookCourseManager.addVersionBean(version, stageList);
        boolean b = RedBookCourseManager.reloadCourse(version, course);
        if (b) {
            if (!course.getWordIdList().isEmpty()) {
                for (Integer wordId : course.getWordIdList()) {
                    RedBookRedisManager.delResourceBean(WORDBEAN_PREFIX + wordId);
                }
            }
        }
        return b;
    }


    public static boolean reloadReadCourse(Integer courseId) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        RedBookCourse course = redBookCourseDao.getCourse(courseId);
        if (course == null) {
            return false;
        }
        RedBookVersion version = RedBookCourseManager.getRedBookVersion(course.getVersionId());
        if (version == null) {
            return false;
        }
        if (!version.getCourseIdList().contains(courseId)) {
            version.addCourseId(courseId);
        }
        boolean b = RedBookCourseManager.reloadReadCourse(version, course);
        return b;
    }


    public static boolean reloadReadUnit(Integer unitId) {
        IRedBookCourseService redBookCourseService = SpringActionSupport.getSpringBean("redBookCourseService", null, IRedBookCourseService.class);
        RedBookUnit unit = redBookCourseService.getUnit(unitId);
        if (unit == null) {
            return false;
        }
        RedBookCourse course = redBookCourseService.getCourse(unit.getCourseId());
        if (course == null) {
            return false;
        }
        IResourceReadArticleService readArticleService = SpringActionSupport.getSpringBean("readArticleService", null, IResourceReadArticleService.class);
        IResourceReadArticleDao readArticleDao = SpringActionSupport.getSpringBean("readArticleDao", null, IResourceReadArticleDao.class);
        IResourceUnitContentReadDao unitContentReadDao = SpringActionSupport.getSpringBean("resourceReadArticleDao", null, IResourceUnitContentReadDao.class);
        //阅读
        loadSyncRead(course,unit,readArticleService,readArticleDao,unitContentReadDao,true);
        RedBookCourseManager.addUnitBean(unit);
        course.setContentNum(course.getContentNum() + unit.getContentNum());
        RedBookCourseManager.addCourseBean(course);
        return true;
    }

    public static boolean reloadReadCourse(RedBookVersion version, RedBookCourse course) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        IResourceReadArticleService readArticleService = SpringActionSupport.getSpringBean("readArticleService", null, IResourceReadArticleService.class);
        IResourceReadArticleDao readArticleDao = SpringActionSupport.getSpringBean("readArticleDao", null, IResourceReadArticleDao.class);
        IResourceUnitContentReadDao unitContentReadDao = SpringActionSupport.getSpringBean("resourceReadArticleDao", null, IResourceUnitContentReadDao.class);

        int contentNums = 0;
        List<RedBookUnit> redBookUnitList = redBookCourseDao.getUnitList(course.getId());
        for (RedBookUnit unit : redBookUnitList) {
            unit.resetContentTypeList();
            loadSyncRead(course,unit,readArticleService,readArticleDao,unitContentReadDao,true);
            RedBookCourseManager.addUnitBean(unit);
            contentNums += unit.getContentNum();
            course.addUnitId(unit.getId());
        }
        course.setUnitNum(redBookUnitList.size());
        course.setContentNum(course.getContentNum() + contentNums);
        RedBookCourseManager.addCourseBean(course);
        return true;
    }

    public static boolean reloadCourse(RedBookVersion version, RedBookCourse course) {
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        IResourceUnitContentWriteDao resourceUnitContentWriteDao = SpringActionSupport.getSpringBean("resourceUnitContentWriteDao", null, IResourceUnitContentWriteDao.class);
        IResourceUnitWriteSentenceDao resourceUnitWriteSentenceDao = SpringActionSupport.getSpringBean("resourceUnitWriteSentenceDao", null, IResourceUnitWriteSentenceDao.class);
        IResourceTopicDao resourceTopicDao = SpringActionSupport.getSpringBean("resourceTopicDao", null, IResourceTopicDao.class);

        IResourceReadArticleService readArticleService = SpringActionSupport.getSpringBean("readArticleService", null, IResourceReadArticleService.class);
        IResourceReadArticleDao readArticleDao = SpringActionSupport.getSpringBean("readArticleDao", null, IResourceReadArticleDao.class);
        IResourceUnitContentReadDao unitContentReadDao = SpringActionSupport.getSpringBean("resourceReadArticleDao", null, IResourceUnitContentReadDao.class);

        IResourceListenService resourceListenService = SpringActionSupport.getSpringBean("resourceListenService", null, IResourceListenService.class);
        ResourceUnitContentListenDao contentListenDao = SpringActionSupport.getSpringBean("contentListenDao", null, ResourceUnitContentListenDao.class);


        RedBookArticle article;
        List<RedBookPhonicsLetter> letterList;
        List<RedBookPhonicsLetterPlus> letterPlusList;
        int contentNums = 0;
        List<RedBookUnit> redBookUnitList = redBookCourseDao.getUnitList(course.getId());
        for (RedBookUnit unit : redBookUnitList) {
            if (version.getVersionType() == RedBookConstant.VersionType.LETTER.value) {//认字母
                //加载单词
                List<Integer> unitWordIdList = redBookCourseDao.getWordIdListByUnitId(unit.getId(), null);
                unit.addContentType(RedBookContentTypeEnum.WORD);
                unit.addWordIdList(RedBookContentTypeEnum.WORD, unitWordIdList);
                course.addWordIdList(unitWordIdList);
                course.setContainWord(true);
                unit.resetContentTypeList();
            } else if (version.getVersionType() == RedBookConstant.VersionType.PHONETIC.value) {//音标
                List<Integer> phoneticIdList = redBookCourseDao.getPhoneticIdList(unit.getId());
                RedBookRedisManager.delResourceBean(PHONETIC_ID_LIST_BY_UNIT_ID_PREFIX + unit.getId());
                phoneticIdList.forEach(phoneticId -> {
                    RedBookRedisManager.addResourceToList(PHONETIC_ID_LIST_BY_UNIT_ID_PREFIX + unit.getId(), phoneticId + "");
                    //重新加载音标资源
                    reloadWordBean(phoneticId);
                });
                List<PhoneticCompose> phoneticComposeList = redBookCourseDao.getPhoneticComposeList(unit.getId());
                RedBookRedisManager.delResourceBean(PHONETIC_COMPOSE_ID_LIST_BY_UNIT_ID_PREFIX + unit.getId());
                phoneticComposeList.forEach(phoneticCompose -> {
                    WordBean wordBean = getWordBeanById(phoneticCompose.getComposeWordId());
                    if (wordBean != null) {
                        phoneticCompose.setPhoneticCompose(wordBean.getSpelling());
                        WordBean phonetic1 = getWordBeanById(phoneticCompose.getPhonetic1WordId());
                        phoneticCompose.setPhonetic1(phonetic1.getSpelling());
                        WordBean phonetic2 = getWordBeanById(phoneticCompose.getPhonetic2WordId());
                        phoneticCompose.setPhonetic2(phonetic2.getSpelling());
                        RedBookRedisManager.setResourceBean(PHONETIC_COMPOSE_BEAN_PREFIX + phoneticCompose.getComposeWordId(), phoneticCompose);
                        RedBookRedisManager.addResourceToList(PHONETIC_COMPOSE_ID_LIST_BY_UNIT_ID_PREFIX + unit.getId(), phoneticCompose.getComposeWordId() + "");
                    }
                });
                List<PhoneticExample> phoneticExampleList = redBookCourseDao.getPhoneticExampleList(unit.getId());
                RedBookRedisManager.delResourceBean(PHONETIC_EXAMPLE_ID_LIST_BY_UNIT_ID_PREFIX + unit.getId());
                phoneticExampleList.forEach(phoneticExample -> {
                    WordBean wordBean = getWordBeanById(phoneticExample.getExampleWordId());
                    if (wordBean != null) {
                        phoneticExample.setExample(wordBean.getSpelling());
                        phoneticExample.setExamplePhonetic(wordBean.getSyllable());
                        phoneticExample.setExampleMeaning(wordBean.getMeaning_zh_CN());
                        WordBean phonetic1 = getWordBeanById(phoneticExample.getPhonetic1WordId());
                        phoneticExample.setPhonetic1(phonetic1.getSpelling());
                        if (phoneticExample.getPhonetic2WordId() != null) {
                            WordBean phonetic2 = getWordBeanById(phoneticExample.getPhonetic2WordId());
                            phoneticExample.setPhonetic2(phonetic2.getSpelling());
                        }
                        RedBookRedisManager.setResourceBean(PHONETIC_EXAMPLE_BEAN_PREFIX + phoneticExample.getExampleWordId(), phoneticExample);
                        RedBookRedisManager.addResourceToList(PHONETIC_EXAMPLE_ID_LIST_BY_UNIT_ID_PREFIX + unit.getId(), phoneticExample.getExampleWordId() + "");
                    }
                });
                //旧版
//                List<Map<String, Object>> phonogramWordList = redBookCourseDao.getPhonogramWordList();
//                for(Map<String, Object> map:phonogramWordList){
//                    RedBookCourseManager.addPhonogramWordBean(map);
//                    RedBookCourseManager.addPhonogramWordIdList(Integer.valueOf(map.get("phonogram_id").toString()), Integer.valueOf(map.get("id").toString()));
//                    RedBookCourseManager.addPhonogramWordIdAllList(Integer.valueOf(map.get("id").toString()));
//                }
            } else if (version.getVersionType() == RedBookConstant.VersionType.PHONICS_NEW.value) {//新版自然拼读，加载发音字母和例词（单词）资源
                destoryPhonicsLetterIdListByUnitId(unit.getId());
                //加载发音字母
                letterList = redBookCourseDao.getPhonicsLetterList(unit.getId());
                for (RedBookPhonicsLetter letter : letterList) {
                    List<Integer> unitAllWordIdList = new ArrayList<>();
                    //加载学习单词
                    List<Integer> studyWordIdList = redBookCourseDao.getWordIdListByPhonicsLetterId(letter.getId(), false);
                    //加载测试单词
                    List<Integer> quizWordIdList = redBookCourseDao.getWordIdListByPhonicsLetterId(letter.getId(), true);
                    letter.setStudyWordIdList(studyWordIdList);
                    letter.setQuizWordIdList(quizWordIdList);
                    unitAllWordIdList.addAll(studyWordIdList);
                    unitAllWordIdList.addAll(quizWordIdList);
                    letter.setAllWordIdList(unitAllWordIdList);
                    RedBookCourseManager.addPhonicsLetter(letter);
//                    unit.addWordIdList(RedBookContentTypeEnum.WORD, unitAllWordIdList);
//                    course.addWordIdList(unitAllWordIdList);
                    unit.addWordIdList(RedBookContentTypeEnum.WORD, studyWordIdList);
                    course.addWordIdList(studyWordIdList);
                }
                unit.addContentType(RedBookContentTypeEnum.WORD);
                unit.resetContentTypeList();
                course.setContainWord(true);
            } else if (version.getVersionType() == RedBookConstant.VersionType.PHONICS_NEW_V2.value) {//新版自然拼读2024，加载发音字母和例词（单词）资源和分块资源
                destoryNewPhonicsLetterIdListByUnitId(unit.getId());
                //加载发音字母
                letterPlusList = redBookCourseDao.getNewPhonicsLetterList(unit.getId());
                for (RedBookPhonicsLetterPlus letter : letterPlusList) {
                    //加载例词
                    List<RedBookPhonicsLetterWordInfo> wordInfoList = redBookCourseDao.gePhonicsLetterWordInfoList(letter.getId());
                    //加载分块资源
                    List<RedBookPhonicsLetterPart> letterPartList = redBookCourseDao.getPhonicsLetterPartList(letter.getId());
                    letter.setLetterPartList(letterPartList);
                    letter.setAllWordList(wordInfoList);
                    letter.setStudyWordList(wordInfoList.stream().filter(wordInfo -> wordInfo.getIsQuizWord() == 0).collect(Collectors.toList()));
                    letter.setQuizWordList(wordInfoList.stream().filter(wordInfo -> wordInfo.getIsQuizWord() == 1).collect(Collectors.toList()));
                    RedBookCourseManager.addNewPhonicsLetter(letter);
                    //课程将例词 id 放入 wordIdList
                    course.addWordIdList(letter.getStudyWordList().stream().map(RedBookPhonicsLetterWordInfo::getId).collect(Collectors.toList()));
                    unit.addWordIdList(RedBookContentTypeEnum.WORD, letter.getStudyWordList().stream().map(RedBookPhonicsLetterWordInfo::getId).collect(Collectors.toList()));
                }
                //课程将发音字母 id 放入 phraseIdList
                course.addWordPhraseIdList(letterPlusList.stream().map(RedBookPhonicsLetterPlus::getId).collect(Collectors.toList()));
                unit.addWordIdList(RedBookContentTypeEnum.WORD_PHRASE, letterPlusList.stream().map(RedBookPhonicsLetterPlus::getId).collect(Collectors.toList()));
                unit.addContentType(RedBookContentTypeEnum.WORD);
                unit.resetContentTypeList();
                course.setContainWord(true);
            } else if (version.getVersionType() == RedBookConstant.VersionType.READ_EXPAND.value) {
                //加载新版乐学阅读（已包含文章id列表，文章信息及其各模块信息）
                ReadExpandReadResourceManager.reloadReadExpandReadResource(unit.getId());
            } else if (version.getVersionType() == RedBookConstant.VersionType.READ_EXPAND_SKILL.value) {
                //技能讲练当做语法处理
                loadGrammar(course, redBookCourseDao, unit);
                unit.addContentType(RedBookContentTypeEnum.GRAMMAR);
            } else {
                unit.resetContentTypeList();
                //加载单词和句子单词资源
                List<Integer> unitWordIdList;
                List<Integer> unitSentenceIdList;
                RedBookUnitContentWord contentWord;
                RedBookUnitContentSentence contentSentence;
                for (RedBookContentTypeEnum contentTypeEnum : unit.getContentTypeList()) {
                    switch (contentTypeEnum) {
                        case WORD:
                        case WORD1:
                        case WORD2:
                        case WORD3:
                        case WORD4:
                        case WORD5:
                        case WORD6://词汇
                        case WORD_SPECIAL:
                        case WORD_SPECIAL1:
                        case WORD_SPECIAL2://专有名词
                            contentWord = unit.getContentWord(contentTypeEnum);
                            if (contentWord != null) {
                                contentWord.setHasExampleSentence(false);
                                unitWordIdList = redBookCourseDao.getWordIdListByUnitId(unit.getId(), contentWord.getLevel());
                                if (unitWordIdList != null) {
                                    unit.addWordIdList(contentTypeEnum, unitWordIdList);
                                    course.addWordIdList(unitWordIdList);
                                    //检查有没有例句
                                    WordBean wordBean = getWordBeanById(unitWordIdList.get(0));
                                    if (version.getStage() <= 3 && wordBean != null && wordBean.getExample_en_US() != null && !wordBean.getExample_en_US().trim().isEmpty()) {
                                        contentWord.setHasExampleSentence(true);
                                        course.setContainExample(true);
                                    }
                                }
                            }
                            if (contentTypeEnum.toString().contains("PHRASE")) {
                                course.setContainPhrase(true);
                            }
                            course.setContainWord(true);
                            break;
                        case WORD_PHRASE:
                        case WORD_PHRASE1:
                        case WORD_PHRASE2://词组
                            contentWord = unit.getContentWord(contentTypeEnum);
                            if (contentWord != null) {
                                contentWord.setHasExampleSentence(false);
                                unitWordIdList = redBookCourseDao.getWordIdListByUnitId(unit.getId(), contentWord.getLevel());
                                if (unitWordIdList != null) {
                                    unit.addWordIdList(contentTypeEnum, unitWordIdList);
                                    course.addWordPhraseIdList(unitWordIdList);
                                    //检查有没有例句
                                    WordBean wordBean = getWordBeanById(unitWordIdList.get(0));
                                    if (version.getStage() <= 3 && wordBean != null && wordBean.getExample_en_US() != null && wordBean.getExample_en_US().trim().length() > 0) {
                                        contentWord.setHasExampleSentence(true);
                                        course.setContainPhraseExample(true);
                                    }
                                }
                            }
                            course.setContainPhrase(true);
                            break;
                        case SENTENCE://句子
                        case SENTENCE1:
                        case SENTENCE2:
                        case SENTENCE3:
                        case SENTENCE4:
                        case SENTENCE5:
                        case SENTENCE6:
                            contentSentence = unit.getContentSentence(contentTypeEnum);
                            if (contentSentence != null) {
                                unitSentenceIdList = redBookCourseDao.getSentenceWordIdListByUnitId(unit.getId(),contentSentence.getLevel());
                                if (unitSentenceIdList != null) {
                                    unit.addSentenceIdList(contentTypeEnum,unitSentenceIdList);
                                    course.addSentenceIdList(unitSentenceIdList);
                                }
                            }
                            course.setContainSentence(true);
                            break;
                        case GRAMMAR://语法
                            loadGrammar(course, redBookCourseDao, unit);
                            break;
                        case ARTICLE:
                            //加载课文静态资源。
                            if (unit.getArticleIdList() != null) {
                                for (Integer articleId : unit.getArticleIdList()) {
                                    article = redBookCourseDao.getArticle(articleId);
                                    if (article != null) {
                                        RedBookCourseManager.addArticleBean(article);
                                    }
                                }
                            }
                            course.setContainArticle(true);
                            break;
                        case QUERSTION://习题
                            course.setContainQuestion(true);
                            break;
                        case WRITE://写作
                            loadSyncWrite(course,unit,resourceUnitContentWriteDao,resourceUnitWriteSentenceDao,resourceTopicDao);
                            break;
                        case READ://阅读
                            //只发布阅读的课程单元信息，不发布文章
                            loadSyncRead(course,unit,readArticleService,readArticleDao,unitContentReadDao,false);
                            break;
                        case LISTEN://听力
                            loadSyncListen(course,unit,resourceListenService,contentListenDao);
                            break;
                        default:
                            break;
                    }
                }

            }
            RedBookCourseManager.addUnitBean(unit);
            contentNums += unit.getContentNum();
            course.addUnitId(unit.getId());
        }
        course.setUnitNum(redBookUnitList.size());
        course.setContentNum(contentNums);
        List<RedBookCourseStage> stageList = redBookCourseDao.getCourseStageList(course.getId());
        redBookCourseDao.updateCourseBranchIdByPrimaryKey(course.getId());
        course.setBranchId(course.getBranchId()+1);
        RedBookCourseManager.addCourseBean(course);
        //添加课程和阶段的关系
        RedBookCourseManager.addCourseStageBean(course, stageList);
        return true;
    }

    public static void loadSyncListen(RedBookCourse course,RedBookUnit unit,IResourceListenService resourceListenService,ResourceUnitContentListenDao contentListenDao){
        ResourceUnitListenDTO edit = resourceListenService.edit(unit.getId());
        ResourceUnitContentListenBean unitContentListen = contentListenDao.editByUnitId(unit.getId());
        //过滤课程下没有内容的单元
        if(unitContentListen == null){
            return;
        }
        if(edit != null){
            RedBookUnitContentListen contentListen = new RedBookUnitContentListen();
            contentListen.setId(unitContentListen.getId());
            contentListen.setTopic(unitContentListen.getTopicName());
            contentListen.setResourceUnitId(unit.getId());
            contentListen.setSentenceCount(edit.getSentenceList() == null ? 0 : edit.getSentenceList().size());
            unit.setContentListen(contentListen);
            course.setContainListen(true);
            addCacheListen(edit);
        }
    }

    public static void loadSyncRead(RedBookCourse course,RedBookUnit unit,IResourceReadArticleService readArticleService,IResourceReadArticleDao readArticleDao,IResourceUnitContentReadDao unitContentReadDao,
                                    Boolean readPushFlag){
        List<ResourceReadArticleBean> resourceReadArticleBeans = readArticleDao.listByUnitId(unit.getId());
        ResourceUnitContentReadBean contentReadBean = unitContentReadDao.editByUnitId(unit.getId());
        //过滤课程下没有内容的单元
        if(contentReadBean == null){
            return;
        }
        RedBookUnitContentRead contentRead = new RedBookUnitContentRead();
        contentRead.setId(contentReadBean.getId());
        contentRead.setTopic(contentReadBean.getTopicName());
        contentRead.setResourceUnitId(contentReadBean.getResourceUnitId());
        contentRead.setArticleNum(resourceReadArticleBeans == null ? 0 : resourceReadArticleBeans.size());
        contentRead.setArticleList(resourceReadArticleBeans);
        unit.setContentRead(contentRead);
        //新单元未通过统一课程发布，单独发布添加阅读标识
        if(!unit.getContentTypeList().contains(RedBookContentTypeEnum.READ)){
            if(resourceReadArticleBeans != null){
                unit.addContentType(RedBookContentTypeEnum.READ);
            }
        }
        course.setContainRead(resourceReadArticleBeans != null);

        if(readPushFlag){
            List<ResourceReadArticleBean> articleBeanList = readArticleDao.listByUnitId(unit.getId());
            if(articleBeanList == null || articleBeanList.isEmpty()){
                return;
            }
            articleBeanList.forEach(article -> {
                Integer articleId = article.getArticleId();
                ResourceReadArticleNewDTO articleDTO = readArticleService.edit(articleId);
                ResourceReadArticleDTO readArticleDTO = new ResourceReadArticleDTO();
                BeanUtils.copyProperties(articleDTO,readArticleDTO);
                //加载到缓存
                addCacheArticleDTO(readArticleDTO);
            });
        }

    }

    /**
     * 将听力内容加载到缓存
     * @param edit
     */
    public static void addCacheListen(ResourceUnitListenDTO edit) {
        RedBookRedisManager.setResourceBean((RESOURCE_LISTEN_UNIT_PREFIX + edit.getUnitId()), edit);
    }

    /**
     * 将文章和相关子类信息加载到缓存
     * @param articleDTO
     */
    public static void addCacheArticleDTO(ResourceReadArticleDTO articleDTO) {
        RedBookRedisManager.setResourceBean((RESOURCE_READ_ARTICLE_PREFIX + articleDTO.getArticleId()), articleDTO);
    }

    /**
     * 获取文章和相关子类信息
     * @param articleId
     * @return
     */
    public static ResourceReadArticleDTO getResourceReadArticleDTO(int articleId) {
        return (ResourceReadArticleDTO) RedBookRedisManager.getResourceBean(RESOURCE_READ_ARTICLE_PREFIX + articleId);
    }

    public static ResourceUnitListenDTO getResourceListenDTO(Integer unitId) {
        return (ResourceUnitListenDTO) RedBookRedisManager.getResourceBean(RESOURCE_LISTEN_UNIT_PREFIX + unitId);
    }


    private static void loadSyncWrite(RedBookCourse course, RedBookUnit unit, IResourceUnitContentWriteDao resourceUnitContentWriteDao, IResourceUnitWriteSentenceDao resourceUnitWriteSentenceDao, IResourceTopicDao resourceTopicDao) {
        RedBookUnitContentWrite redBookUnitContentWrite = resourceUnitContentWriteDao.getRecordByUnitId(unit.getId());
        if(redBookUnitContentWrite!=null){
            List<ResourceUnitWriteSentence> sentenceTrainListByUnitId = resourceUnitWriteSentenceDao.getSentenceTrainListByUnitId(unit.getId());
            if(CollectionUtils.isNotEmpty(sentenceTrainListByUnitId)){
                redBookUnitContentWrite.setSentenceList(sentenceTrainListByUnitId);
            }
            Integer topicId = redBookUnitContentWrite.getTopicId();
            if(topicId!=null){
                redBookUnitContentWrite.setTopicName(resourceTopicDao.getTopicNameById(topicId));
            }
            unit.setContentWrite(redBookUnitContentWrite);
            addSyncWriteMethod(unit.getId());
            course.setContainWrite(true);
        }
    }

    private static void loadGrammar(RedBookCourse course, IRedBookCourseDao redBookCourseDao, RedBookUnit unit) {
        if (unit.getContentGrammar() != null) {
            List<GrammarTitleBean> titleList = getTitleList(unit.getId(), 0, 0);
            if (titleList == null) {
                unit.setContentGrammar(null);
            } else {
                List<GrammarTitleBean> basicTitleList = getBasicTitleList(titleList);
                RedBookUnitContentGrammar contentGrammar = unit.getContentGrammar();
                contentGrammar.setTargetContentList(redBookCourseDao.getGrammarContentList(unit.getId(), -1)); //添加标题
                contentGrammar.setSummaryContentList(redBookCourseDao.getGrammarContentList(unit.getId(), -2)); //添加知识点小结
                contentGrammar.setKnowledgeCount(basicTitleList.size());//设置知识点数量
                unit.setContentGrammar(contentGrammar);

                RedBookCourseManager.addGrammarTitleList(titleList, unit.getId());
                List<GrammarQuestionBean> grammarQuestionList = redBookCourseDao.getGrammarQuestionList(unit.getId());
                if(grammarQuestionList != null && !grammarQuestionList.isEmpty()){
                    grammarQuestionList.forEach(data ->{
                        List<GrammarQuestionContentBean> grammarQuestionContentList = redBookCourseDao.getGrammarQuestionContentList(data.getId());
                        data.setQuestionContentBeanList(grammarQuestionContentList);
                    });
                }
                RedBookCourseManager.addGrammarQuestionList(grammarQuestionList, unit.getId());
                RedBookCourseManager.addGrammarCourseQuestionList(redBookCourseDao.getGrammarCourseQuestionList(course.getId()), course.getId());
                course.setContainGrammar(true);
            }
        }
    }

    /**
     * 添加标题内容列表
     */
    public static void addGrammarTitleList(List<GrammarTitleBean> titleList, Integer unitId) {
        if (titleList != null && !titleList.isEmpty()) {
            RedBookRedisManager.delResourceBean(GRAMMAR_TITLELIST_PREFIX + unitId);
            for (GrammarTitleBean titleBean : titleList) {
                RedBookRedisManager.addResourceToList(GRAMMAR_TITLELIST_PREFIX + unitId, titleBean.getId() + "");
                RedBookRedisManager.setResourceBean(GRAMMAR_TITLE_BEAN_PREFIX + titleBean.getId(), titleBean);
            }
        }
    }

    /**
     * 获取单元标题列表
     *
     * @param unitId
     * @return
     */
    public static List<GrammarTitleBean> getGrammarTitleList(Integer unitId) {
        List<String> idList = RedBookRedisManager.getResourceListRange(GRAMMAR_TITLELIST_PREFIX + unitId, 0, -1);
        List<GrammarTitleBean> grammarTitleBeanList = new ArrayList<>();
        for (String id : idList) {
            grammarTitleBeanList.add(getGrammarTitle(Integer.parseInt(id)));
        }
        return grammarTitleBeanList;
    }

    /**
     * 获取底层知识点
     *
     * @param unitTitleList
     * @return
     */
    private static List<GrammarTitleBean> getBasicTitleList(List<GrammarTitleBean> unitTitleList) {
        List<GrammarTitleBean> titleList = new ArrayList<GrammarTitleBean>();
        for (GrammarTitleBean unitTitle : unitTitleList) {
            if (unitTitle.isBasic()) {
                titleList.add(unitTitle);
            }
            if (unitTitle.getTitleList() != null && unitTitle.getTitleList().size() > 0) {
                titleList.addAll(getBasicTitleList(unitTitle.getTitleList()));
            }
        }
        return titleList;
    }

    /**
     * 获取知识点
     *
     * @param titleId
     * @return
     */
    public static GrammarTitleBean getGrammarTitle(Integer titleId) {
        return (GrammarTitleBean) RedBookRedisManager.getResourceBean(GRAMMAR_TITLE_BEAN_PREFIX + titleId);
    }

    /**
     * 添加本节检测试题
     *
     * @param grammarQuestionList
     * @param unitId
     */
    public static void addGrammarQuestionList(List<GrammarQuestionBean> grammarQuestionList, Integer unitId) {
        if (grammarQuestionList != null) {
            RedBookRedisManager.setResourceBean(GRAMMAR_QUESTIONLIST_PREFIX + unitId, grammarQuestionList);
        }
    }

    /**
     * 获取本节检测题目列表
     *
     * @param unitId
     * @return
     */
    public static List<GrammarQuestionBean> getGrammarQuestionList(Integer unitId) {
        return (List<GrammarQuestionBean>) RedBookRedisManager.getResourceBean(GRAMMAR_QUESTIONLIST_PREFIX + unitId);
    }

    /**
     * 添加本章检测试题
     *
     * @param grammarQuestionList
     * @param courseId
     */
    public static void addGrammarCourseQuestionList(List<SimulationQuestionBean> grammarQuestionList, Integer courseId) {
        if (grammarQuestionList != null) {
            RedBookRedisManager.setResourceBean(GRAMMAR_COURSE_QUESTIONLIST_PREFIX + courseId, grammarQuestionList);
        }
    }

    /**
     * 获取本章检测题目列表
     *
     * @param courseId
     * @return
     */
    public static List<SimulationQuestionBean> getGrammarCourseQuestionList(Integer courseId) {
        return (List<SimulationQuestionBean>) RedBookRedisManager.getResourceBean(GRAMMAR_COURSE_QUESTIONLIST_PREFIX + courseId);
    }

    /**
     * 层级标题导入子标题
     *
     * @param unitId
     * @param parentId
     * @param level
     * @return
     */
    private static List<GrammarTitleBean> getTitleList(Integer unitId, Integer parentId, int level) {
        if (level > 3) {
            return null;
        }
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        List<GrammarTitleBean> nextTitleList = null;
        nextTitleList = redBookCourseDao.getGrammarTitleList(unitId, parentId);
        if (nextTitleList.size() == 0) {
            return null;
        }
        for (GrammarTitleBean title : nextTitleList) {
            title.setTitleList(getTitleList(unitId, title.getId(), level + 1));
            title.setSubtitleList(getSubtitleList(unitId, title.getId()));
        }
        return nextTitleList;
    }

    /**
     * 子标题导入题目
     *
     * @param unitId
     * @param titleId
     * @return
     */
    private static List<GrammarSubtitleBean> getSubtitleList(Integer unitId, Integer titleId) {
        List<GrammarSubtitleBean> subtitleList = null;
        IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
        subtitleList = redBookCourseDao.getGrammarSubtitleList(unitId, titleId);
        if (subtitleList.size() == 0) {
            return null;
        }

        List<GrammarContentBean> contentList = null;
        for (GrammarSubtitleBean subtitle : subtitleList) {
            contentList = redBookCourseDao.getGrammarContentList(unitId, subtitle.getId());
            if (contentList.size() > 0) {
                subtitle.setContentList(contentList);
            }

        }
        return subtitleList;
    }

    public static boolean deleteWord(Integer wordId) {
        if (RedBookRedisManager.getResourceBean(WORDBEAN_PREFIX + wordId) != null) {
            addPublishRecord(ResourcePublishRecords.PublishType.WORD_PUBLISH, "wordId", wordId);
        }
        RedBookRedisManager.delResourceBean(WORDBEAN_PREFIX + wordId);
        RedBookRedisManager.delResourceBean(TONGBUWORDBEAN_PREFIX + wordId);
        return true;
    }

    //最后发布id
    private final static String LAST_PUBLISH_ID_PREFIX = "lastPublishId";

    public static void addPublishRecord(ResourcePublishRecords.PublishType publishType, String column, Object value) {
        IResourcePublishRecordsDao resourcePublishRecordsDao = SpringActionSupport.getSpringBean("resourcePublishRecordsDao", null, IResourcePublishRecordsDao.class);
        ResourcePublishRecords publishRecords = new ResourcePublishRecords();
        publishRecords.setPublishType(publishType);
        publishRecords.setRelationColumn(column);
        publishRecords.setRelationValue(value.toString());
        resourcePublishRecordsDao.insertResourcePublishRecords(publishRecords);
        RedBookRedisManager.setResourceString(LAST_PUBLISH_ID_PREFIX, publishRecords.getId() + "");
    }

    public static Integer getLastPublishId() {
        String lastPublishId = RedBookRedisManager.getResourceString(LAST_PUBLISH_ID_PREFIX);
        if (lastPublishId == null) {
            return 0;
        }
        return Integer.parseInt(lastPublishId);
    }


    public static Map<String, Object> getPhonogramWordBean(int phonogramWordId) {
        return (Map<String, Object>) RedBookRedisManager.getResourceBean(PHONOGRAM_WORD_PREFIX + phonogramWordId);
    }

    public static List<String> getPhonogramWordIdListbyPhonogramId(int phonogramWordId) {
        return RedBookRedisManager.getResourceListRange(PHONOGRAM_WORDIDLIST_BY_PHONOGRAMID_PREFIX + phonogramWordId, 0, -1);
    }

    public static List<String> getPhonogramWordIdAllList() {
        return RedBookRedisManager.getResourceListRange(PHONOGRAM_ALL_WORDIDLIST_PREFIX, 0, -1);
    }

    public static void addWordAbbrList(List<WordAbbr> list) {
        RedBookRedisManager.setResourceBean(WORD_ABBR_LIST_BEAN_PREFIX, list);
    }

    public static List<WordAbbr> getWordAbbrList() {
        return (List<WordAbbr>) RedBookRedisManager.getResourceBean(WORD_ABBR_LIST_BEAN_PREFIX);
    }

    /**
     * 获取单词配图地址
     *
     * @param spelling
     * @return
     */
    public static String getWordImgUrl(String spelling) {
        return RedBookRedisManager.getResourceString(WORD_IMGURL_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"));
    }

    public static void addWordImgUrl(String spelling, String imgUrl) {
        RedBookRedisManager.setResourceString(WORD_IMGURL_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"), imgUrl);
    }

    public static String getWordSpeakWordMeaning(String spelling) {
        return RedBookRedisManager.getResourceString(WORD_SPEAK_WORD_MEANING_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"));
    }

    public static void addWordSpeakWordMeaning(String spelling, String imgUrl) {
        RedBookRedisManager.setResourceString(WORD_SPEAK_WORD_MEANING_PREFIX + spelling.replaceAll(" ", "_").replaceAll("'", "-"), imgUrl);
    }
    public static void addSyncWriteMethod(Integer unitId) {
        IWriteTitleDao writeTitleDao = SpringActionSupport.getSpringBean("writeTitleDao", null, IWriteTitleDao.class);
        IWriteContentDao writeContentDao = SpringActionSupport.getSpringBean("writeContentDao", null, IWriteContentDao.class);
        WriteUnitTitleBean writeUnitTitleBean = writeTitleDao.getWriteParientTitleByUnitId(unitId);
        if (writeUnitTitleBean != null) {
            writeUnitTitleBean.setWriteUnitContentBeanList(writeContentDao.getWriteContentListByUnitIdAndTitleId(unitId, writeUnitTitleBean.getId()));
            List<WriteUnitTitleBean> titleListByUnitIdAndParientId = writeTitleDao.getTitleListByUnitIdAndParientId(unitId, writeUnitTitleBean.getId());
            Optional.ofNullable(titleListByUnitIdAndParientId).orElse(new ArrayList<>()).forEach(item -> {
                item.setWriteUnitContentBeanList(writeContentDao.getWriteContentListByUnitIdAndTitleId(unitId, item.getId()));
            });
            writeUnitTitleBean.setSubTitleList(titleListByUnitIdAndParientId);
        }
        RedBookRedisManager.setResourceBean(UNITCONTENTWRITEMETHOD + unitId, writeUnitTitleBean);

    }

    public static WriteUnitTitleBean getSyncWriteMethodByUnitId(Integer unitId) {
        return (WriteUnitTitleBean) RedBookRedisManager.getResourceBean(UNITCONTENTWRITEMETHOD + unitId);
    }
    //判断课程是否在阶段内
    public static Boolean checkCourseInStage(Integer courseId, Integer stage) {
        //获取课程信息
        RedBookCourse course = getRedBookCourse(courseId);
        //判断课程存在
        if (course == null) {
            return Boolean.FALSE;
        }
        if (course.getStage() == stage) {
            return Boolean.TRUE;
        }
        //根据版本和阶段，获取课程列表
        List<RedBookCourse> courseList = getRedBookCourseList(course.getVersionId(), stage);
        //如果课程不在列表内，则返回false
        if (courseList == null || courseList.stream().noneMatch(redBookCourse -> redBookCourse.getId() == courseId)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
