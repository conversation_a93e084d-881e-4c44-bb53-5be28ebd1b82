package com.woxue.resourceservice.util;

import com.woxue.common.util.ObjectManager;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;

/**
 * 小红本redis
 */
public class RedBookRedisManager {
    private static JedisPool pool = null;

    private static final int DB_INDEX = 23;//old 22

    private static String REDIS_SERVER_IP = null;
    private static final int REDIS_SERVER_PORT = 6379;
    private static final int TIMEOUT = 0;
    private static String REDIS_SERVER_PASSWD = null;

    /**
     * 构建redis连接池
     *
     * @return JedisPool
     */
    public static JedisPool getPool() {
        if (pool == null) {
            Properties prop = new Properties();
            try {
                InputStream in = RedBookRedisManager.class.getResourceAsStream("/config.properties");
                prop.load(in);
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            REDIS_SERVER_IP = prop.getProperty("redis.server.host");
            REDIS_SERVER_PASSWD = prop.getProperty("redis.server.password");

            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(2000);
            config.setMaxIdle(50);
            config.setMaxWaitMillis(1000 * 3);
            config.setTestOnBorrow(true);
            pool = new JedisPool(config, REDIS_SERVER_IP, REDIS_SERVER_PORT, TIMEOUT, REDIS_SERVER_PASSWD, DB_INDEX);
        }
        return pool;
    }

    /**
     * 返还到连接池
     *
     * @param pool
     * @param redis
     */
    public static void returnResource(JedisPool pool, Jedis redis) {
        if (redis != null) {
            pool.returnResource(redis);
        }
    }

    public static String getResourceString(String key) {
        String value = null;
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            value = jedis.get(key);
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
        return value;
    }

    public static void setResourceString(String key, String value) {
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            jedis.set(key, value);
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
    }

    public static void setResourceHashString(String key, String field, String value) {
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            jedis.hset(key, field, value);
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
    }

    public static String getResourceHashString(String key, String field) {
        String value = null;
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            value = jedis.hget(key, field);
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
        return value;
    }

    public static void addResourceToList(String key, String value) {
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            jedis.rpush(key, value);
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
    }

    public static void removeResourceFromList(String key, String value) {
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            // 使用LREM命令按值删除元素（可指定删除次数，默认为0表示删除所有匹配项）
            long removedCount = jedis.lrem(key, 0, value);
        } catch (Exception e) {
            //释放redis对象
            if (jedis != null) {
                pool.returnBrokenResource(jedis);
            }
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
    }

    public static void setResourceBean(String key, Object obj) {
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            jedis.set(key.getBytes(), ObjectManager.serialize(obj));
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
    }

    public static Object getResourceBean(String key) {
        Object value = null;
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            value = ObjectManager.unserialize(jedis.get(key.getBytes()));
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
        return value;
    }

    public static List getResourceListRange(String key, int start, int end) {
        List<String> list = null;
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            list = jedis.lrange(key, start, end);
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }

        return list;
    }

    public static void delResourceBean(String key) {
        JedisPool pool = null;
        Jedis jedis = null;
        try {
            pool = getPool();
            jedis = pool.getResource();
            jedis.del(key.getBytes());
        } catch (Exception e) {
            //释放redis对象
            pool.returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            //返还到连接池
            returnResource(pool, jedis);
        }
    }
}
