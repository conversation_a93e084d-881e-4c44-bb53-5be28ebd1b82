package com.woxue.resourceservice.util;

public class PhoneticUtils {

    // 定义音标替换规则
    private static final String[][] PHONETIC_RULES = {
            {" ; ", "ˌ"}, {";", "ˌ"}, {"d=", "ʤ"}, {"t7", "ʧ"}, {"0", "θ"},
            {"1", "ɑ"}, {"2", "ʌ"}, {"3", "ə"}, {"4", "ε"}, {"5", "æ"},
            {"6", "ɔ"}, {"7", "ʃ"}, {"8", "ð"}, {"9", "ŋ"}, {"=", "ʒ"},
            {"-", "ː"}, {"+", "ɒ"}, {"~", "ɪ"}, {"!", "ʊ"}, {"#", "ɜ"},
            {":", "ː"}, {"əː", "ɜː"}, {"ɪː", "iː"}, {"ʊː", "uː"}, {"ɒː", "ɔː"},
            {"ɒɪ", "ɔɪ"}, {"ɔ", "ɒ"}, {"ε", "e"} // 添加单字符规则
    };

    // 将音标替换规则转换为方法
    public static String formatSyllAble(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        String result = input;

        // 遵循替换顺序逐步应用规则
        for (String[] rule : PHONETIC_RULES) {
            result = result.replace(rule[0], rule[1]);
        }

        // 特殊处理单独的 i 和 u，避免误替换
        result = result.replaceAll("\\bi\\b", "ɪ"); // 替换独立音标 i
        result = result.replaceAll("\\bu\\b", "ʊ"); // 替换独立音标 u

        return result;
    }


}
