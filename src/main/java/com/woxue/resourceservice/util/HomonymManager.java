package com.woxue.resourceservice.util;

import java.util.List;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.resourceservice.dao.IHomonymDao;

/**
 * 同音词
 * <AUTHOR>
 *
 */
public class HomonymManager{
	private final static String HOMONYM_SPELLING_PREFIX = "homonymList";
	
	private static List<String> getHomonymList() {
		List<String> homonymList = RedBookRedisManager.getResourceListRange(HOMONYM_SPELLING_PREFIX, 0, -1);
		if(homonymList==null||homonymList.size()<=0){
			homonymList = SpringActionSupport.getSpringBean("homonymDao", null, IHomonymDao.class).loadHomonymList();
			for(String homonym:homonymList){
				RedBookRedisManager.addResourceToList(HOMONYM_SPELLING_PREFIX, homonym);
			}
		}
		return homonymList;
	}
	
	/**
	 * 是否是同音词
	 * @param spelling
	 * @return
	 */
	public static boolean isHomonym(String spelling){
		if(spelling==null||spelling.length()<=0){
			return false;
		}
		boolean b = false;
		List<String> homonymList = getHomonymList();
		if(homonymList.contains(spelling)){
			b = true;
		}
		return b;
	}

	public static boolean clearHomonymList(){
		RedBookRedisManager.delResourceBean(HOMONYM_SPELLING_PREFIX);
		return true;
	}
}
