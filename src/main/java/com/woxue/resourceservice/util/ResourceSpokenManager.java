package com.woxue.resourceservice.util;

import com.woxue.ai.util.AIChatUtil;
import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopic;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent;
import com.woxue.common.util.ExpireTimeConstant;
import com.woxue.common.util.MD5;
import com.woxue.common.util.OSSManager;
import com.woxue.resourcemanage.dao.ISpokenDao;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ResourceSpokenManager {

    private static final String spokenListKey = "spokenList";
    private static final String spokenContentListKey = "spokenContentList:";
    private static final String AI_AUDIO_FILE_CATALOG = "aiAudio/aiSpokenSpeed/";
    private static final String AI_AUDIO_REDIS_CATALOG = "aiAudioKeySpeed:";

    public static void reloadResourceSpoken(){
        ISpokenDao spokenDao = SpringActionSupport.getSpringBean("ISpokenDao", null, ISpokenDao.class);
        List<ResourceSpokenTopic> allResourceSpokenTopic = spokenDao.getAllResourceSpokenTopic();
        RedBookRedisManager.setResourceBean(spokenListKey,allResourceSpokenTopic);
        allResourceSpokenTopic.forEach(resourceSpokenTopic -> {
            List<ResourceSpokenTopicContent> contentByTopicId = spokenDao.getResourceSpokenTopicContentByTopicId(resourceSpokenTopic.getId());
            Optional.ofNullable(contentByTopicId).orElse(new ArrayList<>()).forEach(resourceSpokenTopicContent -> {
                String selling = resourceSpokenTopicContent.getSpelling();
                if(StringUtils.isNotEmpty(selling)){
                    String redisKey = MD5.Md5(selling);
                    String rate=null;
                    if(1==resourceSpokenTopicContent.getType()){
                        rate="-30%";//例句速率降低20%
                    }
                    String soundUrl = RedBookRedisManager.getResourceString(AI_AUDIO_REDIS_CATALOG+redisKey);
                    if(StringUtils.isEmpty(soundUrl)){
                        byte[] males = AIChatUtil.getTextToAudioByRate(selling, "male",rate);
                        InputStream inputStream = new ByteArrayInputStream(males);
                        try {
                            inputStream.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME, AI_AUDIO_FILE_CATALOG + redisKey + ".mp3", inputStream);
                        resourceSpokenTopicContent.setSoundUrl(AI_AUDIO_FILE_CATALOG + redisKey + ".mp3");
                        RedBookRedisManager.setResourceString(AI_AUDIO_REDIS_CATALOG+redisKey, resourceSpokenTopicContent.getSoundUrl());
                    }else {
                        resourceSpokenTopicContent.setSoundUrl(soundUrl);
                    }
                }
            });
            RedBookRedisManager.setResourceBean(spokenContentListKey + resourceSpokenTopic.getId(),contentByTopicId);
        });
    }

    public static List<ResourceSpokenTopic> getAllResourceSpokenTopic(){
        return (List<ResourceSpokenTopic>) RedBookRedisManager.getResourceBean(spokenListKey);
    }

    public static List<ResourceSpokenTopicContent> getResourceSpokenTopicContentByTopicId(int topicId){
        return (List<ResourceSpokenTopicContent>) RedBookRedisManager.getResourceBean(spokenContentListKey + topicId);
    }
}
