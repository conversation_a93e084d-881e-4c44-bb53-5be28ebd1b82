package com.woxue.resourceservice.util;

import com.redbook.kid.common.model.phonics.*;
import com.woxue.common.action.SpringActionSupport;
import com.woxue.resourcemanage.service.KidPhonicsService;
import io.jsonwebtoken.lang.Collections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 少儿自然拼读资源缓存管理器
 */
public class KidPhonicsManager {
    private static final Logger logger = LoggerFactory.getLogger(KidPhonicsManager.class);

    // Redis key前缀
    private static final String PREFIX = "kid:phonics:";
    
    // 课程缓存键模式，用于模式删除
    public static final String KID_PHONICS_COURSE_PATTERN = PREFIX + "c:*";
    
    //自然拼读课程缓存
    public static final String KID_PHONICS_COURSE_CACHE_KEY = PREFIX + "c:";
    //自然拼读课程列表缓存
    public static final String KID_PHONICS_COURSE_LIST_CACHE_KEY = PREFIX + "course_list";
    //单元缓存，添加courseId作为前缀
    public static final String KID_PHONICS_UNIT_CACHE_KEY = PREFIX + "c:{courseId}:u:";
    //单元列表缓存
    public static final String KID_PHONICS_UNIT_LIST_CACHE_KEY = PREFIX + "c:{courseId}:units";
    //音素缓存，添加courseId和unitId作为前缀
    public static final String KID_PHONICS_LETTER_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:l:";
    //音素列表缓存
    public static final String KID_PHONICS_LETTER_LIST_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:letters";
    //音素组件缓存，添加courseId和unitId以及letterId作为前缀
    public static final String KID_PHONICS_LETTER_COMPONENT_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:l:{letterId}:comp:";
    //例词缓存，添加courseId、unitId和letterId作为前缀
    public static final String KID_PHONICS_WORD_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:l:{letterId}:w:";
    //例词列表缓存
    public static final String KID_PHONICS_WORD_LIST_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:l:{letterId}:words";
    //儿歌缓存，添加courseId和unitId作为前缀
    public static final String KID_PHONICS_RHYME_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:r:";
    //儿歌列表缓存
    public static final String KID_PHONICS_RHYME_LIST_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:rhymes";
    //绘本缓存，添加courseId和unitId作为前缀
    public static final String KID_PHONICS_PICTURE_BOOK_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:pb:";
    //绘本列表缓存
    public static final String KID_PHONICS_PICTURE_BOOK_LIST_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:picture_books";
    //绘本内容缓存，添加courseId、unitId和pictureBookId作为前缀
    public static final String KID_PHONICS_PICTURE_BOOK_CONTENT_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:pb:{pictureBookId}:pc:";
    //绘本句子缓存，添加courseId、unitId、pictureBookId和contentId作为前缀
    public static final String KID_PHONICS_PICTURE_BOOK_SENTENCE_CACHE_KEY = PREFIX + "c:{courseId}:u:{unitId}:pb:{pictureBookId}:pc:{contentId}:s:";

    /**
     * 构建单元缓存键
     */
    private static String buildUnitCacheKey(Integer courseId, Integer unitId) {
        return KID_PHONICS_UNIT_CACHE_KEY.replace("{courseId}", courseId.toString()) + unitId;
    }

    /**
     * 构建单元列表缓存键
     */
    private static String buildUnitListCacheKey(Integer courseId) {
        return KID_PHONICS_UNIT_LIST_CACHE_KEY.replace("{courseId}", courseId.toString());
    }

    /**
     * 构建音素缓存键
     */
    private static String buildLetterCacheKey(Integer courseId, Integer unitId, Integer letterId) {
        return KID_PHONICS_LETTER_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString()) + letterId;
    }

    /**
     * 构建音素列表缓存键
     */
    private static String buildLetterListCacheKey(Integer courseId, Integer unitId) {
        return KID_PHONICS_LETTER_LIST_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString());
    }

    /**
     * 构建音素组件缓存键
     */
    private static String buildComponentCacheKey(Integer courseId, Integer unitId, Integer letterId, Integer componentId) {
        return KID_PHONICS_LETTER_COMPONENT_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString())
                .replace("{letterId}", letterId.toString()) + componentId;
    }

    /**
     * 构建例词缓存键
     */
    private static String buildWordCacheKey(Integer courseId, Integer unitId, Integer letterId, Integer wordId) {
        return KID_PHONICS_WORD_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString())
                .replace("{letterId}", letterId.toString()) + wordId;
    }

    /**
     * 构建例词列表缓存键
     */
    private static String buildWordListCacheKey(Integer courseId, Integer unitId, Integer letterId) {
        return KID_PHONICS_WORD_LIST_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString())
                .replace("{letterId}", letterId.toString());
    }

    /**
     * 构建儿歌缓存键
     */
    private static String buildRhymeCacheKey(Integer courseId, Integer unitId, Integer rhymeId) {
        return KID_PHONICS_RHYME_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString()) + rhymeId;
    }

    /**
     * 构建儿歌列表缓存键
     */
    private static String buildRhymeListCacheKey(Integer courseId, Integer unitId) {
        return KID_PHONICS_RHYME_LIST_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString());
    }

    /**
     * 构建绘本缓存键
     */
    private static String buildPictureBookCacheKey(Integer courseId, Integer unitId, Integer pictureBookId) {
        return KID_PHONICS_PICTURE_BOOK_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString()) + pictureBookId;
    }

    /**
     * 构建绘本列表缓存键
     */
    private static String buildPictureBookListCacheKey(Integer courseId, Integer unitId) {
        return KID_PHONICS_PICTURE_BOOK_LIST_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString());
    }

    /**
     * 构建绘本内容缓存键
     */
    private static String buildPictureBookContentCacheKey(Integer courseId, Integer unitId, Integer pictureBookId, Integer contentId) {
        return KID_PHONICS_PICTURE_BOOK_CONTENT_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString())
                .replace("{pictureBookId}", pictureBookId.toString()) + contentId;
    }

    /**
     * 构建绘本句子缓存键
     */
    private static String buildPictureBookSentenceCacheKey(Integer courseId, Integer unitId, Integer pictureBookId, Integer contentId, Integer sentenceId) {
        return KID_PHONICS_PICTURE_BOOK_SENTENCE_CACHE_KEY
                .replace("{courseId}", courseId.toString())
                .replace("{unitId}", unitId.toString())
                .replace("{pictureBookId}", pictureBookId.toString())
                .replace("{contentId}", contentId.toString()) + sentenceId;
    }

    /**
     * 发布自然拼读资源
     * 将课程及其所有资源缓存到Redis
     *
     * @param courseId 课程ID
     */
    public static void publishKidPhonicsResource(Integer courseId) {
        if (courseId == null) {
            logger.error("发布自然拼读资源失败：课程ID为空");
            return;
        }

        try {
            // 先清理该课程的所有缓存
            clearCourseCache(courseId);
            
            KidPhonicsService kidPhonicsService = SpringActionSupport.getSpringBean("kidPhonicsService", null, KidPhonicsService.class);
            KidPhonicsCourse course = kidPhonicsService.getCourseById(courseId);

            if (course == null) {
                logger.warn("发布自然拼读资源失败：未找到ID为{}的课程", courseId);
                return;
            }

            // 缓存课程信息
            cacheCourse(course);

            // 缓存课程下的所有单元
            List<KidPhonicsUnit> units = kidPhonicsService.getUnitsByCourseId(courseId);
            if (!Collections.isEmpty(units)) {
                for (KidPhonicsUnit unit : units) {
                    // 加载单元的完整资源
                    KidPhonicsUnit unitWithResources = kidPhonicsService.loadUnitWithResources(unit);
                    cacheUnit(unitWithResources);
                    
                    if (unitWithResources.getLetterList() != null) {
                        // 缓存单元下的音素
                        for (KidPhonicsLetter letter : unitWithResources.getLetterList()) {
                            cacheLetter(letter, courseId, unit.getId());
                            
                            // 缓存音素下的组件
                            if (letter.getComponentList() != null) {
                                for (KidPhonicsLetterComponent component : letter.getComponentList()) {
                                    cacheComponent(component, courseId, unit.getId(), letter.getId());
                                }
                            }
                            
                            // 缓存音素下的例词
                            if (letter.getWordList() != null) {
                                for (KidPhonicsWord word : letter.getWordList()) {
                                    cacheWord(word, courseId, unit.getId(), letter.getId());
                                }
                            }
                        }
                    }
                    
                    // 缓存单元下的儿歌
                    if (unitWithResources.getRhymeList() != null) {
                        for (KidPhonicsRhyme rhyme : unitWithResources.getRhymeList()) {
                            cacheRhyme(rhyme, courseId, unit.getId());
                        }
                    }
                    
                    // 缓存单元下的绘本
                    if (unitWithResources.getPictureBookList() != null) {
                        for (KidPhonicsPictureBook pictureBook : unitWithResources.getPictureBookList()) {
                            cachePictureBook(pictureBook, courseId, unit.getId());
                            
                            // 缓存绘本内容
                            if (pictureBook.getContentList() != null) {
                                for (KidPhonicsPictureBookContent content : pictureBook.getContentList()) {
                                    cachePictureBookContent(content, courseId, unit.getId(), pictureBook.getId());
                                    
                                    // 缓存绘本句子
                                    if (content.getSentenceList() != null) {
                                        for (KidPhonicsPictureBookSentence sentence : content.getSentenceList()) {
                                            cachePictureBookSentence(sentence, courseId, unit.getId(), pictureBook.getId(), content.getId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            logger.info("成功发布ID为{}的自然拼读资源", courseId);
        } catch (Exception e) {
            logger.error("发布自然拼读资源异常，课程ID: {}", courseId, e);
        }
    }

    /**
     * 缓存课程
     */
    private static void cacheCourse(KidPhonicsCourse course) {
        if (course == null) return;
        
        // 添加到课程列表
        List<String> courseIdList = KidRedisManager.getResourceListRange(KID_PHONICS_COURSE_LIST_CACHE_KEY, 0, -1);
        if (courseIdList == null || !courseIdList.contains(course.getId().toString())) {
            KidRedisManager.addResourceToList(KID_PHONICS_COURSE_LIST_CACHE_KEY, course.getId().toString());
            logger.debug("添加课程ID {}到课程列表", course.getId());
        }
        // 缓存课程对象
        KidRedisManager.setResourceBean(KID_PHONICS_COURSE_CACHE_KEY + course.getId(), course);
    }

    /**
     * 缓存单元
     */
    private static void cacheUnit(KidPhonicsUnit unit) {
        if (unit == null || unit.getCourseId() == null) return;
        
        // 添加到单元列表
        String unitListKey = buildUnitListCacheKey(unit.getCourseId());
        List<String> unitIdList = KidRedisManager.getResourceListRange(unitListKey, 0, -1);
        if (unitIdList == null || !unitIdList.contains(unit.getId().toString())) {
            KidRedisManager.addResourceToList(unitListKey, unit.getId().toString());
            logger.debug("添加单元ID {}到课程{}的单元列表", unit.getId(), unit.getCourseId());
        }
        // 缓存单元对象
        String unitKey = buildUnitCacheKey(unit.getCourseId(), unit.getId());
        KidRedisManager.setResourceBean(unitKey, unit);
    }

    /**
     * 缓存音素
     */
    private static void cacheLetter(KidPhonicsLetter letter, Integer courseId, Integer unitId) {
        if (letter == null || courseId == null || unitId == null) return;
        
        // 添加到音素列表
        String letterListKey = buildLetterListCacheKey(courseId, unitId);
        List<String> letterIdList = KidRedisManager.getResourceListRange(letterListKey, 0, -1);
        if (letterIdList == null || !letterIdList.contains(letter.getId().toString())) {
            KidRedisManager.addResourceToList(letterListKey, letter.getId().toString());
            logger.debug("添加音素ID {}到单元{}的音素列表", letter.getId(), unitId);
        }
        // 缓存音素对象
        String letterKey = buildLetterCacheKey(courseId, unitId, letter.getId());
        KidRedisManager.setResourceBean(letterKey, letter);
    }

    /**
     * 缓存音素组件
     */
    private static void cacheComponent(KidPhonicsLetterComponent component, Integer courseId, Integer unitId, Integer letterId) {
        if (component == null || courseId == null || unitId == null || letterId == null) return;
        String componentKey = buildComponentCacheKey(courseId, unitId, letterId, component.getId());
        KidRedisManager.setResourceBean(componentKey, component);
    }

    /**
     * 缓存例词
     */
    private static void cacheWord(KidPhonicsWord word, Integer courseId, Integer unitId, Integer letterId) {
        if (word == null || courseId == null || unitId == null || letterId == null) return;
        
        // 添加到例词列表
        String wordListKey = buildWordListCacheKey(courseId, unitId, letterId);
        List<String> wordIdList = KidRedisManager.getResourceListRange(wordListKey, 0, -1);
        if (wordIdList == null || !wordIdList.contains(word.getId().toString())) {
            KidRedisManager.addResourceToList(wordListKey, word.getId().toString());
            logger.debug("添加例词ID {}到音素{}的例词列表", word.getId(), letterId);
        }
        // 缓存例词对象
        String wordKey = buildWordCacheKey(courseId, unitId, letterId, word.getId());
        KidRedisManager.setResourceBean(wordKey, word);
    }

    /**
     * 缓存儿歌
     */
    private static void cacheRhyme(KidPhonicsRhyme rhyme, Integer courseId, Integer unitId) {
        if (rhyme == null || courseId == null || unitId == null) return;
        
        // 添加到儿歌列表
        String rhymeListKey = buildRhymeListCacheKey(courseId, unitId);
        List<String> rhymeIdList = KidRedisManager.getResourceListRange(rhymeListKey, 0, -1);
        if (rhymeIdList == null || !rhymeIdList.contains(rhyme.getId().toString())) {
            KidRedisManager.addResourceToList(rhymeListKey, rhyme.getId().toString());
            logger.debug("添加儿歌ID {}到单元{}的儿歌列表", rhyme.getId(), unitId);
        }
        // 缓存儿歌对象
        String rhymeKey = buildRhymeCacheKey(courseId, unitId, rhyme.getId());
        KidRedisManager.setResourceBean(rhymeKey, rhyme);
    }

    /**
     * 缓存绘本
     */
    private static void cachePictureBook(KidPhonicsPictureBook pictureBook, Integer courseId, Integer unitId) {
        if (pictureBook == null || courseId == null || unitId == null) return;
        
        // 添加到绘本列表
        String pictureBookListKey = buildPictureBookListCacheKey(courseId, unitId);
        List<String> pictureBookIdList = KidRedisManager.getResourceListRange(pictureBookListKey, 0, -1);
        if (pictureBookIdList == null || !pictureBookIdList.contains(pictureBook.getId().toString())) {
            KidRedisManager.addResourceToList(pictureBookListKey, pictureBook.getId().toString());
            logger.debug("添加绘本ID {}到单元{}的绘本列表", pictureBook.getId(), unitId);
        }
        // 缓存绘本对象
        String pictureBookKey = buildPictureBookCacheKey(courseId, unitId, pictureBook.getId());
        KidRedisManager.setResourceBean(pictureBookKey, pictureBook);
    }

    /**
     * 缓存绘本内容
     */
    private static void cachePictureBookContent(KidPhonicsPictureBookContent content, Integer courseId, Integer unitId, Integer pictureBookId) {
        if (content == null || courseId == null || unitId == null || pictureBookId == null) return;
        String contentKey = buildPictureBookContentCacheKey(courseId, unitId, pictureBookId, content.getId());
        KidRedisManager.setResourceBean(contentKey, content);
    }

    /**
     * 缓存绘本句子
     */
    private static void cachePictureBookSentence(KidPhonicsPictureBookSentence sentence, Integer courseId, Integer unitId, Integer pictureBookId, Integer contentId) {
        if (sentence == null || courseId == null || unitId == null || pictureBookId == null || contentId == null) return;
        String sentenceKey = buildPictureBookSentenceCacheKey(courseId, unitId, pictureBookId, contentId, sentence.getId());
        KidRedisManager.setResourceBean(sentenceKey, sentence);
    }

    /**
     * 获取所有课程列表
     */
    public static List<KidPhonicsCourse> getAllCourses() {
        try {
            List<String> courseIdList = KidRedisManager.getResourceListRange(KID_PHONICS_COURSE_LIST_CACHE_KEY, 0, -1);
            if (Collections.isEmpty(courseIdList)) {
                return new ArrayList<>();
            }

            return courseIdList.stream()
                    .map(id -> getCourseById(Integer.parseInt(id)))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取所有课程列表异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID获取课程
     */
    public static KidPhonicsCourse getCourseById(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取课程失败：课程ID为空");
            return null;
        }

        try {
            return (KidPhonicsCourse) KidRedisManager.getResourceBean(KID_PHONICS_COURSE_CACHE_KEY + courseId);
        } catch (Exception e) {
            logger.error("获取课程异常，课程ID: {}", courseId, e);
            return null;
        }
    }

    /**
     * 获取课程下的单元列表
     */
    public static List<KidPhonicsUnit> getUnitsByCourseId(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取单元列表失败：课程ID为空");
            return new ArrayList<>();
        }

        try {
            String unitListKey = buildUnitListCacheKey(courseId);
            List<String> unitIdList = KidRedisManager.getResourceListRange(unitListKey, 0, -1);
            if (Collections.isEmpty(unitIdList)) {
                return new ArrayList<>();
            }

            return unitIdList.stream()
                    .map(id -> {
                        String unitKey = buildUnitCacheKey(courseId, Integer.parseInt(id));
                        return (KidPhonicsUnit) KidRedisManager.getResourceBean(unitKey);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取单元列表异常，课程ID: {}", courseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID获取单元
     */
    public static KidPhonicsUnit getUnitById(Integer courseId, Integer unitId) {
        if (courseId == null || unitId == null) {
            logger.warn("获取单元失败：课程ID或单元ID为空");
            return null;
        }

        try {
            String unitKey = buildUnitCacheKey(courseId, unitId);
            return (KidPhonicsUnit) KidRedisManager.getResourceBean(unitKey);
        } catch (Exception e) {
            logger.error("获取单元异常，课程ID: {}, 单元ID: {}", courseId, unitId, e);
            return null;
        }
    }

    /**
     * 获取课程完整资源包（包含所有单元、音素、例词、儿歌、绘本等）
     */
    public static KidPhonicsCourse getCourseWithAllResources(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取课程完整资源失败：课程ID为空");
            return null;
        }

        try {
            // 获取课程基本信息
            KidPhonicsCourse course = getCourseById(courseId);
            if (course == null) {
                return null;
            }

            // 获取所有单元
            List<KidPhonicsUnit> units = getUnitsByCourseId(courseId);
            if (!Collections.isEmpty(units)) {
                for (KidPhonicsUnit unit : units) {
                    Integer unitId = unit.getId();
                    
                    // 获取单元下的音素
                    String letterListKey = buildLetterListCacheKey(courseId, unitId);
                    List<String> letterIdList = KidRedisManager.getResourceListRange(letterListKey, 0, -1);
                    List<KidPhonicsLetter> letters = new ArrayList<>();
                    
                    if (!Collections.isEmpty(letterIdList)) {
                        for (String letterIdStr : letterIdList) {
                            Integer letterId = Integer.parseInt(letterIdStr);
                            String letterKey = buildLetterCacheKey(courseId, unitId, letterId);
                            KidPhonicsLetter letter = (KidPhonicsLetter) KidRedisManager.getResourceBean(letterKey);
                            
                            if (letter != null) {
                                // 获取音素下的例词
                                String wordListKey = buildWordListCacheKey(courseId, unitId, letterId);
                                List<String> wordIdList = KidRedisManager.getResourceListRange(wordListKey, 0, -1);
                                List<KidPhonicsWord> words = new ArrayList<>();
                                
                                if (!Collections.isEmpty(wordIdList)) {
                                    for (String wordIdStr : wordIdList) {
                                        Integer wordId = Integer.parseInt(wordIdStr);
                                        String wordKey = buildWordCacheKey(courseId, unitId, letterId, wordId);
                                        KidPhonicsWord word = (KidPhonicsWord) KidRedisManager.getResourceBean(wordKey);
                                        if (word != null) {
                                            words.add(word);
                                        }
                                    }
                                }
                                letter.setWordList(words);
                                letters.add(letter);
                            }
                        }
                    }
                    unit.setLetterList(letters);

                    // 获取单元下的儿歌
                    String rhymeListKey = buildRhymeListCacheKey(courseId, unitId);
                    List<String> rhymeIdList = KidRedisManager.getResourceListRange(rhymeListKey, 0, -1);
                    List<KidPhonicsRhyme> rhymes = new ArrayList<>();
                    
                    if (!Collections.isEmpty(rhymeIdList)) {
                        for (String rhymeIdStr : rhymeIdList) {
                            Integer rhymeId = Integer.parseInt(rhymeIdStr);
                            String rhymeKey = buildRhymeCacheKey(courseId, unitId, rhymeId);
                            KidPhonicsRhyme rhyme = (KidPhonicsRhyme) KidRedisManager.getResourceBean(rhymeKey);
                            if (rhyme != null) {
                                rhymes.add(rhyme);
                            }
                        }
                    }
                    unit.setRhymeList(rhymes);

                    // 获取单元下的绘本
                    String pictureBookListKey = buildPictureBookListCacheKey(courseId, unitId);
                    List<String> pictureBookIdList = KidRedisManager.getResourceListRange(pictureBookListKey, 0, -1);
                    List<KidPhonicsPictureBook> pictureBooks = new ArrayList<>();
                    
                    if (!Collections.isEmpty(pictureBookIdList)) {
                        for (String pictureBookIdStr : pictureBookIdList) {
                            Integer pictureBookId = Integer.parseInt(pictureBookIdStr);
                            String pictureBookKey = buildPictureBookCacheKey(courseId, unitId, pictureBookId);
                            KidPhonicsPictureBook pictureBook = (KidPhonicsPictureBook) KidRedisManager.getResourceBean(pictureBookKey);
                            if (pictureBook != null) {
                                pictureBooks.add(pictureBook);
                            }
                        }
                    }
                    unit.setPictureBookList(pictureBooks);
                }
            }
            course.setUnits(units);

            return course;
        } catch (Exception e) {
            logger.error("获取课程完整资源异常，课程ID: {}", courseId, e);
            return null;
        }
    }

    /**
     * 加载绘本内容和句子
     */
    private static void loadPictureBookContents(KidPhonicsPictureBook pictureBook) {
        if (pictureBook == null) return;
        
        try {
            // 这个方法在当前流程中没有被调用，已经在getCourseWithAllResources方法中直接处理了绘本内容
            // 如果需要单独加载绘本内容和句子，可以参考下面的实现
            
            // 获取绘本内容列表 - 示例实现，实际中根据键格式和命名规范调整
            // Integer courseId = ...; // 需要从上层传入
            // Integer unitId = pictureBook.getUnitId();
            // Integer pictureBookId = pictureBook.getId();
            // 
            // String contentListKey = "kid:phonics:c:" + courseId + ":u:" + unitId + ":pb:" + pictureBookId + ":contents";
            // List<String> contentIdList = KidRedisManager.getResourceListRange(contentListKey, 0, -1);
            // List<KidPhonicsPictureBookContent> contents = new ArrayList<>();
            // 
            // if (!Collections.isEmpty(contentIdList)) {
            //     for (String contentIdStr : contentIdList) {
            //         Integer contentId = Integer.parseInt(contentIdStr);
            //         String contentKey = buildPictureBookContentCacheKey(courseId, unitId, pictureBookId, contentId);
            //         KidPhonicsPictureBookContent content = (KidPhonicsPictureBookContent) KidRedisManager.getResourceBean(contentKey);
            //         if (content != null) {
            //             // 加载句子列表
            //             String sentenceListKey = "kid:phonics:c:" + courseId + ":u:" + unitId + ":pb:" + pictureBookId + ":pc:" + contentId + ":sentences";
            //             List<String> sentenceIdList = KidRedisManager.getResourceListRange(sentenceListKey, 0, -1);
            //             List<KidPhonicsPictureBookSentence> sentences = new ArrayList<>();
            //             
            //             if (!Collections.isEmpty(sentenceIdList)) {
            //                 for (String sentenceIdStr : sentenceIdList) {
            //                     Integer sentenceId = Integer.parseInt(sentenceIdStr);
            //                     String sentenceKey = buildPictureBookSentenceCacheKey(courseId, unitId, pictureBookId, contentId, sentenceId);
            //                     KidPhonicsPictureBookSentence sentence = (KidPhonicsPictureBookSentence) KidRedisManager.getResourceBean(sentenceKey);
            //                     if (sentence != null) {
            //                         sentences.add(sentence);
            //                     }
            //                 }
            //             }
            //             content.setSentenceList(sentences);
            //             contents.add(content);
            //         }
            //     }
            // }
            // pictureBook.setContentList(contents);
            
            // 当前的getCourseWithAllResources方法已经处理了绘本相关资源的加载，这个方法暂时不需要实现
            // 如果后续需要单独加载绘本内容，可以根据上面的注释实现
        } catch (Exception e) {
            logger.error("加载绘本内容异常，绘本ID: {}", pictureBook.getId(), e);
        }
    }

    /**
     * 清除课程相关缓存
     * 通过课程ID的前缀，一次性清理该课程的所有相关缓存
     */
    public static void clearCourseCache(Integer courseId) {
        if (courseId == null) {
            return;
        }

        try {
            // 使用模式匹配清除所有课程相关缓存
            String coursePattern = PREFIX + "c:" + courseId + "*";
            KidRedisManager.deleteKeysByPattern(coursePattern);
            
            // 从课程列表中移除
            KidRedisManager.removeResourceFromList(KID_PHONICS_COURSE_LIST_CACHE_KEY, courseId.toString());
            
            // 删除课程对象缓存
            KidRedisManager.delResourceBean(KID_PHONICS_COURSE_CACHE_KEY + courseId);
            
            logger.info("清除课程{}相关缓存完成", courseId);
        } catch (Exception e) {
            logger.error("清除课程缓存异常，课程ID: {}", courseId, e);
        }
    }

    /**
     * 清除单元相关缓存
     */
    private static void clearUnitCache(Integer unitId) {
        if (unitId == null) {
            return;
        }

        try {
            // 清除单元缓存
            KidRedisManager.delResourceBean(KID_PHONICS_UNIT_CACHE_KEY + unitId);
            
            // 清除音素相关缓存
            List<String> letterIdList = KidRedisManager.getResourceListRange(KID_PHONICS_LETTER_LIST_CACHE_KEY + unitId, 0, -1);
            if (!Collections.isEmpty(letterIdList)) {
                for (String letterIdStr : letterIdList) {
                    Integer letterId = Integer.parseInt(letterIdStr);
                    clearLetterCache(letterId);
                }
            }
            
            // 清除音素列表缓存
            KidRedisManager.delResourceBean(KID_PHONICS_LETTER_LIST_CACHE_KEY + unitId);
            
            // 清除儿歌相关缓存
            List<String> rhymeIdList = KidRedisManager.getResourceListRange(KID_PHONICS_RHYME_LIST_CACHE_KEY + unitId, 0, -1);
            if (!Collections.isEmpty(rhymeIdList)) {
                for (String rhymeIdStr : rhymeIdList) {
                    Integer rhymeId = Integer.parseInt(rhymeIdStr);
                    KidRedisManager.delResourceBean(KID_PHONICS_RHYME_CACHE_KEY + rhymeId);
                }
            }
            
            // 清除儿歌列表缓存
            KidRedisManager.delResourceBean(KID_PHONICS_RHYME_LIST_CACHE_KEY + unitId);
            
            // 清除绘本相关缓存
            List<String> pictureBookIdList = KidRedisManager.getResourceListRange(KID_PHONICS_PICTURE_BOOK_LIST_CACHE_KEY + unitId, 0, -1);
            if (!Collections.isEmpty(pictureBookIdList)) {
                for (String pictureBookIdStr : pictureBookIdList) {
                    Integer pictureBookId = Integer.parseInt(pictureBookIdStr);
                    clearPictureBookCache(pictureBookId);
                }
            }
            
            // 清除绘本列表缓存
            KidRedisManager.delResourceBean(KID_PHONICS_PICTURE_BOOK_LIST_CACHE_KEY + unitId);
            
            logger.debug("清除单元{}相关缓存完成", unitId);
        } catch (Exception e) {
            logger.error("清除单元缓存异常，单元ID: {}", unitId, e);
        }
    }

    /**
     * 清除音素相关缓存
     */
    private static void clearLetterCache(Integer letterId) {
        if (letterId == null) {
            return;
        }

        try {
            // 清除音素缓存
            KidRedisManager.delResourceBean(KID_PHONICS_LETTER_CACHE_KEY + letterId);
            
            // 清除例词相关缓存
            List<String> wordIdList = KidRedisManager.getResourceListRange(KID_PHONICS_WORD_LIST_CACHE_KEY + letterId, 0, -1);
            if (!Collections.isEmpty(wordIdList)) {
                for (String wordIdStr : wordIdList) {
                    Integer wordId = Integer.parseInt(wordIdStr);
                    KidRedisManager.delResourceBean(KID_PHONICS_WORD_CACHE_KEY + wordId);
                }
            }
            
            // 清除例词列表缓存
            KidRedisManager.delResourceBean(KID_PHONICS_WORD_LIST_CACHE_KEY + letterId);
            
            logger.debug("清除音素{}相关缓存完成", letterId);
        } catch (Exception e) {
            logger.error("清除音素缓存异常，音素ID: {}", letterId, e);
        }
    }

    /**
     * 清除绘本相关缓存
     */
    private static void clearPictureBookCache(Integer pictureBookId) {
        if (pictureBookId == null) {
            return;
        }

        try {
            // 清除绘本缓存
            KidRedisManager.delResourceBean(KID_PHONICS_PICTURE_BOOK_CACHE_KEY + pictureBookId);
            
            // 注：此处应该清除绘本内容和句子缓存，但由于内容和句子没有缓存列表，
            // 这里无法直接获取它们的ID。这是缓存设计中的一个局限。
            
            logger.debug("清除绘本{}相关缓存完成", pictureBookId);
        } catch (Exception e) {
            logger.error("清除绘本缓存异常，绘本ID: {}", pictureBookId, e);
        }
    }
} 