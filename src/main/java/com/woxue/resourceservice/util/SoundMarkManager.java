package com.woxue.resourceservice.util;


public class SoundMarkManager{
	//分音节
	private static final String SOUNDMARK_NEW_PREFIX = "soundmark_new:spelling:";
	public static String getSoundmark(String spelling){
		return RedBookRedisManager.getResourceString(SOUNDMARK_NEW_PREFIX+spelling);
	}

	public static void addSoundmark(String spelling,String soundmark){
		RedBookRedisManager.setResourceString(SOUNDMARK_NEW_PREFIX+spelling, soundmark);
	}

	public static void deleteSoundmark(String spelling){
		RedBookRedisManager.delResourceBean(SOUNDMARK_NEW_PREFIX+spelling);
	}

}
