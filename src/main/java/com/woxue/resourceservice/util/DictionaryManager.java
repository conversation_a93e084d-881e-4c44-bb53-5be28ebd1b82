package com.woxue.resourceservice.util;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.DictionaryBean;
import com.woxue.common.util.UtilControl;
import com.woxue.resourceservice.dao.IDictionaryDao;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DictionaryManager {

	private static final String DICTIONARY_PREFIX = "dictionary:spelling:";
	private static final String DICTIONARY_QUEUE = "dictionary:queue";
	private static final ExecutorService executorService = Executors.newSingleThreadExecutor();

	public static DictionaryBean getDictionary(String spelling) {
		String sanitizedSpelling = sanitizeSpelling(spelling);
		DictionaryBean dictionary = (DictionaryBean) RedBookRedisManager.getResourceBean(DICTIONARY_PREFIX + sanitizedSpelling);
		if (dictionary == null) {
			IDictionaryDao dictionaryDao = SpringActionSupport.getSpringBean("dictionaryDao", null, IDictionaryDao.class);
			dictionary = dictionaryDao.getDictionary(spelling.toLowerCase());
			if (dictionary == null) {
				// 使用 Redis 队列来异步抓取数据
				try (Jedis jedis = RedBookRedisManager.getPool().getResource()) {
					jedis.rpush(DICTIONARY_QUEUE, spelling);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				dictionary.setSoundFile(UtilControl.getWordSoundUrl(dictionary.getSpelling(), null));
				RedBookRedisManager.setResourceBean(DICTIONARY_PREFIX + sanitizedSpelling, dictionary);
			}
		}
		return dictionary;
	}


	public static void processDictionaryQueue() {
		executorService.submit(() -> {
			try (Jedis jedis = RedBookRedisManager.getPool().getResource()) {
				while (true) {
					String spelling = jedis.lpop(DICTIONARY_QUEUE);
					if (spelling != null) {
						String sanitizedSpelling = sanitizeSpelling(spelling);
						if (RedBookRedisManager.getResourceBean(DICTIONARY_PREFIX + sanitizedSpelling) != null){
							continue;
						}
						IDictionaryDao dictionaryDao = SpringActionSupport.getSpringBean("dictionaryDao", null, IDictionaryDao.class);
						try {
							DictionaryBean fetchedDictionary = getURLInfo("http://www.dict.cn/" + sanitizeWordForUrl(spelling), "utf-8");
							if (fetchedDictionary != null && fetchedDictionary.getSpelling() != null && !fetchedDictionary.getSpelling().isEmpty()) {
								fetchedDictionary.setSoundFile(UtilControl.getWordSoundUrl(fetchedDictionary.getSpelling(), null));
								dictionaryDao.insertDictionary(fetchedDictionary);
								RedBookRedisManager.setResourceBean(DICTIONARY_PREFIX + sanitizedSpelling, fetchedDictionary);
							} else {
								// 如果获取失败，将其标记为未找到，下次可以重新尝试获取
								RedBookRedisManager.setResourceBean(DICTIONARY_PREFIX + sanitizedSpelling, new DictionaryBean());
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					} else {
						// 如果队列中没有任务，等待一段时间再继续检查
						Thread.sleep(1000);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		});
	}

	private static String sanitizeSpelling(String spelling) {
		return spelling.replaceAll("[ .?'\\\\]", "_");
	}

	private static String sanitizeWordForUrl(String word) {
		return word.replaceAll(" ", "%20").replaceAll("\\.", "%2E").replaceAll("'", "&#39;");
	}


	public static void reloadDictionaryList(){
		IDictionaryDao dictionaryDao = SpringActionSupport.getSpringBean("dictionaryDao", null, IDictionaryDao.class);
		List<String> listWord = dictionaryDao.getWordSpellingList();//获取单词列表集合
		List<String> listDict = dictionaryDao.getSpellingList();//获取词典列表集合
		List<String> listWordNotlistDict =   getDiffrent5(listWord,listDict);//取出两个list集合中不相等的元素
		String wordName;
		List<String> hasProblemList = new ArrayList<String>();
		for (int i=0;i<listWordNotlistDict.size();i++) {
			try {
				if(listWordNotlistDict.get(i).indexOf(" ")!=-1&&listWordNotlistDict.get(i).indexOf(".")!=-1){
					wordName = listWordNotlistDict.get(i).replaceAll(" ", "%20").replaceAll(".", "_2E");
				}else if(listWordNotlistDict.get(i).indexOf(" ")!=-1){
					wordName = listWordNotlistDict.get(i).replaceAll(" ", "%20");
				}else if(listWordNotlistDict.get(i).indexOf(".")!=-1){
					wordName = listWordNotlistDict.get(i).replaceAll(".", "_2E");
				}else if(listWordNotlistDict.get(i).indexOf("'")!=-1){
					wordName = listWordNotlistDict.get(i).replaceAll("'", "&#39;");
				}else{
					wordName = listWordNotlistDict.get(i);
				}
				DictionaryBean dictionaryBean = getURLInfo("http://www.dict.cn/"+wordName.toLowerCase(),"utf-8");
				if(dictionaryBean.getSpelling()!=""&&dictionaryBean.getSpelling()!=null){
					System.out.println((i+1)+"/"+listWordNotlistDict.size()+"   "+dictionaryBean.getSpelling()+"  ");
					try {
						dictionaryDao.insertDictionary(dictionaryBean);
					} catch (Exception e) {
						hasProblemList.add(dictionaryBean.getSpelling());
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		System.out.println(hasProblemList);
	}
	
	private static List<String> getDiffrent5(List<String> list1, List<String> list2) {
		List<String> diff = new ArrayList<String>();
		List<String> maxList = list1;
		List<String> minList = list2;
		if (list2.size() > list1.size()) {
			maxList = list2;
			minList = list1;
		}
		// 将List中的数据存到Map中
		Map<String, Integer> maxMap = new HashMap<String, Integer>(maxList.size());
		for (String string : maxList) {
			maxMap.put(string, 1);
		}
		// max:1,2,3,4,5
		// min:2,4,6,8,10
 
		// 循环minList中的值，标记 maxMap中 相同的 数据2
		for (String string : minList) {
			// 相同的
			if (maxMap.get(string) != null) {
				maxMap.put(string, 2);
				continue;
			}
			// 不相等的
			diff.add(string);
		}
		// 循环maxMap
		for (Map.Entry<String, Integer> entry : maxMap.entrySet()) {
			if (entry.getValue() == 1) {
				diff.add(entry.getKey());
			}
		}
		return diff;
	}


	public static DictionaryBean getURLInfo(String urlInfo,String charset) throws Exception {
		//读取目的网页URL地址，获取网页源码
		URL url = new URL(urlInfo);
		HttpURLConnection httpUrl = (HttpURLConnection)url.openConnection();
		InputStream is = httpUrl.getInputStream();
		BufferedReader br = new BufferedReader(new InputStreamReader(is,"utf-8"));
		StringBuilder sb = new StringBuilder();
		String line;
		while ((line = br.readLine()) != null) {
			//这里是对链接进行处理
			//line = line.replaceAll("</?a[^>]*>", "");
			//这里是对样式进行处理
			//line = line.replaceAll("<(\\w+)[^>]*>", "<$1>");
			sb.append(line);
		}
		is.close();
		br.close();
		//获得网页源码
		return getDataStructure(sb.toString().trim());
	}
	static Pattern spellingInfo = Pattern.compile("<h1[(.*?)]>(.*?)</h1>", Pattern.DOTALL);
	static Pattern meaningInfo = Pattern.compile("<ul class=\"dict-basic-ul\">(.*?)</ul>", Pattern.DOTALL);
	private static DictionaryBean getDataStructure(String str) {
		//运用正则表达式对获取的网页源码进行数据匹配，提取我们所要的数据，在以后的过程中，我们可以采用httpclient+jsoup,
		List<String> list = match(str,"h1","class","keyword");
		//System.out.println("拼写"+list);
		List<String> listStars = matchStars(str,"a","class","level","class");
		//System.out.println("星星"+listStars);
		List<String> meaninglist = matchMeaning(str);
		//System.out.println("释义"+meaninglist);
		List<String> meaningDetaillist = matchDetailMeaning(str);
		//System.out.println("详细释义"+meaningDetaillist);
		List<String> meaningDuallist = matchDualMeaning(str);
		//System.out.println("双解释义"+meaningDuallist);
		List<String> meaningSentlist = matchSent(str);
		//System.out.println("例句"+meaningSentlist);
		List<String> meaningEssList = matchEss(str);
		//System.out.println("词语用法"+meaningEssList);
		List<String> meaningNfoList = matchNfo(str);
		//System.out.println("近反义词"+meaningNfoList);
		List<String> meaningNwdList = matchNwd(str);
		//System.out.println("临近单词"+meaningNwdList);
		List<String> phoneticList = matchPhonetic(str);
		//System.out.println("音标:"+phoneticList);
		List<String> shapeList = matchShape(str);
		//System.out.println("词形变化"+shapeList);
		List<String> EtmList = matchEtm(str);
		//System.out.println("词源解说"+EtmList);
		List<String> comnList = matchComn(str);
		//System.out.println("常见错误"+comnList);
		List<String> discrimList = matchDiscrim(str);
		//System.out.println("词义辨析"+discrimList);
		List<String> authList = matchAuth(str);
		//System.out.println("经典引文"+authList);
		List<String> collList = matchColl(str);
		//System.out.println("词汇搭配"+collList);
		List<String> phraseList =  matchPhrase(str);
		//System.out.println("常见短语"+phraseList);
		List<String> pattList = matchPatt(str);
		//System.out.println("常见句型"+pattList);
		List<String> enMeaningList = matchEnMeaning(str);
		//System.out.println("匹配英英释义:"+enMeaningList);

		DictionaryBean dictionaryBean = new DictionaryBean();
		dictionaryBean.setSpelling(list.size()>0?list.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setStars(listStars.size()>0?listStars.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setPhonetic(phoneticList.size()>0?phoneticList.get(0):"");
		dictionaryBean.setMeaning(meaninglist.size()>0?meaninglist.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setMeaningDetail(meaningDetaillist.size()>0?meaningDetaillist.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setMeaningDual(meaningDuallist.size()>0?meaningDuallist.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setMeaningEn(enMeaningList.size()>0?enMeaningList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setSent(meaningSentlist.size()>0?meaningSentlist.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setSentPatt(pattList.size()>0?pattList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setSentPhrase(phraseList.size()>0?phraseList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setSentColl(collList.size()>0?collList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setSentAuth(authList.size()>0?authList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setLearnEss(meaningEssList.size()>0?meaningEssList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setLearnDiscrim(discrimList.size()>0?discrimList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setLearnComn(comnList.size()>0?comnList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setLearnEtm(EtmList.size()>0?EtmList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setRelNfo(meaningNfoList.size()>0?meaningNfoList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setRelNwd(meaningNwdList.size()>0?meaningNwdList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		dictionaryBean.setShape(shapeList.size()>0?shapeList.toString().replaceAll("\\[", "").replaceAll("\\]", ""):"");
		return dictionaryBean;
	}

	//匹配拼写
	public static List<String> match(String source, String element, String attr, String attrValue) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + element + "[^<>]*?\\s" + attr + "=['\"]?"+attrValue+"['\"]?\\s.*?>(.*?)</"+element+">";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			result.add(r);
		}
		return result;
	}
	//匹配星星
	public static List<String> matchStars(String source, String element, String attr, String attrValue,String attr1){
		List<String> result = new ArrayList<String>();
		//String reg = "<" + element + "[^<>]*?\\s" + attr + "=['\"]?"+attrValue+"['\"]?\\s.*?>(.*?)</"+element+">";
		String reg = "<"+element +"[^<>]*?\\s"+attr+"=(.*?)\\s.*?"+attrValue+"=(.*?)[^<>]\\s.*?"+"[^<>]*?\\s"+attr1+"=(.*?)\\s.*?></"+element+">";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(3);
			result.add(r);
		}
		return result;
	}

	//匹配释义
	public static List<String> matchMeaning(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "ul" + "[^<>]*?\\s" + "class" + "=['\"]?"+"dict-basic-ul"+"['\"]>(.*?)</"+"ul"+">";
		String cixingreg = "<span>(.*?)</span>";
		String ciyireg = "<strong>(.*?)</strong>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			String[] liList = r.split("</li>");
			for(int i=0;i<liList.length-2;i++){
				Matcher n = Pattern.compile(cixingreg).matcher(liList[i]);
				Matcher o = Pattern.compile(ciyireg).matcher(liList[i]);
				if(n.find()&&o.find()){
					result.add(n.group(1)+o.group(1)+"@");
				}
			}
		}
		if(result.size()<=0){
			reg = "<div class=\"basic clearfix\">(.*?)</div>";
			m = Pattern.compile(reg).matcher(source);
			while (m.find()) {
				String r = m.group(1);
				String[] liList = r.split("</li>");
				for(int i=0;i<liList.length;i++){
					Matcher o = Pattern.compile(ciyireg).matcher(liList[i]);
					if(o.find()){
						result.add(o.group(1)+"@");
					}
				}
			}
			if(result.size()<=0){
				reg = "<div class=\"layout tr\">(.*?)</div>";
				m = Pattern.compile(reg).matcher(source);
				String lireg = "<li>(.*?)</li>";
				while (m.find()) {
					String r = m.group(1);
					Matcher o = Pattern.compile(lireg).matcher(r);
					while (o.find()) {
						result.add(o.group(1)+"@");
					}
				}
			}
		}
		return result;
	}

	/**
	 * 匹配音标
	 * @param source
	 * @return
	 */
	public static List<String> matchPhonetic(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]phonetic['\"][^<>]*>(.*?)</div>";
		String phonetic_reg = "<span>(.*?)</span>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()){
			List<String> liList = new ArrayList<String>();
			String[] r = m.group(1).split("</span>");
			for (String s : r) {
				liList.add(s+"</span>");
			}
			String phoneticStr ="";
			for(int i=0;i<liList.size()-1;i++){
				Matcher n = Pattern.compile(phonetic_reg).matcher(liList.get(i));
				if(n.find()){
					String yinbiao = n.group(1).replaceAll("\\s|\n|\t|\r", "");
					yinbiao = n.group(1).replaceAll("<[^<>]*>", "");
					yinbiao = yinbiao.replaceAll("\\s*|\t|\r|\n", "");
					phoneticStr+=yinbiao+"@";
				}
			}
			result.add(phoneticStr);
		}
		return result;
	}
	/**
	 * 详细释义
	 * @param source
	 * @return
	 */
	public static List<String> matchDetailMeaning(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "div" + "[^<>]*?\\s" + "class" + "=['\"]?"+"layout detail"+"['\"]>(.*?)</"+"div"+">";
		String cixingreg = "<span>(.*?)</span>";
		String ciyireg = "<ol[^<>]*>(.*?)</ol>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixingreg).matcher(r);
			Matcher o = Pattern.compile(ciyireg).matcher(r);
			while(n.find()){
				String cixing = n.group(1).replaceAll("\t|\r|\n", "");
				cixing = cixing.replaceAll("<bdo>", "");
				cixing = cixing.replaceAll("</bdo>", "");
				String ciyi = "";
				if(o.find()){
					ciyi = o.group(1);
					ciyi = ciyi.replaceAll("\t|\r|\n", "");
					ciyi = ciyi.replaceAll("<li>", "");
					ciyi = ciyi.replaceAll("</li>", "@");
				}
				result.add("$"+cixing+"$"+ciyi);
			}
		}
		return result;
	}
	/**
	 * 双解释义
	 * @param source
	 * @return
	 */
	public static List<String> matchDualMeaning(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "div" + "[^<>]*?\\s" + "class" + "=['\"]?"+"layout dual"+"['\"]>(.*?)</"+"div"+">";
		String cixingreg = "<span>(.*?)</span>";
		String ciyireg = "<ol[^<>]*>(.*?)</ol>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixingreg).matcher(r);
			Matcher o = Pattern.compile(ciyireg).matcher(r);
			while(n.find()){
				String cixing = n.group(1).replaceAll("\t|\r|\n", "");
				cixing = cixing.replaceAll("<bdo>", "");
				cixing = cixing.replaceAll("</bdo>", "");
				String ciyi = "";
				if(o.find()){
					ciyi = o.group(1);
					ciyi = ciyi.replaceAll("\t|\r|\n", "");
					ciyi = ciyi.replaceAll("<li>", "");
					ciyi = ciyi.replaceAll("</li>", "@");
				}
				result.add(cixing+ciyi);
			}
		}
		return result;
	}

	//匹配用例
	public static List<String> matchSent(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "div" + "[^<>]*?\\s" + "class" + "=['\"]?"+"layout sort"+"['\"]>(.*?)</"+"h3"+">";
		String cixingreg = "<b>(.*?)</b>";
		String ciyireg = "<ol[^<>]*>(.*?)</ol>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixingreg).matcher(r);
			Matcher o = Pattern.compile(ciyireg).matcher(r);
			while(n.find()){
				String cixing = n.group(1).replaceAll("\t|\r|\n", "");
				String ciyi = "";
				if(o.find()){
					ciyi = o.group(1);
					ciyi = ciyi.replaceAll("\t|\r|\n", "");
					ciyi = ciyi.replaceAll("<li>", "");
					ciyi = ciyi.replaceAll("</li>", "@");
				}
				result.add('$'+cixing+'$'+ciyi);
			}
		}
		return result;
	}

	//匹配讲解
	public static List<String> matchEss(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "div" + "[^<>]*?\\s" + "class" + "=['\"]?"+"layout ess"+"['\"]>(.*?)</"+"div"+">";
		String cixingreg = "<span>(.*?)</span>";
		String ciyireg = "<ol[^<>]*>(.*?)</ol>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixingreg).matcher(r);
			Matcher o = Pattern.compile(ciyireg).matcher(r);
			while(n.find()){
				String cixing = n.group(1).replaceAll("\\s*|\t|\r|\n", "");
				cixing = cixing.replaceAll("<bdo>", "");
				cixing = cixing.replaceAll("</bdo>", "");
				String ciyi = "";
				if(o.find()){
					ciyi = o.group(1);
					ciyi = ciyi.replaceAll("\\s*|\t|\r|\n", "");
					ciyi = ciyi.replaceAll("<li>", "");
					ciyi = ciyi.replaceAll("</li>", "@");
					ciyi = ciyi.replaceAll("<[^<>]*>", "");
				}
				result.add(cixing + '{' + ciyi + '}');
			}
		}
		return result;
	}

	//匹配近义词反义词
	public static List<String> matchNfo(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "div" + "[^<>]*?\\s" + "class" + "=['\"]?"+"layout nfo"+"['\"]>(.*?)</"+"h3"+">";
		String cixingreg = "<div>(.*?)</div>";
		String ciyireg = "<ul[^<>]*>(.*?)</ul>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixingreg).matcher(r);
			Matcher o = Pattern.compile(ciyireg).matcher(r);
			while(n.find()){
				String cixing = n.group(1).replaceAll("\t|\r|\n", "");
				String ciyi = "";
				if(o.find()){
					ciyi = o.group(1);
					ciyi = ciyi.replaceAll("\t|\r|\n", "");
					String spellreg = "<a[^<>]*>(.*?)</a>";
					Matcher p = Pattern.compile(spellreg).matcher(ciyi);
					String spellStr = "";
					while(p.find()){
						spellStr += p.group(1)+"@";
					}
					result.add(cixing+spellStr);
				}
			}
		}
		return result;
	}

	//匹配临近单词
	public static List<String> matchNwd(String source) {
		List<String> result = new ArrayList<String>();
		String reg = "<" + "div" + "[^<>]*?\\s" + "class" + "=['\"]?"+"layout nwd"+"['\"][^<>]*>(.*?)</"+"div"+">";
		String cixingreg = "<a[^<>]*>(.*?)</a>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			r = r.replaceAll("\t|\r|\n", "");
			Matcher n = Pattern.compile(cixingreg).matcher(r);
			String spellStr = "";
			while(n.find()){
				spellStr += n.group(1)+"@";
			}
			result.add(spellStr);
		}
		return result;
	}

	/**
	 * 词形变化
	 * @param source
	 * @return
	 */
	public static List<String> matchShape(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]shape['\"][^<>]*>(.*?)</div>";
		String shape_cixing_reg = "<label>(.*?)</label>";
		String shape_danci_reg = "<a[^<>]*>(.*?)</a>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(shape_cixing_reg).matcher(r);
			String shapeStr = "";
			while(n.find()){
				shapeStr+= n.group(1)+"$";
			}
			shapeStr+="@";
			Matcher d = Pattern.compile(shape_danci_reg).matcher(r);
			while(d.find()){
				String d1 = d.group(1);
				d1 = d1.replaceAll("\t|\r|\n", "");
				shapeStr+= d1+"$";
			}
			result.add(shapeStr);
		}

		return result;
	}
	/**
	 * 词源解说
	 * @return
	 */
	public static List<String> matchEtm(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout etm['\"][^<>]*>(.*?)</div>";
		String cixing_reg = "<li>(.*?)</li>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String etmStr = "";
			while(n.find()){
				String cixing = n.group(1).replaceAll("\t|\r|\n", "");
				etmStr+= cixing;
			}
			result.add(etmStr);
		}
		return result;
	}
	/**
	 * 常见错误
	 * @param source
	 * @return
	 */
	public static List<String> matchComn(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout comn['\"][^<>]*>(.*?)</div>";
		String cixing_reg = "<span>(.*?)</span>";
		String ciyi_reg = "<ol[^<>]*>(.*?)</ol>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String comnStr = "";
			while(n.find()){
				String n1 =  n.group(1).replaceAll("\t|\r|\n", "");
				comnStr+=n1.replaceAll("<[^<>]*>", "");
			}
			Matcher c = Pattern.compile(ciyi_reg).matcher(r);
			String cyStr = "";
			while (c.find()) {
				String c1 =  c.group(1).replaceAll("\t|\r|\n", "");
				String c2 = c1.replaceAll("<p>","@");
				cyStr+=c2.replaceAll("<[^<>]*>", "");
			}
			result.add(comnStr+"{"+cyStr+"}");
		}
		return result;
	}

	/**
	 * 词义辨析
	 * @param source
	 * @return
	 */
	public static List<String> matchDiscrim(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout discrim['\"][^<>]*>(.*?)<h3";
		String cixing_reg = "<span>(.*?)</span>";
		String ciyi_reg = "</span>(.*?)<(?:span|/div)>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String cixingStr = "";
			while(n.find()){
				String cx = n.group(1).replaceAll("\t|\r|\n", "");
				cx = cx.replaceAll("<[^<>]*>", "");
				cixingStr+=cx;
			}
			String ciyiStr = "";
			Matcher cy = Pattern.compile(ciyi_reg).matcher(r);
			while (cy.find()) {
				String cy1 = cy.group(1).replaceAll("\t|\r|\n", "");
				cy1 = cy1.replaceAll("</dl>", "@");
				cy1 = cy1.replaceAll("</li>", ":");
				cy1 = cy1.replaceAll("</b>", ":");
				cy1 = cy1.replaceAll("<[^<>]*>", "");
				ciyiStr+=cy1;
			}
			result.add(cixingStr + '{' + ciyiStr + '}');
		}

		return result;
	}
	/**
	 * 经典引文
	 * @param source
	 * @return
	 */
	public static List<String> matchAuth(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout auth['\"][^<>]*>(.*?)</div>";
		String cixing_reg = "<li>(.*?)</li>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String cixingStr = "";
			while (n.find()) {
				String cixing = n.group().replaceAll("\t|\r|\n", "");
				cixing = cixing.replaceAll("<[^<>]*>", "");
				cixingStr+= cixing;
			}
			result.add(cixingStr);
		}
		return result;
	}
	/**
	 * 词汇搭配
	 * @param source
	 * @return
	 */
	public static List<String> matchColl(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout coll['\"][^<>]*>(.*?)<h3";
		String cixing_reg ="<div>[\n\t\r]*<b>(.*?)</b>";
		String ciyi_reg = "</div>(.*?)</?div>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String cixingStr = "";
			while (n.find()) {
				String cixing = n.group().replaceAll("\t|\r|\n", "");
				cixing = cixing.replaceAll("<div><b>", "");
				cixing = cixing.replaceAll("</b>", "");
				cixingStr+= cixing;
			}
			Matcher c = Pattern.compile(ciyi_reg).matcher(r);
			String ciyiStr = "";
			while (c.find()) {
				String ciyi = c.group(1).replaceAll("\t|\r|\n", "");
				ciyi = ciyi.replaceAll("<ul>", "{");
				ciyi = ciyi.replaceAll("</ul>", "}@");
				ciyi = ciyi.replaceAll("</a>", ":");
				ciyi = ciyi.replaceAll("</li>","、");
				ciyi = ciyi.replaceAll("<[^<>]*>","");
				ciyiStr+=ciyi;
			}
			result.add(cixingStr + '@' + ciyiStr + '}');
		}
		return result;
	}
	/**
	 *  常用短语
	 * @param source
	 * @return
	 */
	public static List<String> matchPhrase(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout phrase['\"][^<>]*>(.*?)</h3>";
		String cixing_reg = "<div>[\n\t\r]*<b>(.*?)</b>";
		String ciyi_reg = "</div>(.*?)</?div>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String cixingStr = "";
			while (n.find()) {
				String cixing = n.group().replaceAll("\t|\r|\n", "");
				cixing = cixing.replaceAll("<div><b>", "");
				cixing = cixing.replaceAll("</b>", "");
				cixingStr+= cixing;
			}
			Matcher c = Pattern.compile(ciyi_reg).matcher(r);
			String ciyiStr = "";
			while (c.find()) {
				String ciyi = c.group(1).replaceAll("\t|\r|\n", "");
				ciyi = ciyi.replaceAll("</dl>", "@");
				ciyi = ciyi.replaceAll("</dt>", ":");
				ciyi = ciyi.replaceAll("<ol><dd>", "");
				ciyi = ciyi.replaceAll("</ol></dd>", "");
				ciyi = ciyi.replaceAll("<dl><dt>", "");
				ciyi = ciyi.replaceAll("<dd><ol>", "");
				//ciyi = ciyi.replaceAll("<[^<>]*>","");
				ciyiStr+=ciyi;
			}
			result.add(cixingStr + '{' + ciyiStr + '}');
		}
		return result;
	}

	/**
	 * 常见句型
	 * @param source
	 * @return
	 */
	public static List<String> matchPatt(String source){
		List<String> result = new ArrayList<String>();
		String patt_reg = "<div[^<>]*?\\sclass=['\"]?layout patt['\"][^<>]*>(.*?)</h3>";
		String patt_cixing_reg = "\\<div>[\n\t\r]*<b>(.*?)</b>";
		String patt_ciyi_reg = "v>(.*?)</di";
		String patt_use = "<b>(.*?)<ol";
		String patt_sent = "<ol[^<>]*>(.*?)</ol>";
		//用作形容词(adj.){用作定语～+ n./pron.:There is a big bell in the room.屋里有口大钟。@Their house is a big one.他们的房子很大。@Tourism has been Thailand's biggest earner of foreign exchange since 1982.自从1982年以来,旅游业一直是泰国最大的外汇来源。@This is a piece of big news.这是一条重要新闻。@The big question is under discussion.那个重要的问题正在讨论之中。@His biggest programme was to design equipment that would fit a number of breeds.他最大的规划是设计一种可适用于不同品种动物的装置。@The young man had big ideas.这个年轻人有抱负。@“Don't cry. You are a big boy now,”the mother said to her son.“不要哭,你已经是大孩子了,”母亲对她的儿子说道。@、用作表语S+be+～:This mouse is very big.这只老鼠很大。@Baggy sweaters are big this year.今年宽松的毛衣大受欢迎。@We have reason to believe that our dictionary will go big among the English teachers.我们有理由相信我们的词典会受到英语教师的欢迎。@He is big and busy.他一直很忙。@、S+be+～+ prep .-phrase:The child is big for his age.就这孩子的年龄来说,他的个儿算是大的。@That's very big of you.你真宽宏大量。@、S+be+～+to -v:Cage is big enough to go to school by herself.凯奇已经长大,可以自己去上学了。@、It is/was+～+of sb+to -v:It was big of you to do that.你这样做度量真大。@、}
		Matcher m = Pattern.compile(patt_reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(patt_cixing_reg).matcher(r);
			String cixingStr = "";
			while (n.find()) {
				cixingStr = n.group(1).replaceAll(patt_cixing_reg,"");
				cixingStr = cixingStr.replaceAll("\t|\r|\n", "");
			}
			String ciyiStr = "";
			Matcher ciyi = Pattern.compile(patt_ciyi_reg).matcher(r);
			while (ciyi.find()) {
				ciyiStr = ciyi.group(1).replaceAll("\t|\r|\n", "");
			}
			String useStr = "";
			Matcher use = Pattern.compile(patt_use).matcher(r);
			while (use.find()) {
				useStr = use.group(1).replaceAll("\t|\r|\n","");
				useStr = useStr.replaceAll("<[^<>]*>", "");
				useStr+=useStr+':';
			}
			String sentStr = "";
			Matcher sent = Pattern.compile(patt_sent).matcher(r);
			while (use.find()) {
				sentStr = sent.group(1).replaceAll("\t|\r|\n","");
				sentStr = sentStr.replaceAll("</li>", "@");
				sentStr = sentStr.replaceAll("<[^<>]*>", "");
				sentStr+=sentStr+'、';
			}
			result.add(cixingStr+'{' + useStr + sentStr + '}');

		}
		return result;
	}

	public static List<String> matchEnMeaning(String source){
		List<String> result = new ArrayList<String>();
		String reg = "<div[^<>]*?\\sclass=['\"]?layout en['\"][^<>]*>(.*?)</div>";
		String cixing_reg = "<span>(.*?)</span>";
		String ciyi_reg = "<ol[^<>]*>(.*?)</ol>";
		Matcher m = Pattern.compile(reg).matcher(source);
		while (m.find()) {
			String r = m.group(1);
			Matcher n = Pattern.compile(cixing_reg).matcher(r);
			String cixingStr = "";
			while (n.find()) {
				String cixing = n.group(1);
				cixingStr+= cixing;
			}
			Matcher cy = Pattern.compile(ciyi_reg).matcher(r);
			String ciyiStr = "";
			while (cy.find()) {
				String ciyi = cy.group(1).replaceAll("\t|\r|\n", "");
				ciyi = ciyi.replaceAll("<li>", "");
				ciyi = ciyi.replaceAll("</li>", "@");
				ciyi = ciyi.replaceAll("<p>", "{");
				ciyi = ciyi.replaceAll("</p>", "}");
				ciyiStr+= ciyi;
			}
			result.add(cixingStr + ciyiStr);
		}
		return result;

	}


	public static void deleteDictionary(String spelling) {
		if (StringUtils.isNotEmpty(spelling)){
			String sanitizedSpelling = sanitizeSpelling(spelling);
			RedBookRedisManager.delResourceBean(DICTIONARY_PREFIX + sanitizedSpelling);
		}
	}
}