package com.woxue.resourceservice.util;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class QuestionBankCourseManager {
	private final String VERSION_COURSE_MAP = "redbook-questionBank-course:versionAndCourse";
	private final String VERSION_MAP = "redbook-questionBank-course:vserion";
	private final String COURSE_MAP = "redbook-questionBank-course:course";
	private final String VERSION_ALL_COURSE_MAP = "redbook-questionBank-courseAll:courseAll";
	private final String UNIT_MAP = "redbook-questionBank-course:unit";
	private final String LESSON_MAP = "redbook-questionBank-course:lesson";

	/**
	 * 获取指定年级的版本教材列表
	 * @param grade
	 * @return
	 */
	public List<Map<String, Object>> getVersionAndOCourseList(Integer grade){
		Map<Integer, List<Map<String, Object>>> courseListMap = (Map<Integer, List<Map<String, Object>>>) RedisManager.getBean(VERSION_COURSE_MAP);
		return courseListMap.get(grade);
	}
	
	/**
	 * 获取指年级的版本列表
	 * @param gradePhase
	 * @return
	 */
	public List<Map<String, Object>> getVersionList(Integer gradePhase){
		Map<Integer, List<Map<String, Object>>> versionListMap = (Map<Integer, List<Map<String, Object>>>) RedisManager.getBean(VERSION_MAP);
		return versionListMap.get(gradePhase);
	}
	
	/**
	 * 获取指定版本的课程列表
	 * @param versionId
	 * @return
	 */
	public List<Map<String, Object>> getCourseList(Integer versionId,Integer gradeId){
		if (versionId==null){
			return null;
		}
		String versionIdAndGradeId = versionId.toString()+'_'+gradeId.toString();
		Map<String, List<Map<String, Object>>> courseListMap = (Map<String, List<Map<String, Object>>>) RedisManager.getBean(COURSE_MAP);
		return courseListMap.get(versionIdAndGradeId);
	}

	public List<Map<String, Object>> getAllCourseList(Integer versionId){
		if (versionId==null){
			return null;
		}
		String versionIdAndGradeId = versionId.toString();
		Map<String, List<Map<String, Object>>> courseListMap = (Map<String, List<Map<String, Object>>>) RedisManager.getBean(VERSION_ALL_COURSE_MAP);
		return courseListMap.get(versionIdAndGradeId);
	}
	
	/**
	 * 获取指定课程的单元列表
	 * @param courseId
	 * @return
	 */
	public List<Map<String, Object>> getUnitList(Integer courseId){
		Map<Integer, List<Map<String, Object>>> unitListMap = (Map<Integer, List<Map<String, Object>>>) RedisManager.getBean(UNIT_MAP);
		return unitListMap.get(courseId);
	}
	
	/**
	 * 获取指定单元的小节列表
	 * @param unitId
	 * @return
	 */
	public List<Map<String, Object>> getLessonList(Integer unitId){
		Map<Integer, List<Map<String, Object>>> lessonListMap = (Map<Integer, List<Map<String, Object>>>) RedisManager.getBean(LESSON_MAP);
		return lessonListMap.get(unitId);
	}

	/*@Override
	public void run(String... args) throws Exception {
			loadAllCourse();
	}*/
}
