package com.woxue.resourceservice.util;

import com.woxue.common.model.SystemInfoBean;


public class SystemInfoManager{
	private static final String SYSTEMINFO_PREFIX = "systeminfo:cid:";
	public static SystemInfoBean getSystemInfoBean(Integer cid){
		Object obj = RedBookRedisManager.getResourceBean(SYSTEMINFO_PREFIX+cid);
		if(null!=obj) {
			return (SystemInfoBean)obj;
		}
		return null;
	}
	
	public static boolean addSystemInfoBean(SystemInfoBean systemInfoBean){
		RedBookRedisManager.setResourceBean(SYSTEMINFO_PREFIX+systemInfoBean.getId(), systemInfoBean);
		return true;
	}
	
}
