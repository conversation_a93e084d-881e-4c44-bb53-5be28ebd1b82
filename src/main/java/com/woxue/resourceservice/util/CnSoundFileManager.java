package com.woxue.resourceservice.util;

import com.redbook.kid.common.util.TencentSpeechUtil;
import com.woxue.common.action.SpringActionSupport;
import com.woxue.resourceservice.dao.ICnSoundFileDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 中文语音文件管理工具类
 * 提供从数据库获取或使用腾讯语音服务生成语音文件的功能
 */
public class CnSoundFileManager {
    private static final Logger logger = LoggerFactory.getLogger(CnSoundFileManager.class);
    
    /**
     * 获取中文文本对应的语音文件路径
     * 如果数据库中存在，则直接返回；否则生成新的语音文件并保存到数据库
     *
     * @param cn 中文文本
     * @return 语音文件路径
     */
    public static String getCnSoundFile(String cn) {
        if (cn == null || cn.trim().isEmpty()) {
            logger.warn("获取中文语音文件失败：中文文本为空");
            return null;
        }
        
        try {
            // 清理中文文本，去除空格和换行符
            cn = cn.replaceAll("[\\s/n]", "");
            
            // 从Spring容器获取DAO对象
            ICnSoundFileDao cnSoundFileDao = SpringActionSupport.getSpringBean("cnSoundFileDao", null, ICnSoundFileDao.class);
            if (cnSoundFileDao == null) {
                logger.error("获取cnSoundFileDao Bean失败");
                // 如果无法获取DAO，回退到直接生成语音
                return TencentSpeechUtil.speechTts("/resource/speech", cn);
            }
            
            // 从数据库查询语音文件
            String soundFile = cnSoundFileDao.getSoundFileByCn(cn);
            
            // 如果数据库中没有记录，则生成新的语音文件并保存
            if (soundFile == null || soundFile.isEmpty()) {
                soundFile = TencentSpeechUtil.speechTts("/resource/speech", cn);
                if (soundFile != null && !soundFile.isEmpty()) {
                    // 保存到数据库
                    try {
                        cnSoundFileDao.saveCnSoundFile(cn, soundFile);
                        logger.info("中文语音文件已保存到数据库: {}", cn);
                    } catch (Exception e) {
                        logger.error("保存中文语音文件到数据库异常: {}", cn, e);
                    }
                }
            } else {
                logger.debug("从数据库获取中文语音文件: {}", cn);
            }
            
            return soundFile;
        } catch (Exception e) {
            logger.error("获取中文语音文件异常: {}", cn, e);
            // 发生异常时回退到直接生成语音
            return TencentSpeechUtil.speechTts("/resource/speech", cn);
        }
    }
} 