package com.woxue.resourceservice.util;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.PictureBook;
import com.woxue.common.model.redBook.PictureBookContent;
import com.woxue.common.model.redBook.PictureBookSentence;
import com.woxue.common.model.redBook.PictureBookWord;
import com.woxue.resourceservice.dao.IPictureBookDao;

import java.util.*;
import java.util.stream.Collectors;

public class PictureBookManager {

    private final static String PICTURE_BOOK_PREFIX = "redBookPicture:pictureId:";
    private final static String PICTURE_BOOK_CONTENT_PREFIX = "redBookPicture:contentId:";

    private final static String PICTURE_BOOK_CONTENT_LIST_PREFIX = "redBookPicture:list:pictureId:";

    private final static String PICTURE_BOOK_SENTENCE_LIST_PREFIX = "redBookPicture:list:sentence:";

    private final static String PICTURE_BOOK_SENTENCE_PREFIX = "redBookPicture:sentenceId:";
    private final static String PICTURE_BOOK_SENTENCE_LAST_UPDATE_PREFIX = "redBookPicture:sentence:updateDate:";
    private final static String PICTURE_BOOK_LAST_UPDATE_LIST_PREFIX = "redBookPicture:pictureBook:updateDate:";
    private final static String PICTURE_ALL_BOOK_ID_PREFIX = "redBookPicture:allId";


    public static boolean reloadPictureBook() {
        IPictureBookDao pictureBookDao = SpringActionSupport.getSpringBean("pictureBookDao", null, IPictureBookDao.class);
        RedBookRedisManager.delResourceBean(PICTURE_ALL_BOOK_ID_PREFIX);
        List<PictureBook> bookList = pictureBookDao.getAllPictureBookList();
        bookList.forEach(PictureBookManager::loadPictureBook);
        return true;
    }
    public static boolean loadPictureBook(Integer bookId){
        IPictureBookDao pictureBookDao = SpringActionSupport.getSpringBean("pictureBookDao", null, IPictureBookDao.class);
        PictureBook pictureBook = pictureBookDao.getPictureBook(bookId);
        return loadPictureBook(pictureBook);
    }
    public static boolean loadPictureBook(PictureBook book) {
        IPictureBookDao pictureBookDao = SpringActionSupport.getSpringBean("pictureBookDao", null, IPictureBookDao.class);
        List<PictureBookContent> pictureBookContentList = pictureBookDao.getPictureBookContentList(book.getId());
        //绘本单词添加
        List<PictureBookWord> wordBeanList = pictureBookDao.getWordListByPictureBookId(book.getId());
        book.setWordBeanList(wordBeanList);
        pictureBookContentList.forEach(content -> {
            List<PictureBookSentence> pictureBookSentenceList = pictureBookDao.getPictureBookSentenceList(content.getId());
            pictureBookSentenceList.forEach(sentence -> {
                if (!sentence.getSoundFile().startsWith("picture_book/")) {
                    sentence.setSoundFile(getSoundFilePath(book.getId(), sentence.getSoundFile()));
                } else {
                    sentence.setSoundFile("/" + sentence.getSoundFile());
                }
                addPictureBookSentence(sentence);
            });
            content.setSentenceList(pictureBookSentenceList);
            if (!content.getPicUrl().startsWith("picture_book/")) {
                content.setPicUrl(getImgFilePath(book.getId(), content.getPicUrl()));
            } else {
                content.setPicUrl("/" + content.getPicUrl());
            }
            addPictureBookContent(content);
        });
        book.setContentList(pictureBookContentList);
        if (book.getThumbnail() != null && !book.getThumbnail().startsWith("picture_book/")) {
            book.setThumbnail(getImgFilePath(book.getId(), book.getThumbnail()));
        } else {
            book.setThumbnail("/" + book.getThumbnail());
        }
        addPictureBook(book);
        setUpdateDate(book);
        return true;
    }

    public static boolean addPictureBook(PictureBook pictureBook) {
        RedBookRedisManager.setResourceBean((PICTURE_BOOK_PREFIX + pictureBook.getId()), pictureBook);
        List<String> idList = RedBookRedisManager.getResourceListRange(PICTURE_ALL_BOOK_ID_PREFIX, 0, -1);
        if (!idList.contains(pictureBook.getId().toString())) {
            RedBookRedisManager.addResourceToList(PICTURE_ALL_BOOK_ID_PREFIX, pictureBook.getId() + "");
        }
        return true;
    }

    public static boolean deletePictureBook(String pictureBookId) {
        RedBookRedisManager.delResourceBean(PICTURE_BOOK_PREFIX + pictureBookId);
        return true;
    }

    public static boolean addPictureBookContent(PictureBookContent pictureBookContent) {
        RedBookRedisManager.setResourceBean((PICTURE_BOOK_CONTENT_PREFIX + pictureBookContent.getId()), pictureBookContent);
        List<String> idList = RedBookRedisManager.getResourceListRange(PICTURE_BOOK_CONTENT_LIST_PREFIX + pictureBookContent.getPictureBookId(), 0, -1);
        if (!idList.contains(pictureBookContent.getId().toString())) {
            RedBookRedisManager.addResourceToList(PICTURE_BOOK_CONTENT_LIST_PREFIX + pictureBookContent.getPictureBookId(), "" + pictureBookContent.getId());
        }
        return true;
    }

    public static boolean addPictureBookSentence(PictureBookSentence pictureBookSentence) {
        RedBookRedisManager.setResourceBean((PICTURE_BOOK_SENTENCE_PREFIX + pictureBookSentence.getId()), pictureBookSentence);
        List<String> idList = RedBookRedisManager.getResourceListRange(PICTURE_BOOK_SENTENCE_LIST_PREFIX + pictureBookSentence.getContentId(), 0, -1);
        if (!idList.contains(pictureBookSentence.getId().toString())) {
            RedBookRedisManager.addResourceToList(PICTURE_BOOK_SENTENCE_LIST_PREFIX + pictureBookSentence.getContentId(), pictureBookSentence.getId() + "");
        }
        return true;
    }

    public static boolean setUpdateDate(PictureBook book) {
        RedBookRedisManager.setResourceBean(PICTURE_BOOK_LAST_UPDATE_LIST_PREFIX + book.getId(), new Date());
        return true;
    }

    public static List<Map> getUpdateDate() {
        List<String> bookListId = RedBookRedisManager.getResourceListRange(PICTURE_ALL_BOOK_ID_PREFIX, 0, -1);
        if (bookListId == null || bookListId.size() == 0) {
            return null;
        }
        return bookListId.stream().map(bookId -> {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put(bookId,RedBookRedisManager.getResourceBean(PICTURE_BOOK_LAST_UPDATE_LIST_PREFIX + bookId));
            return hashMap;
        }).collect(Collectors.toList());
    }

    public static List<PictureBook> getAllPictureBookList() {
        List<String> bookListId = RedBookRedisManager.getResourceListRange(PICTURE_ALL_BOOK_ID_PREFIX, 0, -1);
        if (bookListId == null || bookListId.size() == 0) {
            return null;
        }
        return bookListId.stream().map(bookId -> (PictureBook) RedBookRedisManager.getResourceBean(PICTURE_BOOK_PREFIX + bookId)).collect(Collectors.toList());
    }

    public static PictureBook getPictureBook(Integer pictureBookId) {
        return (PictureBook) RedBookRedisManager.getResourceBean(PICTURE_BOOK_PREFIX + pictureBookId);
    }

    public static PictureBookContent getPictureBookContent(Integer pictureBookContentId) {
        return (PictureBookContent) RedBookRedisManager.getResourceBean(PICTURE_BOOK_CONTENT_PREFIX + pictureBookContentId);
    }

    public static List<PictureBookContent> getPictureBookContentList(Integer pictureBookId) {
        List<String> contentListId = RedBookRedisManager.getResourceListRange(PICTURE_BOOK_CONTENT_LIST_PREFIX + pictureBookId, 0, -1);
        if (contentListId == null || contentListId.size() == 0) {
            return null;
        }
        return contentListId.stream().map(contentId -> (PictureBookContent) RedBookRedisManager.getResourceBean(PICTURE_BOOK_CONTENT_PREFIX + contentId)).collect(Collectors.toList());
    }

    public static PictureBookSentence getPictureBookSentence(Integer pictureBookSentenceId) {
        return (PictureBookSentence) RedBookRedisManager.getResourceBean(PICTURE_BOOK_SENTENCE_PREFIX + pictureBookSentenceId);
    }

    public static List<PictureBookSentence> getPictureBookSentenceList(Integer pictureBookContentId) {
        List<String> sentenceListId = RedBookRedisManager.getResourceListRange(PICTURE_BOOK_SENTENCE_LIST_PREFIX + pictureBookContentId, 0, -1);
        if (sentenceListId == null || sentenceListId.size() == 0) {
            return null;
        }
        return sentenceListId.stream().map(sentenceId -> (PictureBookSentence) RedBookRedisManager.getResourceBean(PICTURE_BOOK_SENTENCE_PREFIX + sentenceId)).collect(Collectors.toList());
    }


    public static String getSoundFilePath(Integer sentenceId, String fileName) {
        return "/picture_book/" + sentenceId + "/mp3/" + fileName;
    }

    public static String getImgFilePath(Integer pictureBookId, String fileName) {
        return "/picture_book/" + pictureBookId + "/img/" + fileName;
    }
}
