package com.woxue.resourceservice.util;

import com.github.pagehelper.StringUtil;
import com.redbook.kid.common.model.*;
import com.redbook.kid.common.util.TencentSpeechUtil;
import com.woxue.common.action.SpringActionSupport;
import com.woxue.resourcemanage.service.ResourceKidService;
import io.jsonwebtoken.lang.Collections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class KidResourceManager {
    private static final Logger logger = LoggerFactory.getLogger(KidResourceManager.class);

    //少儿缓存资源
    public static final String KID_RESOURCE_COURSE_CACHE_KEY = "kidResourceCourse:";
    //场景缓存资源
    public static final String KID_RESOURCE_SCENE_CACHE_KEY = "kidResourceScene:";
    //场景列表缓存资源
    public static final String KID_RESOURCE_SCENE_LIST_CACHE_KEY = "kidResourceSceneList:";
    //单词缓存资源
    public static final String KID_RESOURCE_WORD_CACHE_KEY = "kidResourceWord:";
    //课程列表缓存资源
    public static final String KID_RESOURCE_COURSE_LIST_CACHE_KEY = "kidResourceCourseList:";
    //字母单元缓存资源
    public static final String KID_RESOURCE_LETTER_UNIT_CACHE_KEY = "kidResourceLetterUnit:";
    //单元缓存资源列表
    public static final String KID_RESOURCE_UNIT_LIST_CACHE_KEY = "kidResourceUnitList:";
    //单元缓存资源
    public static final String KID_RESOURCE_UNIT_CACHE_KEY = "kidResourceUnit:";

    /**
     * 发布少儿资源
     *
     * @param courseId 课程ID
     */
    public static void publishKidResource(Integer courseId) {
        if (courseId == null) {
            logger.error("发布少儿资源失败：课程ID为空");
            System.out.println("发布少儿资源失败：课程ID为空");
            return;
        }

        try {
            ResourceKidService resourceKidService = SpringActionSupport.getSpringBean("resourceKidService", null, ResourceKidService.class);
            ResourceKidCourse course = resourceKidService.getCourseById(courseId);

            if (course == null) {
                logger.warn("发布少儿资源失败：未找到ID为{}的课程", courseId);
                System.out.println("发布少儿资源失败：未找到ID为" + courseId + "的课程");
                return;
            }

            // 更新课程分支信息
            course.setBranch(course.getBranch() + 1);
            resourceKidService.updateCourse(course);

            // 缓存课程信息
            // 检查列表中是否已存在该课程ID，避免重复添加
            List<String> courseIdList = KidRedisManager.getResourceListRange(KID_RESOURCE_COURSE_LIST_CACHE_KEY + course.getType(), 0, -1);
            if (courseIdList == null || !courseIdList.contains(courseId.toString())) {
                KidRedisManager.addResourceToList(KID_RESOURCE_COURSE_LIST_CACHE_KEY + course.getType(), courseId.toString());
                logger.info("添加课程ID {}到类型{}的列表中", courseId, course.getType());
                System.out.println("添加课程ID " + courseId + "到类型" + course.getType() + "的列表中");
            } else {
                logger.info("课程ID {}已存在于类型{}的列表中，跳过添加", courseId, course.getType());
            }
            KidRedisManager.setResourceBean(KID_RESOURCE_COURSE_CACHE_KEY + courseId, course);

            // 根据课程类型处理不同资源
            if (KidConstant.CourseType.SYNC.equals(course.getType())) {
                publishSyncCourseResources(resourceKidService, course, courseId);
            } else {
                publishLetterCourseResources(resourceKidService, courseId);
            }

            logger.info("成功发布ID为{}的少儿资源", courseId);
        } catch (Exception e) {
            logger.error("发布少儿资源异常，课程ID: {}", courseId, e);
            System.out.println("发布少儿资源异常，课程ID: " + courseId + "，错误信息：" + e.getMessage());
            System.out.println(e.getStackTrace());
        }
    }

    /**
     * 发布同步课程资源
     */
    private static void publishSyncCourseResources(ResourceKidService resourceKidService, ResourceKidCourse course, Integer courseId) {
        List<ResourceKidScene> scenes = resourceKidService.getSceneByCourseId(courseId);
        if (Collections.isEmpty(scenes)) {
            logger.warn("课程{}没有关联的场景", courseId);
            return;
        }

        for (ResourceKidScene scene : scenes) {
            // 处理单元资源
            if (scene.getUnitList() != null) {
                for (ResourceKidUnit unit : scene.getUnitList()) {
                    // 检查单元列表中是否已存在该单元ID，避免重复添加
                    List<String> unitIdList = KidRedisManager.getResourceListRange(KID_RESOURCE_UNIT_LIST_CACHE_KEY + course.getId(), 0, -1);
                    if (unitIdList == null || !unitIdList.contains(unit.getId() + "")) {
                        KidRedisManager.addResourceToList(KID_RESOURCE_UNIT_LIST_CACHE_KEY + course.getId(), unit.getId() + "");
                        logger.debug("添加单元ID {}到课程{}的列表中", unit.getId(), course.getId());
                    }
                    //绘本处理句子中文音频
                    if (unit.getPictureBook() != null && unit.isOnLine()) {
                        processPictureBookSentences(unit);
                    }
                    unit.setBranch(course.getBranch());

                    KidRedisManager.setResourceBean(KID_RESOURCE_UNIT_CACHE_KEY + unit.getId(), unit);
                    // 处理单词资源
                    cacheWordResources(unit);
                }
            }

            // 缓存场景信息
            // 检查场景列表中是否已存在该场景ID，避免重复添加
            List<String> sceneIdList = KidRedisManager.getResourceListRange(KID_RESOURCE_SCENE_LIST_CACHE_KEY + courseId, 0, -1);
            if (sceneIdList == null || !sceneIdList.contains(scene.getId() + "")) {
                KidRedisManager.addResourceToList(KID_RESOURCE_SCENE_LIST_CACHE_KEY + courseId, scene.getId() + "");
                logger.debug("添加场景ID {}到课程{}的列表中", scene.getId(), courseId);
            }
            KidRedisManager.setResourceBean(KID_RESOURCE_SCENE_CACHE_KEY + scene.getId(), scene);
        }
    }

    /**
     * 处理绘本句子中的中文音频
     *
     * @param unit 资源单元
     */
    private static void processPictureBookSentences(ResourceKidUnit unit) {
        ResourceKidPictureBook pictureBook = unit.getPictureBook();
        int coreSentenceCount = 0;

        for (ResourceKidPictureBookContent content : pictureBook.getContentList()) {
            for (ResourceKidPictureBookSentence sentence : content.getSentenceList()) {
                String exampleZhCN = sentence.getExampleZhCN();
                if (exampleZhCN != null && !exampleZhCN.isEmpty()) {
                    // 清理文本并使用CnSoundFileManager获取音频文件
                    exampleZhCN = exampleZhCN.replaceAll("[\\s/n]", "");
                    sentence.setCnSoundFile(CnSoundFileManager.getCnSoundFile(exampleZhCN));
                }

                if (sentence.getCoreSentence() == 1) {
                    coreSentenceCount++;
                }
            }
        }

        unit.setCoreSentenceCount(coreSentenceCount);
    }

    /**
     * 缓存单词资源
     */
    private static void cacheWordResources(ResourceKidUnit unit) {
        ArrayList<ResourceKidWord> words = unit.getWords();
        if (words != null && unit.isOnLine()) {
            for (ResourceKidWord word : words) {
                String zhCN = word.getMeaningZhCN();
                if (StringUtil.isNotEmpty(zhCN)) {
                    // 使用CnSoundFileManager获取中文音频
                    String tts = CnSoundFileManager.getCnSoundFile(zhCN);
                    if (StringUtil.isNotEmpty(tts)) {
                        word.setCnSoundFile(tts);
                    }
                }
                if (word != null && word.getId() > 0) {
                    KidRedisManager.setResourceBean(KID_RESOURCE_WORD_CACHE_KEY + word.getId(), word);
                }
            }
        }
    }

    /**
     * 发布字母课程资源
     */
    private static void publishLetterCourseResources(ResourceKidService resourceKidService, Integer courseId) {
        List<KidLetterUnit> letterUnits = resourceKidService.getLetterUnitVOByCourseId(courseId);
        if (Collections.isEmpty(letterUnits)) {
            logger.warn("课程{}没有关联的字母单元", courseId);
            return;
        }

        for (KidLetterUnit letterUnit : letterUnits) {
            if (letterUnit != null && letterUnit.getUnitId() > 0) {
                // 检查单元列表中是否已存在该单元ID，避免重复添加
                List<String> unitIdList = KidRedisManager.getResourceListRange(KID_RESOURCE_UNIT_LIST_CACHE_KEY + courseId, 0, -1);
                if (unitIdList == null || !unitIdList.contains(letterUnit.getUnitId() + "")) {
                    KidRedisManager.addResourceToList(KID_RESOURCE_UNIT_LIST_CACHE_KEY + courseId, letterUnit.getUnitId() + "");
                }
                KidRedisManager.setResourceBean(KID_RESOURCE_LETTER_UNIT_CACHE_KEY + letterUnit.getUnitId(), letterUnit);
            }
        }
    }


    /**
     * 获取课程的字母单元列表
     */
    public static List<KidLetterUnit> getLetterUnitByCourseId(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取字母单元列表失败：课程ID为空");
            return new ArrayList<>();
        }

        try {
            List<String> listRange = KidRedisManager.getResourceListRange(KID_RESOURCE_UNIT_LIST_CACHE_KEY + courseId, 0, -1);

            if (Collections.isEmpty(listRange)) {
                return new ArrayList<>();
            }

            List<KidLetterUnit> result = listRange.stream().map(id -> getLetterUnitById(Integer.parseInt(id))).collect(Collectors.toList());
            return result;
        } catch (Exception e) {
            logger.error("获取字母单元列表异常，课程ID: {}", courseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID获取字母单元
     */
    public static KidLetterUnit getLetterUnitById(Integer unitId) {
        if (unitId == null) {
            logger.warn("获取字母单元失败：单元ID为空");
            return null;
        }

        try {
            return (KidLetterUnit) KidRedisManager.getResourceBean(KID_RESOURCE_LETTER_UNIT_CACHE_KEY + unitId);
        } catch (Exception e) {
            logger.error("获取字母单元异常，单元ID: {}", unitId, e);
            return null;
        }
    }

    /**
     * 获取课程的单元列表
     */
    public static List<ResourceKidUnit> getUnitByCourseId(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取单元列表失败：课程ID为空");
            return new ArrayList<>();
        }

        try {
            List<String> listRange = KidRedisManager.getResourceListRange(KID_RESOURCE_UNIT_LIST_CACHE_KEY + courseId, 0, -1);

            if (Collections.isEmpty(listRange)) {
                return new ArrayList<>();
            }

            return listRange.stream()
                    .map(key -> (ResourceKidUnit) KidRedisManager.getResourceBean(KID_RESOURCE_UNIT_CACHE_KEY + key))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取单元列表异常，课程ID: {}", courseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID获取单元
     */
    public static ResourceKidUnit getUnitById(Integer unitId) {
        if (unitId == null) {
            logger.warn("获取单元失败：单元ID为空");
            return null;
        }

        try {
            return (ResourceKidUnit) KidRedisManager.getResourceBean(KID_RESOURCE_UNIT_CACHE_KEY + unitId);
        } catch (Exception e) {
            logger.error("获取单元异常，单元ID: {}", unitId, e);
            return null;
        }
    }

    /**
     * 获取课程的场景列表
     */
    public static List<ResourceKidScene> getSceneByCourseId(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取场景列表失败：课程ID为空");
            return new ArrayList<>();
        }

        try {
            List<String> sceneIdList = KidRedisManager.getResourceListRange(KID_RESOURCE_SCENE_LIST_CACHE_KEY + courseId, 0, -1);
            List<ResourceKidScene> result = sceneIdList.stream()
                    .map(key -> (ResourceKidScene) KidRedisManager.getResourceBean(KID_RESOURCE_SCENE_CACHE_KEY + key))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取场景列表异常，课程ID: {}", courseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID获取场景
     */
    public static ResourceKidScene getSceneById(Integer sceneId) {
        if (sceneId == null) {
            logger.warn("获取场景失败：场景ID为空");
            return null;
        }

        try {
            return (ResourceKidScene) KidRedisManager.getResourceBean(KID_RESOURCE_SCENE_CACHE_KEY + sceneId);
        } catch (Exception e) {
            logger.error("获取场景异常，场景ID: {}", sceneId, e);
            return null;
        }
    }

    /**
     * 获取指定类型的课程列表
     */
    public static List<ResourceKidCourse> getCourseList(KidConstant.CourseType type) {
        if (type == null) {
            logger.warn("获取课程列表失败：课程类型为空");
            return new ArrayList<>();
        }

        try {
            List<String> list = KidRedisManager.getResourceListRange(KID_RESOURCE_COURSE_LIST_CACHE_KEY + type, 0, -1);

            if (Collections.isEmpty(list)) {
                return new ArrayList<>();
            }

            return list.stream()
                    .map(key -> (ResourceKidCourse) KidRedisManager.getResourceBean(KID_RESOURCE_COURSE_CACHE_KEY + key))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取课程列表异常，课程类型: {}", type, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID获取课程
     */
    public static ResourceKidCourse getCourse(Integer courseId) {
        // 检查输入参数是否为 null
        if (courseId == null) {
            return null; // 如果课程 ID 为 null，直接返回 null
        }

        // 定义缓存键
        final String cacheKey = KID_RESOURCE_COURSE_CACHE_KEY + courseId;

        // 从 Redis 中获取资源对象
        Object resourceBean = KidRedisManager.getResourceBean(cacheKey);

        // 检查返回值是否为 null，避免空指针异常
        if (!(resourceBean instanceof ResourceKidCourse)) {
            return null; // 如果缓存中无数据或类型不匹配，返回 null
        }

        // 强制类型转换并返回
        return (ResourceKidCourse) resourceBean;
    }


    /**
     * 根据ID获取单词
     */
    public static ResourceKidWord getWordById(Integer wordId) {
        if (wordId == null) {
            logger.warn("获取单词失败：单词ID为空");
            return null;
        }

        try {
            return (ResourceKidWord) KidRedisManager.getResourceBean(KID_RESOURCE_WORD_CACHE_KEY + wordId);
        } catch (Exception e) {
            logger.error("获取单词异常，单词ID: {}", wordId, e);
            return null;
        }
    }

    /**
     * 根据场景ID获取单元ID列表
     *
     * @param sceneId 场景ID
     * @return 单元ID列表
     */
    public static List<Integer> getUnitIdsBySceneId(Integer sceneId) {
        if (sceneId == null) {
            logger.warn("获取单元ID列表失败：场景ID为空");
            return new ArrayList<>();
        }

        try {
            ResourceKidScene scene = getSceneById(sceneId);
            if (scene == null || scene.getUnitList() == null || scene.getUnitList().isEmpty()) {
                return new ArrayList<>();
            }

            return scene.getUnitList().stream()
                    .map(ResourceKidUnit::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("根据场景获取单元ID列表异常，场景ID: {}", sceneId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据课程ID获取单元ID列表
     *
     * @param courseId 课程ID
     * @return 单元ID列表
     */
    public static List<Integer> getUnitIdsByCourseId(Integer courseId) {
        if (courseId == null) {
            logger.warn("获取单元ID列表失败：课程ID为空");
            return new ArrayList<>();
        }

        try {
            List<String> unitIdStrings = KidRedisManager.getResourceListRange(KID_RESOURCE_UNIT_LIST_CACHE_KEY + courseId, 0, -1);
            if (Collections.isEmpty(unitIdStrings)) {
                return new ArrayList<>();
            }

            return unitIdStrings.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("根据课程获取单元ID列表异常，课程ID: {}", courseId, e);
            return new ArrayList<>();
        }
    }
}