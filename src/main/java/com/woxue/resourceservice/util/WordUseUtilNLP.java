//package com.woxue.resourceservice.util;
//
//import com.woxue.common.util.wordUse.WordUseBean;
//import com.woxue.common.util.wordUse.WordUseUtil;
//import edu.stanford.nlp.ling.CoreAnnotations;
//import edu.stanford.nlp.ling.CoreLabel;
//import edu.stanford.nlp.pipeline.Annotation;
//import edu.stanford.nlp.pipeline.StanfordCoreNLP;
//import edu.stanford.nlp.util.CoreMap;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.*;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.regex.Pattern;
//
///**
// * 单词使用工具类 - 用于处理英语句子中的单词和词组
// * 支持：
// * 1. 单词词形变化识别（动词时态、名词单复数等）
// * 2. 词组匹配（连续和分离词组）
// * 3. 标点符号处理
// * 4. 缩写形式识别
// */
//@Slf4j
//public class WordUseUtilNLP {
//    // 常量定义
//    private static final String NUMBER_PATTERN = "^([0-9]+,)*[0-9]+$";
//    private static final Pattern SPACE_PATTERN = Pattern.compile("\\s+");
//    private static final int MAX_PHRASE_DISTANCE = 5; // 词组单词间最大距离
//
//    // NLP pipeline
//    private static final Properties props = new Properties();
//    private static volatile StanfordCoreNLP pipeline;
//
//    // 缓存机制
//    private static final Map<String, String> lemmaCache = new ConcurrentHashMap<>(1000);
//    private static final Map<String, String> posCache = new ConcurrentHashMap<>(1000);
//    private static final Map<String, List<WordAnalysis>> sentenceAnalysisCache = new ConcurrentHashMap<>(500);
//
//    // 允许在词组中间出现的词
//    private static final Set<String> ALLOWED_MIDDLE_WORDS = new HashSet<>(Arrays.asList(
//            "a", "an", "the", "to", "for", "in", "on", "at", "by", "with",
//            "up", "down", "out", "off", "away", "back", "over", "under",
//            "it", "this", "that", "my", "your", "his", "her", "its", "our", "their"
//    ));
//
//    // 特殊形式映射（包括缩写和不规则变化）
//    private static final Map<String, String> SPECIAL_FORMS = new HashMap<String, String>() {{
//        // 缩写形式
//        put("'m", "am");
//        put("'s", "is");
//        put("'re", "are");
//        put("'ve", "have");
//        put("'d", "would");
//        put("n't", "not");
//        put("'ll", "will");
//        // 不规则动词
//        put("went", "go");
//        put("gone", "go");
//        put("was", "be");
//        put("were", "be");
//        put("been", "be");
//        // 不规则名词
//        put("children", "child");
//        put("teeth", "tooth");
//        put("feet", "foot");
//        put("mice", "mouse");
//    }};
//
//    // 静态初始化
//    static {
//        initializeNLP();
//    }
//
//    /**
//     * 初始化NLP工具
//     */
//    private static void initializeNLP() {
//        // 设置NLP属性
//        props.setProperty("annotators", "tokenize,ssplit,pos,lemma");
//        props.setProperty("coref.algorithm", "neural");
//        props.setProperty("threads", "2");
//        props.setProperty("tokenize.cacheSize", "10000");
//        props.setProperty("pos.cacheSize", "10000");
//        props.setProperty("lemma.cacheSize", "10000");
//    }
//
//    /**
//     * 获取NLP pipeline（懒加载）
//     */
//    private static StanfordCoreNLP getPipeline() {
//        if (pipeline == null) {
//            synchronized (WordUseUtil.class) {
//                if (pipeline == null) {
//                    pipeline = new StanfordCoreNLP(props);
//                }
//            }
//        }
//        return pipeline;
//    }
//
//    /**
//     * 词形分析结果类
//     */
//    private static class WordAnalysis {
//        String original;
//        String lemma;
//        String pos;
//
//        WordAnalysis(String original, String lemma, String pos) {
//            this.original = original;
//            this.lemma = lemma;
//            this.pos = pos;
//        }
//    }
//
//    /**
//     * 分析句子（带缓存）
//     */
//    private static List<WordAnalysis> analyzeSentence(String sentence) {
//        return sentenceAnalysisCache.computeIfAbsent(sentence, k -> {
//            try {
//                Annotation document = new Annotation(k);
//                getPipeline().annotate(document);
//                List<WordAnalysis> analyses = new ArrayList<>();
//
//                List<CoreMap> sentences = document.get(CoreAnnotations.SentencesAnnotation.class);
//                for (CoreMap sent : sentences) {
//                    for (CoreLabel token : sent.get(CoreAnnotations.TokensAnnotation.class)) {
//                        analyses.add(new WordAnalysis(
//                                token.originalText(),
//                                token.get(CoreAnnotations.LemmaAnnotation.class),
//                                token.get(CoreAnnotations.PartOfSpeechAnnotation.class)
//                        ));
//                    }
//                }
//                return analyses;
//            } catch (Exception e) {
//                log.error("Error analyzing sentence: {}", sentence, e);
//                return Collections.emptyList();
//            }
//        });
//    }
//
//    /**
//     * 主方法：处理单词或词组
//     */
//    public static WordUseBean getUserShowData(String exampleSen, String spell, String meaning) {
//        WordUseBean bean = new WordUseBean();
//        bean.setMeaning(meaning);
//        bean.setSpell(spell);
//        bean.setSentence(exampleSen);
//
//        if (StringUtils.isAnyBlank(exampleSen, spell)) {
//            handleException(bean, spell);
//            return bean;
//        }
//
//        try {
//            spell = spell.trim();
//            boolean hasSpace = SPACE_PATTERN.matcher(spell).find();
//
//            // 分析句子
//            List<WordAnalysis> sentenceAnalysis = analyzeSentence(exampleSen);
//            List<String> finalList = processWithPunctuation(sentenceAnalysis);
//            List<String> correctWords = new ArrayList<>();
//
//            // 处理单词或词组
//            if (!hasSpace) {
//                processSimpleWord(bean, spell, finalList, correctWords);
//            } else {
//                processPhrase(bean, spell, finalList, correctWords);
//            }
//
//            // 处理最后的标点符号
//            handleFinalPunctuation(bean, finalList);
//
//            bean.setFinalList(finalList);
//            bean.setHasSpace(hasSpace);
//            bean.setCorrectWords(correctWords);
//
//        } catch (Exception e) {
//            log.error("Error processing word/phrase: {} in sentence: {}", spell, exampleSen, e);
//            handleException(bean, spell);
//        }
//
//        return bean;
//    }
//
//    /**
//     * 处理单个单词
//     */
//    private static void processSimpleWord(WordUseBean bean, String spell,
//                                          List<String> finalList, List<String> correctWords) {
//        boolean found = false;
//
//        for (int i = 0; i < finalList.size() && !found; i++) {
//            String currentWord = finalList.get(i);
//
//            if (isPunctuation(currentWord)) {
//                continue;
//            }
//
//            if (isWordMatch(spell, currentWord)) {
//                found = true;
//                bean.setInputPos(i);
//                correctWords.add(currentWord);
//                bean.setCorrectPos(true);
//            }
//        }
//
//        if (!found) {
//            correctWords.add(spell);
//            bean.setInputPos(0);
//            finalList.clear();
//            finalList.add(spell);
//            bean.setCorrectPos(false);
//        }
//    }
//
//    /**
//     * 处理词组（支持分离词组）
//     */
//    /**
//     * 处理词组（支持分离词组）
//     */
//    private static void processPhrase(WordUseBean bean, String spell,
//                                      List<String> finalList, List<String> correctWords) {
//        String[] phraseWords = spell.split("\\s+");
//        boolean found = false;
//
//        // 先尝试连续匹配
//        findContinuous:
//        for (int i = 0; i < finalList.size(); i++) {
//            if (i + phraseWords.length > finalList.size()) {
//                continue;
//            }
//
//            // 检查连续词组匹配
//            boolean allMatch = true;
//            for (int j = 0; j < phraseWords.length; j++) {
//                if (!isWordMatch(phraseWords[j], finalList.get(i + j))) {
//                    allMatch = false;
//                    continue findContinuous;
//                }
//            }
//
//            if (allMatch) {
//                found = true;
//                bean.setConnect(true);
//                bean.setInputPos(i, i + phraseWords.length - 1);
//                for (int j = 0; j < phraseWords.length; j++) {
//                    correctWords.add(finalList.get(i + j));
//                }
//                return;
//            }
//        }
//
//        // 如果没找到连续匹配，尝试分离匹配
//        if (!found && phraseWords.length == 2) {
//            findSeparate:
//            for (int i = 0; i < finalList.size(); i++) {
//                if (isWordMatch(phraseWords[0], finalList.get(i))) {
//                    // 在有限距离内查找第二个单词
//                    int maxSearchIndex = Math.min(i + MAX_PHRASE_DISTANCE, finalList.size());
//                    for (int j = i + 1; j < maxSearchIndex; j++) {
//                        if (isWordMatch(phraseWords[1], finalList.get(j)) &&
//                                isValidPhraseGap(finalList.subList(i + 1, j))) {
//                            found = true;
//                            bean.setConnect(false);
//                            bean.setInputPos(i, j);
//                            correctWords.add(finalList.get(i));
//                            correctWords.add(finalList.get(j));
//                            return;
//                        }
//                    }
//                }
//            }
//        }
//
//        // 如果没找到任何匹配
//        if (!found) {
//            bean.setConnect(false);
//            correctWords.addAll(Arrays.asList(phraseWords));
//            finalList.clear();
//            finalList.addAll(Arrays.asList(phraseWords));
//            bean.setInputPos(0, phraseWords.length - 1);
//        }
//    }
//
//    /**
//     * 检查词组间隔是否有效
//     */
//    private static boolean isValidPhraseGap(List<String> gap) {
//        for (String word : gap) {
//            if (!ALLOWED_MIDDLE_WORDS.contains(word.toLowerCase()) && !isPunctuation(word)) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//    /**
//     * 检查两个单词是否匹配
//     */
//    private static boolean isWordMatch(String word1, String word2) {
//        // 完全相同
//        if (word1.equalsIgnoreCase(word2)) {
//            return true;
//        }
//
//        // 检查词形还原
//        String lemma1 = getLemma(word1);
//        String lemma2 = getLemma(word2);
//        if (lemma1.equals(lemma2)) {
//            return true;
//        }
//
//        // 检查特殊形式
//        return SPECIAL_FORMS.containsKey(word1.toLowerCase()) &&
//                SPECIAL_FORMS.get(word1.toLowerCase()).equals(word2.toLowerCase()) ||
//                SPECIAL_FORMS.containsKey(word2.toLowerCase()) &&
//                        SPECIAL_FORMS.get(word2.toLowerCase()).equals(word1.toLowerCase());
//    }
//
//    /**
//     * 获取词形还原结果（带缓存）
//     */
//    private static String getLemma(String word) {
//        if (StringUtils.isBlank(word)) {
//            return word;
//        }
//        return lemmaCache.computeIfAbsent(word.toLowerCase(), k -> {
//            try {
//                Annotation document = new Annotation(word);
//                getPipeline().annotate(document);
//                List<CoreMap> sentences = document.get(CoreAnnotations.SentencesAnnotation.class);
//                if (!sentences.isEmpty()) {
//                    List<CoreLabel> tokens = sentences.get(0).get(CoreAnnotations.TokensAnnotation.class);
//                    if (!tokens.isEmpty()) {
//                        return tokens.get(0).get(CoreAnnotations.LemmaAnnotation.class);
//                    }
//                }
//                return word;
//            } catch (Exception e) {
//                log.warn("Error getting lemma for word: {}", word, e);
//                return word;
//            }
//        });
//    }
//
//    /**
//     * 处理句子中的标点符号
//     */
//    private static List<String> processWithPunctuation(List<WordAnalysis> analyses) {
//        List<String> result = new ArrayList<>();
//
//        for (WordAnalysis analysis : analyses) {
//            String word = analysis.original;
//            // 处理数字中的逗号
//            if (word.matches(NUMBER_PATTERN)) {
//                result.add(word);
//                continue;
//            }
//
//            // 处理其他标点符号
//            String[] parts = word.split("(?=[.,!?:;])|(?<=[.,!?:;])");
//            for (String part : parts) {
//                if (!part.trim().isEmpty()) {
//                    result.add(part.trim());
//                }
//            }
//        }
//
//        return result;
//    }
//
//    /**
//     * 处理最后的标点符号
//     */
//    private static void handleFinalPunctuation(WordUseBean bean, List<String> finalList) {
//        if (finalList.isEmpty()) return;
//
//        int[] pos = bean.getInputPos();
//        String lastWord = finalList.get(finalList.size() - 1);
//
//        if (isPunctuation(lastWord)) {
//            boolean canCombine = true;
//            for (int p : pos) {
//                if (p >= finalList.size() - 2) {
//                    canCombine = false;
//                    break;
//                }
//            }
//
//            if (canCombine) {
//                String previousWord = finalList.get(finalList.size() - 2);
//                finalList.remove(finalList.size() - 1);
//                finalList.set(finalList.size() - 1, previousWord + lastWord);
//            }
//        }
//    }
//
//    /**
//     * 处理异常情况
//     */
//    private static void handleException(WordUseBean bean, String spell) {
//        List<String> correctWords = Collections.singletonList(spell);
//        List<String> finalList = Collections.singletonList(spell);
//
//        bean.setInputPos(0);
//        bean.setCorrectWords(correctWords);
//        bean.setFinalList(finalList);
//        bean.setHasSpace(false);
//        bean.setCorrectPos(false);
//        bean.setConnect(false);
//    }
//
//    private static boolean isPunctuation(String str) {
//        return str.matches("[`~!@#$^&*()=|{}':;',\\\\[\\\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]$");
//    }
//    /**
//     * 字符串首字母大写
//     */
//    public static String upperCase(String str) {
//        if (StringUtils.isEmpty(str)) {
//            return str;
//        }
//        char[] ch = str.toCharArray();
//        if (ch[0] >= 'a' && ch[0] <= 'z') {
//            ch[0] = (char) (ch[0] - 32);
//        }
//        return new String(ch);
//    }
//
//    /**
//     * 字符串首字母小写
//     */
//    public static String lowerCase(String str) {
//        if (StringUtils.isEmpty(str)) {
//            return str;
//        }
//        char[] ch = str.toCharArray();
//        if (ch[0] >= 'A' && ch[0] <= 'Z') {
//            ch[0] = (char) (ch[0] + 32);
//        }
//        return new String(ch);
//    }
//
//    /**
//     * 清理缓存
//     * 当缓存大小超过阈值时调用
//     */
//    public static void clearCache() {
//        lemmaCache.clear();
//        posCache.clear();
//        sentenceAnalysisCache.clear();
//    }
//
//    /**
//     * 使用示例
//     */
//    public static void main(String[] args) {
//        // 1. 基本形式
//        WordUseBean result1 = getUserShowData(
//                "He plays football every day.",
//                "play",
//                "玩"
//        );
//        System.out.println("基本形式示例：" + result1.getFinalList());
//
//        // 2. 分离词组
//        WordUseBean result2 = getUserShowData(
//                "Please turn the light off.",
//                "turn off",
//                "关闭"
//        );
//        System.out.println("分离词组示例：" + result2.getFinalList());
//        System.out.println("挖空位置：" + Arrays.toString(result2.getInputPos()));
//
//        // 3. 不规则变化
//        WordUseBean result3 = getUserShowData(
//                "She went to school yesterday.",
//                "go",
//                "去"
//        );
//        System.out.println("不规则变化示例：" + result3.getFinalList());
//    }
//}