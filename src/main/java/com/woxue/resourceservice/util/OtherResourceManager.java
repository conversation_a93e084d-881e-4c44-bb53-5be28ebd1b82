package com.woxue.resourceservice.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.WordBean;
import com.woxue.resourceservice.dao.IRedBookCourseDao;

public class OtherResourceManager {

	private static final String UQUIZ_VOCABULARY_NUM_QUIZ_WORD_LIST_PREFIX = "uquiz:vocabularyNumQuizWord:level:";

	private static final String UQUIZ_KEYBOARDSKILLEDWORD_ID_PREFIX = "uquiz:keyBoardSkilledWord:id:";
	private static final String UQUIZ_KEYBOARDSKILLEDWORD_IDLIST_PREFIX = "uquiz:keyBoardSkilledWordIdList:id";

	/**
	 * 获取词汇量测试的单词列表
	 */
	public static List<WordBean> getVocabularyNumQuizWordList(Integer level){
		Object obj = RedBookRedisManager.getResourceBean(UQUIZ_VOCABULARY_NUM_QUIZ_WORD_LIST_PREFIX + level);
		if(obj==null){
			List<WordBean> wordBeanList = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class).loadVocabularyNumQuizWordList(level);
			if(wordBeanList!=null){
				RedBookRedisManager.setResourceBean(UQUIZ_VOCABULARY_NUM_QUIZ_WORD_LIST_PREFIX + level,wordBeanList);
				return wordBeanList;
			}
			return null;
		}
		return (List<WordBean>) obj;
	}

	/**
	 * 获取键盘熟练度idList
	 */
	public static List<Integer> gekeyBoardSkilledWordIdList(){
		List<Integer> list = new ArrayList<Integer>();
		List<String> gidlist = RedBookRedisManager.getResourceListRange(UQUIZ_KEYBOARDSKILLEDWORD_IDLIST_PREFIX,0,-1);
		if(gidlist != null && !gidlist.isEmpty()){
			for(String gid:gidlist){
				list.add(Integer.valueOf(gid));
			}
		}else{
			 List<Map<String, Object>> keyBoardSkilledWordList = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class).getKeyBoardSkilledWordList();
			 for (Map<String, Object> map : keyBoardSkilledWordList) {
				 RedBookRedisManager.setResourceBean(UQUIZ_KEYBOARDSKILLEDWORD_ID_PREFIX+map.get("id"),map);
				 RedBookRedisManager.addResourceToList(UQUIZ_KEYBOARDSKILLEDWORD_IDLIST_PREFIX,map.get("id")+"");
			 }
			 gidlist = RedBookRedisManager.getResourceListRange(UQUIZ_KEYBOARDSKILLEDWORD_IDLIST_PREFIX,0,-1);
			 for(String gid:gidlist){
					list.add(Integer.valueOf(gid));
			 }
		}
		return list;
	}
	/**
	 * 获取键盘熟练度单词
	 * @param id
	 * @return
	 */
	public static Map<String, Object> getKeyBoardSkilledWord(Integer id){
		Map<String,Object> map = (Map<String, Object>)RedBookRedisManager.getResourceBean(UQUIZ_KEYBOARDSKILLEDWORD_ID_PREFIX+ id);
		return map;
	}
	
}
