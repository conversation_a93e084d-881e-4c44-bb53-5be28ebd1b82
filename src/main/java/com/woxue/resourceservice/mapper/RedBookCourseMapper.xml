<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourceservice.dao.IRedBookCourseDao">
    <update id="updateCourseBranchIdByPrimaryKey">
        update resource_course
        set branch_id = branch_id+1
        where id = #{id}
    </update>
    <update id="updateVersionBranchIdByPrimaryKey">
        update resource_version
        set branch_id = branch_id+1
        where id = #{id}
    </update>
    <select id="getVersion" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookVersion">
        select id
             , name_en            nameEn
             , name_cn            nameCn
             , version_type       versionType
             , relation_type      relationType
             , stage
             , price
             , brief_introduction briefIntroduction
             , display_order      displayOrder
             ,branch_id branchId
        from resource_version
        where id = #{versionId}
    </select>
    <select id="getVersionList"  resultType="com.woxue.common.model.redBook.RedBookVersion">
        select id
            ,name_en nameEn
            ,name_cn nameCn
            ,version_type versionType
            ,relation_type relationType
            ,stage
            ,price
            ,brief_introduction briefIntroduction
            ,display_order displayOrder
            ,branch_id branchId
        from resource_version
        order by stage,version_type,display_order
    </select>
    <select id="getVersionListByType"  resultType="com.woxue.common.model.redBook.RedBookVersion">
        select id
            ,name_en nameEn
            ,name_cn nameCn
            ,version_type versionType
            ,relation_type relationType
            ,stage
            ,price
            ,brief_introduction briefIntroduction
            ,display_order displayOrder
            ,branch_id branchId
        from resource_version
        where version_type=#{versionType}
        order by stage,version_type,display_order
    </select>

    <resultMap type="com.woxue.common.model.redBook.RedBookCourse" id="redbookCourseMap">
        <id column="id" property="id" />
        <result property="versionId" column="version_id" />
        <result property="nameEn" column="name_en" />
        <result property="nameCn" column="name_cn" />
        <result property="unitNum" column="unit_num" />
        <result property="stage" column="stage" />
        <result property="grade" column="grade" />
        <result property="branchId" column="branch_id" />
        <result property="displayOrder" column="display_order" />
        <result property="briefIntroduction" column="brief_introduction" />

        <association property="courseSentence" column="id" select="getCourseSentence" />
        <association property="courseGrammar" column="id" select="getCourseGrammar" />
        <association property="courseQuestion" column="id" select="getCourseQuestion" />
        <association property="courseArticle" column="id" select="getCourseArticle" />

        <collection property="courseWordList" column="id" select="getCourseWord"/>
    </resultMap>
    <select id="getCourseList" parameterType="int" resultMap="redbookCourseMap">
        select id
            ,version_id versionId
            ,name_en nameEn
            ,name_cn nameCn
            ,unit_num unitNum
            ,stage
            ,grade
            ,brief_introduction briefIntroduction
            ,display_order displayOrder
            ,branch_id branchId
        from resource_course
        where version_id = #{versionId} order by display_order
    </select>
    <select id="getCourse" parameterType="int" resultMap="redbookCourseMap">
        select id
             ,version_id versionId
             ,name_en nameEn
             ,name_cn nameCn
             ,unit_num unitNum
             ,stage
             ,grade
             ,brief_introduction briefIntroduction
             ,display_order displayOrder
             ,branch_id branchId
        from resource_course
        where id = #{courseId}
    </select>


    <select id="getCourseWord" parameterType="java.lang.Integer" resultType="com.woxue.common.model.redBook.RedBookCourseWord">
        select id
            ,resource_course_id resourceCourseId
            ,program_name programName
            ,show_name showName
        from resource_course_word
        where resource_course_id = #{courseId} order by `id`
    </select>
    <select id="getCourseSentence" parameterType="java.lang.Integer" resultType="com.woxue.common.model.redBook.RedBookCourseSentence">
        select id
            ,resource_course_id resourceCourseId
            ,program_name programName
            ,show_name showName
        from resource_course_sentence
        where resource_course_id = #{courseId}
    </select>
    <select id="getCourseGrammar" parameterType="java.lang.Integer" resultType="com.woxue.common.model.redBook.RedBookCourseGrammar">
        select id
            ,resource_course_id resourceCourseId
            ,grammar_course_id grammarCourseId
            ,show_name showName
        from resource_course_grammar
        where resource_course_id = #{courseId}
    </select>
    <select id="getCourseQuestion" parameterType="java.lang.Integer" resultType="com.woxue.common.model.redBook.RedBookCourseQuestion">
        select id
            ,resource_course_id resourceCourseId
            ,sync_question_course_id syncQuestionCourseId
            ,show_name showName
        from resource_course_question
        where resource_course_id = #{courseId}
    </select>
    <select id="getCourseArticle" parameterType="java.lang.Integer" resultType="com.woxue.common.model.redBook.RedBookCourseArticle">
        select id
            ,resource_course_id resourceCourseId
            ,program_name programName
            ,show_name showName
        from resource_course_article
        where resource_course_id = #{courseId}
    </select>

    <resultMap type="com.woxue.common.model.redBook.RedBookUnit" id="redbookUnitMap">
        <id column="id" property="id" />
        <result property="courseId" column="course_id" />
        <result property="nameEn" column="name_en" />
        <result property="nameCn" column="name_cn" />
        <result property="displayOrder" column="display_order" />

        <association property="contentSentenceList" column="id" select="getUnitContentSentence" />
        <association property="contentGrammar" column="id" select="getUnitContentGrammar" />
        <association property="contentWrite" column="id" select="getUnitContentWrite" />
        <association property="contentRead" column="id" select="getUnitContentRead" />
        <association property="contentListen" column="id" select="getUnitContentListen" />

        <collection property="contentWordList" column="id" select="getUnitContentWord"/>
        <collection property="contentQuestionList" column="id" select="getUnitContentQuestion"/>
        <collection property="articleIdList" column="id" select="getUnitArticleIdList"/>
    </resultMap>

    <select id="getUnitList" parameterType="int" resultMap="redbookUnitMap">
        select *
        from resource_unit
        where course_id = #{courseId} order by display_order
    </select>

    <select id="getUnitContentWord" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentWord">
        select id
            ,resource_unit_id resourceUnitId
            ,level
            ,program_name pcOldProgramName
            ,unit_name pcOldUnitName
            ,word_count wordCount
            ,has_example_sentence hasExampleSentence
        from resource_unit_content_word
        where resource_unit_id = #{unitId} order by `level`
    </select>
    <select id="getUnitContentSentence" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentSentence">
        select id
            ,resource_unit_id resourceUnitId
            ,program_name pcOldProgramName
            ,unit_name pcOldUnitName
            ,show_name showName
            ,sentence_count sentenceCount
            ,level
        from resource_unit_content_sentence
        where resource_unit_id = #{unitId} order by `level`
    </select>
    <select id="getUnitContentWrite" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentWrite">
        select id
             ,resource_unit_id resourceUnitId
             ,topic_id topicId
             ,`words` words
             ,sample_title sampleTitle
             ,sample_content sampleContent
        from resource_unit_content_write
        where resource_unit_id = #{unitId}
    </select>
    <select id="getUnitContentRead" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentRead">
        select max(r.id) as id, max(r.resource_unit_id) as resourceUnitId, max(r.topic_id) as topicId, max(t.name) as topic,count(a.article_id) as articleNum
        from resource_unit_content_read r
         left join resource_topic t on t.id = r.topic_id
         left join resource_read_article a on r.resource_unit_id = a.unit_id
        where t.type = 1 and r.resource_unit_id =  #{unitId}
    </select>

    <select id="getUnitContentListen" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentListen">
        select max(r.id) as id, max(r.resource_unit_id) as resourceUnitId, max(r.topic_id) as topicId, max(t.name) as topic,count(a.id) as sentenceCount
        from resource_unit_content_listen r
         left join resource_topic t on t.id = r.topic_id
         left join resource_listen_sentence a on r.resource_unit_id = a.unit_id
        where t.type = 3 and r.resource_unit_id =  #{unitId}
    </select>

    <select id="getUnitContentGrammar" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentGrammar">
        select id
            ,resource_unit_id resourceUnitId
            ,grammar_course_id grammarCourseId
            ,grammar_unit_id grammarUnitId
            ,show_name showName
            ,knowledge_count knowLedgeCount
        from resource_unit_content_grammar
        where resource_unit_id = #{unitId}
    </select>
    <select id="getUnitContentQuestion" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookUnitContentQuestion">
        select id
            ,resource_unit_id resourceUnitId
            ,level
            ,sync_question_course_id syncQuestionCourseId
            ,sync_question_unit_id syncQuestionUnitId
            ,show_name showName
            ,paper_id paperId
        from resource_unit_content_question
        where resource_unit_id = #{unitId} order by `level`
    </select>
    <select id="getUnitArticleIdList" parameterType="int" resultType="int">
        select article_id
        from resource_unit_article
        where resource_unit_id = #{unitId} order by `display_order`
    </select>

    <resultMap type="com.woxue.common.model.redBook.RedBookArticle" id="articleMap">
        <id column="id" property="id" />
        <result property="articleName" column="article_name" />
        <result property="sentenceCount" column="sentence_count" />
        <result property="articleType" column="article_type" />

        <collection property="sentenceList" column="id" select="getArticleSentenceList"/>
    </resultMap>

    <select id="getArticle" parameterType="int" resultMap="articleMap">
        select id,article_name,sentence_count,article_type
        from resource_article
        where id = #{articleId}
    </select>
    <select id="getArticleSentenceList" parameterType="int" resultType="com.woxue.common.model.redBook.RedBookArticleSentence">
        select id
            ,article_id articleId
            ,sentence_en_US sentenceEnUS
            ,sentence_zh_CN sentenceZhCN
            ,speaker
            ,sound_file soundFile
            ,show_type showType
            ,modtime
            ,display_order displayOrder
        from resource_article_sentence
        where article_id = #{articleId} order by `display_order`
    </select>
    <select id="getArticleList" resultMap="articleMap">
        select id,article_name,sentence_count,article_type
        from resource_article
    </select>

    <select id="getPhonicsLetterList"  resultType="com.woxue.common.model.redBook.RedBookPhonicsLetter">
        select id,resource_unit_id resourceUnitId,`letter`,sound_url soundUrl
        from resource_unit_phonics_letter
        where resource_unit_id = #{unitId}  order by `display_order`
    </select>

    <!--<resultMap id="RedBookWordResultMap" type="com.woxue.common.model.redBook.RedBookWord">
        <id column="id" property="id"/>
        <result column="spelling" property="spelling"/>
        <result column="syllable" property="syllable"/>
        <result column="meaning_zh_CN" property="meaningZhCN"/>
        <result column="example_en_US" property="exampleEnUS"/>
        <result column="example_zh_CN" property="exampleZhCN"/>
        <result column="is_homonym" property="isHomonym"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>-->

    <select id="getWordIdListByPhonicsLetterId"  resultType="int">
        select word_id
        from resource_unit_phonics_letter_word
        where letter_id = #{letterId} and is_quiz_word=#{isQuizWord}
        order by display_order
    </select>

    <select id="getWordIdListByUnitId"  resultType="int">
        select word_id
        from resource_unit_word
        where resource_unit_id = #{unitId}
        <if test="level!=null">
            and `level`=#{level}
        </if>
        order by display_order
    </select>

    <select id="getSentenceWordIdListByUnitId"  resultType="int">
        select word_id
        from resource_unit_sentence
        where resource_unit_id = #{unitId}
        <if test="level!=null">
            and `level`=#{level}
        </if>
        order by display_order
    </select>

    <!-- 获取节下的标题 -->
    <select id="getGrammarTitleList" resultType="com.woxue.common.model.redBook.GrammarTitleBean">
		SELECT id,unit_id unitId,`name`, parent_id parentId, `level`, is_basic isBasic, disporder
		FROM grammar_title
		WHERE unit_id = #{unitId} AND parent_id = #{parentId}
		ORDER BY disporder, id
	</select>

    <!-- 获取子标题 -->
    <select id="getGrammarSubtitleList" resultType="com.woxue.common.model.redBook.GrammarSubtitleBean">
		SELECT id,unit_id unitId,title_id titleId,`name`,`type`, disporder
		FROM `grammar_subtitle`
		WHERE unit_id = #{unitId} AND title_id = #{titleId}
		ORDER BY disporder, id
	</select>

    <!-- 获取子标题下的问题 -->
    <select id="getGrammarContentList" resultType="com.woxue.common.model.redBook.GrammarContentBean">
		SELECT id,
			unit_id unitId,
			subtitle_id subtitleId,
			question_type questionType,
			question,
			option_a optionA,
			option_b optionB,
			option_c optionC,
			option_d optionD,
			option_e optionE,
			correct_option correctOption,
			wrong_tips_1 wrongTips1,
			wrong_tips_2 wrongTips2,
			wrong_tips_3 wrongTips3,
			parse,
			disporder
		FROM grammar_content
		WHERE unit_id=#{unitId} AND subtitle_id = #{subtitleId}
		ORDER BY disporder, id
	</select>

    <!-- 节下的本节检测试题 -->
    <select id="getGrammarQuestionList" parameterType="Integer" resultType="com.woxue.common.model.redBook.GrammarQuestionBean">
		SELECT id,
			teach_id teachId,
			unit_id unitId,
            answer_type as answerType,
			question_type questionType,
			question,
			option_a optionA,
			option_b optionB,
			option_c optionC,
			option_d optionD,
			option_e optionE,
			correct_option correctOption,
			parse
		FROM `grammar_question`
		WHERE unit_id=#{unitId}
		ORDER BY id
	</select>

    <!-- 节下的本节检测试题-对应多个材料内容表 -->
    <select id="getGrammarQuestionContentList" parameterType="Integer" resultType="com.woxue.common.model.redBook.GrammarQuestionContentBean">
        SELECT id,question_id as questionId,img_content as imgContent,text_content as textContent,display_order as displayOrder
        from grammar_question_content
        WHERE question_id=#{questionId}
        ORDER BY id
    </select>
    <!-- 获取本章检测试题 -->
    <select id="getGrammarCourseQuestionList" parameterType="Integer" resultType="com.woxue.common.model.SimulationQuestionBean">
		SELECT sq.`id`,
-- 			sq.`knowledge_point` knowledgePoint,
            CASE WHEN sq.`question_type` = 3 THEN 4 ELSE sq.`question_type` END AS questionType,
            sq.`question`,
			sq.`option_a` optionA,
			sq.`option_b` optionB,
			sq.`option_c` optionC,
			sq.`option_d` optionD,
			sq.`option_e` optionE,
			sq.`correct_option` correctOption,
			sq.`parse`,
			sq.`difficulty`,
			20 answerTime
		FROM `grammar_question` sq
		left join resource_unit u on  sq.unit_id=u.id
		left join resource_course c on u.course_id=c.id
        WHERE c.id=#{courseId}
	</select>

    <select id="loadVocabularyNumQuizWordList" parameterType="int" resultType="wordBean">
        select
            id wordId,
            spelling,
            syllable,
            meaning_zh_CN,
            example_en_US,
            example_zh_CN
        from vocabulary_num_quiz_word
        where word_level = #{wordLevel}
    </select>

    <!-- 加载键盘熟练度单词信息 -->
    <select id="getKeyBoardSkilledWordList" resultType="java.util.Map">
        SELECT * FROM keyboard_skilled_word
    </select>

    <select id="getSoundMark" parameterType="String" resultType="String">
        select soundmark from soundmark where spelling=#{spelling}
    </select>

    <!--获取单词助记信息-->
    <select id="getWordMnemonics" resultType="map">
        SELECT * FROM `word_mnemonics` WHERE spelling=#{spelling}
    </select>

    <!--获取对应拼写的干扰单词id列表-->
    <select id="getWordDisturbWordIdList" resultType="int">
        SELECT disturb_word_id FROM `word_disturb` WHERE spelling=#{spelling}
    </select>

    <select id="loadWordBean" parameterType="int" resultType="wordBean">
        select
            w.id wordId,
            IFNULL(rw.display_order,rs.display_order) wordIndex,
            IFNULL(rw.resource_unit_id,rs.resource_unit_id) unitId,
            w.spelling,
            w.syllable,
            w.sound_file soundFile,
            w.meaning_en_US,
            w.meaning_zh_CN,
            w.example_en_US,
            w.example_zh_CN,
            w.modify_time
        from resource_word w
            left join resource_unit_word rw on w.id=rw.word_id
            left join resource_unit_sentence rs on w.id=rs.word_id
        where w.id = #{wordId}
    </select>
    <!-- 同步句子句块 -->
    <select id="getSentenceElement" resultType="java.util.Map">
        SELECT word_id,example_en_US,element_jz,example_clause_en_US,element_clause_jz,confirm_flag FROM `sentence_element` where word_id = #{wordId}
    </select>
    <!-- 获取音标例词列表 -->
    <!--<select id="getPhonogramWordList" resultType="java.util.Map">
        SELECT * FROM `phonogram_word`
    </select>-->
    <!-- 获取音标列表 -->
    <select id="getPhoneticIdList" resultType="int">
        SELECT word_id FROM `resource_unit_phonetic` where resource_unit_id=#{unitId} order by display_order
    </select>
    <select id="getPhoneticComposeList" resultType="com.woxue.common.model.redBook.PhoneticCompose">
        SELECT
            resource_unit_id unitId,
            word_id composeWordId,
           phonetic1_word_id phonetic1WordId,
           phonetic2_word_id phonetic2WordId
        FROM `resource_unit_phonetic_compose` where resource_unit_id=#{unitId} order by display_order
    </select>
    <select id="getPhoneticExampleList" resultType="com.woxue.common.model.redBook.PhoneticExample">
        SELECT
            resource_unit_id unitId,
            word_id exampleWordId,
            phonetic1_word_id phonetic1WordId,
            phonetic2_word_id phonetic2WordId
        FROM `resource_unit_phonetic_example` where resource_unit_id=#{unitId} order by display_order
    </select>
    <select id="getWordImgUrl" resultType="string">
        select img_url from `word_img` where BINARY spelling=#{spelling}
    </select>
    <select id="getWordSpeakWordMeaning" resultType="string">
        select word_meaning from `word_speak_word_meaning` where BINARY spelling=#{spelling}
    </select>

    <select id="getPhonicsLetterPartList" resultType="com.woxue.common.model.redBook.RedBookPhonicsLetterPart">
       select id,letter_id letterId,letter,sound_url soundUrl,display_order displayOrder from resource_unit_phonics_letter_part where letter_id=#{letterId} order by display_order
    </select>

    <select id="getNewPhonicsLetterList" resultType="com.woxue.common.model.redBook.RedBookPhonicsLetterPlus">
        select id,resource_unit_id resourceUnitId,`letter`,sound_url soundUrl,syllable
        from resource_unit_phonics_new_letter
        where resource_unit_id = #{unitId}  order by `display_order`
    </select>

    <select id="gePhonicsLetterWordInfoList" resultType="com.woxue.common.model.redBook.RedBookPhonicsLetterWordInfo">
        select id,
               letter_id     letterId,
               spell,
               syllable,
               spell_part    spellPart,
               meaning_en_US meaningEnUS,
               meaning_zh_CN meaningZhCN,
               is_quiz_word  isQuizWord,
               display_order displayOrder
        from resource_unit_phonics_letter_word_info
        where letter_id = #{letterId}
        order by display_order
    </select>


    <delete id="deleteVersionStage">
        delete from `resource_version_stage` where version_id=#{versionId}
    </delete>

    <insert id="insertVersionStage">
        insert into `resource_version_stage` (version_id,stage_id) values (#{versionId},#{stage})
    </insert>

    <delete id="deleteCourseStage">
        delete from `resource_course_stage` where course_id=#{courseId}
    </delete>

    <insert id="insertCourseStage">
        insert into `resource_course_stage` (course_id,stage_id) values (#{courseId},#{stage})
    </insert>

    <select id="getVersionStageList" resultType="com.woxue.common.model.redBook.RedBookVersionStage">
        SELECT version_id versionId,stage FROM `resource_version_stage` WHERE version_id=#{versionId}
    </select>

    <select id="getCourseStageList" resultType="com.woxue.common.model.redBook.RedBookCourseStage">
        SELECT course_id courseId,stage FROM `resource_course_stage` WHERE course_id=#{courseId}
    </select>

    <select id="getCourseByUnitId" resultMap="redbookCourseMap">
        select c.id
        ,c.version_id versionId
        ,c.name_en nameEn
        ,c.name_cn nameCn
        ,c.unit_num unitNum
        ,c.stage
        ,c.grade
        ,c.brief_introduction briefIntroduction
        ,c.display_order displayOrder
        ,branch_id branchId
        from resource_course c
        left join resource_unit u on u.course_id = c.id
        where u.id = #{unitId}
    </select>
</mapper>