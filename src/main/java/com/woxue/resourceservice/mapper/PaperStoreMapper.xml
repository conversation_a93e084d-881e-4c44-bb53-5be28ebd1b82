<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourceservice.dao.IQuestionSuitDao">


    <!-- 获取商城里的试卷列表 -->
    <select id="getUnitPaper" resultType="com.woxue.redbookquestionBank.model.SuitBean">
        SELECT s.id,
            s.`name`,
            s.`type_id`,
            s.`grade_phase` gradePhase,
            s.`grade`,
            s.`version_id` versionId,
            s.`course_id` courseID,
            s.`unit_id` unitId,
            s.`lesson_id`,
            s.`year`,
            s.`province_id`,
            s.`city_id`,
            s.`section_set_num`,
            s.`question_set_num`
        FROM hssword_question_bank.`suit` s
        where  teach_id="system"
        <if test="courseId!=null and courseId &gt; 0">
            AND s.`course_id` = #{courseId}
        </if>
        <if test="unitId!=null and unitId &gt; 0">
            AND s.`unit_id` = #{unitId}
        </if>
    </select>

    <!-- 插入新的 suit -->
    <insert id="insertSuit" useGeneratedKeys="true" keyProperty="newSuit.id">
        INSERT INTO suit (aid, teach_id, name, type_id, grade_phase, grade, version_id, course_id, unit_id, lesson_id, year, province_id, city_id, section_set_num, question_set_num, section_num, question_num, create_time, is_system, sync_status)
        SELECT aid, teach_id, CONCAT(name, '_复制'), type_id, grade_phase, grade, version_id, course_id, unit_id, lesson_id, year, province_id, city_id, section_set_num, question_set_num, section_num, question_num, NOW(), is_system, sync_status
        FROM suit
        WHERE id = #{originalSuitId};
    </insert>

    <!-- 插入 suit_section 数据 -->
    <insert id="insertSuitSections">
        INSERT INTO suit_section (suit_id, name, question_type, question_num, question_score, disporder)
        SELECT #{newSuitId}, name, question_type, question_num, question_score, disporder
        FROM suit_section
        WHERE suit_id = #{originalSuitId};
    </insert>

    <!-- 查询新旧 section_id 映射 -->
    <select id="getSectionIdMapping" resultType="map">
        SELECT id AS newSectionId, temp_original_id AS oldSectionId
        FROM suit_section
        WHERE suit_id = #{newSuitId};
    </select>

    <!-- 查询原始 suit_section_question 数据 -->
    <select id="getOriginalSectionQuestions" resultType="map">
        SELECT section_id, question_id
        FROM suit_section_question
        WHERE section_id IN (SELECT id FROM suit_section WHERE suit_id = #{originalSuitId});
    </select>

    <!-- 批量插入 suit_section_question 数据 -->
    <insert id="batchInsertSectionQuestions">
        INSERT INTO suit_section_question (section_id, question_id)
        VALUES
        <foreach collection="questions" item="question" separator=",">
            (#{question.newSectionId}, #{question.questionId})
        </foreach>
    </insert>


</mapper>
