<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourceservice.dao.IDictionaryDao">
	<resultMap type="com.woxue.common.model.DictionaryBean" id="dictionary">
		<id  property="id" column="id"/>
		<result property="spelling" column="spelling"/>
		<result property="phonetic" column="phonetic"/>
		<result property="meaning" column="meaning"/>
		<result property="meaningDetail" column="meaning_detail"/>
		<result property="meaningDual" column="meaning_dual"/>
		<result property="meaningEn" column="meaning_en"/>
		<result property="sent" column="sent"/>
		<result property="sentPatt" column="sent_patt"/>
		<result property="sentPhrase" column="sent_phrase"/>
		<result property="sentColl" column="sent_coll"/>
		<result property="sentAuth" column="sent_auth"/>
		<result property="learnEss" column="learn_ess"/>
		<result property="learnDiscrim" column="learn_discrim"/>
		<result property="learnComn" column="learn_comn"/>
		<result property="learnEtm" column="learn_etm"/>
		<result property="relNfo" column="rel_nfo"/>
		<result property="relNwd" column="rel_nwd"/>
		<result property="shape" column="shape"/>
		<result property="stars" column="stars"/>
	</resultMap>
	
	<select id="getWordSpellingList" resultType="string">
		SELECT DISTINCT spelling FROM resource_word WHERE spelling != ''
	</select>

	<select id="getSpellingList" resultType="string">
	    SELECT spelling FROM `dictionary_word`
	</select>
	
	<select id="getDictionaryList" resultMap="dictionary">
	    SELECT * FROM `dictionary_word`
	</select>
	
	<select id="getDictionary" resultMap="dictionary"> <!-- BINARY  -->
	    SELECT * FROM `dictionary_word` WHERE `spelling` = #{spelling} limit 1
	</select>
	
	<select id="getDictionaryListByLikeSpelling" resultMap="dictionary">
		SELECT  * FROM `dictionary_word` WHERE `spelling` LIKE CONCAT(#{spelling}, '%') ORDER BY id LIMIT 0,${limitSize}
	</select>
	
	<!-- 插入生词 -->
	<insert id="insertDictionary" >
		insert into `dictionary_word`
			(spelling,phonetic,meaning,meaning_detail,
			meaning_dual,meaning_en,sent,sent_patt,
			sent_phrase,sent_coll,sent_auth,learn_ess,
			learn_discrim,learn_comn,learn_etm,rel_nfo,rel_nwd,shape)
			values
			(#{spelling},#{phonetic},#{meaning},#{meaningDetail},
			#{meaningDual},#{meaningEn},#{sent},#{sentPatt},
			#{sentPhrase},#{sentColl},#{sentAuth},#{learnEss},
			#{learnDiscrim},#{learnComn},#{learnEtm},#{relNfo},#{relNwd},#{shape})
	</insert>

	<update id="updateDictionary">
		UPDATE `dictionary_word` SET
			`spelling`=#{spelling},
			`phonetic`=#{phonetic},
			`meaning`=#{meaning},
			`meaning_detail`=#{meaningDetail},
			`meaning_dual`=#{meaningDual},
			`meaning_en`=#{meaningEn},
			`sent`=#{sent},
			`sent_patt`=#{sentPatt},
			`sent_phrase`=#{sentPhrase},
			`sent_coll`=#{sentColl},
			`sent_auth`=#{sentAuth},
			`learn_ess`=#{learnEss},
			`learn_discrim`=#{learnDiscrim},
			`learn_comn`= #{learnComn},
			`learn_etm`=#{learnEtm},
			`rel_nfo`=#{relNfo},
			`rel_nwd`=#{relNwd},
			`shape`=#{shape}
		WHERE `id`=#{id}
    </update>
</mapper>
