<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourceservice.dao.IPictureBookDao">
    <select id="getPictureBookList" resultType="com.woxue.common.model.redBook.PictureBook">
        select id,
               course_id courseId,
               title,
               en_title  enTitle,
               thumbnail,
               desc_ch   desc_Ch,
               desc_en   desc_En,
               level,
               sort
        from `picture_book`
        where course_id = #{courseId}
    </select>
    <select id="getPictureBook" resultType="com.woxue.common.model.redBook.PictureBook">
        select id,
               course_id courseId,
               title,
               en_title  enTitle,
               thumbnail,
               desc_ch   desc_Ch,
               desc_en   desc_En,
               level,
               sort
        from `picture_book`
        where id = #{pictureBookId}
    </select>
    <select id="getPictureBookContentList" resultType="com.woxue.common.model.redBook.PictureBookContent">
        select id,
               course_id       courseId,
               picture_book_id pictureBookId,
               pic_url         picUrl,
               sentence_num    sentenceNum,
               sort
        from `picture_book_content`
        where picture_book_id = #{pictureBookId}
    </select>
    <select id="getPictureBookContent" resultType="com.woxue.common.model.redBook.PictureBookContent">
        select id,
               course_id       courseId,
               picture_book_id pictureBookId,
               pic_url         picUrl,
               sentence_num    sentenceNum,
               sort
        from `picture_book_content`
        where id = #{pictureBookContentId}
    </select>
    <select id="getPictureBookSentenceList" resultType="com.woxue.common.model.redBook.PictureBookSentence">
        select id,
               course_id             courseId,
               content_id            contentId,
               example_en_us         exampleEnUS,
               example_en_us_element exampleEnUSElement,
               example_zh_cn         exampleZhCN,
               example_zh_py         exampleZhPY,
               speaker               speaker,
               sound_file            soundFile,
               sort,
               last_update_time      lastUpdateTime
        from `picture_book_sentence`
        where content_id = #{pictureBookContentId}
        order by sort
    </select>
    <select id="getPictureBookSentence" resultType="com.woxue.common.model.redBook.PictureBookSentence">
        select id,
               course_id             courseId,
               content_id            contentId,
               example_en_us         exampleEnUS,
               example_en_us_element exampleEnUSElement,
               example_zh_cn         exampleZhCN,
               example_zh_py         exampleZhPY,
               speaker               speaker,
               sound_file            soundFile,
               sort,
               last_update_time      lastUpdateTime
        from `picture_book_sentence`
        where id = #{pictureBookSentenceId}
        order by sort
    </select>
    <select id="getAllPictureBookList" resultType="com.woxue.common.model.redBook.PictureBook">
        select id,
               course_id courseId,
               title,
               en_title  enTitle,
               thumbnail,
               desc_ch   desc_Ch,
               desc_en   desc_En,
               level,
               sort
        from `picture_book`
        order by sort
    </select>

    <select id="getWordListByPictureBookId" resultType="com.woxue.common.model.redBook.PictureBookWord">
        select id wordId,
               spelling,
               syllable,
               sound_file soundFile,
               meaning_en_US,
               meaning_zh_CN,
               example_en_US,
               example_zh_CN,
               synonymous,
               img_url,
               modify_time modifyTime
        from `picture_book_word`
        where picture_book_id = #{pictureBookId}
    </select>
</mapper>