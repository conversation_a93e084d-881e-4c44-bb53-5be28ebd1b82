<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper	namespace="com.woxue.resourceservice.dao.IHomonymDao">
	<insert id="add">
        insert into `homonym` (spelling, meaning)
        values (#{spelling}, #{meaning})
    </insert>
	<update id="update">
		update `homonym`
		set meaning=#{meaning}
		where spelling = #{spelling}
	</update>
	<delete id="delete">
		delete from `homonym`
		where spelling=#{spelling}
	</delete>
	<select id="loadHomonymList" resultType="string">
		select spelling from `homonym`
	</select>
	<select id="list" resultType="java.util.Map">
		select * from `homonym`
		<if test="spelling != null and spelling != ''">
			where spelling like concat('%',#{spelling},'%')
		</if>
		limit #{index},#{pageSize}
	</select>
	<select id="count" resultType="java.lang.Integer">
		select count(*) from `homonym`
		<if test="spelling != null and spelling != ''">
			where spelling like concat('%',#{spelling},'%')
		</if>
	</select>


</mapper>