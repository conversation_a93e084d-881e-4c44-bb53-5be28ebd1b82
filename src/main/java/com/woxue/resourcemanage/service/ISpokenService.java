package com.woxue.resourcemanage.service;

import com.woxue.common.model.redBook.spoken.ResourceSpokenTopic;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent;

import java.util.List;

public interface ISpokenService {

    List<ResourceSpokenTopic> getAllResourceSpokenTopic();

    int insertTopic(String name,String sense,String cover,String videoUrl,String desc,Integer order);

    int insertTopicContent(ResourceSpokenTopicContent resourceSpokenTopicContent);

    int updateTopic(int id,String name,String sense,String cover,String videoUrl,String desc,Integer order);

    int updateTopicContent(ResourceSpokenTopicContent resourceSpokenTopicContent);

    int deleteTopic(int topicId);

    int deleteTopicContent(int id);

    List<ResourceSpokenTopicContent> getResourceSpokenTopicContentByTopicId(int topicId);

    boolean publishTopic();

}
