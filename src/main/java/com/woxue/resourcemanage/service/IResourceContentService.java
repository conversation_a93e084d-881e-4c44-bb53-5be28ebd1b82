package com.woxue.resourcemanage.service;


import com.woxue.resourcemanage.entity.SentenceBean;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-31 09:51
 */
public interface IResourceContentService {
    boolean insertContentWord(Integer resourceUnitId, Integer level, String programName, String unitName, String showName, Integer wordCount, Integer hasExampleSentence);
    boolean insertContentSentence(Integer resourceUnitId, String programName, String unitName, String showName, Integer sentenceCount);
    boolean insertContentGrammar(Integer resourceUnitId, Integer grammarCourseId, Integer grammarUnitId, String showName, Integer knowledgeCount);
    boolean insertContentQuestion(Integer resourceUnitId, Integer level, Integer syncQuestionCourseId, Integer syncQuestionUnitId, String showName, Integer paperId);


    boolean updateContentWord(String programName, String unitName, String showName, Integer wordCount, Integer hasExampleSentence, Integer id);
    boolean updateContentSentence(String programName, String unitName, String showName, Integer sentenceCount, Integer id);
    boolean updateContentGrammar(Integer grammarCourseId, Integer grammarUnitId, String showName, Integer knowledgeCount, Integer id);
    boolean updateContentQuestion(Integer syncQuestionCourseId, Integer syncQuestionUnitId, String showName, Integer paperId, Integer id);

    boolean delContentWord(Integer id);
    boolean delContentSentence(Integer id);
    boolean delContentQuestion(Integer id);
    boolean delContentGrammar(Integer id);

    /*List<Map<String,Object>> getWordShowName(Integer resourceUnitId);
    List<Map<String,Object>> getQuestionShowName(Integer resourceUnitId);

    Map<String,Object> getGrammarShowName(Integer resourceUnitId);
    Map<String,Object> getSentenceShowName(Integer resourceUnitId);*/

    /**
     * 根据课文Id获取课文分段句子
     * @param articleId
     * @return
     */
    List<SentenceBean> getArticleContentList(Integer articleId);

    /**
     * 批量修改课文
     * @param contentList
     * @return                       
     */
    boolean updateArticleContentList(List<SentenceBean> contentList);
}
