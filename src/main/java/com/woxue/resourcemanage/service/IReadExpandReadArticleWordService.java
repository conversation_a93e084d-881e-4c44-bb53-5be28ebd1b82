package com.woxue.resourcemanage.service;



import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean;

import java.util.List;

/**
 * <p>
 * 扩展阅读-文章重点单词 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface IReadExpandReadArticleWordService  {
    List<ReadExpandReadArticleWordBean> list(Integer articleId);

    ReadExpandReadArticleWordBean edit(Integer wordId);

    int save(ReadExpandReadArticleWordBean readexpandReadArticleWordBean);

    int batchSave(List<ReadExpandReadArticleWordBean> wordBeanList);

    int update(ReadExpandReadArticleWordBean readexpandReadArticleWordBean);

    int batchSaveOrUpdateWord(List<ReadExpandReadArticleWordBean> wordBeanList);

    int batchUpdate(List<ReadExpandReadArticleWordBean> wordBeanList);

    int delete(Integer wordId);
}
