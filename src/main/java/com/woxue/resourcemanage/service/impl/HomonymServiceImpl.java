package com.woxue.resourcemanage.service.impl;

import com.woxue.resourcemanage.service.IHomonymService;
import com.woxue.resourceservice.dao.IHomonymDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
@Service
public class HomonymServiceImpl implements IHomonymService {

    @Autowired
    IHomonymDao homonymDao;
    @Override
    public List<Map> list(String spelling, int index, Integer pageSize) {
        return homonymDao.list(spelling,index,pageSize);
    }

    @Override
    public Integer count(String spelling) {
        return homonymDao.count(spelling);
    }

    @Override
    public Boolean add(String spelling, String meaning) {
        return homonymDao.add(spelling,meaning);
    }

    @Override
    public Boolean update(String spelling, String meaning) {
        return homonymDao.update(spelling,meaning);
    }

    @Override
    public Boolean delete(String spelling) {
        return homonymDao.delete(spelling);
    }
}
