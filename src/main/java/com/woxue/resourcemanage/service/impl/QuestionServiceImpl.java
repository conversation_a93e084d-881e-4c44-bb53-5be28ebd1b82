package com.woxue.resourcemanage.service.impl;

import com.woxue.resourcemanage.dao.IResourceContentDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.service.IQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class QuestionServiceImpl implements IQuestionService {

    @Autowired
    IResourceContentDao resourceContentDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;

    @Override
    public List<Map<String, Object>> getUnitList(Integer courseId) {
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, null, null);
        for (Map<String, Object> stringObjectMap : unitList) {
            Integer resourceUnitId = (Integer) stringObjectMap.get("id");
            stringObjectMap.put("questionContent", resourceContentDao.getQuestionShowName(resourceUnitId));
        }
        return unitList;
    }
}
