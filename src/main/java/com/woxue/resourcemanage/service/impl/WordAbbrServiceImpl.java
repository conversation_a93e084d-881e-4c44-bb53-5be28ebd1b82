package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.WordAbbr;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.service.IWordAbbrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class WordAbbrServiceImpl implements IWordAbbrService {

    @Autowired
    IResourceWordDao wordDao;

    @Override
    public List<WordAbbr> list() {
        return wordDao.queryWordAbbr();
    }

    @Override
    public boolean addWordAbbr(WordAbbr wordAbbr) {
        return wordDao.addWordAbbr(wordAbbr)>0;
    }

    @Override
    public boolean updateWordAbbr(WordAbbr wordAbbr) {
        return wordDao.updateWordAbbr(wordAbbr)>0;
    }
}
