package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean;
import com.woxue.resourcemanage.dao.ReadExpandReadArticleQuestionDao;
import com.woxue.resourcemanage.service.IReadExpandReadArticleQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 扩展阅读-文章问题 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Service
public class ReadExpandReadArticleQuestionServiceImpl implements IReadExpandReadArticleQuestionService {

    @Autowired
    ReadExpandReadArticleQuestionDao readexpandReadArticleQuestionDao;

    @Override
    public List<ReadExpandReadArticleQuestionBean> list(Integer articleId) {
        return readexpandReadArticleQuestionDao.list(articleId);
    }

    @Override
    public ReadExpandReadArticleQuestionBean edit(Integer questionId) {
        return readexpandReadArticleQuestionDao.edit(questionId);
    }

    @Override
    public int save(ReadExpandReadArticleQuestionBean readexpandReadArticleQuestionBean) {
        return readexpandReadArticleQuestionDao.save(readexpandReadArticleQuestionBean);
    }

    @Override
    public int batchSave(List<ReadExpandReadArticleQuestionBean> readexpandReadArticleQuestionBeanList) {
        return readexpandReadArticleQuestionDao.batchSave(readexpandReadArticleQuestionBeanList);
    }

    @Override
    public int update(ReadExpandReadArticleQuestionBean readexpandReadArticleQuestionBean) {
        return readexpandReadArticleQuestionDao.update(readexpandReadArticleQuestionBean);
    }
}
