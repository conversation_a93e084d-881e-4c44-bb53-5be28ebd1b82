package com.woxue.resourcemanage.service.impl;

import com.redbook.kid.common.model.phonics.*;
import com.woxue.resourcemanage.service.KidPhonicsService;
import com.woxue.resourceservice.util.KidPhonicsManager;
import com.woxue.resourceservice.dao.KidPhonicsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 少儿自然拼读资源管理服务实现类
 */
@Service
public class KidPhonicsServiceImpl implements KidPhonicsService {

    private static final Logger logger = LoggerFactory.getLogger(KidPhonicsServiceImpl.class);

    @Autowired
    private KidPhonicsMapper kidPhonicsMapper;

    // 课程管理
    @Override
    @Transactional
    public void insertCourse(KidPhonicsCourse course) {
        if (course.getBranch() == null) {
            course.setBranch(1); // 默认分支为1
        }
        if (course.getPublishStatus() == null) {
            course.setPublishStatus(0); // 默认未发布
        }
        kidPhonicsMapper.insertCourse(course);
    }

    @Override
    public KidPhonicsCourse getCourseById(Integer id) {
        return kidPhonicsMapper.getCourseById(id);
    }

    @Override
    @Transactional
    public void updateCourse(KidPhonicsCourse course) {
        kidPhonicsMapper.updateCourse(course);
    }

    @Override
    @Transactional
    public void deleteCourse(Integer id) {
        kidPhonicsMapper.deleteCourse(id);
    }

    @Override
    public List<KidPhonicsCourse> getAllCourses() {
        return kidPhonicsMapper.getAllCourses();
    }

    // 单元管理
    @Override
    @Transactional
    public void insertUnit(KidPhonicsUnit unit) {
        if (unit.getStatus() == null) {
            unit.setStatus(1); // 默认启用
        }
        kidPhonicsMapper.insertUnit(unit);
    }

    @Override
    public KidPhonicsUnit getUnitById(Integer id) {
        return kidPhonicsMapper.getUnitById(id);
    }

    @Override
    @Transactional
    public void updateUnit(KidPhonicsUnit unit) {
        kidPhonicsMapper.updateUnit(unit);
    }

    @Override
    @Transactional
    public void deleteUnit(Integer id) {
        kidPhonicsMapper.deleteUnit(id);
    }

    @Override
    public List<KidPhonicsUnit> getUnitsByCourseId(Integer courseId) {
        return kidPhonicsMapper.getUnitsByCourseId(courseId);
    }

    @Override
    @Transactional
    public void updateUnitStatus(Integer id, Integer status) {
        kidPhonicsMapper.updateUnitStatus(id, status);
    }

    // 音素管理
    @Override
    @Transactional
    public void insertLetter(KidPhonicsLetter letter) {
        if (letter.getStatus() == null) {
            letter.setStatus(1); // 默认启用
        }
        kidPhonicsMapper.insertLetter(letter);
    }

    @Override
    public KidPhonicsLetter getLetterById(Integer id) {
        return kidPhonicsMapper.getLetterById(id);
    }

    @Override
    @Transactional
    public void updateLetter(KidPhonicsLetter letter) {
        kidPhonicsMapper.updateLetter(letter);
    }

    @Override
    @Transactional
    public void deleteLetter(Integer id) {
        kidPhonicsMapper.deleteLetter(id);
    }

    @Override
    public List<KidPhonicsLetter> getLettersByUnitId(Integer unitId) {
        return kidPhonicsMapper.getLettersByUnitId(unitId);
    }

    // 音素组件管理
    @Override
    @Transactional
    public void insertLetterComponent(KidPhonicsLetterComponent component) {
        kidPhonicsMapper.insertLetterComponent(component);
    }

    @Override
    public KidPhonicsLetterComponent getLetterComponentById(Integer id) {
        return kidPhonicsMapper.getLetterComponentById(id);
    }

    @Override
    @Transactional
    public void updateLetterComponent(KidPhonicsLetterComponent component) {
        kidPhonicsMapper.updateLetterComponent(component);
    }

    @Override
    @Transactional
    public void deleteLetterComponent(Integer id) {
        kidPhonicsMapper.deleteLetterComponent(id);
    }

    @Override
    public List<KidPhonicsLetterComponent> getComponentsByLetterId(Integer letterId) {
        return kidPhonicsMapper.getComponentsByLetterId(letterId);
    }

    // 例词管理
    @Override
    @Transactional
    public void insertWord(KidPhonicsWord word) {
        if (word.getStatus() == null) {
            word.setStatus(1); // 默认启用
        }
        kidPhonicsMapper.insertWord(word);
    }

    @Override
    public KidPhonicsWord getWordById(Integer id) {
        return kidPhonicsMapper.getWordById(id);
    }

    @Override
    @Transactional
    public void updateWord(KidPhonicsWord word) {
        kidPhonicsMapper.updateWord(word);
    }

    @Override
    @Transactional
    public void deleteWord(Integer id) {
        kidPhonicsMapper.deleteWord(id);
    }

    @Override
    public List<KidPhonicsWord> getWordsByLetterId(Integer letterId) {
        return kidPhonicsMapper.getWordsByLetterId(letterId);
    }

    // 儿歌管理
    @Override
    @Transactional
    public void insertRhyme(KidPhonicsRhyme rhyme) {
        if (rhyme.getStatus() == null) {
            rhyme.setStatus(1); // 默认启用
        }
        kidPhonicsMapper.insertRhyme(rhyme);
    }

    @Override
    public KidPhonicsRhyme getRhymeById(Integer id) {
        return kidPhonicsMapper.getRhymeById(id);
    }

    @Override
    @Transactional
    public void updateRhyme(KidPhonicsRhyme rhyme) {
        kidPhonicsMapper.updateRhyme(rhyme);
    }

    @Override
    @Transactional
    public void deleteRhyme(Integer id) {
        kidPhonicsMapper.deleteRhyme(id);
    }

    @Override
    public List<KidPhonicsRhyme> getRhymesByUnitId(Integer unitId) {
        return kidPhonicsMapper.getRhymesByUnitId(unitId);
    }

    // 绘本管理
    @Override
    @Transactional
    public void insertPictureBook(KidPhonicsPictureBook pictureBook) {
        if (pictureBook.getStatus() == null) {
            pictureBook.setStatus(1); // 默认启用
        }
        kidPhonicsMapper.insertPictureBook(pictureBook);
    }

    @Override
    public KidPhonicsPictureBook getPictureBookById(Integer id) {
        return kidPhonicsMapper.getPictureBookById(id);
    }

    @Override
    @Transactional
    public void updatePictureBook(KidPhonicsPictureBook pictureBook) {
        kidPhonicsMapper.updatePictureBook(pictureBook);
    }

    @Override
    @Transactional
    public void deletePictureBook(Integer id) {
        kidPhonicsMapper.deletePictureBook(id);
    }

    @Override
    public List<KidPhonicsPictureBook> getPictureBooksByUnitId(Integer unitId) {
        return kidPhonicsMapper.getPictureBooksByUnitId(unitId);
    }

    // 绘本内容管理
    @Override
    @Transactional
    public void insertPictureBookContent(KidPhonicsPictureBookContent content) {
        kidPhonicsMapper.insertPictureBookContent(content);
    }

    @Override
    public KidPhonicsPictureBookContent getPictureBookContentById(Integer id) {
        return kidPhonicsMapper.getPictureBookContentById(id);
    }

    @Override
    @Transactional
    public void updatePictureBookContent(KidPhonicsPictureBookContent content) {
        kidPhonicsMapper.updatePictureBookContent(content);
    }

    @Override
    @Transactional
    public void deletePictureBookContent(Integer id) {
        kidPhonicsMapper.deletePictureBookContent(id);
    }

    @Override
    public List<KidPhonicsPictureBookContent> getPictureBookContentsByPictureBookId(Integer pictureBookId) {
        return kidPhonicsMapper.getPictureBookContentsByPictureBookId(pictureBookId);
    }

    // 绘本句子管理
    @Override
    @Transactional
    public void insertPictureBookSentence(KidPhonicsPictureBookSentence sentence) {
        kidPhonicsMapper.insertPictureBookSentence(sentence);
    }

    @Override
    public KidPhonicsPictureBookSentence getPictureBookSentenceById(Integer id) {
        return kidPhonicsMapper.getPictureBookSentenceById(id);
    }

    @Override
    @Transactional
    public void updatePictureBookSentence(KidPhonicsPictureBookSentence sentence) {
        kidPhonicsMapper.updatePictureBookSentence(sentence);
    }

    @Override
    @Transactional
    public void deletePictureBookSentence(Integer id) {
        kidPhonicsMapper.deletePictureBookSentence(id);
    }

    @Override
    public List<KidPhonicsPictureBookSentence> getPictureBookSentencesByContentId(Integer contentId) {
        return kidPhonicsMapper.getPictureBookSentencesByContentId(contentId);
    }

    // 发布管理
    @Override
    @Transactional
    public void insertPublishRecord(KidPhonicsPublishRecord record) {
        kidPhonicsMapper.insertPublishRecord(record);
    }

    @Override
    public KidPhonicsPublishRecord getPublishRecordById(Integer id) {
        return kidPhonicsMapper.getPublishRecordById(id);
    }

    @Override
    public List<KidPhonicsPublishRecord> getPublishRecordsByCourseId(Integer courseId) {
        return kidPhonicsMapper.getPublishRecordsByCourseId(courseId);
    }

    @Override
    @Transactional
    public void publishCourse(Integer courseId, String publisherName, String remark) {
        // 获取课程
        KidPhonicsCourse course = kidPhonicsMapper.getCourseById(courseId);
        if (course == null) {
            throw new IllegalArgumentException("课程不存在，ID: " + courseId);
        }

        // 增加分支号
        Integer newBranch = course.getBranch() + 1;
        Date publishTime = new Date();
        
        // 更新课程发布信息
        kidPhonicsMapper.updateCoursePublishInfo(courseId, newBranch, publishTime);
        
        // 创建发布记录
        KidPhonicsPublishRecord record = new KidPhonicsPublishRecord();
        record.setCourseId(courseId);
        record.setBranch(newBranch);
        record.setPublishTime(publishTime);
        record.setPublisher(publisherName);
        record.setRemark(remark);
        
        kidPhonicsMapper.insertPublishRecord(record);
        
        // 缓存资源到Redis
        try {
            KidPhonicsManager.publishKidPhonicsResource(courseId);
            logger.info("课程资源缓存成功，课程ID: {}", courseId);
        } catch (Exception e) {
            logger.error("缓存课程资源异常，课程ID: {}", courseId, e);
            // 不中断发布过程，但记录错误
        }
    }

    @Override
    public KidPhonicsUnit loadUnitWithResources(KidPhonicsUnit unit) {
        if (unit == null) {
            return null;
        }
        
        // 加载音素列表
        List<KidPhonicsLetter> letters = kidPhonicsMapper.getLettersByUnitId(unit.getId());
        if (letters != null && !letters.isEmpty()) {
            // 为每个音素加载组件和例词
            for (KidPhonicsLetter letter : letters) {
                if (letter.getIsCombination() != null && letter.getIsCombination() == 1) {
                    letter.setComponentList(kidPhonicsMapper.getComponentsByLetterId(letter.getId()));
                }
                letter.setWordList(kidPhonicsMapper.getWordsByLetterId(letter.getId()));
            }
        }
        unit.setLetterList(letters);
        
        // 加载儿歌列表
        List<KidPhonicsRhyme> rhymes = kidPhonicsMapper.getRhymesByUnitId(unit.getId());
        if (rhymes != null && !rhymes.isEmpty()) {
            unit.setRhymeList(rhymes);
        }
        
        // 加载绘本列表
        List<KidPhonicsPictureBook> pictureBooks = kidPhonicsMapper.getPictureBooksByUnitId(unit.getId());
        if (pictureBooks != null && !pictureBooks.isEmpty()) {
            for (KidPhonicsPictureBook pictureBook : pictureBooks) {
                loadPictureBookWithContents(pictureBook);
            }
        }
        
        return unit;
    }

    @Override
    public KidPhonicsPictureBook loadPictureBookWithContents(KidPhonicsPictureBook pictureBook) {
        if (pictureBook == null) {
            return null;
        }
        
        // 加载绘本内容
        List<KidPhonicsPictureBookContent> contents = kidPhonicsMapper.getPictureBookContentsByPictureBookId(pictureBook.getId());
        if (contents != null && !contents.isEmpty()) {
            // 为每个内容加载句子
            for (KidPhonicsPictureBookContent content : contents) {
                List<KidPhonicsPictureBookSentence> sentences = kidPhonicsMapper.getPictureBookSentencesByContentId(content.getId());
                content.setSentenceList(sentences);
            }
        }
        pictureBook.setContentList(contents);
        
        return pictureBook;
    }
} 