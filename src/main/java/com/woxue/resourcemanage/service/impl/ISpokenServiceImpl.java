package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.spoken.ResourceSpokenTopic;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent;
import com.woxue.resourcemanage.dao.ISpokenDao;
import com.woxue.resourcemanage.service.ISpokenService;
import com.woxue.resourceservice.util.ResourceSpokenManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class ISpokenServiceImpl implements ISpokenService {

    @Autowired
    ISpokenDao iSpokenDao;

    @Override
    public List<ResourceSpokenTopic> getAllResourceSpokenTopic() {
        return iSpokenDao.getAllResourceSpokenTopic();
    }

    @Override
    public int insertTopic(String name, String sense, String cover,String videoUrl, String desc,Integer order) {
        return iSpokenDao.insertTopic(name,sense,cover,videoUrl,desc,order);
    }

    @Override
    public int insertTopicContent(ResourceSpokenTopicContent resourceSpokenTopicContent) {
        return iSpokenDao.insertTopicContent(resourceSpokenTopicContent);
    }

    @Override
    public int updateTopic(int id, String name, String sense, String cover,String videoUrl, String desc,Integer order) {
        return iSpokenDao.updateTopic(id,name,sense,cover,videoUrl,desc,order);
    }

    @Override
    public int updateTopicContent(ResourceSpokenTopicContent resourceSpokenTopicContent) {
        return iSpokenDao.updateTopicContent(resourceSpokenTopicContent);
    }

    @Override
    public int deleteTopic(int topicId) {
        return iSpokenDao.deleteTopic(topicId);
    }

    @Override
    public int deleteTopicContent(int id) {
        return iSpokenDao.deleteTopicContent(id);
    }

    @Override
    public List<ResourceSpokenTopicContent> getResourceSpokenTopicContentByTopicId(int topicId) {
        return iSpokenDao.getResourceSpokenTopicContentByTopicId(topicId);
    }

    @Override
    public boolean publishTopic() {
        ResourceSpokenManager.reloadResourceSpoken();
        return true;
    }
}
