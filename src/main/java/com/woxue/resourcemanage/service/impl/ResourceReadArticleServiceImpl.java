package com.woxue.resourcemanage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.woxue.ai.model.*;
import com.woxue.ai.util.AIReadUtil;
import com.woxue.ai.util.HttpClientPool;
import com.woxue.ai.util.StreamUtil;
import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.*;
import com.woxue.common.model.redBook.read.*;
import com.woxue.common.util.GsonManager;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.OSSManager;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.dao.*;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.read.*;
import com.woxue.resourcemanage.entity.read.*;
import com.woxue.resourcemanage.entity.vo.read.ArticleSerialCodeListVO;
import com.woxue.resourcemanage.entity.vo.read.TopicUnitArticleListVO;
import com.woxue.resourcemanage.service.IResourceReadArticleService;
import com.woxue.resourcemanage.service.IResourceUnitService;
import com.woxue.resourcemanage.util.RedBookStringUtil;
import com.woxue.resourceservice.util.RedisManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
@Service
public class ResourceReadArticleServiceImpl implements IResourceReadArticleService {

    @Autowired
    IResourceReadArticleDao resourceReadArticleDao;
    @Autowired
    IResourceReadArticleQuestionDao resourceReadArticleQuestionDao;

    @Autowired
    IResourceReadArticleCorrelationDao resourceReadArticleCorrelationDao;

    @Autowired
    IResourceReadArticleSelectWordDao resourceReadArticleSelectWordDao;

    @Autowired
    IResourceReadArticleHighWordDao resourceReadArticleHignWordDao;
    @Autowired
    IResourceReadArticleHighSentenceDao resourceReadArticleHignSentenceDao;
    @Autowired
    IResourceUnitContentReadDao resourceUnitContentReadDao;
    @Autowired
    IRedBookCourseService redBookCourseService;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;
    @Autowired
    IResourceUnitService resourceUnitService;
    @Autowired
    IResourceWordDao resourceWordDao;

    private final String IMAGE_SOURCE_DIR_NAME = "newReadUploadSource";
    private final String IMAGE_DIR_NAME = "newReadUpload";
    private final String READ_ARTICLE_CORRELATION_REDIS_KEY = "readArticleCorrelation:articleId:";

    @Override
    public Map<String, Object> listByCourseId(Integer courseId, Integer pageNum, Integer pageSize) {
        Map<String,Object> course = resourceCourseDao.getCourseById(courseId);
        Integer stage = (Integer)course.get("stage");
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, pageNum, pageSize);
        for (Map<String, Object> stringObjectMap : unitList) {
            Integer resourceUnitId = (Integer) stringObjectMap.get("id");
            List<ArticleSerialCodeListVO> articleSerialCodeListVO = this.getArticleSerialCodeListVO(resourceUnitId,stage);
            stringObjectMap.put("articleList",articleSerialCodeListVO);
            ResourceUnitContentReadBean resourceUnitContentReadBean = resourceUnitContentReadDao.editByUnitId(resourceUnitId);
            if(resourceUnitContentReadBean != null){
                stringObjectMap.put("topicId",resourceUnitContentReadBean.getTopicId());
                stringObjectMap.put("topicName",resourceUnitContentReadBean.getTopicName());
            }else {
                stringObjectMap.put("topicId",null);
                stringObjectMap.put("topicName",null);
            }
        }
        Integer unitCount = resourceUnitDao.getUnitCount(courseId);
        Map<String, Object> map = new HashMap<>();
        map.put("unitList", unitList);
        map.put("allCount", unitCount);
        return map;
    }

    @Override
    public List<ResourceReadArticleBean> list(Integer unitId) {
        return resourceReadArticleDao.listByUnitId(unitId);
    }

    public List<ArticleSerialCodeListVO> getArticleSerialCodeListVO(Integer unitId,int stage){
        List<ArticleSerialCodeListVO> list = new ArrayList<>();
        ArticleSerialCodeListVO articleSerialCodeListVO1 = new ArticleSerialCodeListVO();
        articleSerialCodeListVO1.setSerialCode("A");
        ResourceReadArticleBean a = resourceReadArticleDao.editBySerialCode(unitId, "A");
        if(a != null){
            articleSerialCodeListVO1.setArticleId(a.getArticleId());
        }

        ArticleSerialCodeListVO articleSerialCodeListVO2 = new ArticleSerialCodeListVO();
        articleSerialCodeListVO2.setSerialCode("B");
        ResourceReadArticleBean b = resourceReadArticleDao.editBySerialCode(unitId, "B");
        if(b != null){
            articleSerialCodeListVO2.setArticleId(b.getArticleId());
        }

        ArticleSerialCodeListVO articleSerialCodeListVO3 = new ArticleSerialCodeListVO();
        articleSerialCodeListVO3.setSerialCode("C");
        ResourceReadArticleBean c = resourceReadArticleDao.editBySerialCode(unitId, "C");
        if(c != null){
            articleSerialCodeListVO3.setArticleId(c.getArticleId());
        }

        ArticleSerialCodeListVO articleSerialCodeListVO4 = new ArticleSerialCodeListVO();
        articleSerialCodeListVO4.setSerialCode("D");
        ResourceReadArticleBean d = resourceReadArticleDao.editBySerialCode(unitId, "D");
        if(d != null){
            articleSerialCodeListVO4.setArticleId(d.getArticleId());
        }

        list.add(articleSerialCodeListVO1);
        list.add(articleSerialCodeListVO2);
        list.add(articleSerialCodeListVO3);
        list.add(articleSerialCodeListVO4);
        if(stage == 3){
            ArticleSerialCodeListVO articleSerialCodeListVO5 = new ArticleSerialCodeListVO();
            articleSerialCodeListVO5.setSerialCode("E");
            ResourceReadArticleBean e = resourceReadArticleDao.editBySerialCode(unitId, "E");
            if(e != null){
                articleSerialCodeListVO5.setArticleId(e.getArticleId());
            }
            list.add(articleSerialCodeListVO5);
        }
        return list;
    }


    @Override
    public ResourceReadArticleNewDTO edit(Integer articleId) {
        ResourceReadArticleNewDTO resourceReadArticleDTO = new ResourceReadArticleNewDTO();

        ResourceReadArticleBean edit = resourceReadArticleDao.edit(articleId);
        ResourceReadArticleCorrelationBean resourceReadArticleCorrelationBeans = resourceReadArticleCorrelationDao.editByArticleId(articleId);
        List<ResourceReadArticleQuestionBean> questionBeans = resourceReadArticleQuestionDao.listByArticleId(articleId);
        ResourceReadArticleSelectWordBean resourceReadArticleSelectWordBean = resourceReadArticleSelectWordDao.editByArticleId(articleId);
        List<ResourceReadArticleHighWordBean> highWordBeans = resourceReadArticleHignWordDao.listByArticleId(articleId);
        List<ResourceReadArticleHighSentenceBean> sentenceBeans = resourceReadArticleHignSentenceDao.listByArticleId(articleId);

        BeanUtils.copyProperties(edit,resourceReadArticleDTO);
        if(edit.getAiGenWord() != null){
            resourceReadArticleDTO.setAiGenWordList(Arrays.asList(edit.getAiGenWord().split("\\|")));
        }
        //句句对应拆句
        if(resourceReadArticleCorrelationBeans != null){
            if(resourceReadArticleCorrelationBeans.getContentList() != null){
                List<String> list = GsonManager.fromJson(resourceReadArticleCorrelationBeans.getContentList(), List.class);
                resourceReadArticleDTO.setCorrelationContentList(list);
            }
            if(resourceReadArticleCorrelationBeans.getTranslateList() != null){
                List<String> list = GsonManager.fromJson(resourceReadArticleCorrelationBeans.getTranslateList(), List.class);
                resourceReadArticleDTO.setCorrelationTranslateList(list);
            }
        }
        resourceReadArticleDTO.setCorrelationBean(resourceReadArticleCorrelationBeans);
        resourceReadArticleDTO.setQuestionBeanList(questionBeans);
        resourceReadArticleDTO.setSelectWordBean(resourceReadArticleSelectWordBean);
        resourceReadArticleDTO.setHighWordBeanList(highWordBeans);
        resourceReadArticleDTO.setHighSentenceBeanList(sentenceBeans);
        resourceReadArticleDTO.setAnalysisResponse(GsonManager.fromJson(edit.getFullTextParse(), AnalysisResponse.class));
        return resourceReadArticleDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveOrUpdate(ResourceReadArticleNewDTO resourceReadArticleDTO) {
        Map<String,Object> unit = resourceUnitDao.getUnitById(resourceReadArticleDTO.getUnitId());
        Integer courseId = (Integer) unit.get("course_id");
        Map<String,Object> course = resourceCourseDao.getCourseById(courseId);
        Integer versionId = (Integer) course.get("version_id");
        Integer stage = (Integer) course.get("stage");

        Integer articleId = resourceReadArticleDTO.getArticleId();

        resourceReadArticleDTO.setVersionId(versionId);
        resourceReadArticleDTO.setCourseId(courseId);
        //初中：句句对应、多个题目、选词填空、选句填空
        //高中：多个题目、多个难词、多个难句
        //高中七选五：选句填空
        ResourceReadArticleCorrelationBean correlationBean = resourceReadArticleDTO.getCorrelationBean();
        List<ResourceReadArticleQuestionBean> questionBeanList = resourceReadArticleDTO.getQuestionBeanList();
        ResourceReadArticleSelectWordBean selectWordBean = resourceReadArticleDTO.getSelectWordBean();
        List<ResourceReadArticleHighWordBean> highWordBeanList = resourceReadArticleDTO.getHighWordBeanList();
        List<ResourceReadArticleHighSentenceBean> highSentenceBeanList = resourceReadArticleDTO.getHighSentenceBeanList();
        //处理句句对应没有\n的问题
        List<String> correlationContentList = resourceReadArticleDTO.getCorrelationContentList();
        List<String> correlationTranslateList = resourceReadArticleDTO.getCorrelationTranslateList();
        if(correlationContentList != null){
            String content = correlationContentList.stream().map(item -> {
                if (!item.contains("\n")) {
                    return item + "\n";
                }
                return item;
            }).collect(Collectors.joining());
            correlationBean.setContent(content);
        }
        if(correlationTranslateList != null){
            String translate = correlationTranslateList.stream().map(item -> {
                if (!item.contains("\n")) {
                    return item + "\n";
                }
                return item;
            }).collect(Collectors.joining());
            correlationBean.setTranslate(translate);
        }

        //通过getArticleId是否为null，判断是新增还是编辑
        if(resourceReadArticleDTO.getArticleId() == null){
            //新增
            //插入文章（包含选句填空）
            ResourceReadArticleBean resourceReadArticleBean = new ResourceReadArticleBean();
            BeanUtils.copyProperties(resourceReadArticleDTO,resourceReadArticleBean);
            resourceReadArticleBean.setFullTextParse(GsonManager.toJson(resourceReadArticleDTO.getAnalysisResponse()));
            resourceReadArticleDao.save(resourceReadArticleBean);

            articleId = resourceReadArticleBean.getArticleId();

            if(correlationBean != null){
                //插入句句对应
                correlationBean.setArticleId(resourceReadArticleBean.getArticleId());
                correlationBean.setContentList(GsonManager.toJson(resourceReadArticleDTO.getCorrelationContentList()));
                correlationBean.setTranslateList(GsonManager.toJson(resourceReadArticleDTO.getCorrelationTranslateList()));
                resourceReadArticleCorrelationDao.save(correlationBean);
            }

            if(questionBeanList != null && questionBeanList.size() > 0){
                //避免导入问题
                questionBeanList = questionBeanList.stream().filter(item -> item.getQuestion() != null && !item.getQuestion().equals("")).collect(Collectors.toList());
                //插入多个题目
                List<ResourceReadArticleQuestionBean> collect = questionBeanList.stream().map(data -> {
                    data.setArticleId(resourceReadArticleBean.getArticleId());
                    data.setOptionA(data.getOptionA().replace("\n","").trim());
                    data.setOptionB(data.getOptionB().replace("\n","").trim());
                    data.setOptionC(data.getOptionC().replace("\n","").trim());
                    data.setOptionD(data.getOptionD().replace("\n","").trim());
                    return data;
                }).collect(Collectors.toList());
                resourceReadArticleQuestionDao.batchSave(collect);
            }

            if(selectWordBean != null){
                //插入选词填空
                selectWordBean.setArticleId(resourceReadArticleBean.getArticleId());
                resourceReadArticleSelectWordDao.save(selectWordBean);
            }

            if(highWordBeanList != null && highWordBeanList.size() > 0){
                //插入多个难词
                List<ResourceReadArticleHighWordBean> highWordBeans = highWordBeanList.stream().map(data -> {
                    data.setArticleId(resourceReadArticleBean.getArticleId());
                    return data;
                }).collect(Collectors.toList());
                resourceReadArticleHignWordDao.batchSave(highWordBeans);
            }

            if(highSentenceBeanList != null && highSentenceBeanList.size() > 0){
                //插入多个难句
                List<ResourceReadArticleHighSentenceBean> sentenceBeans = highSentenceBeanList.stream().map(data -> {
                    data.setArticleId(resourceReadArticleBean.getArticleId());
                    return data;
                }).collect(Collectors.toList());
                resourceReadArticleHignSentenceDao.batchSave(sentenceBeans);
            }


        }else {
            //编辑
            //更新文章（包含选句填空）
            ResourceReadArticleBean resourceReadArticleBean = new ResourceReadArticleBean();
            BeanUtils.copyProperties(resourceReadArticleDTO,resourceReadArticleBean);
            resourceReadArticleBean.setFullTextParse(GsonManager.toJson(resourceReadArticleDTO.getAnalysisResponse()));
            resourceReadArticleDao.update(resourceReadArticleBean);

            if(correlationBean != null){
                correlationBean.setContentList(GsonManager.toJson(resourceReadArticleDTO.getCorrelationContentList()));
                correlationBean.setTranslateList(GsonManager.toJson(resourceReadArticleDTO.getCorrelationTranslateList()));
                if(correlationBean.getCorrelationId() == null){
                    //插入句句对应
                    correlationBean.setArticleId(resourceReadArticleBean.getArticleId());
                    resourceReadArticleCorrelationDao.save(correlationBean);
                }else {
                    //更新句句对应
                    correlationBean.setArticleId(resourceReadArticleBean.getArticleId());
                    resourceReadArticleCorrelationDao.update(correlationBean);
                }
            }

            if(questionBeanList != null && questionBeanList.size() > 0){
                //避免导入问题
                questionBeanList = questionBeanList.stream().filter(item -> item.getQuestion() != null && !item.getQuestion().equals("")).collect(Collectors.toList());
                //先删除，再插入多个题目
//                resourceReadArticleQuestionDao.deleteByArticleId(resourceReadArticleBean.getArticleId());
                List<ResourceReadArticleQuestionBean> collect = questionBeanList.stream().map(data -> {
                    data.setArticleId(resourceReadArticleBean.getArticleId());
                    data.setOptionA(data.getOptionA().replace("\n","").trim());
                    data.setOptionB(data.getOptionB().replace("\n","").trim());
                    data.setOptionC(data.getOptionC().replace("\n","").trim());
                    data.setOptionD(data.getOptionD().replace("\n","").trim());
                    return data;
                }).collect(Collectors.toList());

                List<ResourceReadArticleQuestionBean> saveCollect = collect.stream().filter(data -> data.getQuestionId() == null).collect(Collectors.toList());
                if(saveCollect != null && saveCollect.size() >0){
                    resourceReadArticleQuestionDao.batchSave(saveCollect);
                }
                List<ResourceReadArticleQuestionBean> updateCollect = collect.stream().filter(data -> data.getQuestionId() != null).collect(Collectors.toList());
                if(updateCollect != null && updateCollect.size() >0){
                    updateCollect.forEach(data ->{
                        resourceReadArticleQuestionDao.update(data);
                    });
                }
            }

           if(selectWordBean != null){
               if(selectWordBean.getId() == null){
                   //插入选词填空
                   selectWordBean.setArticleId(resourceReadArticleBean.getArticleId());
                   resourceReadArticleSelectWordDao.save(selectWordBean);
               }else {
                   //更新选词填空
                   selectWordBean.setArticleId(resourceReadArticleBean.getArticleId());
                   resourceReadArticleSelectWordDao.update(selectWordBean);
               }
           }

            if(highWordBeanList != null && highWordBeanList.size() > 0){
                //先删除，再插入多个难词
                resourceReadArticleHignWordDao.deleteByArticleId(resourceReadArticleBean.getArticleId());
                List<ResourceReadArticleHighWordBean> hignWordBeans = highWordBeanList.stream().map(data -> {
                    data.setArticleId(resourceReadArticleBean.getArticleId());
                    return data;
                }).collect(Collectors.toList());
                resourceReadArticleHignWordDao.batchSave(hignWordBeans);
            }

            if(highSentenceBeanList != null && highSentenceBeanList.size() > 0){
                //先删除，再插入多个难句
                resourceReadArticleHignSentenceDao.deleteByArticleId(resourceReadArticleBean.getArticleId());
                List<ResourceReadArticleHighSentenceBean> sentenceBeans = highSentenceBeanList.stream().map(data -> {
                    data.setArticleId(resourceReadArticleBean.getArticleId());
                    return data;
                }).collect(Collectors.toList());
                resourceReadArticleHignSentenceDao.batchSave(sentenceBeans);
            }
        }

        //更新课程上的阅读状态
        List<RedBookCourse> courseList = resourceCourseDao.getCourseList(versionId, stage);
        Map<Integer, Boolean> collect = courseList.stream().collect(Collectors.toMap(RedBookCourse::getId, RedBookCourse::isContainRead));
        if(!collect.get(resourceReadArticleDTO.getCourseId())){
            resourceCourseDao.updateCourseContentContainStatus(resourceReadArticleDTO.getCourseId(), RedBookContentTypeEnum.READ, true);
        }

        return 1;
    }

    @Override
    public Map<String, Object> uploadPhoto(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> error = new HashMap<String, Object>();
        if (upload == null || upload.isEmpty()) {
            error.put("message", "找不到图片源！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        if (upload.getSize() > 2 * 1024 * 1024) {
            error.put("message", "图片尺寸过大！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!".bmp".equals(suffix) && !".jpg".equals(suffix) && !".png".equals(suffix) && !".gif".equals(suffix) && !".jpeg".equals(suffix)) {
            error.put("message", "文件格式不正确（必须为.jpg/.gif/.bmp/.png/.jpeg文件）");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String imageName = new Date().getTime() + suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if (contextPath.endsWith(File.separator)) {
            contextPath = contextPath.substring(0, contextPath.length() - 1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator) + 1);

        String imagePath = contextPath + IMAGE_SOURCE_DIR_NAME + File.separator + unitId;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(imagePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        imagePath += File.separator + imageName;
        String imageUrl = RedBookConstant.UPLOAD_DOMAIN_NAME + "/" + IMAGE_DIR_NAME + "/" + unitId + "/" + imageName;
        try {
            upload.transferTo(new File(imagePath));
            OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME, IMAGE_DIR_NAME + "/" + unitId + "/" + imageName, Files.newInputStream(Paths.get(imagePath)));
            map.put("uploaded", 1);
            map.put("fileName", imageName);
            map.put("url", imageUrl);
            return map;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (new File(imagePath).exists()) new File(imagePath).delete();
            File file1 = new File(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME));
            if (file1.exists()) file1.delete();
        }

        error.put("message", "未知错误！");
        map.put("uploaded", 0);
        map.put("error", error);
        return map;
    }


    @Override
    public List<ResourceTopic> topicList() {
        return resourceReadArticleDao.topicList();
    }

    @Override
    public List<TopicUnitArticleListVO> getTopicUnitArticleList(Integer topicId) {
        List<ResourceReadArticleBean> topicUnitArticleList = resourceReadArticleDao.getTopicUnitArticleList(topicId);
        if(topicUnitArticleList == null || topicUnitArticleList.isEmpty()){
            return new ArrayList<>();
        }
        List<TopicUnitArticleListVO> topicUnitArticleListVOList = new ArrayList<>();
        List<Integer> untiIdList = topicUnitArticleList.stream().map(ResourceReadArticleBean::getUnitId).distinct().collect(Collectors.toList());
        Map<Integer, List<ResourceReadArticleBean>> collect = topicUnitArticleList.stream().collect(Collectors.groupingBy(ResourceReadArticleBean::getUnitId));

        for(Integer unitId : untiIdList){
            List<ResourceReadArticleBean> resourceReadArticleBeanList = collect.get(unitId);
            TopicUnitArticleListVO topicUnitArticleListVO = new TopicUnitArticleListVO();
            topicUnitArticleListVO.setUnitId(unitId);

            RedBookUnit unit = redBookCourseService.getUnit(unitId);
            RedBookCourse course = redBookCourseService.getCourse(unit.getCourseId());
            RedBookVersion versionById = redBookCourseService.getVersionById(course.getVersionId());
            topicUnitArticleListVO.setVersionCourseUnitName(versionById.getNameCn()+"_"+course.getNameCn()+"_"+unit.getNameCn());

            List<TopicUnitArticleListVO.ArticleVO> articleVOList = new ArrayList<>();
            for(ResourceReadArticleBean data : resourceReadArticleBeanList){
                TopicUnitArticleListVO.ArticleVO articleVO = new TopicUnitArticleListVO.ArticleVO();
                articleVO.setArticleId(data.getArticleId());
                articleVO.setContent(data.getContent());
                articleVO.setSerialCode(data.getSerialCode());
                articleVOList.add(articleVO);
            }
            List<TopicUnitArticleListVO.ArticleVO> articleVOS = articleVOList.stream().sorted(Comparator.comparing(TopicUnitArticleListVO.ArticleVO::getSerialCode)).collect(Collectors.toList());
            topicUnitArticleListVO.setArticleVOList(articleVOS);
            topicUnitArticleListVOList.add(topicUnitArticleListVO);
        }
        return topicUnitArticleListVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveTopicUnitArticleList(ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO) {

        ResourceUnitContentReadBean contentReadBean = resourceUnitContentReadDao.editByUnitId(resourceUnitTopicIdPostDTO.getUnitId());
        if(contentReadBean == null){
            contentReadBean = new ResourceUnitContentReadBean();
            contentReadBean.setTopicId(resourceUnitTopicIdPostDTO.getTopicId());
            contentReadBean.setResourceUnitId(resourceUnitTopicIdPostDTO.getUnitId());
            resourceUnitContentReadDao.save(contentReadBean);
        }else {
            contentReadBean.setTopicId(resourceUnitTopicIdPostDTO.getTopicId());
            resourceUnitContentReadDao.update(contentReadBean);
        }

          //初中文章，复制功能
        if(resourceUnitTopicIdPostDTO.getArticleIdList() != null && resourceUnitTopicIdPostDTO.getArticleIdList().size() > 0){

            List<ResourceReadArticleBean> resourceReadArticleBeans = resourceReadArticleDao.listByUnitId(resourceUnitTopicIdPostDTO.getUnitId());
            //原本文章列表为空，直接插入
            if(resourceReadArticleBeans.isEmpty()){
                this.coverArticle(resourceUnitTopicIdPostDTO);
            }else {
                //原本文章列表不为空，先删除后新增
                for(ResourceReadArticleBean articleBean : resourceReadArticleBeans){
                    //删除多个题目
                    resourceReadArticleQuestionDao.deleteByArticleId(articleBean.getArticleId());
                    //删除句句对应
                    resourceReadArticleCorrelationDao.deleteByArticleId(articleBean.getArticleId());
                    //删除选词填空
                    resourceReadArticleSelectWordDao.deleteByArticleId(articleBean.getArticleId());
                }
                //删除原来单元的文章
                resourceReadArticleDao.deleteByUnitId(resourceUnitTopicIdPostDTO.getUnitId());
                this.coverArticle(resourceUnitTopicIdPostDTO);
            }
        }
        return 1;
    }

    @Override
    public ResourceReadArticleDTO aiGenerateArticle(AIGenerateArticleDTO aiGenerateArticleDTO) {
        ReadArticleParam articleParam = new ReadArticleParam();
        articleParam.setTopic(aiGenerateArticleDTO.getTopicName());
        articleParam.setGrade("初中");
//        articleParam.setQtype_list(new String[]{"细节理解题--题干关键信息","细节理解题--选项关键信息","细节理解题--推断人物观点态度、语气、情感基调","主旨大意题"});
        articleParam.setQtype_list(new String[]{"细节理解题--选项关键信息"});
        articleParam.setWords(aiGenerateArticleDTO.getWordList());
        articleParam.setWord_count(aiGenerateArticleDTO.getWordCount());
        articleParam.setRef_article(aiGenerateArticleDTO.getContent());
        articleParam.setRequire_text(aiGenerateArticleDTO.getRequireText());
        //转换的文章
        ReadArticle readArticle = AIReadUtil.words2article(articleParam);
        //生成的多个题目、多个选句、多个选词
        ReadQuestionResponse readQuestionResponse = readArticle.getReadQuestionResponse();
        //生成的配图
        ReadArticleImg readArticleImg = readArticle.getReadArticleImg();

        //返回文章详情DTO
        ResourceReadArticleDTO resourceReadArticleDTO = new ResourceReadArticleDTO();
        resourceReadArticleDTO.setUnitId(aiGenerateArticleDTO.getUnitId());

        //文章内容、翻译
        String source = readArticle.getSource();
        String translation = readArticle.getTranslation();

        resourceReadArticleDTO.setContent(source.replace("|SPLIT|", ""));
        resourceReadArticleDTO.setTranslate(translation.replace("|SPLIT|", ""));

        //句句对应
        ResourceReadArticleCorrelationBean correlationBean = new ResourceReadArticleCorrelationBean();
        String sourceReplace = source.replace("\\n", "\\n\\n").replace("|SPLIT|", "\\n");
//        String translationReplace = translation.replace("\\n", "\\n\\n").replace("|SPLIT|", "\\n");
//        correlationBean.setContent(this.patternSplitContent(sourceReplace));
//        correlationBean.setTranslate(this.patternSplitContent(translationReplace));

        //问题列表
        List<ResourceReadArticleQuestionBean> questionBeanList = new ArrayList<>();
        //AI生成问题列表
        List<ReadQuestionResponse.Reading> readings = readQuestionResponse.getReading();
        for(ReadQuestionResponse.Reading reading : readings){
            String[] choices = reading.getChoices();
            ResourceReadArticleQuestionBean questionBean = new ResourceReadArticleQuestionBean();
            questionBean.setOptionA(choices[0].replace("A. ",""));
            questionBean.setOptionB(choices[1].replace("B. ",""));
            questionBean.setOptionC(choices[2].replace("C. ",""));
            questionBean.setOptionD(choices[3].replace("D. ",""));
            questionBean.setQuestion(reading.getQuestion());
            questionBean.setAnswer(reading.getAnswer());
            questionBean.setParse(reading.getExplanation());
            questionBeanList.add(questionBean);
        }

        //选词填空
        ResourceReadArticleSelectWordBean selectWordBean = new ResourceReadArticleSelectWordBean();
        //AI生成选词填空
        ReadQuestionResponse.WordBlanks wordBlanks = readQuestionResponse.getWord_blanks();
        List<ReadQuestionResponse.WordBlank> wordBlankList = wordBlanks.getCorrect_options();

        List<String> wordInList = new ArrayList<>();
        List<String> wordInSentenceList = new ArrayList<>();

        for(ReadQuestionResponse.WordBlank wordBlank : wordBlankList){
            wordInList.add(wordBlank.getWord_in_list());
            String wordSentence = wordBlank.getWord_sentence();
            String wordInSentence = wordBlank.getWord_in_sentence();
            String wordSentenceReplace = wordSentence.replace(wordInSentence, "|" + wordInSentence);
            source = sourceReplace.replace(wordSentence, wordSentenceReplace);
            wordInSentenceList.add(wordInSentence);
        }
        //选词填空-赋值
        selectWordBean.setContent(source);
        selectWordBean.setWordList(wordInList.stream().collect(Collectors.joining("|")));
        selectWordBean.setWordContent(wordInSentenceList.stream().collect(Collectors.joining("|")));
        selectWordBean.setWordDisturbance(Arrays.asList(wordBlanks.getInterference_options()).stream().collect(Collectors.joining("|")));
        //AI生成选句填空
        ReadQuestionResponse.SentenceBlanks sentenceBlanks = readQuestionResponse.getSentence_blanks();
        String[] correctOptions = sentenceBlanks.getCorrect_options();
        String[] interferenceOptions = sentenceBlanks.getInterference_options();
        //选句填空
        resourceReadArticleDTO.setSentenceList(Arrays.stream(correctOptions).collect(Collectors.joining("|")));
        resourceReadArticleDTO.setSentenceDisturbance(Arrays.stream(interferenceOptions).collect(Collectors.joining("|")));

        resourceReadArticleDTO.setWordCount(Long.valueOf(readArticle.getWord_count()).intValue());
        resourceReadArticleDTO.setPhoto(readArticleImg.getImages()[0]);
        resourceReadArticleDTO.setCorrelationBean(correlationBean);
        resourceReadArticleDTO.setQuestionBeanList(questionBeanList);
        resourceReadArticleDTO.setSelectWordBean(selectWordBean);
        return resourceReadArticleDTO;
    }

    @Override
    public AnalysisResponse aiArticleAnalysis(AIGenerateArticleDTO aiGenerateArticleDTO) {
        return AIReadUtil.generateAnalysis(aiGenerateArticleDTO.getContent());
    }

    @Override
    public String aiGenerateImages(AIGenerateArticleDTO aiGenerateArticleDTO){
        ReadArticleImg readArticleImg;
        try{
            readArticleImg = AIReadUtil.generateArticleImage(aiGenerateArticleDTO.getContent());
            return readArticleImg.getImages()[0];
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 插入文章
     * @param resourceUnitTopicIdPostDTO
     */
    public void coverArticle(ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO){
        List<Integer> articleIdList = resourceUnitTopicIdPostDTO.getArticleIdList();
        for(int i=0;i<articleIdList.size();i++){
            String serialCode = "";
            Integer articleId = articleIdList.get(i);
            if(i == 0){
                serialCode ="A";
            }
            if(i == 1){
                serialCode ="B";
            }
            if(i == 2){
                serialCode ="C";
            }
            if(i == 3){
                serialCode ="D";
            }
            ResourceReadArticleNewDTO readArticleDTO = this.edit(articleId);
            readArticleDTO.setArticleId(null);
            readArticleDTO.setUnitId(resourceUnitTopicIdPostDTO.getUnitId());
            readArticleDTO.setSerialCode(serialCode);
            this.saveOrUpdate(readArticleDTO);
        }
    }


    @Override
    public int insertTopic(ResourceTopic resourceTopicBean) {
        return resourceReadArticleDao.insertTopic(resourceTopicBean);
    }

    @Override
    public ReadCorrelationDTO patternSplitContent(ReadCorrelationDTO readCorrelationDTO) {
        readCorrelationDTO.setContent(patternSplitContent(readCorrelationDTO.getContent()));
        readCorrelationDTO.setTranslation(patternSplitContent(readCorrelationDTO.getTranslation()));
        return readCorrelationDTO;
    }

    @Override
    public ReadCorrelationDTO patternSplitContentV2(ReadCorrelationDTO readCorrelationDTO) {
        readCorrelationDTO.setContentList(patternSplitContentV2(readCorrelationDTO.getContent()));
        readCorrelationDTO.setTranslationList(patternSplitContentV2(readCorrelationDTO.getTranslation()));
        return readCorrelationDTO;
    }

    /**
     * 正则表达式分割内容
     * @param content 内容
     * @return
     */
    public static String patternSplitContent(String content){
        //句子结束符号的正则表达式
        String regex = "[。？！.?!]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> list = new ArrayList<>();
        int start = 0;
        while (matcher.find()) {
            int end = matcher.end(); // 句子的结索位置
            String sentence = content.substring(start, end) .trim();
            list.add(sentence + "\n");
            start = end;
        }
        return list.stream().collect(Collectors.joining()).trim();
    }

    /**
     * 正则表达式分割内容
     * 解析&nbsp;，返回给前端
     * @param content 内容
     * @return
     */
    public static List<String> patternSplitContentV2(String content){
        //句子结束符号的正则表达式
        String regex = "[。？！.?!]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> list = new ArrayList<>();
        int start = 0;
        while (matcher.find()) {
            int end = matcher.end(); // 句子的结索位置
            String sentenceTrim = content.substring(start, end).trim();
            String sentence = content.substring(start, end);
            //段落
//            if(sentence.contains("&nbsp;")){
//                String newSentence = sentence.replaceAll("&nbsp;", "");
//                if(start == 0){
//                    list.add(newSentence + "\n");
//                }else {
//                    //段落之间，隔一行
//                    list.add("\n");
//                    list.add(newSentence + "\n");
//                }
//            }else
            if(sentence.contains("        ")){
                if(start == 0){
                    list.add(sentenceTrim + "\n");
                }else {
                    //段落之间，隔一行
                    list.add("\n");
                    list.add(sentenceTrim + "\n");
                }
            }else {
                //句子
                list.add(sentenceTrim + "\n");
            }
            start = end;
        }
        return list;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importExcel(MultipartFile multipartFile,Integer versionId,
                              Integer courseId, Integer startReadLine) {

        Workbook wb = null;
        File file = null;
        try {
            file= File.createTempFile(multipartFile.getOriginalFilename(),null);
            multipartFile.transferTo(file);file.deleteOnExit();
            wb = WorkbookFactory.create(file);
        }catch (Exception e) {
            e.printStackTrace();
            return "文件解析失败："+e.getMessage();
        }
        //读取excel表中的sheet，参数为sheet的索引值(从0开始)
        Sheet sheet = wb .getSheetAt(0);
        Map<String, Object> courseById = resourceCourseDao.getCourseById(courseId);
        Integer stage = (Integer) courseById.get("stage");
        //总行数
        int lastRowNum = sheet.getLastRowNum();
        //最终确认articleDTOList
        List<ResourceReadArticleNewDTO> articleDTOList = new ArrayList<>();
        //临时使用
        List<ResourceReadArticleNewDTO> temporaryArticleDTOList = new ArrayList<>();
        //题目信息list
        List<ResourceReadArticleQuestionBean> questionDTOList = new ArrayList<>();
        //除了标题无数据
        if(lastRowNum < 1){
            return "资源导入失败，工作表没有数据!";
        }
        //外循环是循环行，内循环是每行的单元格
        for(int i = startReadLine;i <= lastRowNum; i++){
            //每行数据
            Row row = sheet.getRow(i);
            //row为空，忽略
            if(isEmptyRow(row)){
                continue;
            }
            //学段：高中、初中
            Cell firstCell = row.getCell(0);
            //单元标识：Unit
            Cell secondCell = row.getCell(1);

            if(stage == 2 && isNotEmptyCell(firstCell) && "高中".equals(firstCell.getStringCellValue().trim())){
                return "导入解析失败：第"+(i+1)+"行："+ "初中课程不可以导入高中标识文章";
            }
            if(stage == 3 && isNotEmptyCell(firstCell) && "初中".equals(firstCell.getStringCellValue().trim())){
                return "导入解析失败：第"+(i+1)+"行："+ "高中课程不可以导入初中标识文章";
            }

            try {
                if(isNotEmptyCell(firstCell)  && secondCell.getStringCellValue().contains("Unit")){
                    //赋值组装文章DTO
                    ResourceReadArticleNewDTO articleDTO = this.assignArticleDTO(row);
                    if(articleDTO.getSerialCode() == null){
                        return "导入解析失败：第"+(i+1)+"行：文章顺序/编码(A、B、C、D)不能为空";
                    }
                    //文章list不为空
                    if(!temporaryArticleDTOList.isEmpty()) {
                        ResourceReadArticleNewDTO readArticleDTO = temporaryArticleDTOList.get(temporaryArticleDTOList.size() - 1);
                        readArticleDTO.setQuestionBeanList(questionDTOList);
                        articleDTOList.add(readArticleDTO);
                        questionDTOList = new ArrayList<>();
                    }
                    temporaryArticleDTOList.add(articleDTO);
                    //兼容初中最后一篇导入
                    if(articleDTO.getSerialCode() != null && articleDTO.getSerialCode().equals("D") && "初中".equals(firstCell.getStringCellValue().trim())){
                        articleDTOList.add(articleDTO);
                    }
                }else {
                    //此行是题目信息
                    //赋值组装题目选项DTO
                    ResourceReadArticleQuestionBean questionDTO = this.assignArticleQuestionDTO(row);
                    if(questionDTO != null && questionDTO.getQuestion() != null && !questionDTO.getQuestion().equals("")){
                        questionDTOList.add(questionDTO);
                        if(i == lastRowNum-1){
                            ResourceReadArticleNewDTO readArticleDTO = temporaryArticleDTOList.get(temporaryArticleDTOList.size() - 1);
                            readArticleDTO.setQuestionBeanList(questionDTOList);
                            articleDTOList.add(readArticleDTO);
                        }
                    }
                    //兼容高中最后一篇无题目的内容导入
                    ResourceReadArticleNewDTO readArticleDTO = temporaryArticleDTOList.get(temporaryArticleDTOList.size() - 1);
                    if(readArticleDTO.getSerialCode() != null && readArticleDTO.getSerialCode().equals("E") && "高中".equals(firstCell.getStringCellValue().trim())){
                        articleDTOList.add(readArticleDTO);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                return "导入解析失败：第"+(i+1)+"行："+e.getMessage();
            }
        }

        if(articleDTOList.isEmpty()){
            return "资源导入失败，工作表没有合法数据!";
        }

        //处理逻辑
        for(ResourceReadArticleNewDTO item : articleDTOList){
            Integer unitId = resourceUnitService.getUnitByCourse(courseId, item.getUnitName());
            if(unitId == null){
//                resourceUnitService.insertUnit(item.getUnitName(),item.getUnitName(), courseId);
//                unitId = resourceUnitService.getUnitByCourse(courseId, item.getUnitName());
                return "导入解析失败：单元名"+item.getUnitName()+"不存在（注意空格），需要先创建单元并关联主题之后导入";
            }
            ResourceUnitContentReadBean contentReadBean = resourceUnitContentReadDao.editByUnitId(unitId);
            if(contentReadBean == null || contentReadBean.getTopicName() == null){
                return "导入解析失败：单元名"+item.getUnitName()+"未关联主题";
            }
            ResourceReadArticleBean bean = resourceReadArticleDao.editBySerialCode(unitId, item.getSerialCode());
            if(bean != null){
                return "导入解析失败：单元名"+item.getUnitName()+"的文章编码"+item.getSerialCode()+"已存在";
            }
            //赋值组装文章bean
            item.setContent(this.turnParagraph(item.getContent()));
            item.setTranslate(this.turnParagraph(item.getTranslate()));
            item.setVersionId(versionId);
            item.setCourseId(courseId);
            item.setUnitId(unitId);
            //多个难词
            List<String> splitKeyWords = null;
            if(item.getKeyWords() != null){
                //获取单词list
                splitKeyWords = this.splitKeyWords(item.getKeyWords());
                List<ResourceReadArticleHighWordBean> wordBeanList = this.getWordBeanList(splitKeyWords);
                item.setHighWordBeanList(wordBeanList);
            }
            //多个难句
            String keySentences = item.getKeySentences();
            if(keySentences != null){
                List<ResourceReadArticleHighSentenceBean> highSentenceBeanList = this.getKeySentences(keySentences);
                item.setHighSentenceBeanList(highSentenceBeanList);
            }
            //多个选句
            String chooseSentences = item.getChooseSentences();
            if(chooseSentences != null){
                String[] split = chooseSentences.split("#");
                item.setSentenceList(split[0]);
                item.setSentenceDisturbance(split[1]);
            }
            //处理多个题目
            List<ResourceReadArticleQuestionBean> questionBeanList = item.getQuestionBeanList();
            if(questionBeanList != null){
                questionBeanList.forEach(questionBean ->{
                    Map<String,String> optionABCDMap = new HashMap<>();
                    optionABCDMap.put(questionBean.getOptionA(),"A");
                    optionABCDMap.put(questionBean.getOptionB(),"B");
                    optionABCDMap.put(questionBean.getOptionC(),"C");
                    optionABCDMap.put(questionBean.getOptionD(),"D");
                    questionBean.setAnswer(optionABCDMap.get(questionBean.getAnswer()));
                });
                item.setQuestionBeanList(questionBeanList);
            }
            try {
                //录入
                this.saveOrUpdate(item);
            }catch (Exception e){
                e.printStackTrace();
                return "入库失败："+e.getMessage();
            }
        }
        return "资源导入成功，成功导入"+articleDTOList.size()+"篇文章及其题目选项！";
    }

    /**
     * 转换p标签
     * @param content
     * @return
     */
    public String turnParagraph(String content){
        String[] split = content.split("<br/>");
        List<String> list = new ArrayList<>();
        for(String s : split){
            list.add("<p>"+s+"</p>");
        }
        return list.stream().collect(Collectors.joining());
    }

    /**
     * 获取单词信息列表
     * @param list
     */
    public List<ResourceReadArticleHighWordBean> getWordBeanList(List<String> list){

        List<String> spellingList = new ArrayList<>();
        List<String> meaningZhCnList = new ArrayList<>();
        //偶数：steam
        //奇数：n.蒸气
        for (int i = 0;i < list.size(); i++){
            String str = list.get(i).trim();
            //偶数
            if(i % 2 == 0){
                spellingList.add(str);
            }
            //奇数
            if(i % 2 == 1){
                meaningZhCnList.add(str);
            }
        }

        List<ResourceReadArticleHighWordBean> wordBeanList = new ArrayList<>();
        for(int i = 0;i < spellingList.size(); i++){
            ResourceReadArticleHighWordBean wordBean = new ResourceReadArticleHighWordBean();
            wordBean.setSpelling(spellingList.get(i));
            if(i < meaningZhCnList.size()){
                wordBean.setMeaningZhCN(meaningZhCnList.get(i));
            }
            wordBean.setSort(i);
            //获取单词对应音标
            wordBean.setSyllable(resourceWordDao.querySyllableBySpelling(wordBean.getSpelling()));
            wordBeanList.add(wordBean);
        }

        return wordBeanList;
    }


    /**
     * 获取难句
     * @param keySentences
     * @return
     */
    public List<ResourceReadArticleHighSentenceBean> getKeySentences(String keySentences){
        List<ResourceReadArticleHighSentenceBean> highSentenceBeanList = new ArrayList<>();

        String[] split = keySentences.split("\\|");
        for (int i = 0;i < split.length; i++){
            ResourceReadArticleHighSentenceBean sentenceBean = new ResourceReadArticleHighSentenceBean();
            sentenceBean.setContent(split[i]);
            highSentenceBeanList.add(sentenceBean);
        }
        return highSentenceBeanList;
    }

    /**
     * 赋值组装文章信息
     * 遍历行的单元格，并解析
     * @return
     */
    public ResourceReadArticleNewDTO assignArticleDTO(Row row){
        ResourceReadArticleNewDTO articleDTO = new ResourceReadArticleNewDTO();
        for(Cell cell : row) {
            //第1列学段忽略，从单元标识开始
            //第1列 unit
            if(cell.getColumnIndex() == 1 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setUnitName(cell.getStringCellValue().trim());
            }
            //第2列 标识：文章标识填1,无需导入
//            if(cell.getColumnIndex() == 2){
//                cell.setCellType(CellType.NUMERIC);
//                articleDTO.setTypeId(new Double(cell.getNumericCellValue()).intValue());
//            }
            //第3列 难度：易 5分钟,中 10分钟,难 12分钟
            if(cell.getColumnIndex() == 3 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                String trim = cell.getStringCellValue().trim();
                //默认中
                articleDTO.setDifficulty(2);
                if("易".equals(trim)){
                    articleDTO.setDifficulty(1);
                }
                if("难".equals(trim)){
                    articleDTO.setDifficulty(3);
                }
            }
            //第4列 字数
            if(cell.getColumnIndex() == 4 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.NUMERIC);
                articleDTO.setWordCount(new Double(cell.getNumericCellValue()).intValue());
            }
            //第6列 建议用时
            if(cell.getColumnIndex() == 5 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.NUMERIC);
                articleDTO.setTime(new Double(cell.getNumericCellValue()).intValue());
            }
            //第6列 文章编码
            if(cell.getColumnIndex() == 6 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setSerialCode(cell.getStringCellValue().trim());
            }
            //第7列 题材类型
            if(cell.getColumnIndex() == 7 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                String trim = cell.getStringCellValue().trim();
                //默认说明文
                articleDTO.setArticleType("EXPOSITORY_ESSAY");
                if("应用文".equals(trim)){
                    articleDTO.setArticleType("PRACTICAL_ESSAY");
                }
                if("记叙文".equals(trim)){
                    articleDTO.setArticleType("NARRATIVE_ESSAY");
                }
                if("议论文".equals(trim)){
                    articleDTO.setArticleType("EXPOSITORY_TALK");
                }
            }
            //第8列 标题+标题翻译
            if(cell.getColumnIndex() == 8 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                String trim = cell.getStringCellValue().trim();
                String[] split = trim.split("\\|");
                articleDTO.setArticleTitle(split[0]);
                articleDTO.setTitleTranslation(split[1]);
            }
            //第9列 matter内容
            if(cell.getColumnIndex() == 9 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setContent(cell.getStringCellValue());
            }
            //第10列 translation翻译
            if(cell.getColumnIndex() == 10 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setTranslate(cell.getStringCellValue());
            }
            //第11列 多个难词
            if(cell.getColumnIndex() == 11 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setKeyWords(cell.getStringCellValue());
            }
            //第12列 多个难句
            if(cell.getColumnIndex() == 12 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setKeySentences(cell.getStringCellValue().trim());
            }
            //第13列 多个选句
            if(cell.getColumnIndex() == 13 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setChooseSentences(cell.getStringCellValue().trim());
            }
            //第14列 AI生成参数
            if(cell.getColumnIndex() == 14 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setAiGenWord(cell.getStringCellValue().trim());
            }
        }
        return articleDTO;
    }




    /**
     * 赋值组装题目选项信息
     * 遍历行的单元格，并解析
     * @return
     */
    public ResourceReadArticleQuestionBean assignArticleQuestionDTO(Row row){
        ResourceReadArticleQuestionBean questionDTO = new ResourceReadArticleQuestionBean();
        for(Cell cell : row) {
            //第1列 学段，忽略
            //第2列 unit，忽略
            //第3列 标识：文章标识填1、题填2，忽略
            //第4列 题目题号，忽略
            //第5列 题目问题
            if(cell.getColumnIndex() == 4 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionDTO.setQuestion(cell.getStringCellValue());
            }
            //第6列 题目选项
            if(cell.getColumnIndex() == 5 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                String trim = cell.getStringCellValue().trim();
                String[] split = trim.split("\\|");
                questionDTO.setOptionA(split[0]);
                questionDTO.setOptionB(split[1]);
                questionDTO.setOptionC(split[2]);
                questionDTO.setOptionD(split[3]);
            }
            //第7列 题目正确答案,导入时需要解析
            if(cell.getColumnIndex() == 6 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionDTO.setAnswer(cell.getStringCellValue());
            }
            //第8列 解析
            if(cell.getColumnIndex() == 7 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionDTO.setParse(cell.getStringCellValue());
            }
            //后面列忽略
        }
        return questionDTO;
    }



    /**
     * 分割keyWords，获取单词列表
     * @param keyWords
     * @return
     */
    public List<String> splitKeyWords(String keyWords){
        List<String> list = new ArrayList<>();
        //为了分割||
        String[] split = keyWords.split("\\||\\\\");
        //偶数：steam
        //奇数：n.蒸气
        for(int i = 0;i < split.length; i++){
            if(!split[i].equals("")){
                if(split[i].contains("\n")){
                    list.add(split[i].substring(1));
                }else {
                    list.add(split[i]);
                }
            }
        }
        return list;
    }

    /**
     * 判断单个单元格是否为空
     * @param cell
     * @return
     */
    public static boolean isNotEmptyCell(Cell cell) {
        return cell != null && !cell.getCellTypeEnum().equals(CellType.BLANK);
    }

    /**
     * 判断单个单元格是否为空
     * @param cell
     * @return
     */
    public static boolean isEmptyCell(Cell cell) {
        return cell == null || cell.getCellTypeEnum().equals(CellType.BLANK);
    }


    /**
     * 判断某行是否为空
     * @param row
     * @return
     */
    public static boolean isEmptyRow(Row row) {
        return row == null || row.toString().isEmpty();
    }





}
