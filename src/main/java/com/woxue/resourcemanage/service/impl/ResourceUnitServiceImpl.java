package com.woxue.resourcemanage.service.impl;

import com.woxue.resourcemanage.dao.IArticleDao;
import com.woxue.resourcemanage.dao.IResourceContentDao;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.service.IResourceUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 15:03
 */
@Service
public class ResourceUnitServiceImpl implements IResourceUnitService {
    @Autowired
    IResourceUnitDao resourceUnitDao;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IResourceContentDao resourceContentDao;
    @Autowired
    IArticleDao articleDao;

    @Override
    public Map<String, Object> getUnitList(Integer courseId, Integer pageStart, Integer pageSize) {
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, pageStart, pageSize);
        for (Map<String, Object> stringObjectMap : unitList) {
            Integer resourceUnitId = (Integer) stringObjectMap.get("id");
            stringObjectMap.put("wordContent",resourceContentDao.getWordShowName(resourceUnitId));
            stringObjectMap.put("sentenceContent",resourceContentDao.getSentenceShowName(resourceUnitId));
            stringObjectMap.put("grammarContent",resourceContentDao.getGrammarShowName(resourceUnitId));
            stringObjectMap.put("questionContent",resourceContentDao.getQuestionShowName(resourceUnitId));
            stringObjectMap.put("articleContent",articleDao.getArticleShowName(resourceUnitId));
        }
        Integer unitCount = resourceUnitDao.getUnitCount(courseId);
        Map<String,Object> map=new HashMap<>();
        map.put("unitList",unitList);
        map.put("allCount",unitCount);
        return map;
    }

    @Override
    public Integer getUnitByCourse(Integer courseId, String nameEn) {
        return resourceUnitDao.getUnitByCourse(courseId,nameEn);
    }

    @Override
    public boolean insertUnit(String nameEn, String nameCn,Integer courseId) {
        Integer unitMaxDisplayOrder = resourceUnitDao.getUnitMaxDisplayOrder();
        if(unitMaxDisplayOrder==null){
            unitMaxDisplayOrder=0;
        }
       Integer  displayOrder= unitMaxDisplayOrder+1;
        boolean insertUnitResult = resourceUnitDao.insertUnit(courseId,nameEn, nameCn,displayOrder);
        boolean result=false;
        if(insertUnitResult){
            Integer unitCount = resourceUnitDao.getUnitCount(courseId);
            result= resourceCourseDao.updateCourseUnitNum(unitCount, courseId);
            }
        return result;
    }

    @Override
    public boolean insertUnits(String[] nameEns, Integer courseId) {

        Map<String, Object> course = resourceCourseDao.getCourseById(courseId);
        if (course == null) {
            return false;
        }
        if (Integer.parseInt(course.get("unit_num").toString())>0){
            return false;
        }
        //批量添加
        boolean insertUnitResult = resourceUnitDao.insertUnits(nameEns,courseId);

        if(insertUnitResult){
            Integer unitCount = resourceUnitDao.getUnitCount(courseId);
            return resourceCourseDao.updateCourseUnitNum(unitCount, courseId);
        }
        return false;
    }

    @Override
    public Map<String, Object> getUnitById(Integer id) {
        return resourceUnitDao.getUnitById(id);
    }

    @Override
    public boolean updateUnit(String nameEn, String nameCn, Integer id) {
        return resourceUnitDao.updateUnit(nameEn,nameCn,id);
    }

    @Override
    public boolean updateUnitContentNum(Integer contentNum, Integer resourceUnitId) {
        return resourceUnitDao.updateUnitContentNum(contentNum, resourceUnitId);
    }

   /* @Override
    public Map<String, Object> getUnitQuestion(Integer paperId) {
        return resourceUnitDao.getUnitQuestion(paperId);
    }*/
    @Override
    public Map<String, Object> getUnitQuestion(Integer courseId,Integer paperId) {
        return resourceUnitDao.getUnitQuestionByCourseID(courseId,paperId);
    }


}
