package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.RedBookArticle;
import com.woxue.common.model.redBook.RedBookArticleSentenceShowType;
import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.dao.IArticleDao;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.entity.SentenceBean;
import com.woxue.resourcemanage.service.IArticleService;
import com.woxue.resourcemanage.util.ExcelUtil;
import com.woxue.resourcemanage.util.PropertiesUtils;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/5/30 12:09
 */
@Service
public class ArticleServiceImpl implements IArticleService {
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IArticleDao articleDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;

    @Override
    public HSSJsonReulst insertArticleInfo(MultipartFile[] files, Integer courseId) {
        String msg = "";
        //获得文件
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");
        List<FileItem> items = null;
        for (MultipartFile mf : files) {
            if (!mf.isEmpty()) {
                try {
                    fileStream = mf.getInputStream();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        InputStream is = fileStream;
        try {
            Workbook wb = Workbook.getWorkbook(is);
            int sheets = wb.getNumberOfSheets();

            if (sheets == 1) {
                Sheet sheet = wb.getSheet(0);
                int cols = sheet.getColumns();
                int rows = sheet.getRows();
                //检查行数、列数,工作表名

                if (rows <= 1) {
                    msg = "资源导入，工作表没有数据!";
                    return HSSJsonReulst.ok(msg);
                }
                boolean result = false;
                Integer unitId = null;
                int unitArticleDisplayOrder = 0;
                int articleId = -1;
                int sentenceDisplayOrder = 0;
                String prevUnitName = "";
                String prevArticleName = "";
                for (int i = 1; i < rows; i++) {
                    if (sheet.getCell(0, i).getContents() == null || sheet.getCell(0, i).getContents().trim().length() <= 0) {
                        break;
                    }
                    String unitName = new String(sheet.getCell(0, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                    String articleName = new String(sheet.getCell(1, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                    int articleType = Integer.parseInt(new String(sheet.getCell(2, i).getContents().getBytes("UTF-8"), "UTF-8").trim());
                    RedBookArticleSentenceShowType showType = null;
                    if (articleType == 2) {
                        String showTypeStr = new String(sheet.getCell(3, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                        if (showTypeStr != null) {
                            if (showTypeStr.equals("TITLE")) {
                                showType = RedBookArticleSentenceShowType.TITLE;
                            } else if (showTypeStr.equals("NEW_LINE")) {
                                showType = RedBookArticleSentenceShowType.NEW_LINE;
                            } else if (showTypeStr.equals("NEW_LINE_INDENT")) {
                                showType = RedBookArticleSentenceShowType.NEW_LINE_INDENT;
                            } else if (showTypeStr.equals("SIGN_OFF")) {
                                showType = RedBookArticleSentenceShowType.SIGN_OFF;
                            }
                        }
                    }
                    String speaker = null;
                    if (articleType == 1) {
                        speaker = new String(sheet.getCell(4, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                    }
                    String sentence_en_US = new String(sheet.getCell(5, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                    String sentence_zh_CN = new String(sheet.getCell(6, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                    String soundFileStr = new String(sheet.getCell(7, i).getContents().getBytes("UTF-8"), "UTF-8").trim();
                    //如果包含了/，表示手动填充了完整路径，如果没有包含才需要拼接路径
                    if (soundFileStr.indexOf("/") == -1) {
                        soundFileStr = "/" + PropertiesUtils.getProperty("redBook_article_sound") + "/" + courseId + "/" + soundFileStr;
                    }
                    //新的单元
                    if (!prevUnitName.equals(unitName)) {
                        prevUnitName = unitName;
                        unitId = resourceUnitDao.selectByName(unitName, courseId);
                        prevArticleName = "";
                    }
                    //新的一篇文章
                    if (!prevArticleName.equals(articleName)) {
                        prevArticleName = articleName;
                        //插入文章
                        RedBookArticle article = new RedBookArticle();
                        article.setArticleName(articleName);
                        article.setSentenceCount(0);
                        article.setArticleType(articleType);
                        articleDao.insertArticle(article);
                        articleId = article.getId();
                        //插入和单元关联表；
                        articleDao.insertUnitArticle(unitId, articleId, unitArticleDisplayOrder++);
                        sentenceDisplayOrder = 0;
                    }
                    //插入句子
                    SentenceBean articleSentence = new SentenceBean();
                    articleSentence.setArticleId(articleId);
                    articleSentence.setSentence_en_US(sentence_en_US);
                    articleSentence.setSentence_zh_CN(sentence_zh_CN);
                    articleSentence.setSpeaker(speaker);
                    articleSentence.setSoundFile(soundFileStr);
                    articleSentence.setShowType(showType);
                    articleSentence.setDisplayOrder(sentenceDisplayOrder++);
                    articleDao.insertArticleSentence(articleSentence);
                    //更新文章句子数量。
                    articleDao.updateArticleSentenceCount(articleId);
                    result = true;
                }
                if (result) {
                    resourceCourseDao.updateCourseContentContainStatus(courseId, RedBookContentTypeEnum.ARTICLE, true);
                    msg = "资源导入成功";
                } else {
                    msg = "资源导入异常";
                }
                return HSSJsonReulst.ok(msg);
            }
        } catch (Exception ex) {
            msg = "资源导入异常" + ex.getMessage();
            ex.printStackTrace();
        }
        try {
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return HSSJsonReulst.ok(msg);
    }

    @Override
    public Map<String, Object> getUnitList(Integer courseId, Integer pageIndex, Integer pageSize) {
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, pageIndex, pageSize);
        for (Map<String, Object> stringObjectMap : unitList) {
            Integer resourceUnitId = (Integer) stringObjectMap.get("id");
            stringObjectMap.put("articleList", articleDao.getArticleShowName(resourceUnitId));
        }
        Integer unitCount = resourceUnitDao.getUnitCount(courseId);
        Map<String, Object> map = new HashMap<>();
        map.put("unitList", unitList);
        map.put("allCount", unitCount);
        return map;
    }

    /**
     * Export course resources to an Excel file and send it as a response to the client.
     *
     * @param courseId  the ID of the course whose resources are to be exported
     * @param response  the HttpServletResponse object used to send the Excel file to the client
     */
    @Override
    public void export(Integer courseId, HttpServletResponse response) {
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, 0, 100);
        Map<String, Object> courseById = resourceCourseDao.getCourseById(courseId);
        String fileName = courseById.get("name_cn")+"课程资源";
        String sheetName = "课程资源";
        String[] title = {"单元名称", "文章名称", "文章类型", "显示类型", "对话人名", "英文句子", "中文句子", "音频文件"};
        List<List<String>> values = new ArrayList<>();
        for (int i = 0; i < unitList.size(); i++) {
            Map<String, Object> unit = unitList.get(i);
            Integer unitId = (Integer) unit.get("id");
            List<Map<String, Object>> articleList = articleDao.getArticleShowName(unitId);
            for (Map<String, Object> article : articleList) {
                Integer articleId = (Integer) article.get("articleId");
                List<SentenceBean> sentenceList = articleDao.getArticleContentList(articleId);
                for (SentenceBean sentence : sentenceList) {
                    List<String> list = new ArrayList<>();
                    list.add((String) unit.get("nameCn"));
                    list.add((String) article.get("articleName"));
                    list.add(String.valueOf(article.get("articleType")));
                    list.add(String.valueOf(sentence.getShowType()));
                    list.add(sentence.getSpeaker());
                    list.add(sentence.getSentence_en_US());
                    list.add(sentence.getSentence_zh_CN());
                    list.add(sentence.getSoundFile());
                    values.add(list);
                }
            }
        }
        //导出excel
        HSSFWorkbook wb = ExcelUtil.getHSSFWorkbook(sheetName, title, values, null);
        //响应到客户端
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
