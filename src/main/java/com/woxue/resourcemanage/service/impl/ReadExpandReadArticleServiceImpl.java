package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.RedBookContentTypeEnum;

import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleBean;
import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean;
import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean;
import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.dao.ReadExpandReadArticleDao;
import com.woxue.resourcemanage.entity.dto.ReadExpandReadArticleDTO;
import com.woxue.resourcemanage.entity.dto.ReadExpandReadArticleQuestionDTO;
import com.woxue.resourcemanage.entity.vo.ReadExpandReadArticleVO;
import com.woxue.resourcemanage.enums.ReadExpandArticleQuestionEnum;
import com.woxue.resourcemanage.enums.ReadExpandReadDifficultyEnum;
import com.woxue.resourcemanage.service.*;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 扩展阅读-文章 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Service
public class ReadExpandReadArticleServiceImpl implements IReadExpandReadArticleService {

    @Autowired
    ReadExpandReadArticleDao readexpandReadArticleDao;
    @Autowired
    IReadExpandReadArticleQuestionService questionService;
    @Autowired
    IReadExpandReadArticleCorrelationService correlationService;
    @Autowired
    IReadExpandReadArticleWordService wordService;
    
    @Autowired
    IResourceUnitService resourceUnitService;

    @Autowired
    IResourceWordDao resourceWordDao;
    @Autowired
    IReadExpandKnowledgeQuestionService knowledgeQuestionService;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;
    @Override
    public Map<String, Object> listByCourseId(Integer courseId, Integer pageNum, Integer pageSize) {
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, pageNum, pageSize);
        for (Map<String, Object> stringObjectMap : unitList) {
            Integer resourceUnitId = (Integer) stringObjectMap.get("id");
            List<ReadExpandReadArticleBean> list = readexpandReadArticleDao.list(resourceUnitId);
            List<ReadExpandReadArticleBean> collect = list.stream().map(item -> {
                item.setContent(null);
                item.setTranslate(null);
                return item;
            }).collect(Collectors.toList());
            stringObjectMap.put("articleList",collect);
        }
        Integer unitCount = resourceUnitDao.getUnitCount(courseId);
        Map<String, Object> map = new HashMap<>();
        map.put("unitList", unitList);
        map.put("allCount", unitCount);
        return map;
    }

    @Override
    public List<ReadExpandReadArticleBean> list(Integer unitId) {
        return readexpandReadArticleDao.list(unitId);
    }

    @Override
    public ReadExpandReadArticleVO edit(Integer articleId) {

        ReadExpandReadArticleBean readexpandReadArticleBean = readexpandReadArticleDao.edit(articleId);
        if(readexpandReadArticleBean == null){
            return new ReadExpandReadArticleVO();
        }

        List<ReadExpandReadArticleQuestionBean> questionBeanList = questionService.list(articleId);
        List<ReadExpandReadArticleWordBean> wordBeanList = wordService.list(articleId);
        ReadExpandReadArticleCorrelationBean correlationBean = correlationService.editByArticleId(articleId);

        ReadExpandReadArticleVO readexpandReadArticleVO = new ReadExpandReadArticleVO();
        BeanUtils.copyProperties(readexpandReadArticleBean,readexpandReadArticleVO);
        //重点单词
        readexpandReadArticleVO.setWordBeanList(wordBeanList);

        //句句对应
//        correlationBean.setContent(this.typeSettingContent(readexpandReadArticleBean.getContent()));
//        correlationBean.setTranslate(this.typeSettingContent(readexpandReadArticleBean.getTranslate()));
        readexpandReadArticleVO.setCorrelationBean(correlationBean);

        List<ReadExpandReadArticleQuestionBean> basicsQuestionBeanList = new ArrayList<>();
        List<ReadExpandReadArticleQuestionBean> raiseQuestionBeanList = new ArrayList<>();
        questionBeanList.forEach(item ->{
            if(ReadExpandArticleQuestionEnum.BASIC_TRAIN.getCode().equals(item.getTrainPhase())){
                basicsQuestionBeanList.add(item);
            }
            if(ReadExpandArticleQuestionEnum.RAISE_TRAIN.getCode().equals(item.getTrainPhase())){
                raiseQuestionBeanList.add(item);
            }
        });
        //基础训练
        readexpandReadArticleVO.setBasicsQuestionBeanList(basicsQuestionBeanList);
        //拔高训练
        readexpandReadArticleVO.setRaiseQuestionBeanList(raiseQuestionBeanList);
        //知识重点
        readexpandReadArticleVO.setKnowledgeQuestionBeanList(knowledgeQuestionService.getListByArticleId(articleId));
        return readexpandReadArticleVO;
    }

    @Override
    public int save(ReadExpandReadArticleBean readexpandReadArticleBean) {
        return readexpandReadArticleDao.save(readexpandReadArticleBean);
    }

    @Override
    public int update(ReadExpandReadArticleBean readexpandReadArticleBean) {
        return readexpandReadArticleDao.update(readexpandReadArticleBean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importExcel(MultipartFile multipartFile,Integer versionId,
                                                 Integer courseId, Integer startReadLine) {

        Workbook wb = null;
        File file = null;
        try {
            file= File.createTempFile(multipartFile.getOriginalFilename(),null);
            multipartFile.transferTo(file);file.deleteOnExit();
            wb = WorkbookFactory.create(file);
        }catch (Exception e) {
            e.printStackTrace();
            return "文件解析失败："+e.getMessage();
        }
        //读取excel表中的sheet，参数为sheet的索引值(从0开始)
        Sheet sheet = wb .getSheetAt(0);

        //总行数
        int lastRowNum = sheet.getLastRowNum();
        //最终确认articleDTOList
        List<ReadExpandReadArticleDTO> articleDTOList = new ArrayList<>();
        //临时使用
        List<ReadExpandReadArticleDTO> temporaryArticleDTOList = new ArrayList<>();
        //题目信息list
        List<ReadExpandReadArticleQuestionDTO> questionDTOList = new ArrayList<>();
        //除了标题无数据
        if(lastRowNum < 2){
            return "资源导入失败，工作表没有数据!";
        }
        //外循环是循环行，内循环是每行的单元格
        for(int i = startReadLine;i <= lastRowNum; i++){
            //每行数据
            Row row = sheet.getRow(i);
            //row为空，忽略
            if(isEmptyRow(row)){
                continue;
            }
            Cell firstCell = row.getCell(0);
            Cell secondCell = row.getCell(1);
            //cell为空，忽略
            if(isEmptyCell(secondCell)){
                continue;
            }
            try {
                if(isNotEmptyCell(firstCell)  && firstCell.getStringCellValue().contains("Unit")){
                    //赋值组装文章DTO
                    ReadExpandReadArticleDTO articleDTO = this.assignArticleDTO(row);
                    //文章list不为空
                    if(!temporaryArticleDTOList.isEmpty()) {
                        ReadExpandReadArticleDTO readArticleDTO = temporaryArticleDTOList.get(temporaryArticleDTOList.size() - 1);
                        readArticleDTO.setQuestionDTOList(questionDTOList);
                        articleDTOList.add(readArticleDTO);
                        questionDTOList = new ArrayList<>();
                    }
                    temporaryArticleDTOList.add(articleDTO);
                }else {
                    //此行是题目信息
                    //赋值组装题目选项DTO
                    ReadExpandReadArticleQuestionDTO questionDTO = this.assignArticleQuestionDTO(row);
                    questionDTOList.add(questionDTO);
                    if(i == lastRowNum){
                        ReadExpandReadArticleDTO readArticleDTO = temporaryArticleDTOList.get(temporaryArticleDTOList.size() - 1);
                        readArticleDTO.setQuestionDTOList(questionDTOList);
                        articleDTOList.add(readArticleDTO);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                return "导入解析失败：第"+(i+1)+"行："+e.getMessage();
            }
        }

        if(articleDTOList.isEmpty()){
            return "资源导入失败，工作表没有合法数据!";
        }

        //处理逻辑
        for(ReadExpandReadArticleDTO item : articleDTOList){
            Integer unitId = resourceUnitService.getUnitByCourse(courseId, item.getUnitName());
            if(unitId == null){
                resourceUnitService.insertUnit(item.getUnitName(),item.getUnitName(), courseId);
                unitId = resourceUnitService.getUnitByCourse(courseId, item.getUnitName());
            }
            //赋值组装文章bean
            ReadExpandReadArticleBean articleBean = this.assignArticleBean(item);
            articleBean.setVersionId(versionId);
            articleBean.setCourseId(courseId);
            articleBean.setUnitId(unitId);
            List<String> splitKeyWords = null;
            if(item.getKeyWords() != null){
                //获取单词list
                splitKeyWords = this.splitKeyWords(item.getKeyWords());
                articleBean.setWordCount(splitKeyWords.size() / 2);
            }
            try {
                //插入文章
                readexpandReadArticleDao.save(articleBean);
                //插入句句对应
                this.saveCorrelationBean(articleBean);
                //批量插入文章-题目列表信息
                this.batchSaveQuestionBean(articleBean.getArticleId(),item.getQuestionDTOList());
                //批量插入文章-单词列表信息
                if(item.getKeyWords() != null){
                    this.batchSaveWordBean(articleBean.getArticleId(),splitKeyWords);
                }
            }catch (Exception e){
                e.printStackTrace();
                return "入库失败："+e.getMessage();
            }
        }
        resourceCourseDao.updateCourseContentContainStatus(courseId, RedBookContentTypeEnum.ARTICLE,true);
        return "资源导入成功，成功导入"+articleDTOList.size()+"篇文章及其题目选项！";
    }

    /**
     * 插入句句对应bean
     */
    public void saveCorrelationBean(ReadExpandReadArticleBean articleBean){
        ReadExpandReadArticleCorrelationBean correlationBean = new ReadExpandReadArticleCorrelationBean();
        correlationBean.setArticleId(articleBean.getArticleId());
        correlationBean.setContent(this.typeSettingContent(articleBean.getContent()));
        correlationBean.setTranslate(this.typeSettingContent(articleBean.getTranslate()));
        correlationService.save(correlationBean);
    }

    /**
     * 排版内容
     * @param content
     * @return
     */
    public String typeSettingContent(String content){
        //不包含空格符号，原样返回
        if(!content.contains("&nbsp;&nbsp;&nbsp;&nbsp;")){
            return content;
        }
        //通过空格符号分割内容段落
        String[] split = content.split("&nbsp;&nbsp;&nbsp;&nbsp;");
        List<String> list = new ArrayList<>();
        for(int i=0;i<split.length;i++){
            List<String> sentenceList = patternSplitContent(split[i]);
            list.addAll(sentenceList);
            list.add("\n");
        }
        return list.stream().collect(Collectors.joining()).trim();
    }

    public static void main(String[] args) {
//        String content = "hello,how are you totay? I love programming... Its so much fun!";
//
//        String regex = "[。？！.?!]";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher matcher = pattern.matcher(content);
//        List<String> list = new ArrayList<>();
//        int start = 0;
//        while (matcher.find()) {
//            int end = matcher.end(); // 句子的结索位置
//            String sentence = content.substring(start, end) .trim();
//            list.add(sentence + "\n");
//            start = end;
//        }

        String content = "Once upon a time, in a small village, there lived a father, who was a kind man with a loving mother and a little sister.\\nOne day, he saw his own name in the obituary column of the newspaper, but it was a mistake.\\nThe father was known for inventing a new medicine, and people called him the \\u0027Healer of the World\\u0027.\\nBut when he read the words \\u0027Merchant of Death\\u0027, he asked himself, \\u0027Is this how I am going to be remembered?\\u0027\\nHe decided that he needed to make some change in his life, so he started working toward world peace.\\nFrom that day on, he dedicated himself to helping others and making the world a better place.\\nHis family was proud of him, especially his mother and sister, who had always supported him.\\nYears went by, and the father\\u0027s efforts paid off, he was remembered today as a hero.\\nHis legacy lived on, and people from all over the world honored him for his work in medicine and peace.\\nThe father\\u0027s story inspired many, and he became a symbol of hope and kindness.\\nHis mother and sister smiled whenever they thought of him, knowing he made a difference.\\nThe father\\u0027s story taught us that even small actions can make a big impact.\\nWe can all be like him, making a change in our own lives and the world around us.\\nAnd so, the father\\u0027s legacy continued to grow, inspiring future generations.\\nHis mother and sister looked up to him as a role model, and his sister learned from him.\\nThe father\\u0027s kindness and compassion touched the hearts of many, and his story was told and retold.\\nHis mother and sister were proud to call him their own, and his legacy lived on.\\nThe father\\u0027s story showed us that everyone can make a difference, no matter how small.\\n";

        List<String> strings = patternSplitContent(content);

        System.out.println(strings.stream().collect(Collectors.joining("\\n")).trim());
        System.out.println("-------------------");
        System.out.println(strings.stream().collect(Collectors.joining()).trim());




    }

    /**
     * 正则表达式分割内容
     * @param content 内容
     * @return
     */
    public static List<String> patternSplitContent(String content){
        //句子结束符号的正则表达式
        String regex = "[。？！.?!]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> list = new ArrayList<>();
        int start = 0;
        while (matcher.find()) {
            int end = matcher.end(); // 句子的结索位置
            String sentence = content.substring(start, end) .trim();
            list.add(sentence + "\n");
            start = end;
        }
        return list;
    }

    /**
     * 赋值组装文章bean
     * @return
     */
    public ReadExpandReadArticleBean assignArticleBean(ReadExpandReadArticleDTO item){
        ReadExpandReadArticleBean articleBean = new ReadExpandReadArticleBean();
        articleBean.setYear(item.getYear());
        articleBean.setTitle(item.getTitle());
        articleBean.setSource(item.getSource());
        articleBean.setSerialCode(item.getSerialCode());
        if(ReadExpandArticleQuestionEnum.getByName(item.getArticleName()) != null){
            articleBean.setArticleType(ReadExpandArticleQuestionEnum.getByName(item.getArticleName()).getCode());
        }
        articleBean.setTheme(item.getTheme());
        articleBean.setContent(item.getContent());
        articleBean.setTranslate(item.getTranslate());
        articleBean.setTime(ReadExpandReadDifficultyEnum.getByNumber(item.getDifficulty()).getTime());
        //articleBean.setGrade();
        articleBean.setDifficulty(item.getDifficulty());
        return articleBean;
    }


    /**
     * 批量插入文章-题目信息列表
     * @param articleId
     * @param readArticleQuestionDTOList
     */
    public void batchSaveQuestionBean(Integer articleId,List<ReadExpandReadArticleQuestionDTO> readArticleQuestionDTOList){
        List<ReadExpandReadArticleQuestionBean> questionBeanList = new ArrayList<>();
        readArticleQuestionDTOList.forEach(questionDTO -> {
            ReadExpandReadArticleQuestionBean questionBean = new ReadExpandReadArticleQuestionBean();
            questionBean.setArticleId(articleId);
            String trainType = null;
            if(questionDTO.getTypeId() == 2){
                //基础训练
                trainType = ReadExpandArticleQuestionEnum.BASIC_TRAIN.getName();
            }
            if(questionDTO.getTypeId() == 3){
                //拔高训练
                trainType =  ReadExpandArticleQuestionEnum.RAISE_TRAIN.getName();
            }
            questionBean.setTrainPhase(ReadExpandArticleQuestionEnum.getByName(trainType).getCode());
            questionBean.setNumber(questionDTO.getNumber());
            questionBean.setQuestion(questionDTO.getQuestion());
            questionBean.setQuestionTranslation(questionDTO.getQuestionTranslation());
            if(questionDTO.getOptionABCD() != null){
                //分割符号“|”
                String[] split = questionDTO.getOptionABCD().split("\\||\\\\");
                for(int i=0 ;i<split.length;i++){
                    if(i == 0){
                        questionBean.setOptionA(split[0]);
                    }
                    if(i == 1){
                        questionBean.setOptionB(split[1]);
                    }
                    if(i == 2){
                        questionBean.setOptionC(split[2]);
                    }
                    if(i == 3){
                        questionBean.setOptionD(split[3]);
                    }
                }

            }
            if(questionDTO.getOptionABCDTranslation() != null) {
                //分割符号“|”
                String[] split = questionDTO.getOptionABCDTranslation().split("\\||\\\\");
                for(int i=0 ;i<split.length;i++){
                    if(i == 0){
                        questionBean.setOptionATranslation(split[0]);
                    }
                    if(i == 1){
                        questionBean.setOptionBTranslation(split[1]);
                    }
                    if(i == 2){
                        questionBean.setOptionCTranslation(split[2]);
                    }
                    if(i == 3){
                        questionBean.setOptionDTranslation(split[3]);
                    }
                }
            }

            Map<String,String> optionABCDMap = new HashMap<>();
            optionABCDMap.put(questionBean.getOptionA(),"A");
            optionABCDMap.put(questionBean.getOptionB(),"B");
            optionABCDMap.put(questionBean.getOptionC(),"C");
            optionABCDMap.put(questionBean.getOptionD(),"D");
            questionBean.setAnswer(optionABCDMap.get(questionDTO.getAnswer()));
            questionBeanList.add(questionBean);
        });
        //list不为空，批量插入
        if(!questionBeanList.isEmpty()){
            questionService.batchSave(questionBeanList);
        }

    }


    /**
     * 分割keyWords，获取单词列表
     * @param keyWords
     * @return
     */
    public List<String> splitKeyWords(String keyWords){
        List<String> list = new ArrayList<>();
        //为了分割||
        String[] split = keyWords.split("\\||\\\\");
        //偶数：steam
        //奇数：n.蒸气
        for(int i = 0;i < split.length; i++){
            if(!split[i].equals("")){
                if(split[i].contains("\n")){
                    list.add(split[i].substring(1));
                }else {
                    list.add(split[i]);
                }
            }
        }
        return list;
    }


    /**
     * 批量插入文章-单词信息列表
     * @param articleId
     * @param list
     */
    public void batchSaveWordBean(Integer articleId,List<String> list){

        List<String> spellingList = new ArrayList<>();
        List<String> meaningZhCnList = new ArrayList<>();
        //偶数：steam
        //奇数：n.蒸气
        for (int i = 0;i < list.size(); i++){
            //偶数
            if(i % 2 == 0){
                spellingList.add((list.get(i)));
            }
            //奇数
            if(i % 2 == 1){
                meaningZhCnList.add((list.get(i)));
            }
        }

        List<ReadExpandReadArticleWordBean> wordBeanList = new ArrayList<>();
        for(int i = 0;i < spellingList.size(); i++){
            ReadExpandReadArticleWordBean wordBean = new ReadExpandReadArticleWordBean();
            wordBean.setArticleId(articleId);
            wordBean.setSpelling(spellingList.get(i));
            if(i < meaningZhCnList.size()){
                wordBean.setMeaningZhCn(meaningZhCnList.get(i));
            }
            //获取单词对应音标
            wordBean.setSyllable(resourceWordDao.querySyllableBySpelling(wordBean.getSpelling()));
            wordBeanList.add(wordBean);
        }

        //list不为空，批量插入
        if(!wordBeanList.isEmpty()){
            wordService.batchSave(wordBeanList);
        }
    }

    /**
     * 赋值组装文章信息
     * 遍历行的单元格，并解析
     * @return
     */
    public ReadExpandReadArticleDTO assignArticleDTO(Row row){
        ReadExpandReadArticleDTO articleDTO = new ReadExpandReadArticleDTO();
        for(Cell cell : row) {
            //第1列 unit
            if(cell.getColumnIndex() == 0){
                cell.setCellType(CellType.STRING);
                articleDTO.setUnitName(cell.getStringCellValue().trim());
            }
            //第2列 标识：文章标识填1、阅读训练填2、拔高训练填3
            if(cell.getColumnIndex() == 1){
                cell.setCellType(CellType.NUMERIC);
                articleDTO.setTypeId(new Double(cell.getNumericCellValue()).intValue());
            }
            //第3列 title标题
            if(cell.getColumnIndex() == 2 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                articleDTO.setTitle(cell.getStringCellValue().trim());
            }
            //第4列 字数
            if(cell.getColumnIndex() == 3){
                cell.setCellType(CellType.NUMERIC);
                articleDTO.setWordCount(new Double(cell.getNumericCellValue()).intValue());
            }
            //第5列 题干译文，不需要处理
    //        if(cell.getColumnIndex() == 4){
    //            System.out.println("第5列："+cell.getStringCellValue());
    //        }
            //第6列 难度
            if(cell.getColumnIndex() == 5){
                cell.setCellType(CellType.NUMERIC);
                articleDTO.setDifficulty(new Double(cell.getNumericCellValue()).intValue());
            }
            //第7列 年份
            if(cell.getColumnIndex() == 6){
                cell.setCellType(CellType.NUMERIC);
                BigDecimal year = new BigDecimal(cell.getNumericCellValue());
                articleDTO.setYear(year.toPlainString());
            }
            //第8列 文章顺序
            if(cell.getColumnIndex() == 7){
                cell.setCellType(CellType.STRING);
                articleDTO.setSerialCode(cell.getStringCellValue().trim());
            }
            //第9列 题材类型
            if(cell.getColumnIndex() == 8){
                cell.setCellType(CellType.STRING);
                articleDTO.setArticleName(cell.getStringCellValue().trim());
            }
            //第10列 matter内容
            if(cell.getColumnIndex() == 9){
                cell.setCellType(CellType.STRING);
                articleDTO.setContent(cell.getStringCellValue());
            }
            //第11列 translation翻译
            if(cell.getColumnIndex() == 10){
                cell.setCellType(CellType.STRING);
                articleDTO.setTranslate(cell.getStringCellValue());
            }
            //第12列 key_words单词拼接
            if(cell.getColumnIndex() == 11){
                cell.setCellType(CellType.STRING);
                articleDTO.setKeyWords(cell.getStringCellValue());
            }
            //第13列 主题
            if(cell.getColumnIndex() == 12){
                cell.setCellType(CellType.STRING);
                articleDTO.setTheme(cell.getStringCellValue().trim());
            }
            //第14列 来源
            if(cell.getColumnIndex() == 13){
                cell.setCellType(CellType.STRING);
                articleDTO.setSource(cell.getStringCellValue().trim());
            }
        }
        return articleDTO;
    }


    /**
     * 赋值组装题目选项信息
     * 遍历行的单元格，并解析
     * @return
     */
    public ReadExpandReadArticleQuestionDTO assignArticleQuestionDTO(Row row){
        ReadExpandReadArticleQuestionDTO questionDTO = new ReadExpandReadArticleQuestionDTO();
        for(Cell cell : row) {
            //第1列 unit，忽略
            //第2列 标识：文章标识填1、阅读训练填2、拔高训练填3
            if(cell.getColumnIndex() == 1){
                cell.setCellType(CellType.NUMERIC);
                questionDTO.setTypeId(new Double(cell.getNumericCellValue()).intValue());
            }
            //第3列 题目题号
            if(cell.getColumnIndex() == 2){
                cell.setCellType(CellType.NUMERIC);
                questionDTO.setNumber(new Double(cell.getNumericCellValue()).intValue());
            }
            //第4列 题目问题
            if(cell.getColumnIndex() == 3){
                cell.setCellType(CellType.STRING);
                questionDTO.setQuestion(cell.getStringCellValue());
            }
            //第5列 题干译文
            if(cell.getColumnIndex() == 4){
                cell.setCellType(CellType.STRING);
                questionDTO.setQuestionTranslation(cell.getStringCellValue());
            }
            //第6列 题目选项,不为空时（空着会解析为0）
            if(cell.getColumnIndex() == 5 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionDTO.setOptionABCD(cell.getStringCellValue());
            }
            //第7列 选项译文,不为空时（空着会解析为0）
            if(cell.getColumnIndex() == 6 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionDTO.setOptionABCDTranslation(cell.getStringCellValue());
            }
            //第8列 题目正确答案,不为空时（空着会解析为0）
            if(cell.getColumnIndex() == 7 && isNotEmptyCell(cell)){
                //可能为数字，所以强制转换字符类型
                cell.setCellType(CellType.STRING);
                questionDTO.setAnswer(cell.getStringCellValue());
            }
            //后面列忽略
        }
        return questionDTO;
    }

    /**
     * 判断单个单元格是否为空
     * @param cell
     * @return
     */
    public static boolean isNotEmptyCell(Cell cell) {
        return cell != null && !cell.getCellTypeEnum().equals(CellType.BLANK);
    }

    /**
     * 判断单个单元格是否为空
     * @param cell
     * @return
     */
    public static boolean isEmptyCell(Cell cell) {
        return cell == null || cell.getCellTypeEnum().equals(CellType.BLANK);
    }


    /**
     * 判断某行是否为空
     * @param row
     * @return
     */
    public static boolean isEmptyRow(Row row) {
        return row == null || row.toString().isEmpty();
    }


 }
