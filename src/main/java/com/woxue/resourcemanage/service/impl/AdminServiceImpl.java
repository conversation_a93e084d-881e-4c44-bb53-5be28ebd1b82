package com.woxue.resourcemanage.service.impl;

import com.woxue.common.util.MD5;
import com.woxue.resourcemanage.dao.IAdminDao;
import com.woxue.resourcemanage.entity.AdminBean;
import com.woxue.resourcemanage.service.IAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminServiceImpl implements IAdminService {

    @Autowired
    private IAdminDao adminDao;

    @Override
    public AdminBean getUser(String name, String password) {
        password = MD5.JM(MD5.KL(MD5.Md5(password)));
        return adminDao.getAdmin(name,password);
    }


}
