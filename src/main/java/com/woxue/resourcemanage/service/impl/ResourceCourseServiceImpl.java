package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.common.model.redBook.RedBookCourseStage;
import com.woxue.common.util.GsonManager;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.dao.IResourceVersionDao;
import com.woxue.resourcemanage.entity.ResourceCourse;
import com.woxue.resourcemanage.entity.vo.RedBookCourseVO;
import com.woxue.resourcemanage.service.IResourceCourseService;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 13:48
 */
@Service
public class ResourceCourseServiceImpl implements IResourceCourseService {
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;
    @Autowired
    IResourceVersionDao resourceVersionDao;


    @Override
    public List<RedBookCourseVO> getCourseList(Integer versionId,Integer stage) {
        int versionPublishStatus = RedBookCourseManager.getRedBookVersion(versionId)==null?0:1;
        List<RedBookCourseVO> list = new ArrayList<>();
        List<RedBookCourse> courseList = resourceCourseDao.getCourseList(versionId,stage);
        courseList.forEach(redBookCourse -> {
            RedBookCourseVO redBookCourseVO = new RedBookCourseVO();
            BeanUtils.copyProperties(redBookCourse, redBookCourseVO);
            //如果版本未发布，那课程不允许发布
            if(versionPublishStatus==0){
                redBookCourseVO.setPublishStatus(-1);
            }else{
                redBookCourseVO.setPublishStatus(RedBookCourseManager.getRedBookCourse(redBookCourse.getId())==null?0:1);
            }
            list.add(redBookCourseVO);
        });
        return list;
    }


    @Override
    public boolean insertCourse(Integer versionId, String nameEn, String nameCn, Integer stage, Integer grade,String paramString,Integer resourceCourseId) {
        Integer maxDisplayOrder = resourceCourseDao.getMaxDisplayOrder();
        if(maxDisplayOrder==null){
            maxDisplayOrder=0;
        }
        Integer displayOrder=maxDisplayOrder+1;
        ResourceCourse resourceCourse=new ResourceCourse();
        resourceCourse.setVersionId(versionId);
        resourceCourse.setNameEn(nameEn);
        resourceCourse.setNameCn(nameCn);
        resourceCourse.setUnitNum(0);
        resourceCourse.setStage(stage);
        resourceCourse.setGrade(grade);
        resourceCourse.setDisplayOrder(displayOrder);
        boolean insertResult=false;
        boolean insertState=false;
        if(resourceCourseId==null){
            insertResult = resourceCourseDao.insertCourse(resourceCourse);
            resourceCourseId = resourceCourse.getId();
            insertState=true;
        }else{
            insertResult = resourceCourseDao.updateCourse(nameEn,nameCn,grade,resourceCourseId);
        }
        if(insertResult){
            List<Map<String,Object>> paramList = GsonManager.fromJson(paramString, List.class);
            Map<String, Object> word1 = paramList.get(0);
            Map<String, Object> word2 = paramList.get(1);
            Map<String, Object> word3 = paramList.get(2);
            Map<String, Object> sentence = paramList.get(3);
            Map<String, Object> grammar= paramList.get(4);
            Map<String, Object> question = paramList.get(5);
            Map<String, Object> article = paramList.get(6);
            //单词1
            if(!insertState){
                if(word1!=null&&word1.get("oldProgramName")!=null){
                    resourceCourseDao.delOldCourseWord(resourceCourseId,word1.get("oldProgramName").toString());
                    List<Integer> oldProgramName = resourceUnitDao.getUnitContentWordIdList(resourceCourseId);
                    if(!word1.get("oldProgramName").equals(word1.get("programName"))){
                        for (Integer id : oldProgramName) {
                            resourceUnitDao.delUnitContentWord(id,word1.get("oldProgramName").toString());
                        }
                    }
                }
            }
            if(word1!=null&&word1.get("programName")!=null){
                resourceCourseDao.insertCourseWord(resourceCourseId,word1.get("programName").toString(),word1.get("showName").toString());
            }

            //单词2
            if(!insertState){
                if (word2!=null&&word2.get("oldProgramName")!=null){
                    resourceCourseDao.delOldCourseWord(resourceCourseId,word2.get("oldProgramName").toString());
                    List<Integer> oldProgramName = resourceUnitDao.getUnitContentWordIdList(resourceCourseId);
                    if(!word1.get("oldProgramName").equals(word2.get("programName"))){
                        for (Integer id : oldProgramName) {
                            resourceUnitDao.delUnitContentWord(id,word2.get("oldProgramName").toString());
                        }
                    }
                }
            }
            if(word2!=null&&word2.get("programName")!=null){
                resourceCourseDao.insertCourseWord(resourceCourseId,word2.get("programName").toString(),word2.get("showName").toString());
            }

            //单词3
            if(!insertState){
                if(word3!=null&&word3.get("oldProgramName")!=null){
                    resourceCourseDao.delOldCourseWord(resourceCourseId,word3.get("oldProgramName").toString());
                    List<Integer> oldProgramName = resourceUnitDao.getUnitContentWordIdList(resourceCourseId);
                    if(!word1.get("oldProgramName").equals(word3.get("programName"))){
                        for (Integer id : oldProgramName) {
                            resourceUnitDao.delUnitContentWord(id,word3.get("oldProgramName").toString());
                        }
                    }
                }
            }
            if(word3!=null&&word3.get("programName")!=null){
                resourceCourseDao.insertCourseWord(resourceCourseId,word3.get("programName").toString(),word3.get("showName").toString());
            }
            //句子
            Map<String, Object> courseSentence = resourceCourseDao.getCourseSentence(resourceCourseId);
            if(!insertState){
                if(courseSentence!=null&&sentence!=null&&!courseSentence.get("programName").equals(sentence.get("programName"))){
                        List<Integer> oldProgramName = resourceUnitDao.getUnitContentSentenceIdList(resourceCourseId,sentence.get("programName").toString());
                        for (Integer id : oldProgramName) {
                            resourceUnitDao.delUnitContentSentence(id);
                        }
                    resourceCourseDao.delOldCourseSentence(resourceCourseId);
                    courseSentence = resourceCourseDao.getCourseSentence(resourceCourseId);
                }
            }
            if(sentence!=null&&courseSentence==null){
                resourceCourseDao.insertCourseSentence(resourceCourseId,sentence.get("programName").toString(),sentence.get("showName").toString());
            }

            //优题
            Map<String, Object> courseQuestion = resourceCourseDao.getCourseQuestion(resourceCourseId);
            if(!insertState){
                if(courseQuestion!=null&&question!=null&&!courseQuestion.get("programId").equals(question.get("programId"))){
                        List<Integer> oldProgramName = resourceUnitDao.getUnitContentQuestionIdList(resourceCourseId,Integer.valueOf(((Double) question.get("programId")).intValue()));
                        for (Integer id : oldProgramName) {
                            resourceUnitDao.delUnitContentQuestion(id);
                        }
                    resourceCourseDao.delOldCourseQuestion(resourceCourseId);
                    courseQuestion = resourceCourseDao.getCourseQuestion(resourceCourseId);
                }
                }
            if(question!=null&&courseQuestion==null){
                resourceCourseDao.insertCourseQuestion(resourceCourseId,Integer.valueOf(((Double) question.get("programId")).intValue()),question.get("showName").toString());
            }
            //语法
            Map<String, Object> courseGrammar = resourceCourseDao.getCourseGrammar(resourceCourseId);
                if(!insertState){
                    if(courseGrammar!=null&&grammar!=null&&!courseGrammar.get("programId").equals(grammar.get("programId"))){
                            List<Integer> oldProgramName = resourceUnitDao.getUnitContentGrammarIdList(resourceCourseId,Integer.valueOf(((Double) grammar.get("programId")).intValue()));
                            for (Integer id : oldProgramName) {
                                resourceUnitDao.delUnitContentGrammar(id);
                            }
                        resourceCourseDao.delOldCourseGrammar(resourceCourseId);
                        courseGrammar = resourceCourseDao.getCourseGrammar(resourceCourseId);
                    }
                }
            if(grammar!=null&&courseGrammar==null){
                resourceCourseDao.insertCourseGrammar(resourceCourseId,Integer.valueOf(((Double) grammar.get("programId")).intValue()),grammar.get("showName").toString());
            }

            //课文
            Map<String, Object> courseArticle = resourceCourseDao.getCourseArticle(resourceCourseId);
            if(!insertState){
                    if(courseArticle!=null&&article!=null&&!courseArticle.get("programName").equals(article.get("programName"))){
                            List<Integer> oldProgramName = resourceUnitDao.getUnitContentArticleIdList(resourceCourseId,article.get("programName").toString());
                            for (Integer id : oldProgramName) {
                                resourceUnitDao.delUnitContentArticle(id);
                            }
                        resourceCourseDao.delOldCourseArticle(resourceCourseId);
                        courseArticle = resourceCourseDao.getCourseArticle(resourceCourseId);
                    }
                }
            if(article!=null&&courseArticle==null){
                resourceCourseDao.insertCourseArticle(resourceCourseId,article.get("programName").toString(),article.get("showName").toString());
            }

        }

        return true;

    }

    @Override
    public Map<String, Object> getCourseById(Integer id) {
        Map<String, Object> courseById = resourceCourseDao.getCourseById(id);
        return courseById;
    }

    @Override
    public boolean updateCourse(String nameEn, String nameCn, Integer grade, Integer id) {
        return resourceCourseDao.updateCourse(nameEn,nameCn,grade,id);
    }



    @Override
    public Map<String,Object> getCourseRelevant(Integer courseId){
        Map<String,Object> stringObjectMap=new HashMap<>();
        stringObjectMap.put("courseWord",resourceCourseDao.getCourseWord(courseId));
        stringObjectMap.put("courseSentence",resourceCourseDao.getCourseSentence(courseId));
        stringObjectMap.put("courseQuestion",resourceCourseDao.getCourseQuestion(courseId));
        stringObjectMap.put("courseGrammar",resourceCourseDao.getCourseGrammar(courseId));
        stringObjectMap.put("courseArticle",resourceCourseDao.getCourseArticle(courseId));
        return stringObjectMap;
    }

    @Override
    public Map<String, Object> isHaveCourseWord(String programName) {
        return resourceCourseDao.isHaveCourseWord(programName);
    }

    @Override
    public Map<String, Object> isHaveCourseSentence(String programName) {
        return resourceCourseDao.isHaveCourseSentence(programName);
    }

    @Override
    public Map<String, Object> isHaveCourseArticle(String programName) {
        return resourceCourseDao.isHaveCourseArticle(programName);
    }

    @Override
    public Map<String, Object> isHaveCourseQuestion(Integer programId) {
        return resourceCourseDao.isHaveCourseQuestion(programId);
    }

    @Override
    public Map<String, Object> isHaveCourseGrammar(Integer programId) {
        return resourceCourseDao.isHaveCourseGrammar(programId);
    }

    @Override
    public boolean insertCourseStage(Integer courseId,Integer... stageList) {
        resourceVersionDao.deleteCourseStage(courseId);
        for (Integer redBookCourseStage : stageList) {
            resourceVersionDao.insertCourseStage(courseId,redBookCourseStage);
        }
        RedBookCourseManager.reloadCourse(courseId);
        return true;
    }


}
