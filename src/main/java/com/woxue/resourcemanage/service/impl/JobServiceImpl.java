package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.WordBean;
import com.woxue.common.util.GsonManager;
import com.woxue.common.util.MD5;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.entity.AiMnemonicDTO;
import com.woxue.resourcemanage.service.AiGenerateService;
import com.woxue.resourcemanage.service.JobService;
import com.woxue.resourceservice.util.RedisManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class JobServiceImpl implements JobService {


    @Autowired
    IRedBookCourseService redBookCourseService;

    @Autowired
    AiGenerateService aiGenerateService;

    @Override
    public void aiMnemonicJob() {
        String key = "aiMnemonicFeedBack:";

        List<String> stringList = Arrays.asList("resourceWord", "word", "sentence", "write", "article");
        String redisKey = null;
        String redisStr = null;

        for(String moduleType : stringList){
            List<String> resourceListRange = RedisManager.getResourceListRange(key + moduleType, 0, -1);
            if(resourceListRange.isEmpty()){
                continue;
            }
            List<AiMnemonicDTO> list = resourceListRange.stream().map(resource -> {
                return GsonManager.fromJson(resource, AiMnemonicDTO.class);
            }).collect(Collectors.toList());
            for(AiMnemonicDTO aiMnemonicDTO : list){

                if(aiMnemonicDTO.getLikeNum() == 0 && aiMnemonicDTO.getDislikeNum() == 0){
                    continue;
                }

                //赋值反馈内容
                switch (moduleType){
                    case "article":
                        redisKey= "aiReciteMethod" + ":" + aiMnemonicDTO.getArticleId() + ":" + aiMnemonicDTO.getGradeName();
                        redisStr = RedisManager.getString(redisKey);
                        aiMnemonicDTO.setFeedContent(redisStr);
                        break;
                    case "write":
                        redisKey="aiWriteGuide"+":" +aiMnemonicDTO.getCourseId()+":"+ aiMnemonicDTO.getUnitId() + ":" + aiMnemonicDTO.getGradeName();
                        redisStr = RedisManager.getString(redisKey);
                        break;
                    case "sentence":
                        redisKey="aiSentenceStructure"+":" + MD5.newMd5(aiMnemonicDTO.getRelationContent());
                        redisStr = RedisManager.getString(redisKey);
                        break;
                    case "word":
                        WordBean wordBean = redBookCourseService.getWordBeanById(aiMnemonicDTO.getWordId());
                        redisKey="aiWordMemory"+":"+ MD5.newMd5(wordBean.getSpelling());
                        redisStr = RedisManager.getString(redisKey);
                        break;
                    case "resourceWord":
                        redisStr = redBookCourseService.getWordMnemonics(aiMnemonicDTO.getWordId());
                        break;
                }
                aiMnemonicDTO.setFeedContent(redisStr);
                aiGenerateService.saveOrUpdate(aiMnemonicDTO);
            }
            //入库后，清空缓存
            RedisManager.delResourceBeanFromList(key + moduleType);
        }
    }
}
