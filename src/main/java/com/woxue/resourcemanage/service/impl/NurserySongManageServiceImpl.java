package com.woxue.resourcemanage.service.impl;

import com.redbook.kid.common.model.NurserySongStatusEnum;
import com.redbook.kid.common.model.NurserySongDO;
import com.redbook.kid.common.model.NurserySongSentenceDO;
import com.redbook.kid.common.model.NurserySongWithSentencesDTO;
import com.woxue.resourcemanage.util.NurserySongValidatorUtil;
import com.woxue.resourcemanage.util.SubtitleParserUtil;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.dao.INurserySongDao;
import com.woxue.resourcemanage.dao.INurserySongSentenceDao;
import com.woxue.resourcemanage.service.INurserySongManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 儿歌管理服务实现类
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
@Slf4j
@Service
public class NurserySongManageServiceImpl implements INurserySongManageService {

    @Autowired
    private INurserySongDao nurserySongDao;

    @Autowired
    private INurserySongSentenceDao nurserySongSentenceDao;

    @Override
    @Transactional
    public HSSJsonReulst<Integer> addSong(NurserySongDO song) {
        try {
            // 数据验证
            NurserySongValidatorUtil.ValidationResult validationResult = NurserySongValidatorUtil.validateSong(song);
            if (!validationResult.isValid()) {
                return HSSJsonReulst.errorMsg("数据验证失败: " + validationResult.toString());
            }

            // 设置默认值
            if (song.getPlayCount() == null) {
                song.setPlayCount(0);
            }
            if (song.getRecordingCount() == null) {
                song.setRecordingCount(0);
            }
            if (song.getSentenceCount() == null) {
                song.setSentenceCount(0);
            }
            if (song.getStatus() == null) {
                song.setStatus(NurserySongStatusEnum.PENDING.getCode());
            }
            if (song.getSortOrder() == null) {
                song.setSortOrder(0);
            }

            int result = nurserySongDao.insertSong(song);
            if (result > 0) {
                log.info("添加儿歌成功，ID: {}, 标题: {}", song.getId(), song.getTitle());

                // 注意：添加儿歌后，需要手动调用处理接口来解析字幕文件

                return HSSJsonReulst.ok(song.getId());
            } else {
                return HSSJsonReulst.errorMsg("添加儿歌失败");
            }
        } catch (Exception e) {
            log.error("添加儿歌异常", e);
            return HSSJsonReulst.errorMsg("添加儿歌异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<Boolean> updateSong(NurserySongDO song) {
        try {
            if (song.getId() == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            // 检查儿歌是否存在
            NurserySongDO existingSong = nurserySongDao.getSongById(song.getId());
            if (existingSong == null) {
                return HSSJsonReulst.errorMsg("儿歌不存在");
            }

            // 数据验证
            NurserySongValidatorUtil.ValidationResult validationResult = NurserySongValidatorUtil.validateSong(song);
            if (!validationResult.isValid()) {
                return HSSJsonReulst.errorMsg("数据验证失败: " + validationResult.toString());
            }

            int result = nurserySongDao.updateSong(song);
            if (result > 0) {
                log.info("更新儿歌成功，ID: {}, 标题: {}", song.getId(), song.getTitle());
                return HSSJsonReulst.ok(true);
            } else {
                return HSSJsonReulst.errorMsg("更新儿歌失败");
            }
        } catch (Exception e) {
            log.error("更新儿歌异常", e);
            return HSSJsonReulst.errorMsg("更新儿歌异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<Boolean> deleteSong(Integer id) {
        try {
            if (id == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            // 检查儿歌是否存在
            NurserySongDO existingSong = nurserySongDao.getSongById(id);
            if (existingSong == null) {
                return HSSJsonReulst.errorMsg("儿歌不存在");
            }

            // 删除儿歌及其句子
            nurserySongSentenceDao.deleteSentencesBySongId(id);
            int result = nurserySongDao.deleteSong(id);

            if (result > 0) {
                log.info("删除儿歌成功，ID: {}", id);
                return HSSJsonReulst.ok(true);
            } else {
                return HSSJsonReulst.errorMsg("删除儿歌失败");
            }
        } catch (Exception e) {
            log.error("删除儿歌异常", e);
            return HSSJsonReulst.errorMsg("删除儿歌异常: " + e.getMessage());
        }
    }

    @Override
    public HSSJsonReulst<NurserySongWithSentencesDTO> getSongDetail(Integer id) {
        try {
            if (id == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            NurserySongDO song = nurserySongDao.getSongById(id);
            if (song == null) {
                return HSSJsonReulst.errorMsg("儿歌不存在");
            }

            List<NurserySongSentenceDO> sentences = nurserySongSentenceDao.getSentencesBySongId(id);
            NurserySongWithSentencesDTO result = new NurserySongWithSentencesDTO(song, sentences);

            return HSSJsonReulst.ok(result);
        } catch (Exception e) {
            log.error("获取儿歌详情异常", e);
            return HSSJsonReulst.errorMsg("获取儿歌详情异常: " + e.getMessage());
        }
    }

    @Override
    public HSSJsonReulst<Map<String, Object>> getSongList(Map<String, Object> params) {
        try {
            // 修正分页参数类型，确保为int
            Object offsetObj = params.get("offset");
            Object pageSizeObj = params.get("pageSize");
            if (offsetObj != null) {
                int offset = (offsetObj instanceof Number) ? ((Number) offsetObj).intValue() : Integer.parseInt(offsetObj.toString());
                params.put("offset", offset);
            }
            if (pageSizeObj != null) {
                int pageSize = (pageSizeObj instanceof Number) ? ((Number) pageSizeObj).intValue() : Integer.parseInt(pageSizeObj.toString());
                params.put("pageSize", pageSize);
            }
            List<NurserySongDO> songs = nurserySongDao.getSongList(params);
            int totalCount = nurserySongDao.getSongCount(params);
            Map<String, Object> result = new HashMap<>();
            result.put("list", songs);
            result.put("totalCount", totalCount);

            return HSSJsonReulst.ok(result);
        } catch (Exception e) {
            log.error("获取儿歌列表异常", e);
            return HSSJsonReulst.errorMsg("获取儿歌列表异常: " + e.getMessage());
        }
    }

    @Override
    public HSSJsonReulst<List<SubtitleParserUtil.SentenceTimeline>> uploadAndParseSubtitle(Integer songId, MultipartFile subtitleFile) {
        try {
            if (songId == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            if (subtitleFile == null || subtitleFile.isEmpty()) {
                return HSSJsonReulst.errorMsg("字幕文件不能为空");
            }

            // 检查儿歌是否存在
            NurserySongDO song = nurserySongDao.getSongById(songId);
            if (song == null) {
                return HSSJsonReulst.errorMsg("儿歌不存在");
            }

            // 读取文件内容
            String content = new String(subtitleFile.getBytes(), StandardCharsets.UTF_8);
            String fileName = subtitleFile.getOriginalFilename();

            // 解析字幕
            List<SubtitleParserUtil.SentenceTimeline> timelines = SubtitleParserUtil.parseSubtitle(content, fileName);

            if (timelines.isEmpty()) {
                return HSSJsonReulst.errorMsg("字幕文件解析失败，未找到有效内容");
            }

            log.info("字幕解析成功，儿歌ID: {}, 句子数量: {}", songId, timelines.size());
            return HSSJsonReulst.ok(timelines);

        } catch (IOException e) {
            log.error("读取字幕文件异常", e);
            return HSSJsonReulst.errorMsg("读取字幕文件异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("解析字幕文件异常", e);
            return HSSJsonReulst.errorMsg("解析字幕文件异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<ProcessingResult> processSongResources(Integer songId) {
        try {
            if (songId == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            // 获取儿歌信息
            NurserySongDO song = nurserySongDao.getSongById(songId);
            if (song == null) {
                return HSSJsonReulst.errorMsg("儿歌不存在");
            }

            // 检查字幕文件URL
            if (song.getSubtitleFileUrl() == null || song.getSubtitleFileUrl().trim().isEmpty()) {
                return HSSJsonReulst.errorMsg("字幕文件URL为空，无法处理");
            }

            // 下载并解析字幕文件
            String subtitleContent = downloadSubtitleFile(song.getSubtitleFileUrl());
            if (subtitleContent == null || subtitleContent.trim().isEmpty()) {
                return HSSJsonReulst.errorMsg("字幕文件下载失败或内容为空");
            }

            // 解析字幕生成句子时间轴
            String fileName = extractFileNameFromUrl(song.getSubtitleFileUrl());
            List<SubtitleParserUtil.SentenceTimeline> timelines =
                SubtitleParserUtil.parseSubtitle(subtitleContent, fileName);

            if (timelines.isEmpty()) {
                return HSSJsonReulst.errorMsg("字幕文件解析失败，未找到有效内容");
            }

            // 验证解析结果
            NurserySongValidatorUtil.ValidationResult validationResult =
                validateTimelineData(timelines, song.getDuration());

            if (!validationResult.isValid()) {
                log.warn("字幕解析数据验证有警告，songId: {}, 警告: {}", songId, validationResult.toString());
            }

            // 删除旧的句子数据
            nurserySongSentenceDao.deleteSentencesBySongId(songId);

            // 转换并批量插入新的句子数据
            List<NurserySongSentenceDO> sentences = convertToSentenceEntities(timelines, songId);
            int insertCount = nurserySongSentenceDao.batchInsertSentences(sentences);

            // 更新儿歌的句子数量
            nurserySongDao.updateSentenceCount(songId, sentences.size());

            ProcessingResult result = ProcessingResult.success(songId, sentences.size());
            result.setMessage(String.format("处理成功，共解析 %d 个句子，插入 %d 条记录",
                timelines.size(), insertCount));
            result.setValidationResult(validationResult);

            log.info("儿歌资源处理成功，songId: {}, 句子数: {}", songId, sentences.size());
            return HSSJsonReulst.ok(result);

        } catch (Exception e) {
            log.error("处理儿歌资源异常", e);
            ProcessingResult result = ProcessingResult.failure("处理异常: " + e.getMessage(), e);
            return HSSJsonReulst.errorMsg(result.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<Boolean> updateSongStatus(Integer id, Integer status) {
        try {
            if (id == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            if (status == null) {
                return HSSJsonReulst.errorMsg("状态不能为空");
            }

            // 验证状态值
            NurserySongStatusEnum statusEnum = NurserySongStatusEnum.fromCode(status);
            if (statusEnum == null) {
                return HSSJsonReulst.errorMsg("无效的状态值");
            }

            int result = nurserySongDao.updateSongStatus(id, status);
            log.info("更新儿歌状态，儿歌ID: {}, 状态: {}, 影响行数: {}", id, statusEnum.getDesc(), result);
            return HSSJsonReulst.ok(result > 0);

        } catch (Exception e) {
            log.error("更新儿歌状态异常", e);
            return HSSJsonReulst.errorMsg("更新状态异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<Boolean> batchUpdateStatus(List<Integer> ids, Integer status) {
        try {
            if (ids == null || ids.isEmpty()) {
                return HSSJsonReulst.errorMsg("儿歌ID列表不能为空");
            }

            if (status == null) {
                return HSSJsonReulst.errorMsg("状态不能为空");
            }

            // 验证状态值
            NurserySongStatusEnum statusEnum = NurserySongStatusEnum.fromCode(status);
            if (statusEnum == null) {
                return HSSJsonReulst.errorMsg("无效的状态值");
            }

            int result = nurserySongDao.batchUpdateStatus(ids, status);
            log.info("批量更新儿歌状态，ID列表: {}, 状态: {}, 影响行数: {}", ids, statusEnum.getDesc(), result);
            return HSSJsonReulst.ok(result > 0);

        } catch (Exception e) {
            log.error("批量更新儿歌状态异常", e);
            return HSSJsonReulst.errorMsg("批量更新状态异常: " + e.getMessage());
        }
    }

    @Override
    public HSSJsonReulst<List<NurserySongSentenceDO>> getSentencesBySongId(Integer songId) {
        try {
            if (songId == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }

            List<NurserySongSentenceDO> sentences = nurserySongSentenceDao.getSentencesBySongId(songId);
            return HSSJsonReulst.ok(sentences);

        } catch (Exception e) {
            log.error("获取句子列表异常", e);
            return HSSJsonReulst.errorMsg("获取句子列表异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<Boolean> updateSentence(NurserySongSentenceDO sentence) {
        try {
            if (sentence == null || sentence.getId() == null) {
                return HSSJsonReulst.errorMsg("句子信息不能为空");
            }

            // 验证句子数据
            NurserySongValidatorUtil.ValidationResult validationResult =
                NurserySongValidatorUtil.validateSentence(sentence, sentence.getSentenceIndex(), null);

            if (!validationResult.isValid()) {
                return HSSJsonReulst.errorMsg("句子数据验证失败: " + validationResult.toString());
            }

            // 计算时长
            if (sentence.getStartTime() != null && sentence.getEndTime() != null) {
                sentence.setDuration(sentence.getEndTime() - sentence.getStartTime());
            }

            int result = nurserySongSentenceDao.updateSentence(sentence);
            if (result > 0) {
                log.info("更新句子成功，ID: {}", sentence.getId());
                return HSSJsonReulst.ok(true);
            } else {
                return HSSJsonReulst.errorMsg("更新句子失败");
            }

        } catch (Exception e) {
            log.error("更新句子异常", e);
            return HSSJsonReulst.errorMsg("更新句子异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public HSSJsonReulst<Boolean> deleteSentence(Integer sentenceId) {
        try {
            if (sentenceId == null) {
                return HSSJsonReulst.errorMsg("句子ID不能为空");
            }

            int result = nurserySongSentenceDao.deleteSentence(sentenceId);
            if (result > 0) {
                log.info("删除句子成功，ID: {}", sentenceId);
                return HSSJsonReulst.ok(true);
            } else {
                return HSSJsonReulst.errorMsg("删除句子失败，句子可能不存在");
            }

        } catch (Exception e) {
            log.error("删除句子异常", e);
            return HSSJsonReulst.errorMsg("删除句子异常: " + e.getMessage());
        }
    }

    @Override
    public HSSJsonReulst<NurserySongValidatorUtil.ValidationResult> validateSongData(NurserySongDO song, List<NurserySongSentenceDO> sentences) {
        try {
            // 验证儿歌基础信息
            NurserySongValidatorUtil.ValidationResult songResult = NurserySongValidatorUtil.validateSong(song);

            // 验证句子列表
            NurserySongValidatorUtil.ValidationResult sentenceResult =
                NurserySongValidatorUtil.validateSentences(sentences, song.getDuration());

            // 合并验证结果
            songResult.merge(sentenceResult);

            return HSSJsonReulst.ok(songResult);

        } catch (Exception e) {
            log.error("验证儿歌数据异常", e);
            return HSSJsonReulst.errorMsg("验证数据异常: " + e.getMessage());
        }
    }

    @Override
    public HSSJsonReulst<ProcessingResult> reprocessSongResources(Integer songId) {
        // 重新处理逻辑与processSongResources类似
        return processSongResources(songId);
    }

    @Override
    public HSSJsonReulst<BatchProcessingResult> batchProcessSongs(List<Integer> songIds) {
        try {
            if (songIds == null || songIds.isEmpty()) {
                return HSSJsonReulst.errorMsg("儿歌ID列表不能为空");
            }

            List<ProcessingResult> results = new ArrayList<>();
            int successCount = 0;
            int failureCount = 0;

            for (Integer songId : songIds) {
                HSSJsonReulst<ProcessingResult> result = processSongResources(songId);
                if (result.isOK() && result.getData().isSuccess()) {
                    successCount++;
                    results.add(result.getData());
                } else {
                    failureCount++;
                    ProcessingResult failResult = ProcessingResult.failure(result.getMsg(), null);
                    failResult.setSongId(songId);
                    results.add(failResult);
                }
            }

            BatchProcessingResult batchResult = new BatchProcessingResult(
                songIds.size(), successCount, failureCount, results);

            return HSSJsonReulst.ok(batchResult);

        } catch (Exception e) {
            log.error("批量处理儿歌资源异常", e);
            return HSSJsonReulst.errorMsg("批量处理异常: " + e.getMessage());
        }
    }

    /**
     * 下载字幕文件内容
     */
    private String downloadSubtitleFile(String subtitleFileUrl) {
        try {
            if (subtitleFileUrl.startsWith("http://") || subtitleFileUrl.startsWith("https://")) {
                // 从网络URL下载
                java.net.URL url = new java.net.URL(subtitleFileUrl);
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(url.openStream(), StandardCharsets.UTF_8))) {
                    StringBuilder content = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line).append("\n");
                    }
                    return content.toString();
                }
            } else {
                // 本地文件路径或相对路径
                log.warn("暂不支持本地文件路径下载: {}", subtitleFileUrl);
                return null;
            }
        } catch (Exception e) {
            log.error("下载字幕文件失败，URL: {}", subtitleFileUrl, e);
            return null;
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "subtitle.srt";
        }

        try {
            String fileName = url.substring(url.lastIndexOf("/") + 1);
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            return fileName.isEmpty() ? "subtitle.srt" : fileName;
        } catch (Exception e) {
            log.warn("提取文件名失败，使用默认文件名，URL: {}", url);
            return "subtitle.srt";
        }
    }

    /**
     * 验证时间轴数据
     */
    private NurserySongValidatorUtil.ValidationResult validateTimelineData(
            List<SubtitleParserUtil.SentenceTimeline> timelines, Integer songDuration) {

        // 转换为句子实体进行验证
        List<NurserySongSentenceDO> sentences = new ArrayList<>();
        for (SubtitleParserUtil.SentenceTimeline timeline : timelines) {
            NurserySongSentenceDO sentence = new NurserySongSentenceDO();
            sentence.setSentenceIndex(timeline.getIndex());
            sentence.setStartTime(timeline.getStartTime());
            sentence.setEndTime(timeline.getEndTime());
            
            // 处理双语字幕：将中文放入拼音字段，英文放入歌词字段
            if (timeline.getIsBilingual() != null && timeline.getIsBilingual()) {
                // 双语字幕：英文作为歌词文本，中文作为拼音
                sentence.setLyricsText(timeline.getEnglishText());
                sentence.setLyricsPinyin(timeline.getChineseText());
            } else {
                // 单语字幕：判断是中文还是英文
                String text = timeline.getText();
                if (containsChinese(text)) {
                    // 中文字幕：中文放入拼音字段，歌词字段为空
                    sentence.setLyricsText("");
                    sentence.setLyricsPinyin(text);
                } else {
                    // 英文字幕：英文放入歌词字段，拼音字段为空
                    sentence.setLyricsText(text);
                    sentence.setLyricsPinyin("");
                }
            }
            
            sentence.setDuration(timeline.getDuration());
            sentences.add(sentence);
        }

        return NurserySongValidatorUtil.validateSentences(sentences, songDuration);
    }

    /**
     * 转换时间轴数据为句子实体
     */
    private List<NurserySongSentenceDO> convertToSentenceEntities(
            List<SubtitleParserUtil.SentenceTimeline> timelines, Integer songId) {

        List<NurserySongSentenceDO> sentences = new ArrayList<>();

        for (SubtitleParserUtil.SentenceTimeline timeline : timelines) {
            NurserySongSentenceDO sentence = new NurserySongSentenceDO();
            sentence.setSongId(songId);
            sentence.setSentenceIndex(timeline.getIndex());
            sentence.setStartTime(timeline.getStartTime());
            sentence.setEndTime(timeline.getEndTime());
            
            // 处理双语字幕：将中文放入拼音字段，英文放入歌词字段
            if (timeline.getIsBilingual() != null && timeline.getIsBilingual()) {
                // 双语字幕：英文作为歌词文本，中文作为拼音
                sentence.setLyricsText(timeline.getEnglishText());
                sentence.setLyricsPinyin(timeline.getChineseText());
            } else {
                // 单语字幕：判断是中文还是英文
                String text = timeline.getText();
                if (containsChinese(text)) {
                    // 中文字幕：中文放入拼音字段，歌词字段为空
                    sentence.setLyricsText("");
                    sentence.setLyricsPinyin(text);
                } else {
                    // 英文字幕：英文放入歌词字段，拼音字段为空
                    sentence.setLyricsText(text);
                    sentence.setLyricsPinyin("");
                }
            }
            
            sentence.setDuration(timeline.getDuration());

            // 设置默认难度评分
            sentence.setDifficultyScore(BigDecimal.valueOf(1.0));

            sentences.add(sentence);
        }

        return sentences;
    }
    
    /**
     * 判断文本是否包含中文字符
     *
     * @param text 待检查的文本
     * @return 如果包含中文字符返回true，否则返回false
     */
    private boolean containsChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        for (char c : text.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT) {
                return true;
            }
        }
        return false;
    }
}
