package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.GrammarQuestionContentBean;
import com.woxue.common.model.redBook.RedBookConstant;
import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.common.util.OSSManager;
import com.woxue.resourcemanage.dao.INewGrammarManagerDao;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.entity.grammar.*;
import com.woxue.resourcemanage.service.INewGrammarManagerService;
import com.woxue.resourcemanage.util.ContentStack;
import com.woxue.resourcemanage.util.WaterMarkUtils;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import com.woxue.resourceservice.util.RedBookRedisManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022 -02-23 10:46
 */
@Service
public class NewGrammarManagerServiceImpl implements INewGrammarManagerService {

    private final String TEMP_GRAMMAR_IMAGE_PREFIX = "tempNewGrammarImage:userId:unitId:";
    private final String IMAGE_SOURCE_DIR_NAME = "newGrammarUploadSource";
    private final String IMAGE_DIR_NAME = "newGrammarUpload";
    @Autowired
    INewGrammarManagerDao grammarManagerDao;
    @Autowired
    IRedBookCourseDao redBookCourseDao;
    @Autowired
    IResourceCourseDao resourceCourseDao;

    static final String ENDPOINT = "http://oss-cn-beijing.aliyuncs.com";

    public List<SimulationQuestionBean> getCourseQuestionList(Integer courseId, Integer pageNum, Integer pageSize){
        Integer pageStart = (pageNum-1)*pageSize;
        return grammarManagerDao.getCourseQuestionList(courseId, pageStart, pageSize);
    }

    public List<Map<String, Object>> getCourseQuestionNum(Integer courseId) {
        return grammarManagerDao.getCourseQuestionNum(courseId);
    }

    @Override
    public List<GrammarUnitBean> getUnitList(Integer courseId) {
        List<GrammarUnitBean> unitList = grammarManagerDao.getUnitList(courseId);
        for (GrammarUnitBean unit : unitList) {
            unit.setGrammarNewUnitBean(this.getGrammarCardDetail(unit.getUnitId()));
        }
        return unitList;
    }

    @Override
    public Boolean addGrammarUnit(Integer unitId) {
        //获取课程 判断是否有grammar
        RedBookCourse course = redBookCourseDao.getCourseByUnitId(unitId);
        if (course!=null&&!course.isContainGrammar()){
            resourceCourseDao.updateCourseContentContainStatus(course.getId(), RedBookContentTypeEnum.GRAMMAR, true);
        }
        return grammarManagerDao.addGrammarUnit(unitId)>0;
    }

    public Map<String, Object> getCourseFullNameById(Integer courseId) {
        GrammarCourseBean course = grammarManagerDao.getCourse(courseId);
        Map<String, Object> map = new HashMap<>();
        if(course!=null){
            GrammarVersionBean version = grammarManagerDao.getVersion(course.getVersionId());
            map.put("courseName", course.getName());
            map.put("courseId", course.getId());
            map.put("versionName", version.getName());
            map.put("versionId", version.getId());
            map.put("versionType", version.getVersionType());
            switch(version.getGrade()){
                case 1: map.put("gradeName", "小学");
                    version.setGrade(30);
                break;
                case 2: map.put("gradeName", "初中"); version.setGrade(40);break;
                case 3: map.put("gradeName", "高中"); version.setGrade(50); break;
            }
            map.put("gradeId", version.getGrade());
        }
        return map;
    }

    @Override
    public Integer getCourseQuestionIdMinNum(Integer courseId) {
        return grammarManagerDao.getCourseQuestionIdMinNum(courseId);
    }

    @Override
    public boolean addCourseQuestionList(List<SimulationQuestionBean> questionList) {
        if(questionList.isEmpty()){
            return false;
        }
        for (SimulationQuestionBean question : questionList) {
            grammarManagerDao.addCourseQuestion(question);
        }
        return true;
    }

    @Override
    public boolean updateCourseQuestion(SimulationQuestionBean question) {
        return grammarManagerDao.updateCourseQuestion(question);
    }

    public boolean deleteCourseQuestion(String teachId, Integer courseId,Integer[] deleteIdArr) {
        if(deleteIdArr==null || deleteIdArr.length==0){
            return false;
        }
        boolean flag = grammarManagerDao.deleteCourseQuestion(deleteIdArr);
        for (Integer id : deleteIdArr) {
            SimulationQuestionBean question=grammarManagerDao.getCourseQuestionById(id);
        }
        return flag;
    }

    public Map<String, Object> getSimulationQuestionList(String aid,
                                                         String teachId, Integer[] sourceTypeArr, Integer gradePhase, Integer[] gradeArr,
                                                         String[] knowledgePointArr, Integer[] questionTypeArr,
                                                         Integer[] difficultyArr, String orderBy, Integer pageNum, Integer pageSize) {
        //处理知识点
        List<Map<String, String>> knowledgePointList = new ArrayList<Map<String, String>>();
        Map<String, String> knowledgeMap = null;
        String endKnowledge;
        for (String startKnowledge : knowledgePointArr) {
            knowledgeMap = new HashMap<String, String>();
            if(startKnowledge.endsWith("00")){
                if(startKnowledge.endsWith("0000")){
                    if(startKnowledge.endsWith("000000")){
                        endKnowledge = startKnowledge.replaceAll("000000$", "999999"); //一级
                    }else{
                        endKnowledge = startKnowledge.replaceAll("0000$", "9999");  //二级
                    }
                }else{
                    endKnowledge = startKnowledge.replaceAll("00$", "99");  //三级
                }
            }else{
                endKnowledge = startKnowledge;  //四级
            }
            knowledgeMap.put("start", startKnowledge);
            knowledgeMap.put("end", endKnowledge);
            knowledgePointList.add(knowledgeMap);
        }


        Map<String, Object> map = new HashMap<String, Object>();
        List<SimulationQuestionBean> questionList = new ArrayList<SimulationQuestionBean>();
        if(sourceTypeArr==null || sourceTypeArr.length==0){
            sourceTypeArr = new Integer[]{0,1,2,3};
        }
        if(isContain(sourceTypeArr,1)){//系统题库
            List<SimulationQuestionBean> systemQuestionList = grammarManagerDao.getSystemQuestionList(gradePhase, gradeArr, knowledgePointList, questionTypeArr,difficultyArr);
            questionList.addAll(systemQuestionList);
        }
        if(isContain(sourceTypeArr,2)){//共享题库
            List<SimulationQuestionBean> shareQuestionList = null;
            if(isContain(sourceTypeArr,3)){
                shareQuestionList = grammarManagerDao.getShareQuestionList(aid,null,gradePhase, gradeArr, knowledgePointList, questionTypeArr,difficultyArr);
            }else if(isContain(sourceTypeArr,0)){
                shareQuestionList = grammarManagerDao.getShareQuestionList(null,teachId,gradePhase, gradeArr, knowledgePointList, questionTypeArr,difficultyArr);
            }else{
                shareQuestionList = grammarManagerDao.getShareQuestionList(null,null,gradePhase, gradeArr, knowledgePointList, questionTypeArr,difficultyArr);
            }
            questionList.addAll(shareQuestionList);
        }
        if(isContain(sourceTypeArr,3)){//分校题库
            List<SimulationQuestionBean> agentQuestionList = grammarManagerDao.getAgentQuestionList(aid, gradePhase, gradeArr, knowledgePointList, questionTypeArr,difficultyArr);
            questionList.addAll(agentQuestionList);
        }else if(isContain(sourceTypeArr,0)){//自建题库
            List<SimulationQuestionBean> teachQuestionList = grammarManagerDao.getTeachQuestionList(teachId, gradePhase, gradeArr, knowledgePointList, questionTypeArr,difficultyArr);
            questionList.addAll(teachQuestionList);
        }
        if(questionList.isEmpty()){
            map.put("questionNum", 0);
            return map;
        }
        //排序
        if(orderBy!=null && !orderBy.isEmpty()){
            String tempProp = orderBy.substring(0, orderBy.indexOf(" "));
            final String prop = tempProp.substring(0, 1).toUpperCase()+tempProp.substring(1);
            final String order = orderBy.substring(orderBy.indexOf(" ")+1);
            questionList.sort((q1, q2) -> {
                Long value1 = 0L;
                Long value2 = 0L;
                Object obj1 = null;
                Object obj2 = null;
                try {
                    Method method = SimulationQuestionBean.class.getMethod("get" + prop);
                    obj1 = method.invoke(q1);
                    obj2 = method.invoke(q2);
                    if (obj1 == null && obj2 == null) {
                        return 0;
                    }
                    if (obj1 != null && obj2 == null) {
                        return "ASC".equalsIgnoreCase(order) ? 1 : -1;
                    }
                    if (obj1 == null) {
                        return "ASC".equalsIgnoreCase(order) ? -1 : 1;
                    }
                    if (obj1 instanceof Date && obj2 instanceof Date) {
                        value1 = ((Date) obj1).getTime();
                        value2 = ((Date) obj2).getTime();
                    } else {
                        value1 = Long.parseLong(obj1.toString());
                        value2 = Long.parseLong(obj2.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return 1;//出问题就不排序了
                }
                if ("ASC".equalsIgnoreCase(order)) {
                    return value1.compareTo(value2);
                } else {
                    return value2.compareTo(value1);
                }
            });
        }

        map.put("questionNum", questionList.size());
        if(pageSize>0){//分页
            int pageStart = (pageNum-1)*pageSize;
            int pageEnd = pageStart+pageSize;
            if(pageEnd>questionList.size()){
                pageEnd = questionList.size();
            }
            questionList = questionList.subList(pageStart, pageEnd);
        }
        map.put("questionList", questionList);
        return map;
    }

    private <T> boolean isContain(T[] array, T element){
        if(array==null || element==null){
            return false;
        }
        for (T t : array) {
            if (element.equals(t)) {
                return true;
            }
        }
        return false;
    }


    public boolean addAllCourseQuestion(String aid, String teachId, Integer courseId,
                                        Integer[] sourceTypeArr,  Integer gradePhase, Integer[] gradeArr,
                                        String[] knowledgePointArr, Integer[] questionTypeArr,
                                        Integer[] difficultyArr) {
        boolean flag  = false;
        Map<String, Object> map = this.getSimulationQuestionList(aid, teachId, sourceTypeArr, gradePhase, gradeArr, knowledgePointArr, questionTypeArr, difficultyArr, null, null, 0);
        List<SimulationQuestionBean> questionList = (List<SimulationQuestionBean>) map.get("questionList");
        if(questionList==null){
            return false;
        }
        List<SimulationQuestionBean> newQuestionList = new ArrayList<SimulationQuestionBean>();
        Integer[] questionIdArr = new Integer[questionList.size()];
        for(int i=0; i<questionList.size(); i++){
            if(grammarManagerDao.getQuestionByIdFromProgram(questionList.get(i).getId(), questionList.get(i).getCourseId()) > 0) {
                grammarManagerDao.updateQuestionDelete((questionList.get(i).getId()));
                flag = true;
                continue;
            }
            SimulationQuestionBean question=grammarManagerDao.getQuestionById((questionList.get(i).getId()));
            questionIdArr[i] = questionList.get(i).getId();
            newQuestionList.add(question);
        }
        if(newQuestionList.size() > 0) {
            flag = grammarManagerDao.addNewCourseQuestion(courseId, newQuestionList);
        }
        /*grammarManagerDao.updateCourseLevelQuestionNum(courseId);*/
        /*CourseUpdateDetailBean courseUpdateDetail = grammarManagerDao.getCourseUpdateDetail(courseId, null, 10);
        if(courseUpdateDetail==null){
            grammarManagerDao.addCourseUpdateDetail(teachId, courseId, null, 10);
        }*/
        return flag;
    }

    @Override
    public boolean addAllUnitQuestion(String aid, String teachId, Integer unitId, Integer[] sourceTypeArr,
                                      Integer gradePhase, Integer[] gradeArr, String[] knowledgePointArr, Integer[] questionTypeArr,
                                      Integer[] difficultyArr) {
        boolean flag  = false;
        Map<String, Object> map = this.getSimulationQuestionList(aid, teachId, sourceTypeArr, gradePhase, gradeArr, knowledgePointArr, questionTypeArr, difficultyArr, null, null, 0);
        List<SimulationQuestionBean> questionList = (List<SimulationQuestionBean>) map.get("questionList");
        if(questionList==null){
            return false;
        }
        Integer[] questionIdArr = new Integer[questionList.size()];
        for(int i=0; i<questionList.size(); i++){
            if(questionList.get(i).getQuestionType()==4) {
                questionList.get(i).setQuestionType(3);
            }
            questionIdArr[i] = questionList.get(i).getId();
        }
        List<GrammarQuestionBean> list = new ArrayList<>();
        for (SimulationQuestionBean questionBean : questionList) {
            GrammarQuestionBean grammarQuestionBean = new GrammarQuestionBean();
            BeanUtils.copyProperties(questionBean, grammarQuestionBean);
            grammarQuestionBean.setUnitId(unitId);
            grammarQuestionBean.setSource(1);
            grammarQuestionBean.setAreaId(0);
            GrammarQuestionBean apply = grammarQuestionBean;
            list.add(apply);
        }
        flag = grammarManagerDao.addQuestionList(list);
        return flag;
    }

    @Override
    public boolean addSelectUnitQuestion(String teachId, Integer unitId, Integer[] questionIdArr) {
        List<SimulationQuestionBean> questionList=new ArrayList<SimulationQuestionBean>();
        for(Integer questionId:questionIdArr) {
            SimulationQuestionBean question=grammarManagerDao.getQuestionById(questionId);
            if(question.getQuestionType()==4) {
                question.setQuestionType(3);
            }
            questionList.add(question);
        }
        return grammarManagerDao.addQuestionList(questionList.stream().map(simulationQuestionBean -> {
            GrammarQuestionBean grammarQuestionBean = new GrammarQuestionBean();
            BeanUtils.copyProperties(simulationQuestionBean, grammarQuestionBean);
            grammarQuestionBean.setUnitId(unitId);
            grammarQuestionBean.setSource(1);
            grammarQuestionBean.setAreaId(0);
            return grammarQuestionBean;
        }).collect(Collectors.toList()));
    }

    public boolean addSelectCourseQuestion(String teachId, Integer courseId, Integer[] questionIdArr) {
        boolean flag = false;
        List<SimulationQuestionBean> questionList=new ArrayList<SimulationQuestionBean>();
        for(Integer questionId:questionIdArr) {
            if(grammarManagerDao.getQuestionByIdFromProgram(questionId, courseId) > 0) {
                grammarManagerDao.updateQuestionDelete(questionId);
                flag = true;
                continue;
            }
            SimulationQuestionBean question=grammarManagerDao.getQuestionById(questionId);
            questionList.add(question);
        }
        if(!questionList.isEmpty()) {
            flag = grammarManagerDao.addNewCourseQuestion(courseId, questionList);
        }
        return flag;
    }

    public GrammarNewUnitBean getGrammarCardDetail(Integer unitId) {
        GrammarNewUnitBean grammarCard = grammarManagerDao.getGrammarCard(unitId);
        if(grammarCard!=null){
            List<GrammarUnitContentBean> targetContentList = grammarManagerDao.getUnitContentList(unitId, -1);
            List<GrammarUnitContentBean> summaryContentList = grammarManagerDao.getUnitContentList(unitId, -2);
            grammarCard.setTargetContentList(targetContentList);
            grammarCard.setSummaryContentList(summaryContentList);
            List<GrammarUnitTitleBean> titleList = getTitleList(unitId, 0, 0);
            grammarCard.setTitleList(titleList);
        }
        return grammarCard;
    }

    @Override
    public boolean deleteContent(Integer unitId, Integer questionType, Integer contentId) {
        switch(questionType) {
            case 101://一级标题
            case 102://二级标题
            case 103://三级标题
                grammarManagerDao.deleteTitle(contentId);
                break;
            case 104://四级标题（子标题）
                grammarManagerDao.deleteSubTitle(contentId);
                break;
            default:
                grammarManagerDao.deleteContent(contentId);
                break;
        }
        return true;
    }

    /**
     * 层级标题导入子标题
     * @param unitId
     * @param parentId
     * @param level
     * @return
     */
    private List<GrammarUnitTitleBean> getTitleList(Integer unitId, Integer parentId, int level) {
        if(level>3){
            return null;
        }
        List<GrammarUnitTitleBean> nextTitleList = null;
        nextTitleList=grammarManagerDao.getUnitTitleListByParentId(unitId, parentId);
        if(nextTitleList.size()==0){
            return null;
        }

        for (GrammarUnitTitleBean title : nextTitleList) {
            title.setTitleList(getTitleList(unitId, title.getId(), level+1));
            title.setSubtitleList(getSubtitleList(unitId, title.getId()));
        }
        return nextTitleList;
    }

    /**
     * 子标题导入题目
     * @param unitId
     * @param titleId
     * @return
     */
    private List<GrammarUnitSubtitleBean> getSubtitleList(Integer unitId, Integer titleId) {
        List<GrammarUnitSubtitleBean> subtitleList = null;

        subtitleList=grammarManagerDao.getUnitSubtitleList(unitId, titleId);
        if(subtitleList.isEmpty()){
            return null;
        }

        List<GrammarUnitContentBean> contentList = null;
        for (GrammarUnitSubtitleBean subtitle : subtitleList) {
            contentList = grammarManagerDao.getUnitContentList(unitId, subtitle.getId());
            if(!contentList.isEmpty()){
                subtitle.setContentList(contentList);
            }

        }
        return subtitleList;
    }

    public List<GrammarUnitContentBean> getCopiedContentList(String[] idArr) {
        //提取不同类型id列表
        List<Integer> titleIdList = new ArrayList<Integer>();
        List<Integer> subtitleIdList = new ArrayList<Integer>();
        List<Integer> contentIdList = new ArrayList<Integer>();
        String[] idSplit = null;
        for (String idStr : idArr) {
            idSplit = idStr.split("_");
            if("title".equals(idSplit[0])){
                titleIdList.add(Integer.valueOf(idSplit[1]));
            }else if("subtitle".equals(idSplit[0])){
                subtitleIdList.add(Integer.valueOf(idSplit[1]));
            }else if("content".equals(idSplit[0])){
                contentIdList.add(Integer.valueOf(idSplit[1]));
            }
        }
        //获取不同类型内容
        List<GrammarUnitTitleBean> titleList = null;
        if(!titleIdList.isEmpty()){
            titleList = grammarManagerDao.getUnitTitleListByIdList(titleIdList);
        }
        List<GrammarUnitSubtitleBean> subtitleList = null;
        if(!subtitleIdList.isEmpty()){
            subtitleList = grammarManagerDao.getUnitSubtitleListByIdList(subtitleIdList);
        }
        List<GrammarUnitContentBean> contentList = null;
        if(!contentIdList.isEmpty()){
            contentList = grammarManagerDao.getUnitContentListByIdList(contentIdList);
        }
        //把获取的内容整合在一起
        List<GrammarUnitContentBean> allContentList = new ArrayList<GrammarUnitContentBean>();
        String contentId;
        for (String idStr : idArr) {
            idSplit = idStr.split("_");
            contentId = idSplit[1];
            if("title".equals(idSplit[0])){
                GrammarUnitContentBean content = new GrammarUnitContentBean();
                if("-1".equals(contentId)){ //学习目标
                    content.setId(-1);
                    content.setQuestion("学习目标");
                    content.setQuestionType(101);
                    allContentList.add(content);
                }else if("-2".equals(contentId)){ //知识点小结
                    content.setId(-2);
                    content.setQuestion("知识点小结");
                    content.setQuestionType(101);
                    allContentList.add(content);
                }else{ //普通标题
                    if(titleList==null){
                        continue;
                    }
                    for(GrammarUnitTitleBean title: titleList){
                        if(title.getId().toString().equals(contentId)){
                            content.setId(title.getId());
                            content.setQuestion(title.getName());
                            content.setQuestionType(100+title.getLevel());
                            allContentList.add(content);
                            break;
                        }
                    }
                }
            }else if("subtitle".equals(idSplit[0])){
                if(subtitleList==null){
                    continue;
                }
                for(GrammarUnitSubtitleBean subtitle: subtitleList){
                    if(subtitle.getId().toString().equals(contentId)){
                        GrammarUnitContentBean content = new GrammarUnitContentBean();
                        content.setId(subtitle.getId());
                        content.setQuestion(subtitle.getName());
                        content.setQuestionType(104);
                        allContentList.add(content);
                        break;
                    }
                }
            }else if("content".equals(idSplit[0])){
                if(contentList==null){
                    continue;
                }
                for(GrammarUnitContentBean content: contentList){
                    if(content.getId().toString().equals(contentId)){
                        allContentList.add(content);
                        break;
                    }
                }
            }
        }
        return allContentList;
    }

    public Map<String, Object> getUnitName(Integer unitId) {
        GrammarNewUnitBean unit = grammarManagerDao.getGrammarCard(unitId);
        Integer courseId = unit.getCourseId();
        Map<String, Object> map = this.getCourseFullNameById(courseId);
        map.put("unitName", unit.getName());
        map.put("unitId", unit.getId());
        return map;
    }

    public Map<String, Object> getKnowledgeListWithNum(Integer unitId) {
        List<Map<String, Object>> knowList = grammarManagerDao.getKnowledgeList(unitId);
        int allKnowNum = grammarManagerDao.getKnowQuestionNum(unitId, true);
        int noKnowNum = grammarManagerDao.getKnowQuestionNum(unitId, false);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("knowList", knowList);
        map.put("allKnowNum", allKnowNum);
        map.put("noKnowNum", noKnowNum);
        return map;
    }
    public Map<String, Object> getQuestionList(Integer unitId, Integer pageNum, Integer pageSize, String titleIds) {
        Integer pageStart = (pageNum-1)*pageSize;
        Integer pageEnd = pageStart + pageSize;
        List<GrammarQuestionBean> questionList = grammarManagerDao.getQuestionList(unitId, titleIds);
        questionList.forEach(question -> {
            List<GrammarQuestionContentBean> grammarQuestionContentList = redBookCourseDao.getGrammarQuestionContentList(question.getId());
            question.setQuestionContentBeanList(grammarQuestionContentList);
        });

        Integer questionNum = questionList.size();
        Map<String, Object> map = new HashMap<String, Object>();
        if(pageNum==1){
            map.put("questionNum", questionNum);
        }
        if(pageEnd>questionNum){
            pageEnd = questionNum;
        }
        questionList = questionList.subList(pageStart, pageEnd);
        map.put("questionList", questionList);
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestion(GrammarQuestionBean question) {
        question.setTeachId("system");
        boolean flag = grammarManagerDao.updateQuestion(question);
        //传递的材料内容
        List<GrammarQuestionContentBean> questionContentBeanList = question.getQuestionContentBeanList();
        //原有的材料内容
        List<GrammarQuestionContentBean> grammarQuestionContentList = redBookCourseDao.getGrammarQuestionContentList(question.getId());
        //可能在原有阶段新增、修改、删除

        //传递的材料内容为空，则删除所有材料内容
        if(questionContentBeanList == null || questionContentBeanList.isEmpty()){
            grammarManagerDao.deleteQuestionContentByQuestionId(question.getId());
        }
        //筛选出来需要新增材料内容
        List<GrammarQuestionContentBean> needAddList = questionContentBeanList.stream().filter(data -> data.getQuestionId() == null).collect(Collectors.toList());
        //筛选出来需要修改材料内容
        List<GrammarQuestionContentBean> needUpdateList = questionContentBeanList.stream().filter(data -> data.getQuestionId() != null).collect(Collectors.toList());
        List<Integer> idList = needUpdateList.stream().map(GrammarQuestionContentBean::getId).collect(Collectors.toList());

        //插入
        if(!needAddList.isEmpty()){
            grammarManagerDao.addQuestionContentList(question.getId(),needAddList);
        }
        //更新
        if(!needUpdateList.isEmpty()){
            needUpdateList.forEach(data ->{
                grammarManagerDao.updateQuestionContent(data);
            });
        }
        //删除
        grammarQuestionContentList.forEach(data ->{
            if(!idList.contains(data.getId())){
                grammarManagerDao.deleteQuestionContentById(data.getId());
            }
        });

        if(flag){
            List<Integer> titleIdList = question.getTitleIdList();
            grammarManagerDao.deleteTitleQuestion(new Integer[]{question.getId()});
            if(titleIdList!=null && !titleIdList.isEmpty()){
                List<Map<String, Integer>> titleQuestionList = new ArrayList<Map<String, Integer>>();
                Map<String, Integer> tqMap = null;
                for (Integer titleId : titleIdList) {
                    tqMap = new HashMap<String, Integer>();
                    tqMap.put("titleId", titleId);
                    tqMap.put("questionId", question.getId());
                    titleQuestionList.add(tqMap);
                }
                grammarManagerDao.addTitleQuestion(titleQuestionList);
            }
        }
        return flag;
    }
    @Transactional(rollbackFor = Exception.class)
    public boolean addQuestionList(List<GrammarQuestionBean> questionList) {
        if(questionList.isEmpty()){
            return false;
        }
        List<Integer> titleIdList = null;
        List<Map<String, Integer>> titleQuestionList;
        Map<String, Integer> tqMap = null;
        for (GrammarQuestionBean question : questionList) {
            grammarManagerDao.addQuestion(question);

            List<GrammarQuestionContentBean> questionContentBeanList = question.getQuestionContentBeanList();
            if(questionContentBeanList != null && !questionContentBeanList.isEmpty()){
                grammarManagerDao.addQuestionContentList(question.getId(),questionContentBeanList);
            }

            titleIdList = question.getTitleIdList();
            if(titleIdList!=null && !titleIdList.isEmpty()){
                titleQuestionList = new ArrayList<Map<String, Integer>>();
                for (Integer titleId : titleIdList) {
                    tqMap = new HashMap<String, Integer>();
                    tqMap.put("titleId", titleId);
                    tqMap.put("questionId", question.getId());
                    titleQuestionList.add(tqMap);
                }
                grammarManagerDao.addTitleQuestion(titleQuestionList);
            }
        }

        return true;
    }

    public List<Map<String, Object>> getApplyQuestionList(Integer unitId, Integer titleId) {
        List<Map<String, Object>> applyQuestionList = grammarManagerDao.getApplyQuestionList(unitId, titleId);
        return applyQuestionList;
    }


    public boolean deleteQuestion(String teachId, Integer courseId, Integer unitId, Integer[] questionIdArr) {
        boolean flag = grammarManagerDao.deleteQuestion(questionIdArr);
        if(flag){
            grammarManagerDao.deleteTitleQuestion(questionIdArr);
        }
        return flag;
    }

    @Override
    public List<Map<String, Object>> getAreaList(){
       return grammarManagerDao.getAreaList();
    }
    @Transactional
    public Map<String, Object> saveGrammarCard(String contextPath, GrammarNewUnitBean grammarCard) {
        //删除未使用的图片
        Integer unitId = grammarCard.getId();
        deleteNotUseImage(contextPath, grammarCard);
        //保存卡片内容
        List<GrammarUnitContentBean> contentList = grammarCard.getGrammarContentList();
        for (GrammarUnitContentBean content : contentList) {
            content.setUnitId(unitId);
        }
        List<GrammarUnitContentBean> errList = addUnitContentList(contentList);

        //更新互动步数
        grammarManagerDao.updateUnitInteNum(unitId, grammarCard.getInteNum());
        grammarManagerDao.updateUnitKnowNum(unitId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("success", true);
        map.put("cardId", grammarCard.getId());
        map.put("errList", errList);
        return map;
    }


    /**
     * 删除未使用的图片
     * @param contextPath
     * @return
     * @throws Exception
     */
    private void deleteNotUseImage(String contextPath, GrammarNewUnitBean grammarCard){
        //获取使用的图片的路径
        List<String> useImageNameList = new ArrayList<String>();
        List<GrammarUnitContentBean> grammarContentList = grammarCard.getGrammarContentList();
        List<Integer> updateContentId = new ArrayList<Integer>();//存放需要更新的内容的id
        for (GrammarUnitContentBean content : grammarContentList) {
            if(content.getId()!=null){
                updateContentId.add(content.getId());
            }
            catchImagePath(useImageNameList, content);
        }

        //删除未使用的图片和修改后删除掉的那些图片
        String key = TEMP_GRAMMAR_IMAGE_PREFIX+grammarCard.getTeachId()+":"+grammarCard.getId();
        List<String> allImageNameList = (List<String>) RedBookRedisManager.getResourceBean(key);
        if(allImageNameList==null){
            allImageNameList = new ArrayList<String>();
        }
        if(updateContentId.size()>0){
            List<GrammarUnitContentBean> oldGrammarContentList = grammarManagerDao.getGrammarContentList(updateContentId.toArray(new Integer[1]));
            for (GrammarUnitContentBean oldContent : oldGrammarContentList) {
                catchImagePath(allImageNameList, oldContent);
            }
        }

        File imageFile;
        for (String imagePathStr : allImageNameList) {
            if(!useImageNameList.contains(imagePathStr)){
                OSSManager.delete(OSSManager.UPLOAD_BUCKETNAME, imagePathStr);
            }
        }
        //删除Redis中存放的临时图片路径
        RedBookRedisManager.delResourceBean(key);
    }
    /**
     * 捕获语法内容中的图片地址
     * @param list
     * @param content
     */
    private void catchImagePath(List<String> list, GrammarUnitContentBean content){
        String imageUrl, imageName;
        String reg="[a-zA-z]+://[^\"]*";
        Pattern pattern;
        Matcher matcher;
        String text = content.getQuestion()+content.getOptionA()+content.getOptionB()+content.getOptionC()+content.getOptionD();
        pattern = Pattern.compile(reg);
        matcher = pattern.matcher(text);
        while(matcher.find()){
            imageUrl = matcher.group();
            if(!imageUrl.contains(IMAGE_DIR_NAME)){
                continue;
            }
            imageName = imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            list.add(imageName);
        }
    }

    private List<Integer> unitTitleIdList;
    private List<Integer> unitSubtitleIdList;
    private List<Integer> unitContentIdList;
    private List<GrammarUnitContentBean> addUnitContentList(List<GrammarUnitContentBean> contentList){
        disporderArr=new int[]{1, 1, 1, 1, 1};
        ArrayList<GrammarUnitContentBean> errList = new ArrayList<>();
        unitTitleIdList = new ArrayList<Integer>();
        unitSubtitleIdList = new ArrayList<Integer>();
        unitContentIdList = new ArrayList<Integer>();
        ContentStack contentStack = new ContentStack();
        for (GrammarUnitContentBean content : contentList) {
            if(content.getQuestionType()==null){
                continue;
            }
            switch(content.getQuestionType()){
                case 101://一级标题
                case 102://二级标题
                case 103://三级标题
                case 104://四级标题（子标题）
                    checkAndPersistTitle(content, contentStack);
                    break;
                default://非标题内容
                    //插入练一练
				/*if(contentStack.getTop()!=null &&
					contentStack.getTop().getQuestionType()==6 &&
					contentStack.getTop().getQuestion().equals("练一练")){
					grammarTransferDao.addNewUnitPractice(content);
					break;
				}*/
                    //添加单元卡片内容
                    if(contentStack.getTop()==null){
                        break;
                    }
                    GrammarUnitContentBean unitContent = new GrammarUnitContentBean();
                    unitContent.setId(content.getId());
                    unitContent.setUnitId(content.getUnitId());
                    if(contentStack.getTop().getQuestionType()==101){
                        if(contentStack.getTop().getQuestion().equals("学习目标")){
                            if(content.getQuestionType()!=4){
                                break;
                            }
                            unitContent.setSubtitleId(-1); //subtitleId为-1代表是学习目标
                        }else if(contentStack.getTop().getQuestion().equals("知识点小结")){
                            if(content.getQuestionType()!=4){
                                break;
                            }
                            unitContent.setSubtitleId(-2); //subtitleId为-2代表是知识点小结
                        }else{
                            break;
                        }
                    }else if(contentStack.getTop().getQuestionType()!=104){
                        break;
                    }else{
                        unitContent.setSubtitleId(contentStack.getTop().getId());
                    }
                    Integer questionType = content.getQuestionType();
                    if (questionType==5){
                        if (content.getCorrectOption().contains("-1")){
                            errList.add(content);
                        }
                    }
                    unitContent.setQuestionType(questionType);
                    unitContent.setQuestion(content.getQuestion());
                    unitContent.setOptionA(content.getOptionA());
                    unitContent.setOptionB(content.getOptionB());
                    unitContent.setOptionC(content.getOptionC());
                    unitContent.setOptionD(content.getOptionD());
                    unitContent.setOptionE(content.getOptionE());
                    unitContent.setCorrectOption(content.getCorrectOption());
                    unitContent.setWrongTips1(content.getWrongTips1());
                    unitContent.setWrongTips2(content.getWrongTips2());
                    unitContent.setWrongTips3(content.getWrongTips3());
                    unitContent.setParse(content.getParse());
                    unitContent.setDisporder(disporderArr[4]++);
                    if (unitContent.getId()!=null&&unitContent.getId()==-1){
                        unitContent.setId(null);
                    }
                    if(unitContent.getId()==null){//新增的内容
                        grammarManagerDao.addUnitContent(unitContent);
                    }else{//修改原先的内容
                        grammarManagerDao.updateUnitContent(unitContent);
                    }
                    unitContentIdList.add(unitContent.getId());
                    break;
            }
        }
        while(contentStack.size()>0){
            if("isBasic".equals(contentStack.getTop().getParse())){
                grammarManagerDao.updateTitleIsBasic(contentStack.getTop().getId());
            }
            contentStack.pop(); //栈顶是同一级或低级的标题，则清掉，保证栈顶元素一定是要插入元素的父级
        }
        return errList;
    }

    /**
     * 持久化标题内容
     * @param questionType
     * @param contentStack
     */
    private int[] disporderArr;

    private String format;
    public void checkAndPersistTitle(GrammarUnitContentBean content, ContentStack contentStack){
        Integer questionType = content.getQuestionType();
        List<Integer> persistTypeList = null;
        switch(questionType){
            case 101: persistTypeList = Arrays.asList(101, 102, 103, 104);break;
            case 102: persistTypeList = Arrays.asList(102, 103, 104);break;
            case 103: persistTypeList = Arrays.asList(103, 104);break;
            case 104: persistTypeList = Collections.singletonList(104);break;
        }
        while(contentStack.size()>0 &&persistTypeList.contains(contentStack.getTop().getQuestionType())){
            if("isBasic".equals(contentStack.getTop().getParse())){
                grammarManagerDao.updateTitleIsBasic(contentStack.getTop().getId());
            }
            contentStack.pop(); //栈顶是同一级或低级的标题，则清掉，保证栈顶元素一定是要插入元素的父级
        }

        int level = 0;
        switch(questionType){
            case 101: level = 1;break;
            case 102: level = 2;break;
            case 103: level = 3;break;
            case 104: level = 4;break;
        }
        if(level<4){
            if(level!=1 && contentStack.size()==0){
                return;
            }
            if(level==1 && (content.getQuestion().equals("练一练") ||
                    content.getQuestion().equals("学习目标") ||
                    content.getQuestion().equals("知识点小结"))){//练一练则不插入标题表中
                contentStack.push(content);
                return;
            }
            GrammarUnitTitleBean unitTitle = new GrammarUnitTitleBean();
            unitTitle.setId(content.getId());
            unitTitle.setName(content.getQuestion());
            unitTitle.setUnitId(content.getUnitId());
            unitTitle.setParentId(level==1?0:contentStack.getTop().getId());
            unitTitle.setLevel(level);
            unitTitle.setBasic(false);
            unitTitle.setDisporder(disporderArr[level-1]++);
            if (unitTitle.getId()!=null&&unitTitle.getId()==-1) {
                unitTitle.setId(null);
            }
            if(unitTitle.getId()==null){//新增的内容
                grammarManagerDao.addUnitTitle(unitTitle);
                content.setId(unitTitle.getId());
            }else{//修改原先的内容
                grammarManagerDao.updateUnitTitle(unitTitle);
            }
            unitTitleIdList.add(unitTitle.getId());
            contentStack.push(content);
            switch(level){
                case 1:
                    disporderArr[1] = 1;
                    disporderArr[2] = 1;
                    disporderArr[3] = 1;
                    break;
                case 2:
                    disporderArr[2] = 1;
                    disporderArr[3] = 1;
                    break;
                case 3:
                    disporderArr[3] = 1;
                    break;
            }
        }else{
            if(contentStack.size()==0){
                return;
            }

            if(!"isBasic".equals(contentStack.getTop().getParse())){
                contentStack.getTop().setParse("isBasic");
            }

            if(contentStack.getTop().getId()==null){
                return;
            }
            GrammarUnitSubtitleBean subtitle = new GrammarUnitSubtitleBean();
            subtitle.setId(content.getId());
            subtitle.setUnitId(content.getUnitId());
            subtitle.setTitleId(contentStack.getTop().getId());
            if("观察".equals(content.getQuestion())){
                subtitle.setType(1);
                subtitle.setName("观察");
            }else if("知识点".equals(content.getQuestion()) || "提炼".equals(content.getQuestion())){
                subtitle.setType(2);
                subtitle.setName("提炼");
            }else if("巩固".equals(content.getQuestion()) || "强化".equals(content.getQuestion())){
                subtitle.setType(3);
                subtitle.setName("强化");
            }else{
                subtitle.setType(4);
                subtitle.setName("应用");
            }
            subtitle.setDisporder(disporderArr[level-1]++);
//            subtitle.setId(null);
            if(subtitle.getId()==null){//新增的内容
                grammarManagerDao.addUnitSubtitle(subtitle);
                content.setId(subtitle.getId());
            }else{//修改原先的内容
                grammarManagerDao.updateUnitSubtitle(subtitle);
            }
            unitSubtitleIdList.add(subtitle.getId());
            contentStack.push(content);
            disporderArr[4] = 1;
        }
    }

    public Map<String, String> uploadImage(Integer unitId, MultipartFile upload,
                                           HttpServletRequest request) {
        Map<String, String> map = new HashMap<String, String>();
        if(upload==null || upload.isEmpty()){
            return null;
        }
        if(upload.getSize()>2*1024*1024){
            map.put("message", "Size is too big");
            return map;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix=originalFilename.substring(originalFilename.lastIndexOf("."));
        if(!".bmp".equals(suffix) && !".jpg".equals(suffix) && !".png".equals(suffix) && !".gif".equals(suffix)
                && !".jpeg".equals(suffix)){
            map.put("message", "Format is wrong");
            return map;
        }
        String imageName = new Date().getTime()+suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if(contextPath.endsWith(File.separator)){
            contextPath = contextPath.substring(0, contextPath.length()-1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator)+1);

        String imagePath = contextPath +IMAGE_SOURCE_DIR_NAME+File.separator+unitId;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(imagePath);
        if(!file.exists()){
            file.mkdirs();
        }
        imagePath += File.separator+ imageName;
        String imageUrl = RedBookConstant.UPLOAD_DOMAIN_NAME+"/"+IMAGE_DIR_NAME+"/"+unitId+"/"+imageName;
        try {
            upload.transferTo(new File(imagePath));
            WaterMarkUtils.addWaterMark(imagePath, imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME));
            OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME,IMAGE_DIR_NAME+"/"+unitId+"/"+imageName, Files.newInputStream(Paths.get(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME))));
            addImagePathToRedis(unitId, IMAGE_DIR_NAME+"/"+unitId+"/"+imageName);
            map.put("message", "ok");
            map.put("imageUrl", imageUrl);
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if (new File(imagePath).exists())new File(imagePath).delete();
            File file1 = new File(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME)
            );
            if (file1.exists()) file1.delete();
        }
        return map;
    }

    public Map<String, Object> textUploadByPaste( Integer unitId,
                                                 MultipartFile upload, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> error = new HashMap<String, Object>();
		/*response.setContentType("text/html;charset=utf-8");
		response.setCharacterEncoding("utf-8");
		response.setHeader("Access-Control-Allow-Origin","*");*/
        if(upload==null || upload.isEmpty()){
            error.put("message", "找不到图片源！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        if(upload.getSize()>2*1024*1024){
            error.put("message", "图片尺寸过大！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix=originalFilename.substring(originalFilename.lastIndexOf("."));
        if(!".bmp".equals(suffix) && !".jpg".equals(suffix) && !".png".equals(suffix) && !".gif".equals(suffix)
                && !".jpeg".equals(suffix)){
            error.put("message", "文件格式不正确（必须为.jpg/.gif/.bmp/.png/.jpeg文件）");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String imageName = new Date().getTime()+suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if(contextPath.endsWith(File.separator)){
            contextPath = contextPath.substring(0, contextPath.length()-1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator)+1);

        String imagePath = contextPath +IMAGE_SOURCE_DIR_NAME+File.separator+unitId;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(imagePath);
        if(!file.exists()){
            file.mkdirs();
        }
        imagePath += File.separator+ imageName;
        String imageUrl = RedBookConstant.UPLOAD_DOMAIN_NAME+"/"+IMAGE_DIR_NAME+"/"+unitId+"/"+imageName;
        try {
            upload.transferTo(new File(imagePath));
            //如果是 gif 就不处理水印
            if (!imageName.contains("gif")){
                WaterMarkUtils.addWaterMark(imagePath, imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME));
                OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME,IMAGE_DIR_NAME+"/"+unitId+"/"+imageName, Files.newInputStream(Paths.get(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME))));
            }else {
                OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME,IMAGE_DIR_NAME+"/"+unitId+"/"+imageName, Files.newInputStream(Paths.get(imagePath)));
            }
            addImagePathToRedis(unitId, IMAGE_DIR_NAME+"/"+unitId+"/"+imageName);
            map.put("uploaded", 1);
            map.put("fileName", imageName);
            map.put("url", imageUrl);
            return map;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if (new File(imagePath).exists())new File(imagePath).delete();
            File file1 = new File(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME)
            );
            if (file1.exists()) file1.delete();
        }

        error.put("message", "未知错误！");
        map.put("uploaded", 0);
        map.put("error", error);
        return map;
    }
    public Map<String, Object> audioUploadByPaste( Integer unitId,
                                                  MultipartFile upload, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> error = new HashMap<String, Object>();
		/*response.setContentType("text/html;charset=utf-8");
		response.setCharacterEncoding("utf-8");
		response.setHeader("Access-Control-Allow-Origin","*");*/
        if(upload==null || upload.isEmpty()){
            error.put("message", "找不到文件源！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        if(upload.getSize()>5*1024*1024){
            error.put("message", "音频文件尺寸过大！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix=originalFilename.substring(originalFilename.lastIndexOf("."));
        if(!".mp3".equalsIgnoreCase(suffix)&&!".mp4".equalsIgnoreCase(suffix)&&!".wav".equalsIgnoreCase(suffix) && !".wma".equalsIgnoreCase(suffix) && !".mov".equalsIgnoreCase(suffix) && !".midi".equalsIgnoreCase(suffix) && !".ra".equalsIgnoreCase(suffix)){
            error.put("message", "文件格式不正确（必须为.mp3/.mp4/.wav/.wma/.mov/.midi/.ra文件）");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String audioName = new Date().getTime()+suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if(contextPath.endsWith(File.separator)){
            contextPath = contextPath.substring(0, contextPath.length()-1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator)+1);

        String audioPath = contextPath +IMAGE_SOURCE_DIR_NAME+File.separator+unitId;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(audioPath);
        if(!file.exists()){
            file.mkdirs();
        }
        audioPath += File.separator+ audioName;
        String audioUrl = RedBookConstant.UPLOAD_DOMAIN_NAME+"/"+IMAGE_DIR_NAME+"/"+unitId+"/"+audioName;
        try {
            upload.transferTo(new File(audioPath));
            OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME,IMAGE_DIR_NAME+"/"+unitId+"/"+audioName, Files.newInputStream(Paths.get(audioPath)));
            addImagePathToRedis(unitId, IMAGE_DIR_NAME+"/"+unitId+"/"+audioName);
            map.put("uploaded", 1);
            map.put("fileName", audioName);
            map.put("url", audioUrl);
            return map;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if (new File(audioPath).exists())new File(audioPath).delete();
            File file1 = new File(audioPath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME)
            );
            if (file1.exists()) file1.delete();
        }

        error.put("message", "未知错误！");
        map.put("uploaded", 0);
        map.put("error", error);
        return map;
    }
    public void textUpload(Integer unitId, MultipartFile upload,
                           HttpServletResponse response, HttpServletRequest request) {
        response.setContentType("text/html;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Allow-Origin","*");
        if(upload==null || upload.isEmpty()){
            return;
        }
        PrintWriter out = null;
        try {
            out = response.getWriter();
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        // CKEditor提交的很重要的一个参数
        String callback = request.getParameter("CKEditorFuncNum");
        if(upload.getSize()>2*1024*1024){
            out.println("<script type=\"text/javascript\">");
            out.println("alert('图片尺寸过大！');");
            out.println("</script>");
            return;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix=originalFilename.substring(originalFilename.lastIndexOf("."));
        if(!".bmp".equals(suffix) && !".jpg".equals(suffix) && !".png".equals(suffix) && !".gif".equals(suffix)
                && !".jpeg".equals(suffix)){
            out.println("<script type=\"text/javascript\">");
            out.println("alert('文件格式不正确（必须为.jpg/.gif/.bmp/.png/.jpeg文件）');");
            out.println("</script>");
            return;
        }
        String imageName = new Date().getTime()+suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if(contextPath.endsWith(File.separator)){
            contextPath = contextPath.substring(0, contextPath.length()-1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator)+1);

        String imagePath = contextPath +IMAGE_SOURCE_DIR_NAME+File.separator;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(imagePath);
        if(!file.exists()){
            file.mkdirs();
        }
        imagePath += File.separator+ imageName;
        try {
            upload.transferTo(new File(imagePath));
            //如果是 gif 就不处理水印
            if (!imageName.contains("gif")){
                WaterMarkUtils.addWaterMark(imagePath, imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME));
                OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME,IMAGE_DIR_NAME+"/"+unitId+"/"+imageName, Files.newInputStream(Paths.get(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME))));
            }else {
                OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME,IMAGE_DIR_NAME+"/"+unitId+"/"+imageName, Files.newInputStream(Paths.get(imagePath)));
            }
            addImagePathToRedis(unitId, IMAGE_DIR_NAME+"/"+unitId+"/"+imageName);
            // 返回"图像"选项卡并显示图片  request.getContextPath()为web项目名
            out.println("<script type=\"text/javascript\">");
            out.println("window.parent.CKEDITOR.tools.callFunction(" + callback
                    + ",'" + RedBookConstant.UPLOAD_DOMAIN_NAME+"/"+IMAGE_DIR_NAME+"/"+unitId+"/"+imageName+ "','')");
            out.println("</script>");
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if (new File(imagePath).exists())new File(imagePath).delete();
            File file1 = new File(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME)
            );
            if (file1.exists()) file1.delete();
        }
    }

    @Override
    public boolean updateUnitKnowledgeCount(Integer unitId, Integer count) {
        return grammarManagerDao.updateUnitKnowledgeCount(unitId,count)>0;
    }

    private void addImagePathToRedis(Integer unitId, String imageName){
        String key = TEMP_GRAMMAR_IMAGE_PREFIX+unitId;
        List<String> imageNameList = (List<String>) RedBookRedisManager.getResourceBean(key);
        if(imageNameList==null){
            imageNameList = new ArrayList<String>();
        }
        imageNameList.add(imageName);
        RedBookRedisManager.setResourceBean(key, imageNameList);
    }
}
