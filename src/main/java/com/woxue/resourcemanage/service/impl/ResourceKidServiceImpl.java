package com.woxue.resourcemanage.service.impl;

import com.redbook.kid.common.model.*;
import com.woxue.resourcemanage.service.ResourceKidService;
import com.woxue.resourceservice.dao.ResourceKidMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service("resourceKidService")
public class ResourceKidServiceImpl implements ResourceKidService {
    @Resource
    private ResourceKidMapper allResourceKidMapper;

    // resource_kid_course相关方法实现
    @Override
    public void insertCourse(ResourceKidCourse course) {
        allResourceKidMapper.insertCourse(course);
    }

    @Override
    public ResourceKidCourse getCourseById(int id) {
        return allResourceKidMapper.getCourseById(id);
    }

    @Override
    public void updateCourse(ResourceKidCourse course) {
        allResourceKidMapper.updateCourse(course);
    }

    @Override
    public void deleteCourse(int id) {
        allResourceKidMapper.deleteCourse(id);
    }

    @Override
    public List<ResourceKidCourse> getAllCourses() {
        return allResourceKidMapper.getAllCourses();
    }

    // resource_kid_picture_book相关方法实现
    @Override
    public void insertPictureBook(ResourceKidPictureBook pictureBook) {
        allResourceKidMapper.insertPictureBook(pictureBook);
    }

    @Override
    public ResourceKidPictureBook getPictureBookById(int id) {
        return allResourceKidMapper.getPictureBookById(id);
    }

    @Override
    public void updatePictureBook(ResourceKidPictureBook pictureBook) {
        allResourceKidMapper.updatePictureBook(pictureBook);
    }

    @Override
    public void deletePictureBook(int id) {
        allResourceKidMapper.deletePictureBook(id);
    }

    @Override
    public List<ResourceKidPictureBook> getAllPictureBooks() {
        return allResourceKidMapper.getAllPictureBooks();
    }

    // resource_kid_picture_book_content相关方法实现
    @Override
    public void insertPictureBookContent(ResourceKidPictureBookContent content) {
        allResourceKidMapper.insertPictureBookContent(content);
    }

    @Override
    public ResourceKidPictureBookContent getPictureBookContentById(int id) {
        return allResourceKidMapper.getPictureBookContentById(id);
    }

    @Override
    public void updatePictureBookContent(ResourceKidPictureBookContent content) {
        allResourceKidMapper.updatePictureBookContent(content);
    }

    @Override
    public void deletePictureBookContent(int id) {
        allResourceKidMapper.deletePictureBookContent(id);
    }

    @Override
    public List<ResourceKidPictureBookContent> getAllPictureBookContents() {
        return allResourceKidMapper.getAllPictureBookContents();
    }

    @Override
    public List<ResourceKidPictureBookContent> getPictureBookContentByPictureBookId(int pictureBookId) {
        return allResourceKidMapper.getPictureBookContentByPictureBookId(pictureBookId);
    }

    // resource_kid_picture_book_sentence相关方法实现
    @Override
    public void insertPictureBookSentence(ResourceKidPictureBookSentence sentence) {
        allResourceKidMapper.insertPictureBookSentence(sentence);
    }

    @Override
    public ResourceKidPictureBookSentence getPictureBookSentenceById(int id) {
        return allResourceKidMapper.getPictureBookSentenceById(id);
    }

    @Override
    public void updatePictureBookSentence(ResourceKidPictureBookSentence sentence) {
        allResourceKidMapper.updatePictureBookSentence(sentence);
    }

    @Override
    public void deletePictureBookSentence(int id) {
        allResourceKidMapper.deletePictureBookSentence(id);
    }

    @Override
    public List<ResourceKidPictureBookSentence> getAllPictureBookSentences() {
        return allResourceKidMapper.getAllPictureBookSentences();
    }

    @Override
    public List<ResourceKidPictureBookSentence> getPictureBookSentenceByPictureBookId(int pictureBookId) {
        return allResourceKidMapper.getPictureBookSentenceByPictureBookId(pictureBookId);
    }

    // resource_kid_scene相关方法实现
    @Override
    public void insertScene(ResourceKidScene scene) {
        allResourceKidMapper.insertScene(scene);
    }

    @Override
    public ResourceKidScene getSceneById(int id) {
        return allResourceKidMapper.getSceneById(id);
    }

    @Override
    public void updateScene(ResourceKidScene scene) {
        allResourceKidMapper.updateScene(scene);
    }

    @Override
    public void deleteScene(int id) {
        allResourceKidMapper.deleteScene(id);
    }

    @Override
    public List<ResourceKidScene> getAllScenes() {
        return allResourceKidMapper.getAllScenes();
    }

    @Override
    public List<ResourceKidScene> getSceneByCourseId(int courseId) {
        List<ResourceKidScene> sceneByCourseId = allResourceKidMapper.getSceneByCourseId(courseId);
        sceneByCourseId.forEach(scene -> {
            List<ResourceKidUnit> unitBySceneId = allResourceKidMapper.getUnitBySceneId(scene.getId());
            for (ResourceKidUnit resourceKidUnit : unitBySceneId) {
                fillUnit(resourceKidUnit);
            }
            scene.setUnitList(unitBySceneId);
        });
        return sceneByCourseId;
    }

    // resource_kid_unit相关方法实现
    @Override
    public void insertUnit(ResourceKidUnit unit) {
        allResourceKidMapper.insertUnit(unit);
    }

    @Override
    public ResourceKidUnit getUnitById(int id) {
        return allResourceKidMapper.getUnitById(id);
    }

    @Override
    public void updateUnit(ResourceKidUnit unit) {
        allResourceKidMapper.updateUnit(unit);
    }

    @Override
    public void deleteUnit(int id) {
        allResourceKidMapper.deleteUnit(id);
    }

    @Override
    public List<ResourceKidUnit> getUnitBySceneId(int sceneId) {
        return allResourceKidMapper.getUnitBySceneId(sceneId);
    }

    @Override
    public List<ResourceKidUnit> getUnitByCourseId(int courseId) {
        List<ResourceKidUnit> unitByCourseId = allResourceKidMapper.getUnitByCourseId(courseId);
        ResourceKidCourse courseById = getCourseById(courseId);
        //同步课程填充数据
        if (courseById!=null&&courseById.getType().equals(KidConstant.CourseType.SYNC)){
            for (ResourceKidUnit unit : unitByCourseId) {
                fillUnit(unit);
            }
        }
        return unitByCourseId;
    }

    private void fillUnit(ResourceKidUnit unit) {
        unit.setWords(allResourceKidMapper.getWordByUnitId(unit.getId()));
        unit.setSong(allResourceKidMapper.getVideoByUnitId(unit.getId()));
        unit.setPictureBook(getPictureBookByUnitId(unit.getId()));
    }

    public ResourceKidPictureBook getPictureBookByUnitId(int unitId) {
        ResourceKidPictureBook pictureBook = allResourceKidMapper.getPictureBookByUnitId(unitId);
        if (pictureBook != null) {
            pictureBook.setContentList(allResourceKidMapper.getPictureBookContentByPictureBookId(pictureBook.getId()));
            for (ResourceKidPictureBookContent content : pictureBook.getContentList()) {
                content.setSentenceList(allResourceKidMapper.getPictureBookSentenceByPictureBookContentIdId(content.getId()));
            }
        }
        return pictureBook;
    }

    @Override
    public void addLetterUnit(String letter, Integer unitId,Integer courseId) {
        //判断 letter 必须是 小写字母
        if (!letter.matches("[a-z]")){
            throw new RuntimeException("字母必须是小写字母");
        }
        if (unitId==null){
            if (courseId==null){
                throw new RuntimeException("courseId不能为空");
            }
            ResourceKidUnit resourceKidUnit = new ResourceKidUnit();
            resourceKidUnit.setNameCn(letter);
            resourceKidUnit.setCourseId(courseId);
            resourceKidUnit.setNameEn(letter);
            if (allResourceKidMapper.insertUnit(resourceKidUnit)>0){
             unitId=resourceKidUnit.getId();
            }
        }
        allResourceKidMapper.insertLetter(unitId,letter);
    }

    @Override
    public void updateLetterUnit(String letter, Integer unitId) {
        allResourceKidMapper.updateLetter(unitId,letter);
    }

    @Override
    public void addLetterExample(ResourceKidLetterExample kidLetterExample) {
        allResourceKidMapper.insertLetterExample(kidLetterExample);
    }

    @Override
    public void updateLetterExample(ResourceKidLetterExample kidLetterExample) {
        allResourceKidMapper.updateLetterExample(kidLetterExample);
    }

    @Override
    public void deleteLetterExample(Integer id) {
        allResourceKidMapper.deleteLetterExample(id);
    }

    @Override
    public void setUnitOnLine(Integer unitId, Boolean onLine) {
        allResourceKidMapper.setUnitOnLine(unitId,onLine);
    }

    @Override
    public List<ResourceKidUnit> getAllUnits() {
        return allResourceKidMapper.getAllUnits();
    }

    @Override
    public List<KidLetterUnit> getLetterUnitVOByCourseId(int courseId) {
        ResourceKidCourse course = getCourseById(courseId);
        if (course!=null&&course.getType().equals(KidConstant.CourseType.LETTER)){
            List<ResourceKidUnit> units = getUnitByCourseId(courseId);
            return units.stream().map(unit -> {
                KidLetterUnit kidLetterUnitVO = new KidLetterUnit();
                kidLetterUnitVO.setUnitId(unit.getId());
                kidLetterUnitVO.setUnitName(unit.getNameEn());
                kidLetterUnitVO.setOnLine(unit.isOnLine());
                ResourceKidVideo video = allResourceKidMapper.getVideoByUnitId(unit.getId());
                if (video!=null){
                    kidLetterUnitVO.setSong(video);
                }
                ResourceKidLetter letterByUnitId = allResourceKidMapper.getLetterByUnitId(unit.getId());
                if (letterByUnitId!=null){
                    letterByUnitId.reload();
                    kidLetterUnitVO.setLetter(letterByUnitId);
                    kidLetterUnitVO.setLetterList(allResourceKidMapper.getLetterExampleByLetterId(kidLetterUnitVO.getLetter().getId()));
                }
                return kidLetterUnitVO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    // resource_kid_video相关方法实现
    @Override
    public void insertVideo(ResourceKidVideo video) {
        allResourceKidMapper.insertVideo(video);
    }

    @Override
    public ResourceKidVideo getVideoById(int id) {
        return allResourceKidMapper.getVideoById(id);
    }

    @Override
    public void updateVideo(ResourceKidVideo video) {
        allResourceKidMapper.updateVideo(video);
    }

    @Override
    public void deleteVideo(int id) {
        allResourceKidMapper.deleteVideo(id);
    }

    @Override
    public List<ResourceKidVideo> getAllVideos() {
        return allResourceKidMapper.getAllVideos();
    }

    @Override
    public ResourceKidVideo getVideoByUnitId(int unitId) {
        return allResourceKidMapper.getVideoByUnitId(unitId);
    }

    // resource_kid_word相关方法实现
    @Override
    public void insertWord(ResourceKidWord word) {
        allResourceKidMapper.insertWord(word);
    }

    @Override
    public ResourceKidWord getWordById(int id) {
        return allResourceKidMapper.getWordById(id);
    }

    @Override
    public void updateWord(ResourceKidWord word) {
        allResourceKidMapper.updateWord(word);
    }

    @Override
    public void deleteWord(int id) {
        allResourceKidMapper.deleteWord(id);
    }

    @Override
    public List<ResourceKidWord> getAllWords() {
        return allResourceKidMapper.getAllWords();
    }

    @Override
    public List<ResourceKidWord> getWordByUnitId(int unitId) {
        return allResourceKidMapper.getWordByUnitId(unitId);
    }

}