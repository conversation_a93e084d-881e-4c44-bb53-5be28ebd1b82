package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean;
import com.woxue.resourcemanage.dao.ReadExpandReadArticleCorrelationDao;
import com.woxue.resourcemanage.service.IReadExpandReadArticleCorrelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 扩展阅读-文章句句对应 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Service
public class ReadExpandReadArticleCorrelationServiceImpl implements IReadExpandReadArticleCorrelationService {

    @Autowired
    ReadExpandReadArticleCorrelationDao readexpandReadArticleCorrelationDao;

    @Override
    public List<ReadExpandReadArticleCorrelationBean> list() {
        return readexpandReadArticleCorrelationDao.list();
    }

    @Override
    public ReadExpandReadArticleCorrelationBean edit(Integer correlationId) {
        return readexpandReadArticleCorrelationDao.edit(correlationId);
    }

    @Override
    public ReadExpandReadArticleCorrelationBean editByArticleId(Integer articleId) {
        return readexpandReadArticleCorrelationDao.editByArticleId(articleId);
    }

    @Override
    public int save(ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean) {
        return readexpandReadArticleCorrelationDao.save(readexpandReadArticleCorrelationBean);
    }

    @Override
    public int update(ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean) {
        return readexpandReadArticleCorrelationDao.update(readexpandReadArticleCorrelationBean);
    }
}
