package com.woxue.resourcemanage.service.impl;

import java.util.List;

import com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean;
import com.woxue.resourcemanage.dao.ReadExpandKnowledgeQuestionDao;
import com.woxue.resourcemanage.entity.dto.ReadExpandKnowledgeQuestionParamsDTO;
import com.woxue.resourcemanage.service.IReadExpandKnowledgeQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 扩展知识重点-试题Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@Service
public class ReadExpandKnowledgeQuestionServiceImpl implements IReadExpandKnowledgeQuestionService
{
    @Autowired
    private ReadExpandKnowledgeQuestionDao readexpandKnowledgeQuestionDao;

    /**
     * 查询扩展知识重点-试题
     * 
     * @param id 扩展知识重点-试题主键
     * @return 扩展知识重点-试题
     */
    @Override
    public ReadExpandKnowledgeQuestionBean selectReadExpandKnowledgeQuestionById(Long id)
    {
        return readexpandKnowledgeQuestionDao.selectReadExpandKnowledgeQuestionById(id);
    }

    /**
     * 查询扩展知识重点-试题列表
     * @return 扩展知识重点-试题
     */
    @Override
    public List<ReadExpandKnowledgeQuestionBean> selectReadExpandKnowledgeQuestionList(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO)
    {
        return readexpandKnowledgeQuestionDao.selectReadExpandKnowledgeQuestionList(questionParamsDTO);
    }

    @Override
    public int count(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO) {
        return readexpandKnowledgeQuestionDao.count(questionParamsDTO);
    }

    @Override
    public List<ReadExpandKnowledgeQuestionBean> getListByArticleId(Integer articleId) {
        ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO = new ReadExpandKnowledgeQuestionParamsDTO();
        questionParamsDTO.setArticleId(articleId);
        return readexpandKnowledgeQuestionDao.selectReadExpandKnowledgeQuestionList(questionParamsDTO);
    }

    /**
     * 新增扩展知识重点-试题
     * 
     * @param ReadExpandKnowledgeQuestionBean 扩展知识重点-试题
     * @return 结果
     */
    @Override
    public int insertReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean ReadExpandKnowledgeQuestionBean)
    {
        return readexpandKnowledgeQuestionDao.insertReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean);
    }

    /**
     * 修改扩展知识重点-试题
     * 
     * @param ReadExpandKnowledgeQuestionBean 扩展知识重点-试题
     * @return 结果
     */
    @Override
    public int updateReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean ReadExpandKnowledgeQuestionBean)
    {
        return readexpandKnowledgeQuestionDao.updateReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean);
    }

    /**
     * 批量删除扩展知识重点-试题
     * 
     * @param ids 需要删除的扩展知识重点-试题主键
     * @return 结果
     */
    @Override
    public int deleteReadExpandKnowledgeQuestionByIds(Long[] ids)
    {
        return readexpandKnowledgeQuestionDao.deleteReadExpandKnowledgeQuestionByIds(ids);
    }

    /**
     * 删除扩展知识重点-试题信息
     * 
     * @param id 扩展知识重点-试题主键
     * @return 结果
     */
    @Override
    public int deleteReadExpandKnowledgeQuestionById(Long id)
    {
        return readexpandKnowledgeQuestionDao.deleteReadExpandKnowledgeQuestionById(id);
    }
}
