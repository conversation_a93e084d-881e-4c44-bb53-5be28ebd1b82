package com.woxue.resourcemanage.service.impl;

import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.service.ISoundMarkService;
import com.woxue.resourceservice.util.SoundMarkManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
@Service
public class SoundMarkServiceImpl implements ISoundMarkService {

    @Autowired
    IResourceWordDao resourceWordDao;
    @Override
    public List<Map<String, Object>> list(String spelling, int index, Integer pageSize) {
        return resourceWordDao.getSoundMarkList(spelling,index,pageSize);
    }

    @Override
    public Boolean add(String spelling, String soundmark) {
        boolean b = resourceWordDao.insertSoundMark(spelling,soundmark);
        if (b) SoundMarkManager.addSoundmark(spelling,soundmark);
        return b;
    }

    @Override
    public Boolean update(String oldSpelling,String spelling, String soundmark) {
        boolean b = resourceWordDao.updateSoundMark(oldSpelling, spelling, soundmark);
        if (b) SoundMarkManager.addSoundmark(spelling,soundmark);
        return b;
    }

    @Override
    public Boolean delete(String spelling) {
        Boolean b = resourceWordDao.deleteSoundMark(spelling);
        if (b) SoundMarkManager.deleteSoundmark(spelling);
        return b;
    }

    @Override
    public Integer count(String spelling) {
        return resourceWordDao.countSoundMark(spelling);
    }
}
