package com.woxue.resourcemanage.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.wordUse.WordUseBean;
import com.woxue.common.util.wordUse.WordUseUtil;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.entity.ResourceUnitSentence;
import com.woxue.resourcemanage.entity.ResourceUnitWord;
import com.woxue.resourcemanage.entity.WordEntity;
import com.woxue.resourcemanage.entity.vo.MatchResultVO;
import com.woxue.resourcemanage.service.IWordService;
import com.woxue.resourceservice.util.RedBookCourseManager;
import com.woxue.resourceservice.util.RedBookRedisManager;
import com.woxue.resourceservice.util.WordUseManager;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.Jedis;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
public class WordServiceImpl implements IWordService {

    private final IResourceCourseDao resourceCourseDao;
    private final IResourceWordDao resourceWordDao;
    private final IResourceUnitDao resourceUnitDao;

    public WordServiceImpl(IResourceCourseDao resourceCourseDao, IResourceWordDao resourceWordDao, IResourceUnitDao resourceUnitDao) {
        this.resourceCourseDao = resourceCourseDao;
        this.resourceWordDao = resourceWordDao;
        this.resourceUnitDao = resourceUnitDao;
    }

    @Override
    public HSSJsonReulst insertWordInfo(MultipartFile[] files, Integer courseId, RedBookContentTypeEnum contentType) {
        // 校验内容类型是否支持导入
        if (contentType == RedBookContentTypeEnum.GRAMMAR || contentType == RedBookContentTypeEnum.QUERSTION || contentType == RedBookContentTypeEnum.ARTICLE) {
            return HSSJsonReulst.errorMsg("不支持该专项导入：" + contentType.getCnName());
        }

        InputStream fileStream = extractFileStream(files);
        if (fileStream == null) {
            return HSSJsonReulst.errorMsg("文件读取失败");
        }

        try (InputStream is = fileStream) {
            Workbook wb = Workbook.getWorkbook(is);
            if (wb.getNumberOfSheets() == 1) {
                return processWorkbook(wb, courseId, contentType);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return HSSJsonReulst.errorMsg("资源导入异常");
        }
        return HSSJsonReulst.ok("资源导入成功");
    }

    // 从上传的文件中提取输入流
    private InputStream extractFileStream(MultipartFile[] files) {
        for (MultipartFile mf : files) {
            if (!mf.isEmpty()) {
                try {
                    return mf.getInputStream();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    // 处理工作簿，解析Excel文件内容
    private HSSJsonReulst processWorkbook(Workbook wb, Integer courseId, RedBookContentTypeEnum contentType) {
        Sheet sheet = wb.getSheet(0);
        int rows = sheet.getRows();

        if (rows <= 1) {
            return HSSJsonReulst.ok("资源导入，工作表没有数据!");
        }

        List<WordEntity> wordEntities = parseSheet(sheet, rows, contentType);
        if (insertWordEntities(wordEntities, courseId, contentType)) {
            resourceCourseDao.updateCourseContentContainStatus(courseId,contentType,true);
            return HSSJsonReulst.ok("资源导入成功");
        }
        return HSSJsonReulst.errorMsg("资源导入异常");
    }

    // 解析Excel中的内容，生成WordEntity列表
    private List<WordEntity> parseSheet(Sheet sheet, int rows, RedBookContentTypeEnum contentType) {
        List<WordEntity> list = new ArrayList<>();
        for (int i = 1; i < rows; i++) {
            WordEntity wordEntity = new WordEntity();
            wordEntity.setUnitName(sheet.getCell(0, i).getContents().trim());
            try {
                wordEntity.setLevel(parseInteger(sheet.getCell(1, i).getContents()));
            } catch (Exception e) {
                wordEntity.setLevel(1);
            }
            try {
                wordEntity.setWordId(parseInteger(sheet.getCell(2, i).getContents()));
            } catch (Exception e) {
                wordEntity.setWordId(0);
            }

            wordEntity.setSpelling(sheet.getCell(3, i).getContents().trim().replaceAll("\t", " "));
            wordEntity.setSyllable(sheet.getCell(4, i).getContents().trim());
            wordEntity.setMeaning_en_US(sheet.getCell(5, i).getContents().trim());
            wordEntity.setMeaning_zh_CN(sheet.getCell(6, i).getContents().trim());
            wordEntity.setExample_en_US(sheet.getCell(7, i).getContents().trim());
            wordEntity.setExample_zh_CN(sheet.getCell(8, i).getContents().trim());
            wordEntity.setModify_time(new Date());

            if (isValidWordEntity(wordEntity, contentType)) {
                list.add(wordEntity);
            }
        }
        return list;
    }

    // 检查WordEntity是否合法
    private boolean isValidWordEntity(WordEntity wordEntity, RedBookContentTypeEnum contentType) {
        if (contentType == RedBookContentTypeEnum.WORD) {
            return wordEntity.getLevel() >= 0 && wordEntity.getLevel() <= 12;
        } else if (contentType == RedBookContentTypeEnum.WORD_PHRASE) {
            return wordEntity.getLevel() >= 7 && wordEntity.getLevel() <= 9;
        } else if (contentType == RedBookContentTypeEnum.SENTENCE) {
            return wordEntity.getLevel() >= 1 && wordEntity.getLevel() <= 6;
        }
        return false;
    }

    // 插入WordEntity到数据库
    private boolean insertWordEntities(List<WordEntity> wordEntities, Integer courseId, RedBookContentTypeEnum contentType) {
        boolean result = false;
        Integer unitId = null;
        String unitName = "";
        int unitWordDisplayOrder = 0;

        for (WordEntity wordEntity : wordEntities) {
            if (wordEntity.getWordId() > 0) {
                resourceWordDao.updateByPrimaryKeySelective(wordEntity);
            } else {
                if(StringUtils.isBlank(wordEntity.getUnitName())){
                    continue;
                }
                if (!unitName.equals(wordEntity.getUnitName())) {
                    unitName = wordEntity.getUnitName();
                    unitId = resourceUnitDao.selectByName(wordEntity.getUnitName(), courseId);
                    if (unitId == null) {
                        continue;
                    }
                    unitWordDisplayOrder = resourceWordDao.queryUnitMaxDisplayOrder(unitId) + 1;
                }
                setSyllableIfAbsent(wordEntity);
                if (resourceWordDao.insertWordInfo(wordEntity)) {
                    result = true;
                    insertUnitEntity(wordEntity, unitId, contentType, unitWordDisplayOrder++);
                    try {
                        WordUseBean wordUseBean = new WordUseBean();
                        wordUseBean.setSpell(wordEntity.getSpelling());
                        wordUseBean.setSentence(wordEntity.getExample_en_US());
                        wordUseBean.setMeaning(wordEntity.getExample_zh_CN());
                        // 使用 Redis 队列来异步抓取数据
                        try (Jedis jedis = RedBookRedisManager.getPool().getResource()) {
                            jedis.rpush(WordUseManager.WORD_USE_QUEUE, JSON.toJSONString(wordUseBean));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }catch (Exception e){

                    }
                }
            }
        }
        return result;
    }

    // 如果拼写字段存在，但音节字段为空，从数据库中设置音节
    private void setSyllableIfAbsent(WordEntity wordEntity) {
        if (StringUtils.isNotBlank(wordEntity.getSpelling()) && StringUtils.isBlank(wordEntity.getSyllable())) {
            wordEntity.setSyllable(resourceWordDao.querySyllableBySpelling(wordEntity.getSpelling()));
        }
    }

    // 根据内容类型插入相应的Unit实体
    private void insertUnitEntity(WordEntity wordEntity, Integer unitId, RedBookContentTypeEnum contentType, int unitWordDisplayOrder) {
        if (contentType.toString().startsWith("WORD")) {
            ResourceUnitWord resourceUnitWord = new ResourceUnitWord(unitId, wordEntity.getLevel(), wordEntity.getWordId(), unitWordDisplayOrder);
            resourceWordDao.insertUnitWord(resourceUnitWord);
            if (resourceUnitDao.updateUnitContentWordCount(unitId, wordEntity.getLevel()) <= 0) {
                resourceUnitDao.insertUnitContentWord(unitId, wordEntity.getLevel(),
                        StringUtils.isNotBlank(wordEntity.getExample_en_US()) && StringUtils.isNotBlank(wordEntity.getExample_zh_CN()));
            }
        } else {
            resourceWordDao.insertSentenceElement(wordEntity.getWordId(), wordEntity.getExample_en_US(), null,null,null);
            ResourceUnitSentence resourceUnitSentence = new ResourceUnitSentence(unitId, wordEntity.getLevel(), wordEntity.getWordId(), unitWordDisplayOrder);
            resourceWordDao.insertUnitSentence(resourceUnitSentence);
            if (resourceUnitDao.updateUnitContentSentenceCount(unitId, wordEntity.getLevel()) <= 0) {
                resourceUnitDao.insertUnitContentSentence(unitId, wordEntity.getLevel());
            }
        }
    }

    // 将字符串解析为整数
    private Integer parseInteger(String value) {
        try {
            return StringUtils.isNotBlank(value) ? Integer.parseInt(value.trim()) : null;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    @Override
    public List<WordEntity> queryWords(Integer courseId, Integer wordId, String spelling, Integer pageIndex, Integer pageSize, String orderBy, RedBookContentTypeEnum type) {
        return resourceWordDao.getWordList(wordId, spelling, courseId, pageIndex, pageSize, type);
    }

    @Override
    public WordEntity queryWord(Integer wordId) {
        List<WordEntity> wordList = queryWords(null, wordId, null, null, null, null, null);
        if (wordList != null && !wordList.isEmpty()) {
            return wordList.get(0);
        }
        return null;
    }

    @Override
    public Integer queryWordsCount(Integer courseId, Integer wordId, String spelling, RedBookContentTypeEnum type) {
        return resourceWordDao.getWordCount(wordId, spelling, courseId, type);
    }

    @Override
    public List<WordEntity> querySentences(Integer courseId, Integer wordId, String spelling, Integer divide, Integer pageIndex, Integer pageSize, String orderBy) {
        return resourceWordDao.getSentenceList(wordId, spelling, courseId, divide, pageIndex, pageSize);
    }

    @Override
    public Integer querySentencesCount(Integer courseId, Integer wordId, String spelling, Integer divide) {
        return resourceWordDao.getSentenceCount(wordId, spelling, courseId, divide);
    }

    @Override
    public Integer queryExampleCount(String spelling, Integer courseId, String searchType) {
        return resourceWordDao.queryExampleCount(spelling, courseId, searchType);
    }

    @Override
    public List<WordEntity> queryExamplesList(String spelling, Integer courseId, Integer pageIndex, Integer pageSize, String searchFrom) {
        return resourceWordDao.queryExamplesList(spelling, courseId, pageIndex, pageSize, searchFrom);
    }

    @Override
    public int updateWordDetail(Integer wordId, String spelling, String syllable, String meaning_en_US, String meaning_zh_CN, String example_en_US, String example_zh_CN) {
        return resourceWordDao.updateWordDetail(wordId, spelling, syllable, meaning_en_US, meaning_zh_CN, example_en_US, example_zh_CN);
    }

    @Override
    public int updateSentenceDetail(WordEntity wordEntity) {
        if (resourceWordDao.updateSentenceElement(wordEntity.getWordId(), wordEntity.getSentenceCompositionEn(), wordEntity.getSentenceCompositionCn(), wordEntity.getSentenceClauseCompositionEn(),wordEntity.getSentenceClauseCompositionCn(),wordEntity.getConfirmFlag()) > 0) {
            return resourceWordDao.updateSentence(wordEntity.getWordId(), wordEntity.getExample_en_US(), wordEntity.getExample_zh_CN());
        }
        return 0;
    }

    @Override
    public MatchResultVO matchSentenceElement(Integer courseId) {
        MatchResultVO resultVO = new MatchResultVO();
        List<WordEntity> wordList = resourceWordDao.getSentenceList(null, null, courseId, null, null, null);
        wordList.forEach(wordEntity -> {
            if (StringUtils.isNotBlank(wordEntity.getExample_en_US()) && StringUtils.isBlank(wordEntity.getSentenceCompositionCn())) {
                resultVO.setCount(resultVO.getCount() + 1);
                Map<String, String> sentenceElementBySentence = resourceWordDao.getSentenceElementBySentence(wordEntity.getExample_en_US());
                if (sentenceElementBySentence != null && StringUtils.isNotBlank(sentenceElementBySentence.get("element_jz"))) {
                    if (resourceWordDao.updateSentenceElement(wordEntity.getWordId(), sentenceElementBySentence.get("example_en_US").trim(), sentenceElementBySentence.get("element_jz").trim(),sentenceElementBySentence.get("example_clause_en_US"),sentenceElementBySentence.get("element_clause_jz"),wordEntity.getConfirmFlag()) > 0) {
                        RedBookCourseManager.deleteWord(wordEntity.getWordId());
                        resultVO.setMatchCount(resultVO.getMatchCount() + 1);
                    }
                }
            }
        });
        return resultVO;
    }

    @Override
    public List<WordEntity> queryWordNoSyllable(Integer courseId, Integer pageIndex, Integer pageSize) {
        return resourceWordDao.getNoSyllableWordList(courseId, pageIndex, pageSize);
    }

    @Override
    public Integer queryWordNoSyllableCount(Integer courseId) {
        return resourceWordDao.getNoSyllableWordCount(courseId);
    }

    @Override
    public List<WordEntity> queryWordNoExample(Integer courseId, Integer pageIndex, Integer pageSize) {
        return resourceWordDao.getNoExampleWordList(courseId, pageIndex, pageSize);
    }

    @Override
    public Integer queryWordNoExampleCount(Integer courseId) {
        return resourceWordDao.getNoExampleWordCount(courseId);
    }

    @Override
    public Map<String, String> getWordMnemonics(String spelling) {
        return resourceWordDao.getWordMnemonics(spelling);
    }

    @Override
    public List<Map<String, String>> getWordMnemonicsList(Integer pageIndex, Integer pageSize) {
        return resourceWordDao.getWordMnemonicsList(pageIndex, pageSize);
    }

    @Override
    public Integer getWordMnemonicsCount() {
        return resourceWordDao.getWordMnemonicsCount();
    }

    @Override
    public void insertWordMnemonics(Map<String, String> wordMnemonics) {
        resourceWordDao.insertWordMnemonics(wordMnemonics);
    }

    @Override
    public void updateWordMnemonics(Map<String, String> wordMnemonics) {
        resourceWordDao.updateWordMnemonics(wordMnemonics);
    }

    @Override
    public List<Map<String, Object>> getSoundMarkList(String spelling, Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            pageNum = (pageNum - 1) * pageSize;
        }
        return resourceWordDao.getSoundMarkList(spelling, pageNum, pageSize);
    }

    @Override
    public boolean updateSoundMark(String oldSpelling, String newSpelling, String soundMark) {
        if (StringUtils.isNotBlank(newSpelling) && StringUtils.isNotBlank(soundMark)) {
            return resourceWordDao.updateSoundMark(oldSpelling, newSpelling, soundMark);
        }
        return false;
    }

    @Override
    public boolean insertSoundMark(String spelling, String soundMark) {
        if (StringUtils.isNotBlank(spelling) && StringUtils.isNotBlank(soundMark)) {
            String spell = resourceWordDao.getSoundMark(spelling);
            if (spell == null) {
                return resourceWordDao.insertSoundMark(spelling, soundMark);
            }
        }
        return false;
    }

    @Override
    public Integer getSoundMarkCount() {
        return resourceWordDao.getSoundMarkCount();
    }

    @Override
    public List<Map<String, Object>> getWordUseList(String spelling,Integer status, Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            pageNum = (pageNum - 1) * pageSize;
        }
        return resourceWordDao.getWordUseList(spelling,status, pageNum, pageSize);
    }

    @Override
    public Integer getWordUseCount(String spelling,Integer status) {
        return resourceWordDao.getWordUseCount(spelling,status);
    }

    @Override
    public Integer updateWordUse(String spelling, String sentence, WordUseBean wordUseBean) {
        return resourceWordDao.updateWordUse(spelling, sentence, JSON.toJSONString(wordUseBean));
    }

    @Override
    public boolean generateWordUse(Integer courseId) {
        //查询当前课程的词汇
        List<WordEntity> wordList = resourceWordDao.getWordList(null, null, courseId, null, null, null);
        wordList.forEach(wordEntity -> {
            if (StringUtils.isNotBlank(wordEntity.getExample_en_US())) {
                // 使用 Redis 队列来异步抓取数据
                WordUseBean wordUseBean =new WordUseBean();
                wordUseBean.setSentence(wordEntity.getExample_en_US());
                wordUseBean.setSpell(wordEntity.getSpelling());
                wordUseBean.setMeaning(wordEntity.getExample_zh_CN());
                try (Jedis jedis = RedBookRedisManager.getPool().getResource()) {
                    jedis.rpush(WordUseManager.WORD_USE_QUEUE, JSON.toJSONString(wordUseBean));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        return true;
    }



}
