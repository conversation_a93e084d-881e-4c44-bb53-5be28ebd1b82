package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.*;
import com.woxue.resourcemanage.dao.IResourcePictureBookDao;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.service.ResourcePictureBookService;
import com.woxue.resourceservice.dao.IPictureBookDao;
import com.woxue.resourceservice.util.PictureBookManager;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Transactional
public class ResourcePictureBookServiceImpl implements ResourcePictureBookService {

    @Autowired
    IResourcePictureBookDao resourcePictureBookDao;
    @Autowired
    IPictureBookDao pictureBookDao;
    @Autowired
    IResourceWordDao resourceWordDao;


    @Override
    public List<PictureBook> getPictureBookList() {
        List<PictureBook> allPictureBookList = pictureBookDao.getAllPictureBookList();
        allPictureBookList.forEach(pictureBook -> {
            if (StringUtils.isNotBlank(pictureBook.getThumbnail())&& !pictureBook.getThumbnail().startsWith("picture_book/")) {
                pictureBook.setThumbnail(PictureBookManager.getImgFilePath(pictureBook.getId(),pictureBook.getThumbnail()));
            }
        });
        return allPictureBookList;
    }

    @Override
    public List<PictureBookContent> getPictureBookContentList(Integer pictureBookId) {
        List<PictureBookContent> pictureBookContentList = pictureBookDao.getPictureBookContentList(pictureBookId);
        for (PictureBookContent pictureBookContent : pictureBookContentList) {
            pictureBookContent.setSentenceList(getPictureBookSentenceList(pictureBookContent.getId()));
            if (StringUtils.isNotBlank(pictureBookContent.getPicUrl())&& !pictureBookContent.getPicUrl().startsWith("picture_book/")) {
                pictureBookContent.setPicUrl(PictureBookManager.getImgFilePath(pictureBookContent.getPictureBookId(),pictureBookContent.getPicUrl()));
            }
        }
        return pictureBookContentList;
    }

    @Override
    public List<PictureBookSentence> getPictureBookSentenceList(Integer pictureBookContentId) {
        List<PictureBookSentence> pictureBookSentenceList = pictureBookDao.getPictureBookSentenceList(pictureBookContentId);
        for (PictureBookSentence pictureBookSentence : pictureBookSentenceList) {
            if (StringUtils.isNotBlank(pictureBookSentence.getSoundFile())&& !pictureBookSentence.getSoundFile().startsWith("picture_book/")) {
                pictureBookSentence.setSoundFile(PictureBookManager.getSoundFilePath(pictureBookSentence.getPictureBookId(),pictureBookSentence.getSoundFile()));
            }
        }
        return pictureBookSentenceList;
    }

    @Override
    public Boolean insertPictureBook(PictureBook pictureBook) {
        List<RedBookVersion> redBookVersionList = RedBookCourseManager.getRedBookVersionList(17, -1);
        if (CollectionUtils.isEmpty(redBookVersionList)){
            pictureBook.setCourseId(-1);
        }else {
            List<RedBookCourse> redBookCourseList = RedBookCourseManager.getRedBookCourseList(redBookVersionList.get(0).getId());
            if (CollectionUtils.isEmpty(redBookCourseList)){
                pictureBook.setCourseId(-1);
            }else {
                pictureBook.setCourseId(redBookCourseList.get(0).getId());
            }
        }
        pictureBook.setThumbnail("");
        return resourcePictureBookDao.insertPictureBook(pictureBook);
    }

    @Override
    public Boolean updatePictureBookById(PictureBook pictureBook) {
        return resourcePictureBookDao.updatePictureBookById(pictureBook);
    }

    @Override
    public Boolean insertPictureBookContent(PictureBookContent pictureBookContent) {
        List<RedBookVersion> redBookVersionList = RedBookCourseManager.getRedBookVersionList(17, -1);
        if (CollectionUtils.isEmpty(redBookVersionList)){
            pictureBookContent.setCourseId(-1);
        }else {
            List<RedBookCourse> redBookCourseList = RedBookCourseManager.getRedBookCourseList(redBookVersionList.get(0).getId());
            if (CollectionUtils.isEmpty(redBookCourseList)){
                pictureBookContent.setCourseId(-1);
            }else {
                pictureBookContent.setCourseId(redBookCourseList.get(0).getId());
            }
        }
        if (pictureBookContent.getSentenceNum()==null)pictureBookContent.setSentenceNum(0);
        return resourcePictureBookDao.insertPictureBookContent(pictureBookContent);
    }

    @Override
    public Boolean deletePictureBookById(Integer pictureBookId) {
        return resourcePictureBookDao.deletePictureBookById(pictureBookId)&&PictureBookManager.deletePictureBook(pictureBookId.toString())&&resourcePictureBookDao.deletePictureBookContentByPictureBookId(pictureBookId)&&resourcePictureBookDao.deletePictureBookSentenceByPictureBookId(pictureBookId);
    }

    @Override
    public Boolean updatePictureBookContentById(PictureBookContent pictureBookContent) {
        if (pictureBookContent.getSentenceNum()==null)pictureBookContent.setSentenceNum(getPictureBookSentenceList(pictureBookContent.getId()).size());
        return resourcePictureBookDao.updatePictureBookContentById(pictureBookContent);
    }

    @Override
    public Boolean insertPictureBookSentence(PictureBookSentence pictureBookSentence) {
        List<RedBookVersion> redBookVersionList = RedBookCourseManager.getRedBookVersionList(17, -1);
        if (CollectionUtils.isEmpty(redBookVersionList)){
            pictureBookSentence.setCourseId(-1);
        }else {
            List<RedBookCourse> redBookCourseList = RedBookCourseManager.getRedBookCourseList(redBookVersionList.get(0).getId());
            if (CollectionUtils.isEmpty(redBookCourseList)){
                pictureBookSentence.setCourseId(-1);
            }else {
                pictureBookSentence.setCourseId(redBookCourseList.get(0).getId());
            }
        }
        PictureBookContent pictureBookContent = pictureBookDao.getPictureBookContent(pictureBookSentence.getContentId());
        pictureBookContent.setSentenceNum(pictureBookContent.getSentenceNum()+1);
        resourcePictureBookDao.updatePictureBookContentById(pictureBookContent);
        return resourcePictureBookDao.insertPictureBookSentence(pictureBookSentence);
    }

    @Override
    public Boolean deletePictureBookSentence(Integer pictureBookSentenceId) {
        return resourcePictureBookDao.deletePictureBookSentenceByPictureBookSentenceId(pictureBookSentenceId);
    }

    @Override
    public Boolean updatePictureBookSentenceById(PictureBookSentence pictureBookSentence) {
        PictureBookContent pictureBookContent = pictureBookDao.getPictureBookContent(pictureBookSentence.getContentId());
        pictureBookContent.setSentenceNum(getPictureBookSentenceList(pictureBookSentence.getContentId()).size());
        resourcePictureBookDao.updatePictureBookContentById(pictureBookContent);
        return resourcePictureBookDao.updatePictureBookSentenceById(pictureBookSentence);
    }

    @Override
    public Boolean publishPictureBook() {
        return PictureBookManager.reloadPictureBook();
    }

    @Override
    public List<PictureBookWord> getWordListByPictureBookId(Integer pictureBookId) {
        return pictureBookDao.getWordListByPictureBookId(pictureBookId);
    }

    @Override
    public Boolean insertPictureBookWord(Integer pictureBookId, String spelling, String syllable, String meaningEnUs, String meaningZhCn, String exampleEnUs, String exampleZhCn,String imgUrl) {
        if (StringUtils.isBlank(imgUrl)){
            imgUrl = "";
        }
        if (StringUtils.isBlank(syllable)){
            syllable = resourceWordDao.querySyllableBySpelling(spelling);
        }
        return resourcePictureBookDao.insertPictureBookWord(pictureBookId,spelling,syllable,meaningEnUs,meaningZhCn,exampleEnUs,exampleZhCn,imgUrl);
    }

    @Override
    public Boolean updatePictureBookWord(Integer wordId, Integer pictureBookId, String spelling, String syllable, String meaningEnUs, String meaningZhCn, String exampleEnUs, String exampleZhCn,String imgUrl) {
        if (StringUtils.isBlank(imgUrl)){
            imgUrl = "";
        }
        if (StringUtils.isBlank(syllable)){
            syllable = resourceWordDao.querySyllableBySpelling(spelling);
        }
        return resourcePictureBookDao.updatePictureBookWord(wordId,pictureBookId,spelling,syllable,meaningEnUs,meaningZhCn,exampleEnUs,exampleZhCn,imgUrl);
    }

    @Override
    public Boolean deletePictureBookWord(Integer wordId) {
        return resourcePictureBookDao.deletePictureBookWord(wordId);
    }
}
