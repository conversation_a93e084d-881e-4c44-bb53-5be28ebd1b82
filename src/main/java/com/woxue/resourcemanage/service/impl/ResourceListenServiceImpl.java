package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.*;
import com.woxue.common.util.OSSManager;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.dao.*;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.listen.ResourceListenSentenceImport;
import com.woxue.common.model.redBook.listen.ResourceUnitListenDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceUnitTopicIdPostDTO;
import com.woxue.common.model.redBook.listen.ResourceListenSentenceBean;
import com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean;
import com.woxue.common.model.redBook.listen.ResourceListenSentenceSelectWordBean;
import com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean;
import com.woxue.resourcemanage.service.IResourceListenService;
import com.woxue.resourcemanage.service.IResourceUnitService;
import com.woxue.resourcemanage.util.PropertiesUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025-01-15 16:58
 */
@Service
public class ResourceListenServiceImpl implements IResourceListenService {


    @Autowired
    IRedBookCourseService redBookCourseService;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;
    @Autowired
    IResourceUnitService resourceUnitService;
    @Autowired
    IResourceWordDao resourceWordDao;

    private final String IMAGE_SOURCE_DIR_NAME = "newListenUploadSource";
    private final String IMAGE_DIR_NAME = "newListenUpload";

    @Autowired
    ResourceListenSentenceDao sentenceDao;
    @Autowired
    ResourceListenSentenceQuestionDao questionDao;
    @Autowired
    ResourceListenSentenceSelectWordDao sentenceSelectWordDao;
    @Autowired
    ResourceUnitContentListenDao contentListenDao;
    @Autowired
    IResourceReadArticleDao resourceReadArticleDao;

    @Override
    public Map<String, Object> listByCourseId(Integer courseId, Integer pageNum, Integer pageSize) {
        Map<String,Object> course = resourceCourseDao.getCourseById(courseId);
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, pageNum, pageSize);
        for (Map<String, Object> stringObjectMap : unitList) {
            Integer resourceUnitId = (Integer) stringObjectMap.get("id");
            ResourceUnitContentListenBean unitContentListen = contentListenDao.editByUnitId(resourceUnitId);
            if(unitContentListen != null){
                stringObjectMap.put("topicId",unitContentListen.getTopicId());
                stringObjectMap.put("topicName",unitContentListen.getTopicName());
            }else {
                stringObjectMap.put("topicId",null);
                stringObjectMap.put("topicName",null);
            }
        }
        Integer unitCount = resourceUnitDao.getUnitCount(courseId);
        Map<String, Object> map = new HashMap<>();
        map.put("unitList", unitList);
        map.put("allCount", unitCount);
        return map;
    }


    @Override
    public ResourceUnitListenDTO edit(Integer unitId) {
        ResourceUnitListenDTO resourceUnitListenDTO = new ResourceUnitListenDTO();
        List<ResourceListenSentenceQuestionBean> resourceListenSentenceQuestionBeans = questionDao.listByUnitId(unitId);
        List<ResourceListenSentenceSelectWordBean> resourceListenSentenceSelectWords = sentenceSelectWordDao.listByUnitId(unitId);
        List<ResourceListenSentenceBean> resourceListenSentences = sentenceDao.listByUnitId(unitId);
//        if(resourceListenSentences != null){
//            resourceListenSentences.forEach(sentence -> {
//                sentence.setSoundFile(RedBookConstant.MEDIA_DOMAIN_NAME + sentence.getSoundFile());
//            });
//        }
        resourceUnitListenDTO.setUnitId(unitId);
        resourceUnitListenDTO.setSentenceList(resourceListenSentences);
        resourceUnitListenDTO.setQuestionList(resourceListenSentenceQuestionBeans);
        resourceUnitListenDTO.setSentenceSelectWordList(resourceListenSentenceSelectWords);
        return resourceUnitListenDTO;
    }

    @Override
    public int deleteSentence(Integer id) {
        return sentenceDao.deleteResourceListenSentenceById(id);
    }

    @Override
    public int deleteQuestion(Integer questionId) {
        return questionDao.deleteResourceListenSentenceQuestionByQuestionId(questionId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveOrUpdate(ResourceUnitListenDTO resourceUnitListenDTO) {
        Map<String,Object> unit = resourceUnitDao.getUnitById(resourceUnitListenDTO.getUnitId());
        Integer courseId = (Integer) unit.get("course_id");
        Map<String,Object> course = resourceCourseDao.getCourseById(courseId);
        Integer versionId = (Integer) course.get("version_id");
        Integer stage = (Integer) course.get("stage");

        Integer unitId = resourceUnitListenDTO.getUnitId();
        List<ResourceListenSentenceBean> sentenceList = resourceUnitListenDTO.getSentenceList();
        List<ResourceListenSentenceQuestionBean> questionList = resourceUnitListenDTO.getQuestionList();
        List<ResourceListenSentenceSelectWordBean> sentenceSelectWordList = resourceUnitListenDTO.getSentenceSelectWordList();

        if (!CollectionUtils.isEmpty(sentenceList)) {
            for(ResourceListenSentenceBean sentence : sentenceList){
                sentence.setUnitId(unitId);
                //新增时，id需要为null
                if (sentence.getSoundFile().indexOf("/") == -1) {
                    String soundFileStr = "/" + PropertiesUtils.getProperty("redBook_listen_sound") + "/" + courseId + "/" + sentence.getSoundFile();
                    sentence.setSoundFile(soundFileStr);
                }
                sentenceDao.replaceInsert(sentence);
            }
        }
        if (!CollectionUtils.isEmpty(questionList)) {
            for(ResourceListenSentenceQuestionBean question : questionList){
                question.setUnitId(unitId);
                //新增时，id需要为null
                questionDao.replaceInsert(question);
            }
        }
        if (!CollectionUtils.isEmpty(sentenceList) && !CollectionUtils.isEmpty(sentenceSelectWordList)) {
            sentenceSelectWordDao.deleteByUnitId(unitId);
            sentenceSelectWordList.forEach(sentenceSelectWord -> {
                sentenceSelectWord.setUnitId(unitId);
            });
            //需要sentenceId不为null
            sentenceSelectWordDao.batchInsert(sentenceSelectWordList);
        }

        //更新课程上的听力状态
        List<RedBookCourse> courseList = resourceCourseDao.getCourseList(versionId, stage);
        Map<Integer, Boolean> collect = courseList.stream().collect(Collectors.toMap(RedBookCourse::getId, RedBookCourse::isContainRead));
        if(!collect.get(courseId)){
            resourceCourseDao.updateCourseContentContainStatus(courseId, RedBookContentTypeEnum.LISTEN, true);
        }

        return 1;
    }




    @Override
    public Map<String, Object> uploadPhoto(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> error = new HashMap<String, Object>();
        if (upload == null || upload.isEmpty()) {
            error.put("message", "找不到图片源！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        if (upload.getSize() > 2 * 1024 * 1024) {
            error.put("message", "图片尺寸过大！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!".bmp".equals(suffix) && !".jpg".equals(suffix) && !".png".equals(suffix) && !".gif".equals(suffix) && !".jpeg".equals(suffix)) {
            error.put("message", "文件格式不正确（必须为.jpg/.gif/.bmp/.png/.jpeg文件）");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String imageName = new Date().getTime() + suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if (contextPath.endsWith(File.separator)) {
            contextPath = contextPath.substring(0, contextPath.length() - 1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator) + 1);

        String imagePath = contextPath + IMAGE_SOURCE_DIR_NAME + File.separator + unitId;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(imagePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        imagePath += File.separator + imageName;
        String imageUrl = RedBookConstant.UPLOAD_DOMAIN_NAME + "/" + IMAGE_DIR_NAME + "/" + unitId + "/" + imageName;
        try {
            upload.transferTo(new File(imagePath));
            OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME, IMAGE_DIR_NAME + "/" + unitId + "/" + imageName, Files.newInputStream(Paths.get(imagePath)));
            map.put("uploaded", 1);
            map.put("fileName", imageName);
            map.put("url", imageUrl);
            return map;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (new File(imagePath).exists()) new File(imagePath).delete();
            File file1 = new File(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME));
            if (file1.exists()) file1.delete();
        }

        error.put("message", "未知错误！");
        map.put("uploaded", 0);
        map.put("error", error);
        return map;
    }


    @Override
    public List<ResourceTopic> topicList() {
        return contentListenDao.topicList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveTopicUnitListenList(ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO) {
        ResourceUnitContentListenBean contentListen = contentListenDao.editByUnitId(resourceUnitTopicIdPostDTO.getUnitId());
        if(contentListen == null){
            contentListen = new ResourceUnitContentListenBean();
            contentListen.setTopicId(resourceUnitTopicIdPostDTO.getTopicId());
            contentListen.setResourceUnitId(resourceUnitTopicIdPostDTO.getUnitId());
            contentListenDao.insertResourceUnitContentListen(contentListen);
        }else {
            contentListen.setTopicId(resourceUnitTopicIdPostDTO.getTopicId());
            contentListenDao.updateResourceUnitContentListen(contentListen);
        }
        return 1;
    }

    @Override
    public int insertTopic(ResourceTopic resourceTopicBean) {
        return resourceReadArticleDao.insertTopic(resourceTopicBean);
    }


    /**
     * 正则表达式分割内容
     * @param content 内容
     * @return
     */
    public static String patternSplitContent(String content){
        //句子结束符号的正则表达式
        String regex = "[。？！.?!]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> list = new ArrayList<>();
        int start = 0;
        while (matcher.find()) {
            int end = matcher.end(); // 句子的结索位置
            String sentence = content.substring(start, end) .trim();
            list.add(sentence + "\n");
            start = end;
        }
        return list.stream().collect(Collectors.joining()).trim();
    }

    /**
     * 正则表达式分割内容
     * 解析&nbsp;，返回给前端
     * @param content 内容
     * @return
     */
    public static List<String> patternSplitContentV2(String content){
        //句子结束符号的正则表达式
        String regex = "[。？！.?!]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> list = new ArrayList<>();
        int start = 0;
        while (matcher.find()) {
            int end = matcher.end(); // 句子的结索位置
            String sentenceTrim = content.substring(start, end).trim();
            String sentence = content.substring(start, end);
            //段落
//            if(sentence.contains("&nbsp;")){
//                String newSentence = sentence.replaceAll("&nbsp;", "");
//                if(start == 0){
//                    list.add(newSentence + "\n");
//                }else {
//                    //段落之间，隔一行
//                    list.add("\n");
//                    list.add(newSentence + "\n");
//                }
//            }else
            if(sentence.contains("        ")){
                if(start == 0){
                    list.add(sentenceTrim + "\n");
                }else {
                    //段落之间，隔一行
                    list.add("\n");
                    list.add(sentenceTrim + "\n");
                }
            }else {
                //句子
                list.add(sentenceTrim + "\n");
            }
            start = end;
        }
        return list;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importExcel(MultipartFile multipartFile,
                              Integer courseId, Integer startReadLine) {

        Workbook wb = null;
        File file = null;
        try {
            file= File.createTempFile(multipartFile.getOriginalFilename(),null);
            multipartFile.transferTo(file);file.deleteOnExit();
            wb = WorkbookFactory.create(file);
        }catch (Exception e) {
            e.printStackTrace();
            return "文件解析失败："+e.getMessage();
        }
        //读取excel表中的sheet，参数为sheet的索引值(从0开始)
        Sheet sheet = wb .getSheetAt(0);
        Map<String, Object> courseById = resourceCourseDao.getCourseById(courseId);
        Integer stage = (Integer) courseById.get("stage");
        //总行数
        int lastRowNum = sheet.getLastRowNum();

        //最终确认articleDTOList
        List<ResourceListenSentenceImport> sentenceImportList = new ArrayList<>();

        //除了标题无数据
        if(lastRowNum < 1){
            return "资源导入失败，工作表没有数据!";
        }
        //外循环是循环行，内循环是每行的单元格
        for(int i = startReadLine;i <= lastRowNum; i++){
            //每行数据
            Row row = sheet.getRow(i);
            //row为空，忽略
            if(isEmptyRow(row)){
                continue;
            }
            //单元标识：Unit
            Cell firstCell = row.getCell(0);

            try {
                if(isNotEmptyCell(firstCell)  && firstCell.getStringCellValue().contains("Unit")){
                    //赋值组装文章DTO
                    ResourceListenSentenceImport sentenceImport = this.assignSentence(row);
                    sentenceImportList.add(sentenceImport);
                }
            }catch (Exception e){
                e.printStackTrace();
                return "导入解析失败：第"+(i+1)+"行："+e.getMessage();
            }
        }

        if(sentenceImportList.isEmpty()){
            return "资源导入失败，工作表没有合法数据!";
        }

        for(int i = 0;i < sentenceImportList.size();i++){
            ResourceListenSentenceImport sentenceImport = sentenceImportList.get(i);
            Integer unitId = resourceUnitService.getUnitByCourse(courseId, sentenceImport.getUnitName());
            if(unitId == null){
                return "导入解析失败：单元名"+sentenceImport.getUnitName()+"不存在（注意空格），需要先创建单元并关联主题之后导入";
            }

            //插入句子
            ResourceListenSentenceBean sentenceBean = new ResourceListenSentenceBean();
            sentenceBean.setUnitId(resourceUnitService.getUnitByCourse(courseId, sentenceImport.getUnitName()));
            sentenceBean.setSentenceEnUs(sentenceImport.getSentenceEnUs());
            sentenceBean.setSentenceZhCn(sentenceImport.getSentenceZhCn());
            sentenceBean.setSpeaker(sentenceImport.getSpeaker());
            if (sentenceImport.getSoundFile().indexOf("/") == -1) {
                String soundFileStr = "/" + PropertiesUtils.getProperty("redBook_listen_sound") + "/" + courseId + "/" + sentenceImport.getSoundFile();
                sentenceImport.setSoundFile(soundFileStr);
            }
            sentenceBean.setSoundFile(sentenceImport.getSoundFile());
            sentenceBean.setDisplayOrder(i+1);
            sentenceDao.insertResourceListenSentence(sentenceBean);

            String fillWordList = sentenceImport.getFillWordList();
            if(fillWordList != null){
                String[] word = fillWordList.split("#");
                for(int j = 0;j < word.length;j++){
                    String[] word1 = word[j].split("\\|");
                    //插入句子对应的选词
                    ResourceListenSentenceSelectWordBean sentenceSelectWord = new ResourceListenSentenceSelectWordBean();
                    sentenceSelectWord.setUnitId(sentenceBean.getUnitId());
                    sentenceSelectWord.setSentenceId(sentenceBean.getId());
                    sentenceSelectWord.setSentenceContent(sentenceBean.getSentenceEnUs().replace(word1[0], "|" + word1[0] + "|"));
                    sentenceSelectWord.setWord(word1[0]);
                    sentenceSelectWord.setWordDisturbance(word1[1]);
                    sentenceSelectWord.setType(1);
                    sentenceSelectWordDao.insertResourceListenSentenceSelectWord(sentenceSelectWord);
                }
            }

        }

        return "资源导入成功，成功导入"+sentenceImportList.size()+"个句子及其各自选文填词！";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importQuestionExcel(MultipartFile multipartFile,
                              Integer courseId, Integer startReadLine) {

        Workbook wb = null;
        File file = null;
        try {
            file= File.createTempFile(multipartFile.getOriginalFilename(),null);
            multipartFile.transferTo(file);file.deleteOnExit();
            wb = WorkbookFactory.create(file);
        }catch (Exception e) {
            e.printStackTrace();
            return "文件解析失败："+e.getMessage();
        }
        //读取excel表中的sheet，参数为sheet的索引值(从0开始)
        Sheet sheet = wb .getSheetAt(0);
        Map<String, Object> courseById = resourceCourseDao.getCourseById(courseId);
        Integer stage = (Integer) courseById.get("stage");
        //总行数
        int lastRowNum = sheet.getLastRowNum();

        //题目信息list
        List<ResourceListenSentenceQuestionBean> questionList = new ArrayList<>();
        //除了标题无数据
        if(lastRowNum < 1){
            return "资源导入失败，工作表没有数据!";
        }
        //外循环是循环行，内循环是每行的单元格
        for(int i = startReadLine;i <= lastRowNum; i++){
            //每行数据
            Row row = sheet.getRow(i);
            //row为空，忽略
            if(isEmptyRow(row)){
                continue;
            }
            //单元标识：Unit
            Cell firstCell = row.getCell(0);
            try {
                if(isNotEmptyCell(firstCell) && firstCell.getStringCellValue().contains("Unit")){
                    //赋值组装文章DTO
                    ResourceListenSentenceQuestionBean questionBean = this.assignQuestion(row);
                    questionList.add(questionBean);
                }
            }catch (Exception e){
                e.printStackTrace();
                return "导入解析失败：第"+(i+1)+"行："+e.getMessage();
            }
        }

        if(questionList.isEmpty()){
            return "资源导入失败，工作表没有合法数据!";
        }

        //处理逻辑
        for(ResourceListenSentenceQuestionBean questionBean : questionList){
            Integer unitId = resourceUnitService.getUnitByCourse(courseId, questionBean.getUnitName());
            if(unitId == null){
                return "导入解析失败：单元名"+questionBean.getUnitName()+"不存在（注意空格），需要先创建单元并关联主题之后导入";
            }
            questionBean.setUnitId(unitId);
            questionDao.insertResourceListenSentenceQuestion(questionBean);
        }
        return "资源导入成功，成功导入"+questionList.size()+"个题目！";
    }



    /**
     * 赋值组装句子信息
     * 遍历行的单元格，并解析
     * @return
     */
    public ResourceListenSentenceImport assignSentence(Row row){
        ResourceListenSentenceImport sentenceImport = new ResourceListenSentenceImport();
        for(Cell cell : row) {
            //第1列 unit
            if (cell.getColumnIndex() == 0 && isNotEmptyCell(cell)) {
                cell.setCellType(CellType.STRING);
                sentenceImport.setUnitName(cell.getStringCellValue().trim());
            }
            //第2列 对话人名
            if (cell.getColumnIndex() == 1 && isNotEmptyCell(cell)) {
                cell.setCellType(CellType.STRING);
                sentenceImport.setSpeaker(cell.getStringCellValue().trim());
            }
            //第3列 文章英文
            if (cell.getColumnIndex() == 2 && isNotEmptyCell(cell)) {
                cell.setCellType(CellType.STRING);
                sentenceImport.setSentenceEnUs(cell.getStringCellValue().trim());
            }
            //第4列 文章中文
            if (cell.getColumnIndex() == 3 && isNotEmptyCell(cell)) {
                cell.setCellType(CellType.STRING);
                sentenceImport.setSentenceZhCn(cell.getStringCellValue().trim());
            }
            //第5列 音频地址
            if (cell.getColumnIndex() == 4 && isNotEmptyCell(cell)) {
                cell.setCellType(CellType.STRING);
                sentenceImport.setSoundFile(cell.getStringCellValue().trim());
            }
            //第6列 听文填词 3|2#7|8#|same|different
            if (cell.getColumnIndex() == 5 && isNotEmptyCell(cell)) {
                cell.setCellType(CellType.STRING);
                sentenceImport.setFillWordList(cell.getStringCellValue().trim());
            }

        }
        return sentenceImport;
    }




    /**
     * 赋值组装题目选项信息
     * 遍历行的单元格，并解析
     * @return
     */
    public ResourceListenSentenceQuestionBean assignQuestion(Row row){
        ResourceListenSentenceQuestionBean questionBean = new ResourceListenSentenceQuestionBean();
        for(Cell cell : row) {
            //第1列 unit
            if(cell.getColumnIndex() == 0 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionBean.setUnitName(cell.getStringCellValue().trim());
            }
            //题目问题
            if(cell.getColumnIndex() == 1 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionBean.setQuestion(cell.getStringCellValue());
            }
            //题目选项
            if(cell.getColumnIndex() == 2 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                String trim = cell.getStringCellValue().trim();
                String[] split = trim.split("\\|");
                if(split.length == 3){
                    questionBean.setOptionA(split[0]);
                    questionBean.setOptionB(split[1]);
                    questionBean.setOptionC(split[2]);
                }else if(split.length == 4){
                    questionBean.setOptionA(split[0]);
                    questionBean.setOptionB(split[1]);
                    questionBean.setOptionC(split[2]);
                    questionBean.setOptionD(split[3]);
                }
            }
            //题目正确答案：ABCD或填空题的词汇
            if(cell.getColumnIndex() == 3 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionBean.setAnswer(cell.getStringCellValue().trim());
                if(questionBean.getAnswer().equals("A") || questionBean.getAnswer().equals("B") || questionBean.getAnswer().equals("C")){
                    questionBean.setType(1);
                }else {
                    questionBean.setType(2);
                }
            }
            //题目解析
            if(cell.getColumnIndex() == 4 && isNotEmptyCell(cell)){
                cell.setCellType(CellType.STRING);
                questionBean.setParse(cell.getStringCellValue());
            }
        }
        return questionBean;
    }




    /**
     * 判断单个单元格是否为空
     * @param cell
     * @return
     */
    public static boolean isNotEmptyCell(Cell cell) {
        return cell != null && !cell.getCellTypeEnum().equals(CellType.BLANK);
    }

    /**
     * 判断单个单元格是否为空
     * @param cell
     * @return
     */
    public static boolean isEmptyCell(Cell cell) {
        return cell == null || cell.getCellTypeEnum().equals(CellType.BLANK);
    }


    /**
     * 判断某行是否为空
     * @param row
     * @return
     */
    public static boolean isEmptyRow(Row row) {
        return row == null || row.toString().isEmpty();
    }
}
