package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.dao.ReadExpandReadArticleWordDao;
import com.woxue.resourcemanage.service.IReadExpandReadArticleWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 扩展阅读-文章重点单词 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Service
public class ReadExpandReadArticleWordServiceImpl implements IReadExpandReadArticleWordService {

    @Autowired
    ReadExpandReadArticleWordDao readexpandReadArticleWordDao;

    @Autowired
    IResourceWordDao resourceWordDao;

    @Override
    public List<ReadExpandReadArticleWordBean> list(Integer articleId) {
        return readexpandReadArticleWordDao.list(articleId);
    }

    @Override
    public ReadExpandReadArticleWordBean edit(Integer wordId) {
        return readexpandReadArticleWordDao.edit(wordId);
    }

    @Override
    public int save(ReadExpandReadArticleWordBean readexpandReadArticleWordBean) {
        readexpandReadArticleWordBean.setSyllable(resourceWordDao.querySyllableBySpelling(readexpandReadArticleWordBean.getSpelling()));
        return readexpandReadArticleWordDao.save(readexpandReadArticleWordBean);
    }

    @Override
    public int batchSave(List<ReadExpandReadArticleWordBean> wordBeanList) {
        return readexpandReadArticleWordDao.batchSave(wordBeanList);
    }

    @Override
    public int update(ReadExpandReadArticleWordBean readexpandReadArticleWordBean) {
        readexpandReadArticleWordBean.setSyllable(resourceWordDao.querySyllableBySpelling(readexpandReadArticleWordBean.getSpelling()));
        return readexpandReadArticleWordDao.update(readexpandReadArticleWordBean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchSaveOrUpdateWord(List<ReadExpandReadArticleWordBean> wordBeanList) {
        //批量插入的单词列表
        List<ReadExpandReadArticleWordBean> batchSave = wordBeanList.stream()
                .filter(item -> !Optional.ofNullable(item.getWordId()).isPresent())
                .collect(Collectors.toList());
        //批量更新的单词列表
        List<ReadExpandReadArticleWordBean> batchUpdate = wordBeanList.stream()
                .filter(item -> Optional.ofNullable(item.getWordId()).isPresent())
                .collect(Collectors.toList());
        if(!batchSave.isEmpty()){
            readexpandReadArticleWordDao.batchSave(batchSave);
        }
        if(!batchUpdate.isEmpty()){
            batchUpdate.stream().forEach(item ->{
                readexpandReadArticleWordDao.update(item);
            });
//            readexpandReadArticleWordDao.batchUpdate(batchUpdate);
        }
        return 1;
    }

    @Override
    public int batchUpdate(List<ReadExpandReadArticleWordBean> wordBeanList) {
//        return readexpandReadArticleWordDao.batchUpdate(wordBeanList);
        if(!wordBeanList.isEmpty()){
            wordBeanList.stream().forEach(item ->{
                readexpandReadArticleWordDao.update(item);
            });
        }
        return 1;
    }

    @Override
    public int delete(Integer wordId) {
        return readexpandReadArticleWordDao.delete(wordId);
    }
}
