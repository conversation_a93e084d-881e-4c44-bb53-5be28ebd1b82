package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.WordBean;
import com.woxue.resourcemanage.dao.IResourceWordDao;
import com.woxue.resourcemanage.entity.vo.WordDisturbListVO;
import com.woxue.resourcemanage.service.IWordDisturbService;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WordDisturbServiceImpl implements IWordDisturbService {
    @Autowired
    IResourceWordDao iResourceWordDao;

    @Override
    public WordDisturbListVO list(String spelling, Integer pageIndex, Integer pageSize) {
        WordDisturbListVO result = new WordDisturbListVO();
        result.setCount(iResourceWordDao.countWordDisturbSpelling(spelling));
        if(result.getCount()>0){
            result.setList(iResourceWordDao.getWordDisturbList(spelling, pageIndex, pageSize));
        }
        return result;
    }

    @Override
    public boolean change(String spelling, Integer wordId) {
        WordBean wordBean = RedBookCourseManager.getWordBeanById(wordId);
        if(wordBean==null){
            return false;
        }
        boolean result = iResourceWordDao.updateWordDisturb(spelling,wordId,wordBean.getSpelling(),wordBean.getMeaning_zh_CN())>0;
        if(result){
            //删除redis数据
            RedBookCourseManager.deleteWordDisturb(spelling);
        }
        return result;
    }
}
