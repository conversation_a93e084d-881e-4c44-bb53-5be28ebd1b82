package com.woxue.resourcemanage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.woxue.ai.util.HttpClientPool;
import com.woxue.ai.util.StreamUtil;
import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.*;
import com.woxue.common.util.MD5;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.dao.*;
import com.woxue.resourcemanage.entity.*;
import com.woxue.resourcemanage.service.AiGenerateService;
import com.woxue.resourcemanage.util.RedBookStringUtil;
import com.woxue.resourceservice.util.RedisManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-12 11:31
 */
@Service
public class AiGenerateServiceImpl implements AiGenerateService {

    @Autowired
    private AiMnemonicArticleDao aiMnemonicArticleDao;
    @Autowired
    private AiMnemonicWriteDao aiMnemonicWriteDao;
    @Autowired
    private AiMnemonicSentenceDao aiMnemonicSentenceDao;
    @Autowired
    private AiMnemonicWordDao aiMnemonicWordDao;
    @Autowired
    private ResourceMnemonicWordDao resourceMnemonicWordDao;
    @Autowired
    IRedBookCourseService redBookCourseService;
    @Override
    public Map<String, Object> list(AiGenerateQuery aiGenerateQuery, HttpServletResponse response) {
        aiGenerateQuery.setCourseDetails(aiGenerateQuery.getCourseDetails().trim());
        aiGenerateQuery.setRelationContent(aiGenerateQuery.getRelationContent().trim());
        Integer pageOffset = aiGenerateQuery.getPageSize();
        Integer pageNum = aiGenerateQuery.getPageNum();
        String moduleType = aiGenerateQuery.getModuleType();
        if(pageNum > 0){
            pageOffset = (pageNum - 1) * aiGenerateQuery.getPageSize();
        }
        aiGenerateQuery.setPageNum(pageOffset);
        if(StringUtils.isNotEmpty(aiGenerateQuery.getOrder()) && StringUtils.isNotEmpty(aiGenerateQuery.getSort())){
            if("likeNum".equals(aiGenerateQuery.getOrder())){
                aiGenerateQuery.setOrder("like_num");
            }else if("dislikeNum".equals(aiGenerateQuery.getOrder())){
                aiGenerateQuery.setOrder("dislike_num");
            }else {
                aiGenerateQuery.setOrder(null);
            }
        }

        Map<String, Object> map = new HashMap<>();
        int count;
       switch (moduleType){
               case "article":
                   List<AiMnemonicArticle> aiMnemonicArticles = aiMnemonicArticleDao.selectAiMnemonicArticleList(aiGenerateQuery);
                   count = aiMnemonicArticleDao.count(aiGenerateQuery);
                   map.put("total", count);
                   if(!aiMnemonicArticles.isEmpty()){
                       List<AiMnemonicParent> collect = aiMnemonicArticles.stream().map(item -> {
                           AiMnemonicParent parent = new AiMnemonicParent();
                           parent.setModuleType(moduleType);
                           BeanUtils.copyProperties(item, parent);
                           return parent;
                       }).collect(Collectors.toList());
                       map.put("list", collect);
                       return map;
                   }
                   break;
               case "write":
                   List<AiMnemonicWrite> aiMnemonicWrites = aiMnemonicWriteDao.selectAiMnemonicWriteList(aiGenerateQuery);
                   count = aiMnemonicWriteDao.count(aiGenerateQuery);
                   map.put("total", count);
                   if(!aiMnemonicWrites.isEmpty()){
                       List<AiMnemonicParent> collect = aiMnemonicWrites.stream().map(item -> {
                           AiMnemonicParent parent = new AiMnemonicParent();
                           parent.setModuleType(moduleType);
                           BeanUtils.copyProperties(item, parent);
                           return parent;
                       }).collect(Collectors.toList());
                       map.put("list", collect);
                       return map;
                   }
                   break;
               case "sentence":
                   List<AiMnemonicSentence> aiMnemonicSentences = aiMnemonicSentenceDao.selectAiMnemonicSentenceList(aiGenerateQuery);
                   count = aiMnemonicSentenceDao.count(aiGenerateQuery);
                   map.put("total", count);
                   if(!aiMnemonicSentences.isEmpty()){
                       List<AiMnemonicParent> collect = aiMnemonicSentences.stream().map(item -> {
                           AiMnemonicParent parent = new AiMnemonicParent();
                           parent.setModuleType(moduleType);
                           BeanUtils.copyProperties(item, parent);
                           return parent;
                       }).collect(Collectors.toList());
                       map.put("list", collect);
                       return map;
                   }
                   break;
               case "word":
                   List<AiMnemonicWord> aiMnemonicWords = aiMnemonicWordDao.selectAiMnemonicWordList(aiGenerateQuery);
                   count = aiMnemonicWordDao.count(aiGenerateQuery);
                   map.put("total", count);
                   if(!aiMnemonicWords.isEmpty()){
                       List<AiMnemonicParent> collect = aiMnemonicWords.stream().map(item -> {
                           AiMnemonicParent parent = new AiMnemonicParent();
                           parent.setModuleType(moduleType);
                           BeanUtils.copyProperties(item, parent);
                           return parent;
                       }).collect(Collectors.toList());
                       map.put("list", collect);
                       return map;
                   }
                   break;
               case "resourceWord":
                   List<ResourceMnemonicWord> resourceMnemonicWords = resourceMnemonicWordDao.selectResourceMnemonicWordList(aiGenerateQuery);
                   count = resourceMnemonicWordDao.count(aiGenerateQuery);
                   map.put("total", count);
                   if(!resourceMnemonicWords.isEmpty()){
                       List<AiMnemonicParent> collect = resourceMnemonicWords.stream().map(item -> {
                           AiMnemonicParent parent = new AiMnemonicParent();
                           parent.setModuleType(moduleType);
                           BeanUtils.copyProperties(item, parent);
                           return parent;
                       }).collect(Collectors.toList());
                       map.put("list", collect);
                       return map;
                   }
                   break;
       }
        map.put("list", new ArrayList<>());
        return map;
    }

    @Override
    public void execute(Integer id,String moduleType,HttpServletResponse response) {
        if("article".equals(moduleType)){
            AiMnemonicArticle aiMnemonicArticle = aiMnemonicArticleDao.selectAiMnemonicArticleById(id);
            String grade = getGrade(aiMnemonicArticle.getCourseId());
            aiReciteMethod(aiMnemonicArticle.getArticleId(),grade, response);
        }else if("write".equals(moduleType)){
            AiMnemonicWrite aiMnemonicWrite = aiMnemonicWriteDao.selectAiMnemonicWriteById(id);
            String grade = getGrade(aiMnemonicWrite.getCourseId());
            aiWriteGuide(aiMnemonicWrite.getUnitId(),grade, response);
        }else if("sentence".equals(moduleType)){
            AiMnemonicSentence aiMnemonicSentence = aiMnemonicSentenceDao.selectAiMnemonicSentenceById(id);
            aiSentenceStructure(aiMnemonicSentence.getRelationContent(), response);
        }
    }

    @Override
    public String executeWord(Integer id, HttpServletResponse response) {
        AiMnemonicWord aiMnemonicWord = aiMnemonicWordDao.selectAiMnemonicWordById(id);
        return aiWordMemory(aiMnemonicWord.getWordId());
    }

    @Override
    public Integer update(AiMnemonicParent aiMnemonicParent) {
        String redisKey;
        switch (aiMnemonicParent.getModuleType()){
            case "article":
                AiMnemonicArticle aiMnemonicArticle = new AiMnemonicArticle();
                aiMnemonicArticle.setId(aiMnemonicParent.getId());
                aiMnemonicArticle.setFeedContent(aiMnemonicParent.getFeedContent());
                AiMnemonicArticle article = aiMnemonicArticleDao.selectAiMnemonicArticleById(aiMnemonicParent.getId());
                //更新缓存
                redisKey="aiReciteMethod"+":" + article.getArticleId() + ":" + article.getGradeName();
                RedisManager.setExString(redisKey,StreamUtil.DEFAULT_REDIS_CACHE_TIME,aiMnemonicArticle.getFeedContent());
                aiMnemonicArticle.setDislikeNum(0);
                aiMnemonicArticle.setStatus(1);
                return aiMnemonicArticleDao.updateAiMnemonicArticle(aiMnemonicArticle);
            case "write":
                AiMnemonicWrite aiMnemonicWrite = new AiMnemonicWrite();
                aiMnemonicWrite.setId(aiMnemonicParent.getId());
                aiMnemonicWrite.setFeedContent(aiMnemonicParent.getFeedContent());
                AiMnemonicWrite write = aiMnemonicWriteDao.selectAiMnemonicWriteById(aiMnemonicParent.getId());
                //更新缓存
                redisKey="aiWriteGuide"+":" + write.getCourseId() + ":"+ write.getUnitId() + ":" + write.getGradeName();
                RedisManager.setExString(redisKey,StreamUtil.DEFAULT_REDIS_CACHE_TIME,aiMnemonicWrite.getFeedContent());
                aiMnemonicWrite.setDislikeNum(0);
                aiMnemonicWrite.setStatus(1);
                return aiMnemonicWriteDao.updateAiMnemonicWrite(aiMnemonicWrite);
            case "sentence":
                AiMnemonicSentence aiMnemonicSentence = new AiMnemonicSentence();
                aiMnemonicSentence.setId(aiMnemonicParent.getId());
                aiMnemonicSentence.setFeedContent(aiMnemonicParent.getFeedContent());
                AiMnemonicSentence sentence = aiMnemonicSentenceDao.selectAiMnemonicSentenceById(aiMnemonicParent.getId());
                //更新缓存
                redisKey="aiSentenceStructure"+":" + MD5.newMd5(sentence.getRelationContent().trim());
                RedisManager.setExString(redisKey,StreamUtil.DEFAULT_REDIS_CACHE_TIME,aiMnemonicSentence.getFeedContent());
                aiMnemonicSentence.setDislikeNum(0);
                aiMnemonicSentence.setStatus(1);
                return aiMnemonicSentenceDao.updateAiMnemonicSentence(aiMnemonicSentence);
            case "word":
                AiMnemonicWord aiMnemonicWord = new AiMnemonicWord();
                aiMnemonicWord.setId(aiMnemonicParent.getId());
                aiMnemonicWord.setFeedContent(aiMnemonicParent.getFeedContent());
                AiMnemonicWord word = aiMnemonicWordDao.selectAiMnemonicWordById(aiMnemonicParent.getId());
                //更新缓存
                redisKey="aiWordMemory"+":" + MD5.newMd5(word.getRelationContent().trim());
                RedisManager.setExString(redisKey,StreamUtil.DEFAULT_REDIS_CACHE_TIME,aiMnemonicWord.getFeedContent());
                aiMnemonicWord.setDislikeNum(0);
                aiMnemonicWord.setStatus(1);
                return aiMnemonicWordDao.updateAiMnemonicWord(aiMnemonicWord);
        }
        return 0;
    }

    @Override
    public Integer handleIgnore(Integer id, String moduleType) {
        switch (moduleType){
            case "article":
                AiMnemonicArticle aiMnemonicArticle = aiMnemonicArticleDao.selectAiMnemonicArticleById(id);
                aiMnemonicArticle.setStatus(2);
                return aiMnemonicArticleDao.updateAiMnemonicArticle(aiMnemonicArticle);
            case "write":
                AiMnemonicWrite aiMnemonicWrite = aiMnemonicWriteDao.selectAiMnemonicWriteById(id);
                aiMnemonicWrite.setStatus(2);
                return aiMnemonicWriteDao.updateAiMnemonicWrite(aiMnemonicWrite);
            case "sentence":
                AiMnemonicSentence aiMnemonicSentence = aiMnemonicSentenceDao.selectAiMnemonicSentenceById(id);
                aiMnemonicSentence.setStatus(2);
                return aiMnemonicSentenceDao.updateAiMnemonicSentence(aiMnemonicSentence);
            case "word":
                AiMnemonicWord aiMnemonicWord = aiMnemonicWordDao.selectAiMnemonicWordById(id);
                aiMnemonicWord.setStatus(2);
                return aiMnemonicWordDao.updateAiMnemonicWord(aiMnemonicWord);
            case "resourceWord":
                ResourceMnemonicWord resourceMnemonicWord = resourceMnemonicWordDao.selectResourceMnemonicWordById(id);
                resourceMnemonicWord.setStatus(2);
                return resourceMnemonicWordDao.updateResourceMnemonicWord(resourceMnemonicWord);
        }
        return 0;
    }

    @Override
    public Integer saveOrUpdate(AiMnemonicDTO aiMnemonicDTO) {
        switch (aiMnemonicDTO.getModuleType()){
            case "article":
                AiMnemonicArticle aiMnemonicArticle = aiMnemonicArticleDao.selectByArticleId(aiMnemonicDTO.getArticleId(),aiMnemonicDTO.getGradeName());
                if(aiMnemonicArticle != null){
                    aiMnemonicArticle.setStatus(0);
                    int dislikeNum = aiMnemonicArticle.getDislikeNum() + aiMnemonicDTO.getDislikeNum();
                    int likeNum = aiMnemonicArticle.getLikeNum() +  aiMnemonicDTO.getLikeNum();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicArticle);
                    aiMnemonicArticle.setDislikeNum(dislikeNum);
                    aiMnemonicArticle.setLikeNum(likeNum);
                    aiMnemonicArticleDao.updateAiMnemonicArticle(aiMnemonicArticle);
                }else {
                    aiMnemonicArticle = new AiMnemonicArticle();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicArticle);
                    aiMnemonicArticleDao.insertAiMnemonicArticle(aiMnemonicArticle);
                }
                break;
            case "write":
                AiMnemonicWrite aiMnemonicWrite = aiMnemonicWriteDao.selectByUnitId(aiMnemonicDTO.getUnitId(),aiMnemonicDTO.getGradeName());
                if(aiMnemonicWrite != null){
                    int dislikeNum = aiMnemonicWrite.getDislikeNum() + aiMnemonicDTO.getDislikeNum();
                    int likeNum = aiMnemonicWrite.getLikeNum() +  aiMnemonicDTO.getLikeNum();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicWrite);
                    aiMnemonicWrite.setStatus(0);
                    aiMnemonicWrite.setDislikeNum(dislikeNum);
                    aiMnemonicWrite.setLikeNum(likeNum);
                    aiMnemonicWriteDao.updateAiMnemonicWrite(aiMnemonicWrite);
                }else {
                    aiMnemonicWrite = new AiMnemonicWrite();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicWrite);
                    aiMnemonicWriteDao.insertAiMnemonicWrite(aiMnemonicWrite);
                }
                break;
            case "sentence":
                AiMnemonicSentence aiMnemonicSentence = aiMnemonicSentenceDao.selectSentenceId(aiMnemonicDTO.getSentenceId());
                if(aiMnemonicSentence != null){
                    int dislikeNum = aiMnemonicSentence.getDislikeNum() + aiMnemonicDTO.getDislikeNum();
                    int likeNum = aiMnemonicSentence.getLikeNum() +  aiMnemonicDTO.getLikeNum();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicSentence);
                    aiMnemonicSentence.setStatus(0);
                    aiMnemonicSentence.setDislikeNum(dislikeNum);
                    aiMnemonicSentence.setLikeNum(likeNum);
                    aiMnemonicSentenceDao.updateAiMnemonicSentence(aiMnemonicSentence);
                }else {
                    aiMnemonicSentence = new AiMnemonicSentence();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicSentence);
                    aiMnemonicSentenceDao.insertAiMnemonicSentence(aiMnemonicSentence);
                }
                break;
            case "word":
                AiMnemonicWord aiMnemonicWord = aiMnemonicWordDao.selectByWordId(aiMnemonicDTO.getWordId());
                if(aiMnemonicWord != null){
                    int dislikeNum = aiMnemonicWord.getDislikeNum() + aiMnemonicDTO.getDislikeNum();
                    int likeNum = aiMnemonicWord.getLikeNum() +  aiMnemonicDTO.getLikeNum();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicWord);
                    aiMnemonicWord.setStatus(0);
                    aiMnemonicWord.setDislikeNum(dislikeNum);
                    aiMnemonicWord.setLikeNum(likeNum);
                    aiMnemonicWordDao.updateAiMnemonicWord(aiMnemonicWord);
                }else {
                    aiMnemonicWord = new AiMnemonicWord();
                    BeanUtils.copyProperties(aiMnemonicDTO,aiMnemonicWord);
                    aiMnemonicWordDao.insertAiMnemonicWord(aiMnemonicWord);
                }
                break;
            case "resourceWord":
                ResourceMnemonicWord resourceMnemonicWord = resourceMnemonicWordDao.selectByWordId(aiMnemonicDTO.getWordId());
                if(resourceMnemonicWord != null){
                    int dislikeNum = resourceMnemonicWord.getDislikeNum() + aiMnemonicDTO.getDislikeNum();
                    int likeNum = resourceMnemonicWord.getLikeNum() +  aiMnemonicDTO.getLikeNum();
                    BeanUtils.copyProperties(aiMnemonicDTO,resourceMnemonicWord);
                    resourceMnemonicWord.setStatus(0);
                    resourceMnemonicWord.setDislikeNum(dislikeNum);
                    resourceMnemonicWord.setLikeNum(likeNum);
                    resourceMnemonicWordDao.updateResourceMnemonicWord(resourceMnemonicWord);
                }else {
                    resourceMnemonicWord = new ResourceMnemonicWord();
                    BeanUtils.copyProperties(aiMnemonicDTO,resourceMnemonicWord);
                    resourceMnemonicWordDao.insertResourceMnemonicWord(resourceMnemonicWord);
                }
                break;
        }
        return 1;
    }

    /**
     * 写作-AI写作辅导
     * @param unitId
     * @param response
     * @return
     */
    public void aiWriteGuide(Integer unitId,String grade, HttpServletResponse response){
        PrintWriter out= StreamUtil.getPrintWriter(response);
        if(out==null){
//            return ("AI句子学法生成异常，请稍后重试");
            return;
        }
        RedBookUnit unit = redBookCourseService.getUnit(unitId);
        RedBookUnitContentWrite contentWrite = unit.getContentWrite();
        if(unit == null || contentWrite==null){
//            return ("单元数据异常");
            return;
        }
        RedBookDeepSeekParam deepSeekParam = new RedBookDeepSeekParam();
        deepSeekParam.setGrade(grade);
        deepSeekParam.setQuestion(RedBookStringUtil.convertRichTextToPlainText(contentWrite.getSampleTitle()));
        String result = StreamUtil.postStream(DeepSeekUrl.WRITING_GUIDE, JSONObject.toJSONString(deepSeekParam), out);
//        return result;
    }

    /**
     * 课文-AI背法
     * @param articleId
     * @param response
     * @return
     */
    public String aiReciteMethod(Integer articleId,String grade, HttpServletResponse response) {
        PrintWriter out=StreamUtil.getPrintWriter(response);
        if(out==null){
            return ("AI句子学法生成异常，请稍后重试");
        }
        RedBookArticle redBookArticle = redBookCourseService.getArticle(articleId);
        if(redBookArticle==null){
            return ("未查询到课文信息");
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<RedBookArticleSentence> sentenceList = redBookArticle.getSentenceList();
        if (CollectionUtils.isNotEmpty(sentenceList)) {
            for (int i = 0; i < sentenceList.size(); i++) {
                stringBuilder.append(sentenceList.get(i).getSentenceEnUS());
                if (i + 1 < sentenceList.size() && sentenceList.get(i + 1).getShowType() != null) {
                    stringBuilder.append("\n");
                }
            }
        }
        RedBookDeepSeekParam deepSeekParam = new RedBookDeepSeekParam();
        deepSeekParam.setGrade(grade);
        deepSeekParam.setArticle(stringBuilder.toString());
        String result = StreamUtil.postStream(DeepSeekUrl.ARTICLE_RECITING_SKILL, JSONObject.toJSONString(deepSeekParam), out);
        return result;
    }

    public String getGrade(Integer courseId){
        RedBookCourse course = redBookCourseService.getCourse(courseId);
        String grade = "初中";
        if(course != null){
            int stage = course.getStage();
            switch (stage){
                case 1:
                    grade = "小学";
                    break;
                case 2:
                    grade = "初中";
                    break;
                case 3:
                    grade = "高中";
                    break;
            }
        }
        return grade;
    }


    /**
     * 句子-AI学法生成
     * @param sentence
     * @param response
     * @return
     */
    public String aiSentenceStructure(String sentence, HttpServletResponse response){
        PrintWriter out=StreamUtil.getPrintWriter(response);
        if(out==null){
            return ("AI句子学法生成异常，请稍后重试");
        }
        RedBookDeepSeekParam deepSeekParam = new RedBookDeepSeekParam();
        deepSeekParam.setSentence(sentence);
        String result = StreamUtil.postStream(DeepSeekUrl.SENTENCE_STRUCTURE, JSONObject.toJSONString(deepSeekParam), out);
        return result;
    }

    /**
     * 词汇-AI记法生成
     * @param wordId
     * @return
     */
    public String aiWordMemory(Integer wordId){
        WordBean wordBean = redBookCourseService.getWordBeanById(wordId);
        Map<String,String> map=new HashMap<>();
        map.put("word",wordBean.getSpelling());
        String cn= getMeaningSoudFileCn(wordBean);
        map.put("translation",cn);
        String response="";
        try {
            response = HttpClientPool.sendPost(DeepSeekUrl.WORD_MEMORY, JSONObject.toJSONString(map));
        } catch (IOException e) {
        }
        if(StringUtils.isNotEmpty(response)){
            JSONObject jsonResponse = JSONObject.parseObject(response);
            if(jsonResponse!=null && jsonResponse.containsKey("data")){
                return jsonResponse.getString("data");
            }
        }
        return ("AI记法生成异常，稍后重试");
    }

    public String getMeaningSoudFileCn(WordBean wBean) {
        /*List<Map<Integer, String>> meaningList = wBean.getMeaningList();
        String cn = "";
        if (CollectionUtils.isNotEmpty(meaningList)) {
            Map<Integer, String> integerStringMap = meaningList.get(0);
            if (integerStringMap != null) {
                cn = integerStringMap.get(1);
            }
            if(!RedBookStringUtil.isContainChinese(cn)&&meaningList.size()>1){
                integerStringMap = meaningList.get(1);
                if (integerStringMap != null) {
                    cn = integerStringMap.get(1);
                }
            }
        } else {
            cn = wBean.getMeaning_zh_CN();
        }
        //①截取字符串，有的含义太长，读不完单词播放器就跳到下一个了，所以没必要全翻译
        String[] split = cn.split("；");
        cn = split[0];
        //缩写词：去除词性
        cn=RedBookStringUtil.extractChinese(cn);
        //②去除括号之间的内容，示例：以…开始(begin with)
        return RedBookStringUtil.removeParentheses(cn);*/
        List<Map<Integer, String>> meaningList = wBean.getMeaningList();
        String cn = "";
        if (!meaningList.isEmpty()) {
            Map<Integer, String> integerStringMap = meaningList.get(0);
            if (integerStringMap != null) {
                cn = integerStringMap.get(1);
            }
            if(!RedBookStringUtil.isContainChinese(cn)&&meaningList.size()>1){
                integerStringMap = meaningList.get(1);
                if (integerStringMap != null) {
                    cn = integerStringMap.get(1);
                }
            }
        } else {
            cn = wBean.getMeaning_zh_CN();
        }
        //①截取字符串，有的含义太长，读不完单词播放器就跳到下一个了，所以没必要全翻译
        String[] split = cn.split("；");
        cn = split[0];
        //②去除括号之间的内容，示例：以…开始(begin with)
//        cn = RedBookStringUtil.removeParentheses(cn);
        //缩写词：去除词性 PLA  abbr.中国人民解放军
        cn= RedBookStringUtil.extractChineseNew(cn);
        return cn;
    }


}
