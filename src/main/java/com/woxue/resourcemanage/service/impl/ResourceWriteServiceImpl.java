package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.*;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.OSSManager;
import com.woxue.resourcemanage.dao.*;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.write.EditSampleDto;
import com.woxue.resourcemanage.entity.dto.write.EditWordDto;
import com.woxue.resourcemanage.entity.dto.write.SaveUnitTopicDto;
import com.woxue.resourcemanage.entity.write.TopicRelatedUnitBean;
import com.woxue.resourcemanage.entity.write.WriteNewUnitBean;
import com.woxue.resourcemanage.service.IResourceWriteService;
import com.woxue.resourcemanage.util.WriteContentStack;
import com.woxue.resourceservice.util.RedBookRedisManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ResourceWriteServiceImpl implements IResourceWriteService {
    private final String TEMP_WRITE_IMAGE_PREFIX = "tempNewWriteImage:userId:unitId:";
    private final String IMAGE_SOURCE_DIR_NAME = "newWriteUploadSource";
    private final String IMAGE_DIR_NAME = "newWriteUpload";
    @Autowired
    IResourceTopicDao resourceTopicDao;
    @Autowired
    IResourceUnitContentWriteDao resourceUnitContentWriteDao;
    @Autowired
    IResourceUnitWriteSentenceDao resourceUnitWriteSentenceDao;
    @Autowired
    IWriteContentDao writeContentDao;
    @Autowired
    IWriteTitleDao writeTitleDao;
    @Autowired
    IResourceVersionDao resourceVersionDao;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveWriteMethodCard(String contextPath, WriteNewUnitBean writeNewUnitBean) {
        //删除未使用的图片
        Integer unitId = writeNewUnitBean.getId();
        deleteNotUseImage(contextPath, writeNewUnitBean);
        writeTitleDao.deleteByUnitId(unitId);
        writeContentDao.deleteByUnitId(unitId);
        //保存卡片内容
        List<WriteUnitContentBean> contentList = writeNewUnitBean.getWriteContentList();
        for (WriteUnitContentBean content : contentList) {
            content.setId(null);
            content.setTitleId(null);
            content.setUnitId(unitId);
        }
        List<WriteUnitContentBean> errList = addUnitContentList(contentList);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("success", true);
        map.put("cardId", writeNewUnitBean.getId());
        map.put("errList", errList);
        return map;
    }

    @Override
    public Map<String, Object> textUploadByPaste(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> error = new HashMap<String, Object>();
		/*response.setContentType("text/html;charset=utf-8");
		response.setCharacterEncoding("utf-8");
		response.setHeader("Access-Control-Allow-Origin","*");*/
        if (upload == null || upload.isEmpty()) {
            error.put("message", "找不到图片源！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        if (upload.getSize() > 2 * 1024 * 1024) {
            error.put("message", "图片尺寸过大！");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String originalFilename = upload.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!".bmp".equals(suffix) && !".jpg".equals(suffix) && !".png".equals(suffix) && !".gif".equals(suffix) && !".jpeg".equals(suffix)) {
            error.put("message", "文件格式不正确（必须为.jpg/.gif/.bmp/.png/.jpeg文件）");
            map.put("uploaded", 0);
            map.put("error", error);
            return map;
        }
        String imageName = new Date().getTime() + suffix;
        // 文件保存路径
        String contextPath = request.getSession().getServletContext().getRealPath("/");
        if (contextPath.endsWith(File.separator)) {
            contextPath = contextPath.substring(0, contextPath.length() - 1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator) + 1);

        String imagePath = contextPath + IMAGE_SOURCE_DIR_NAME + File.separator + unitId;
        //图片目录是否存在，没有则创建新的目录
        File file = new File(imagePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        imagePath += File.separator + imageName;
        String imageUrl = RedBookConstant.UPLOAD_DOMAIN_NAME + "/" + IMAGE_DIR_NAME + "/" + unitId + "/" + imageName;
        try {
            upload.transferTo(new File(imagePath));
            OSSManager.upload(OSSManager.UPLOAD_BUCKETNAME, IMAGE_DIR_NAME + "/" + unitId + "/" + imageName, Files.newInputStream(Paths.get(imagePath)));
            addImagePathToRedis(unitId, IMAGE_DIR_NAME + "/" + unitId + "/" + imageName);
            map.put("uploaded", 1);
            map.put("fileName", imageName);
            map.put("url", imageUrl);
            return map;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (new File(imagePath).exists()) new File(imagePath).delete();
            File file1 = new File(imagePath.replace(IMAGE_SOURCE_DIR_NAME, IMAGE_DIR_NAME));
            if (file1.exists()) file1.delete();
        }

        error.put("message", "未知错误！");
        map.put("uploaded", 0);
        map.put("error", error);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HSSJsonReulst<Integer> addTopic(String topicName, String type) {
        if (StringUtils.isEmpty(topicName) || StringUtils.isEmpty(topicName.trim())) {
            return HSSJsonReulst.errorMsg("主题信息为空！");
        }
        if (type == null) {
            return HSSJsonReulst.errorMsg("主题类型为空！");
        }
        ResourceTopic resourceTopic = resourceTopicDao.selectTopicByNameAndType(topicName, type);
        if (resourceTopic != null) {
            return HSSJsonReulst.errorMsg("主题:" + topicName + "已存在！");
        }
        resourceTopic = new ResourceTopic();
        resourceTopic.setName(topicName);
        resourceTopic.setType(type);
        int i = resourceTopicDao.addTopic(type,topicName);
        if (i > 0) {
            return HSSJsonReulst.ok(resourceTopic.getId());
        }
        return HSSJsonReulst.errorMsg("保存失败！");
    }

    @Override
    public HSSJsonReulst<List<ResourceTopic>> getTopicListByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return HSSJsonReulst.errorMsg("主题类型为空！");
        }
        return HSSJsonReulst.ok(resourceTopicDao.selectTopicListByType(type));
    }

    @Override
    public HSSJsonReulst<List<WriteUnitTitleBean>> getWriteMethodByUnitId(Integer unitId) {
        List<WriteUnitTitleBean> resultList = new ArrayList<>();
        WriteUnitTitleBean writeUnitTitleBean = writeTitleDao.getWriteParientTitleByUnitId(unitId);
        if (writeUnitTitleBean != null) {
            resultList.add(writeUnitTitleBean);
            resultList.addAll(writeTitleDao.getTitleListByUnitIdAndParientId(unitId, writeUnitTitleBean.getId()));
        }
        Optional.ofNullable(resultList).orElse(new ArrayList<>()).forEach(item -> {
            item.setWriteUnitContentBeanList(writeContentDao.getWriteContentListByUnitIdAndTitleId(unitId, item.getId()));
        });
        return HSSJsonReulst.ok(resultList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HSSJsonReulst<Integer> addSentenceTrain(ResourceUnitWriteSentence resourceUnitWriteSentence) {
        if (resourceUnitWriteSentence.getUnitId() == null) {
            return HSSJsonReulst.errorMsg("单元id为空！");
        }
        if (StringUtils.isEmpty(resourceUnitWriteSentence.getSentenceEnUs()) || StringUtils.isEmpty(resourceUnitWriteSentence.getSentenceEnUs().trim())) {
            return HSSJsonReulst.errorMsg("句子英文为空！");
        }
        if (StringUtils.isEmpty(resourceUnitWriteSentence.getSentenceZhCn()) || StringUtils.isEmpty(resourceUnitWriteSentence.getSentenceZhCn().trim())) {
            return HSSJsonReulst.errorMsg("句子中文为空！");
        }
        RedBookUnitContentWrite redBookUnitContentWrite = resourceUnitContentWriteDao.getRecordByUnitId(resourceUnitWriteSentence.getUnitId());
        if (redBookUnitContentWrite == null) {
            redBookUnitContentWrite = new RedBookUnitContentWrite();
            redBookUnitContentWrite.setResourceUnitId(resourceUnitWriteSentence.getUnitId());
            int m =  resourceUnitContentWriteDao.addUnitContent(redBookUnitContentWrite);
            if (m > 0) {
                updateUnitData(resourceUnitWriteSentence.getUnitId());
            }
        }

        resourceUnitWriteSentence.setId(null);
        resourceUnitWriteSentence.setDisplayOrder(0);
        int i = resourceUnitWriteSentenceDao.addSentence(resourceUnitWriteSentence);
        if (i > 0) {
            return HSSJsonReulst.ok(resourceUnitWriteSentence.getId());
        }
        return HSSJsonReulst.errorMsg("保存失败！");
    }

    @Override
    public HSSJsonReulst<List<ResourceUnitWriteSentence>> getSentenceTrainListByUnitId(Integer unitId) {
        return HSSJsonReulst.ok(resourceUnitWriteSentenceDao.getSentenceTrainListByUnitId(unitId));
    }

    @Override
    public HSSJsonReulst<Boolean> editSentenceTrain(ResourceUnitWriteSentence resourceUnitWriteSentence) {
        resourceUnitWriteSentence.setDisplayOrder(0);
        int i = resourceUnitWriteSentenceDao.updateSentence(resourceUnitWriteSentence);
        if (i > 0) {
            return HSSJsonReulst.ok(true);
        } else {
            return HSSJsonReulst.errorMsg("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HSSJsonReulst<Boolean> saveUnitTopic(SaveUnitTopicDto saveUnitTopicDto) {
        if (saveUnitTopicDto == null || saveUnitTopicDto.getResourceUnitId() == null) {
            return HSSJsonReulst.errorMsg("参数信息为空！");
        }
        RedBookUnitContentWrite record = resourceUnitContentWriteDao.getRecordByUnitId(saveUnitTopicDto.getResourceUnitId());
        if (record == null) {
            record = new RedBookUnitContentWrite();
            record.setResourceUnitId(saveUnitTopicDto.getResourceUnitId());
        }
        record.setTopicId(saveUnitTopicDto.getTopicId());
        if (saveUnitTopicDto.getRelated()) {
            Integer relatedWriteMethodUnitId = saveUnitTopicDto.getRelatedWriteMethodUnitId();
            if (relatedWriteMethodUnitId != null) {
                WriteUnitTitleBean writeUnitTitleBean = writeTitleDao.getWriteParientTitleByUnitId(relatedWriteMethodUnitId);
                Map<Integer, Integer> oldNewIdMap = new HashMap<>();
                if (writeUnitTitleBean != null) {
                    writeTitleDao.deleteByUnitId(saveUnitTopicDto.getResourceUnitId());
                    Integer oldId = writeUnitTitleBean.getId();
                    writeUnitTitleBean.setId(null);
                    writeUnitTitleBean.setUnitId(saveUnitTopicDto.getResourceUnitId());
                    writeTitleDao.addUnitTitle(writeUnitTitleBean);
                    Integer newId = writeUnitTitleBean.getId();
                    if (newId > 0) {
                        oldNewIdMap.put(oldId, newId);
                        List<WriteUnitTitleBean> titleListByUnitIdAndParientId = writeTitleDao.getTitleListByUnitIdAndParientId(relatedWriteMethodUnitId, oldId);
                        titleListByUnitIdAndParientId.forEach(item -> {
                            Integer itemOldId = item.getId();
                            item.setId(null);
                            item.setParentId(newId);
                            item.setUnitId(saveUnitTopicDto.getResourceUnitId());
                            writeTitleDao.addUnitTitle(item);
                            Integer itemNewId = item.getId();
                            oldNewIdMap.put(itemOldId, itemNewId);
                        });
                    }
                }
                List<WriteUnitContentBean> writeUnitContentBeans = writeContentDao.selectListByUnitId(relatedWriteMethodUnitId);
                if (CollectionUtils.isNotEmpty(writeUnitContentBeans)) {
                    writeContentDao.deleteByUnitId(saveUnitTopicDto.getResourceUnitId());
                    writeUnitContentBeans.forEach(item -> {
                        item.setId(null);
                        item.setUnitId(saveUnitTopicDto.getResourceUnitId());
                        item.setTitleId(oldNewIdMap.get(item.getTitleId()));
                        writeContentDao.addUnitContent(item);
                    });
                }
            }
            Integer relatedSentenceUnitId = saveUnitTopicDto.getRelatedSentenceUnitId();
            if (relatedSentenceUnitId != null) {
                List<ResourceUnitWriteSentence> sentenceTrainListByUnitId = resourceUnitWriteSentenceDao.getSentenceTrainListByUnitId(relatedSentenceUnitId);
                if (CollectionUtils.isNotEmpty(sentenceTrainListByUnitId)) {
                    resourceUnitWriteSentenceDao.deleteByUnitId(saveUnitTopicDto.getResourceUnitId());
                    sentenceTrainListByUnitId.forEach(item -> {
                        ResourceUnitWriteSentence resourceUnitWriteSentence=new ResourceUnitWriteSentence();
                        resourceUnitWriteSentence.setUnitId(saveUnitTopicDto.getResourceUnitId());
                        resourceUnitWriteSentence.setSentenceEnUs(item.getSentenceEnUs());
                        resourceUnitWriteSentence.setSentenceZhCn(item.getSentenceZhCn());
                        resourceUnitWriteSentence.setDisplayOrder(0);
                        resourceUnitWriteSentenceDao.addSentence(resourceUnitWriteSentence);
                    });
                }
            }
            Integer relatedSampleUnitId = saveUnitTopicDto.getRelatedSampleUnitId();
            if (relatedSampleUnitId != null) {
                RedBookUnitContentWrite sampleUnitContentWrite = resourceUnitContentWriteDao.getRecordByUnitId(relatedSampleUnitId);
                if (sampleUnitContentWrite != null && StringUtils.isNotEmpty(sampleUnitContentWrite.getSampleTitle())) {
                    record.setSampleTitle(sampleUnitContentWrite.getSampleTitle());
                    record.setSampleContent(sampleUnitContentWrite.getSampleContent());
                }
            }
        }
        int i = 0;
        if (record.getId() == null) {
            i = resourceUnitContentWriteDao.addUnitContent(record);
            updateUnitData(record.getResourceUnitId());
        } else {
            resourceUnitContentWriteDao.updateUnitContent(record);
        }
        if (i > 0) {
            return HSSJsonReulst.ok(true);
        }
        return HSSJsonReulst.errorMsg("保存失败");
    }

    @Override
    public HSSJsonReulst<Boolean> importSentence(MultipartFile file, Integer courseId) throws IOException {
        Map<String, Integer> unitNameIdMap = new HashMap<>();
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, null, null);
        if (CollectionUtils.isNotEmpty(unitList)) {
            unitNameIdMap = unitList.stream().collect(Collectors.toMap(item -> item.get("nameEn").toString(), item -> Integer.parseInt(String.valueOf(item.get("id")))));
        }
        List<ResourceUnitWriteSentence> insertList = new ArrayList<>();
        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);
            int rowStart = 1; // 跳过标题行，从第二行开始
            for (Row row : sheet) {
                if (row.getRowNum() < rowStart) continue;
                Cell unitNameCell = row.getCell(0);
                if (unitNameCell != null) {
                    if (unitNameIdMap.containsKey(unitNameCell.getStringCellValue())) {
                        ResourceUnitWriteSentence resourceUnitWriteSentence = new ResourceUnitWriteSentence();
                        resourceUnitWriteSentence.setSentenceZhCn(row.getCell(1).getStringCellValue());
                        resourceUnitWriteSentence.setSentenceEnUs(row.getCell(2).getStringCellValue());
                        resourceUnitWriteSentence.setUnitId(unitNameIdMap.get(unitNameCell.getStringCellValue()));
                        insertList.add(resourceUnitWriteSentence);
                    }
                }

            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            resourceUnitWriteSentenceDao.batchInsert(insertList);
        }
        return HSSJsonReulst.ok(true);
    }

    @Override
    public HSSJsonReulst<Boolean> importSample(MultipartFile file, Integer courseId) throws IOException {
        Map<String, Integer> unitNameIdMap = new HashMap<>();
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, null, null);
        if (CollectionUtils.isNotEmpty(unitList)) {
            unitNameIdMap = unitList.stream().collect(Collectors.toMap(item -> item.get("nameEn").toString(), item -> Integer.parseInt(String.valueOf(item.get("id")))));
        }
        int limitTime=600;
        int maxTime=600;
        int sampleParagraphNum=3;
        int limitWord=200;
        int maxWord=200;
        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);
            int rowStart = 1; // 跳过标题行，从第二行开始
            for (Row row : sheet) {
                if (row.getRowNum() < rowStart) continue;
                Cell unitNameCell = row.getCell(0);
                if (unitNameCell != null) {
                    unitNameCell.setCellType(CellType.STRING);
                    if (unitNameIdMap.containsKey(unitNameCell.getStringCellValue())) {
                        RedBookUnitContentWrite recordByUnitId = resourceUnitContentWriteDao.getRecordByUnitId(unitNameIdMap.get(unitNameCell.getStringCellValue()));
                        if (recordByUnitId != null) {
                            recordByUnitId.setSampleTitle(row.getCell(1).getStringCellValue());
                            recordByUnitId.setSampleContent(row.getCell(2).getStringCellValue());
                            Cell cell3 = row.getCell(3);
                            if (cell3 != null && cell3.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                sampleParagraphNum= (int) cell3.getNumericCellValue();
                            }
                            recordByUnitId.setSampleParagraphNum(sampleParagraphNum);

                            Cell cell4 = row.getCell(4);
                            if (cell4 != null && cell4.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                limitTime= (int) cell4.getNumericCellValue();
                            }
                            recordByUnitId.setLimitTime(limitTime);

                            Cell cell5 = row.getCell(5);
                            if (cell5 != null && cell5.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                maxTime= (int) cell5.getNumericCellValue();
                            }
                            recordByUnitId.setMaxTime(maxTime);


                            Cell cell6 = row.getCell(6);
                            if (cell6 != null && cell6.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                limitWord= (int) cell6.getNumericCellValue();
                            }
                            recordByUnitId.setLimitWord(limitWord);

                            Cell cell7 = row.getCell(7);
                            if (cell7 != null && cell7.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                maxWord= (int) cell7.getNumericCellValue();
                            }
                            recordByUnitId.setMaxWord(maxWord);

                            resourceUnitContentWriteDao.updateUnitContent(recordByUnitId);
                        } else {
                            recordByUnitId=new RedBookUnitContentWrite();
                            recordByUnitId.setResourceUnitId(unitNameIdMap.get(unitNameCell.getStringCellValue()));
                            recordByUnitId.setSampleTitle(row.getCell(1).getStringCellValue());
                            recordByUnitId.setSampleContent(row.getCell(2).getStringCellValue());
                            Cell cell3 = row.getCell(3);
                            if (cell3 != null && cell3.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                sampleParagraphNum= (int) cell3.getNumericCellValue();
                            }
                            recordByUnitId.setSampleParagraphNum(sampleParagraphNum);
                            Cell cell4 = row.getCell(4);
                            if (cell4 != null && cell4.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                limitTime= (int) cell4.getNumericCellValue();
                            }
                            recordByUnitId.setLimitTime(limitTime);

                            Cell cell5 = row.getCell(5);
                            if (cell5 != null && cell5.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                maxTime= (int) cell5.getNumericCellValue();
                            }
                            recordByUnitId.setMaxTime(maxTime);


                            Cell cell6 = row.getCell(6);
                            if (cell6 != null && cell6.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                limitWord= (int) cell6.getNumericCellValue();
                            }
                            recordByUnitId.setLimitWord(limitWord);

                            Cell cell7 = row.getCell(7);
                            if (cell7 != null && cell7.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                maxWord= (int) cell7.getNumericCellValue();
                            }
                            recordByUnitId.setMaxWord(maxWord);
                            resourceUnitContentWriteDao.addUnitContent(recordByUnitId);
                        }
                    }
                }
            }
        }
        return HSSJsonReulst.ok(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HSSJsonReulst<Boolean> editUnitWords(EditWordDto editWordDto) {
        Integer unitId = editWordDto.getUnitId();
        String words = editWordDto.getWords();
        RedBookUnitContentWrite record = resourceUnitContentWriteDao.getRecordByUnitId(unitId);
        int i = 0;
        if (record != null) {
            record.setWords(words);
            i = resourceUnitContentWriteDao.updateUnitContent(record);
        } else {
            record = new RedBookUnitContentWrite();
            record.setWords(words);
            record.setResourceUnitId(unitId);
            i = resourceUnitContentWriteDao.addUnitContent(record);
            if (i > 0) {
                updateUnitData(unitId);
            }
        }
        if (i > 0) {
            return HSSJsonReulst.ok(true);
        } else {
            return HSSJsonReulst.errorMsg("保存失败");
        }
    }

    private void updateUnitData(Integer unitId) {
        Map<String, Object> unitMap = resourceUnitDao.getUnitById(unitId);
        if (unitMap != null) {
            resourceUnitDao.updateUnitContentNum(1, unitId);
            Integer courseId = (Integer) unitMap.get("course_id");
            Map<String, Object> courseMap = resourceCourseDao.getCourseById(courseId);
            if (courseMap != null) {
                Object containWrite = courseMap.get("contain_write");
                if (containWrite != null && containWrite.toString().equalsIgnoreCase("false")) {
                    resourceCourseDao.updateCourseContentContainStatus(courseId, RedBookContentTypeEnum.WRITE, true);
                }
            }
        }
    }

    @Override
    public HSSJsonReulst<RedBookUnitContentWrite> getSampleByUnitId(Integer unitId) {
        return HSSJsonReulst.ok(resourceUnitContentWriteDao.getRecordByUnitId(unitId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HSSJsonReulst<Boolean> editSample(EditSampleDto editSampleDto) {
        Integer unitId = editSampleDto.getUnitId();
        String sampleTitle = editSampleDto.getSampleTitle();
        String sampleContent = editSampleDto.getSampleContent();
        if (unitId == null) {
            return HSSJsonReulst.errorMsg("参数为空");
        }
        RedBookUnitContentWrite record = resourceUnitContentWriteDao.getRecordByUnitId(unitId);
        int i = 0;
        if (record != null) {
            record.setSampleTitle(sampleTitle);
            record.setSampleContent(sampleContent);
            record.setLimitTime(editSampleDto.getLimitTime());
            record.setMaxTime(editSampleDto.getMaxTime());
            record.setSampleParagraphNum(editSampleDto.getSampleParagraphNum());
            record.setLimitWord(editSampleDto.getLimitWord());
            record.setMaxWord(editSampleDto.getMaxWord());
            i =resourceUnitContentWriteDao.updateUnitContent(record);
        } else {
            record=new RedBookUnitContentWrite();
            record.setResourceUnitId(unitId);
            record.setSampleTitle(sampleTitle);
            record.setSampleContent(sampleContent);
            record.setLimitTime(editSampleDto.getLimitTime());
            record.setMaxTime(editSampleDto.getMaxTime());
            record.setSampleParagraphNum(editSampleDto.getSampleParagraphNum());
            record.setLimitWord(editSampleDto.getLimitWord());
            record.setMaxWord(editSampleDto.getMaxWord());
            i = resourceUnitContentWriteDao.addUnitContent(record);
            if (i > 0) {
                updateUnitData(unitId);
            }
        }
        if (i > 0) {
            return HSSJsonReulst.ok(true);
        } else {
            return HSSJsonReulst.errorMsg("保存失败");
        }
    }


    @Override
    public HSSJsonReulst<List<RedBookUnit>> getUnitListByCourseId(Integer courseId) {
        Map<String, Object> courseMap = resourceCourseDao.getCourseById(courseId);
        if (courseMap == null) {
            return HSSJsonReulst.errorMsg("未查到课程信息");
        }
        List<RedBookUnit> resultList = new ArrayList<>();
        List<Map<String, Object>> unitList = resourceUnitDao.getUnitList(courseId, null, null);
        Optional.ofNullable(unitList).orElse(new ArrayList<>()).forEach(item -> {
            Integer resourceUnitId = (Integer) item.get("id");
            RedBookUnit redBookUnit = new RedBookUnit();
            redBookUnit.setId(resourceUnitId);
            redBookUnit.setNameCn((String) item.get("nameCn"));
            redBookUnit.setNameEn((String) item.get("nameEn"));
            RedBookUnitContentWrite resourceUnitContentWrite = resourceUnitContentWriteDao.getRecordByUnitId(resourceUnitId);
            if (resourceUnitContentWrite == null) {
                resourceUnitContentWrite = new RedBookUnitContentWrite();
                resourceUnitContentWrite.setResourceUnitId(resourceUnitId);
            } else {
                Integer topicId = resourceUnitContentWrite.getTopicId();
                if (topicId != null) {
                    resourceUnitContentWrite.setTopicName(resourceTopicDao.getTopicNameById(topicId));
                }
            }
            redBookUnit.setContentWrite(resourceUnitContentWrite);
            resultList.add(redBookUnit);
        });
        return HSSJsonReulst.ok(resultList);
    }

    @Override
    public HSSJsonReulst<List<TopicRelatedUnitBean>> getTopicRelatedUnitList(Integer topicId) {
        List<TopicRelatedUnitBean> resultList = new ArrayList<>();
        List<Integer> unitIdList = resourceUnitContentWriteDao.getTopicRelatedUnitIdList(topicId);
        Optional.ofNullable(unitIdList).orElse(new ArrayList<>()).forEach(unitId -> {
            Map<String, Object> unitMap = resourceUnitDao.getUnitById(unitId);
            if (unitMap != null) {
                TopicRelatedUnitBean topicRelatedUnitBean = new TopicRelatedUnitBean();
                topicRelatedUnitBean.setResourceUnitId(unitId);
                topicRelatedUnitBean.setResourceUnitName((String) unitMap.get("name_cn"));
                Integer courseId = (Integer) unitMap.get("course_id");
                topicRelatedUnitBean.setResourceCourseId(courseId);
                Map<String, Object> courseMap = resourceCourseDao.getCourseById(courseId);
                topicRelatedUnitBean.setResourceCourseNameCn((String) courseMap.get("name_cn"));
                Integer versionId = (Integer) courseMap.get("version_id");
                topicRelatedUnitBean.setResourceVersionId(versionId);
                topicRelatedUnitBean.setResourceVersionNameCn((String) resourceVersionDao.getVersionById(versionId).get("name_cn"));
                resultList.add(topicRelatedUnitBean);
            }

        });
        return HSSJsonReulst.ok(resultList);
    }

    private void addImagePathToRedis(Integer unitId, String imageName) {
        String key = TEMP_WRITE_IMAGE_PREFIX + unitId;
        List<String> imageNameList = (List<String>) RedBookRedisManager.getResourceBean(key);
        if (imageNameList == null) {
            imageNameList = new ArrayList<String>();
        }
        imageNameList.add(imageName);
        RedBookRedisManager.setResourceBean(key, imageNameList);
    }

    private List<Integer> unitTitleIdList;
    private List<Integer> unitContentIdList;
    private int[] disporderArr;

    private List<WriteUnitContentBean> addUnitContentList(List<WriteUnitContentBean> contentList) {
        disporderArr = new int[]{1, 1, 1, 1, 1};
        ArrayList<WriteUnitContentBean> errList = new ArrayList<>();
        unitTitleIdList = new ArrayList<Integer>();
        unitContentIdList = new ArrayList<Integer>();
        WriteContentStack contentStack = new WriteContentStack();
        for (WriteUnitContentBean content : contentList) {
            if (content.getQuestionType() == null) {
                continue;
            }
            switch (content.getQuestionType()) {
                case 101://一级标题
                case 102://二级标题
                case 103://三级标题
                case 104://四级标题（子标题）
                    checkAndPersistTitle(content, contentStack);
                    break;
                default://非标题内容
                    //添加单元卡片内容
                    if (contentStack.getTop() == null) {
                        break;
                    }
                    WriteUnitContentBean unitContent = new WriteUnitContentBean();
                    unitContent.setId(content.getId());
                    unitContent.setUnitId(content.getUnitId());
                    unitContent.setTitleId(contentStack.getTop().getId());
                    Integer questionType = content.getQuestionType();
                    if (questionType == 5) {
                        if (content.getCorrectOption().contains("-1")) {
                            errList.add(content);
                        }
                    }
                    unitContent.setQuestionType(questionType);
                    unitContent.setQuestion(content.getQuestion());
                    unitContent.setOptionA(content.getOptionA());
                    unitContent.setOptionB(content.getOptionB());
                    unitContent.setOptionC(content.getOptionC());
                    unitContent.setOptionD(content.getOptionD());
                    unitContent.setOptionE(content.getOptionE());
                    unitContent.setCorrectOption(content.getCorrectOption());
                    unitContent.setWrongTips1(content.getWrongTips1());
                    unitContent.setWrongTips2(content.getWrongTips2());
                    unitContent.setWrongTips3(content.getWrongTips3());
                    unitContent.setParse(content.getParse());
                    unitContent.setDisplayOrder(disporderArr[4]++);
                    if (unitContent.getId() != null && unitContent.getId() == -1) {
                        unitContent.setId(null);
                    }
                    if (unitContent.getId() == null) {//新增的内容
                        writeContentDao.addUnitContent(unitContent);
                    } else {//修改原先的内容
                        writeContentDao.updateUnitContent(unitContent);
                    }
                    unitContentIdList.add(unitContent.getId());
                    break;
            }
        }
        while (contentStack.size() > 0) {
            if ("isBasic".equals(contentStack.getTop().getParse())) {
                writeTitleDao.updateTitleIsBasic(contentStack.getTop().getId());
            }
            contentStack.pop(); //栈顶是同一级或低级的标题，则清掉，保证栈顶元素一定是要插入元素的父级
        }
        return errList;
    }

    public void checkAndPersistTitle(WriteUnitContentBean content, WriteContentStack contentStack) {
        Integer questionType = content.getQuestionType();
        List<Integer> persistTypeList = null;
        switch (questionType) {
            case 101:
                persistTypeList = Arrays.asList(101, 102, 103, 104);
                break;
            case 102:
                persistTypeList = Arrays.asList(102, 103, 104);
                break;
            case 103:
                persistTypeList = Arrays.asList(103, 104);
                break;
            case 104:
                persistTypeList = Collections.singletonList(104);
                break;
        }
        while (contentStack.size() > 0 && persistTypeList.contains(contentStack.getTop().getQuestionType())) {
            if ("isBasic".equals(contentStack.getTop().getParse())) {
                writeTitleDao.updateTitleIsBasic(contentStack.getTop().getId());
            }
            contentStack.pop(); //栈顶是同一级或低级的标题，则清掉，保证栈顶元素一定是要插入元素的父级
        }

        int level = 0;
        switch (questionType) {
            case 101:
                level = 1;
                break;
            case 102:
                level = 2;
                break;
            case 103:
                level = 3;
                break;
            case 104:
                level = 4;
                break;
        }

        WriteUnitTitleBean unitTitle = new WriteUnitTitleBean();
        unitTitle.setId(content.getId());
        unitTitle.setName(content.getQuestion());
        unitTitle.setUnitId(content.getUnitId());
        unitTitle.setParentId(level == 1 ? 0 : contentStack.getTop().getId());
        unitTitle.setLevel(level);
        unitTitle.setBasic(false);
        unitTitle.setDisplayOrder(disporderArr[level - 1]++);
        if (unitTitle.getId() != null && unitTitle.getId() == -1) {
            unitTitle.setId(null);
        }
        if (unitTitle.getId() == null) {//新增的内容
            writeTitleDao.addUnitTitle(unitTitle);
            content.setId(unitTitle.getId());
        } else {//修改原先的内容
            writeTitleDao.updateUnitTitle(unitTitle);
        }
        unitTitleIdList.add(unitTitle.getId());
        contentStack.push(content);
        switch (level) {
            case 1:
                disporderArr[1] = 1;
                disporderArr[2] = 1;
                disporderArr[3] = 1;
                break;
            case 2:
                disporderArr[2] = 1;
                disporderArr[3] = 1;
                break;
            case 3:
                disporderArr[3] = 1;
                break;
        }
    }

    /**
     * 删除未使用的图片
     *
     * @param contextPath
     * @return
     * @throws Exception
     */
    private void deleteNotUseImage(String contextPath, WriteNewUnitBean writeNewUnitBean) {
        //获取使用的图片的路径
        List<String> useImageNameList = new ArrayList<String>();
        List<WriteUnitContentBean> writeUnitContentBeanList = writeNewUnitBean.getWriteContentList();
        List<Integer> updateContentId = new ArrayList<Integer>();//存放需要更新的内容的id
        for (WriteUnitContentBean content : writeUnitContentBeanList) {
            if (content.getId() != null) {
                updateContentId.add(content.getId());
            }
            catchImagePath(useImageNameList, content);
        }
        //删除未使用的图片和修改后删除掉的那些图片
        String key = TEMP_WRITE_IMAGE_PREFIX + writeNewUnitBean.getTeachId() + ":" + writeNewUnitBean.getId();
        List<String> allImageNameList = (List<String>) RedBookRedisManager.getResourceBean(key);
        if (allImageNameList == null) {
            allImageNameList = new ArrayList<String>();
        }
        if (updateContentId.size() > 0) {
            List<WriteUnitContentBean> oldWriteContentList = writeContentDao.getWriteContentList(updateContentId.toArray(new Integer[1]));
            for (WriteUnitContentBean oldContent : oldWriteContentList) {
                catchImagePath(allImageNameList, oldContent);
            }
        }

        File imageFile;
        for (String imagePathStr : allImageNameList) {
            if (!useImageNameList.contains(imagePathStr)) {
                OSSManager.delete(OSSManager.UPLOAD_BUCKETNAME, imagePathStr);
            }
        }
        //删除Redis中存放的临时图片路径
        RedBookRedisManager.delResourceBean(key);
    }

    /**
     * 捕获语法内容中的图片地址
     *
     * @param list
     * @param content
     */
    private void catchImagePath(List<String> list, WriteUnitContentBean content) {
        String imageUrl, imageName;
        String reg = "[a-zA-z]+://[^\"]*";
        Pattern pattern;
        Matcher matcher;
        String text = content.getQuestion() + content.getOptionA() + content.getOptionB() + content.getOptionC() + content.getOptionD();
        pattern = Pattern.compile(reg);
        matcher = pattern.matcher(text);
        while (matcher.find()) {
            imageUrl = matcher.group();
            if (!imageUrl.contains(IMAGE_DIR_NAME)) {
                continue;
            }
            imageName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
            list.add(imageName);
        }
    }
}
