package com.woxue.resourcemanage.service.impl;

import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.resourcemanage.dao.IArticleDao;
import com.woxue.resourcemanage.dao.IResourceContentDao;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceUnitDao;
import com.woxue.resourcemanage.entity.SentenceBean;
import com.woxue.resourcemanage.service.IResourceContentService;
import com.woxue.resourceservice.dao.IQuestionSuitDao;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-31 09:52
 */
@Service
public class ResourceContentServiceImpl implements IResourceContentService {

    @Autowired
    IResourceContentDao resourceContentDao;
    @Autowired
    IArticleDao articleDao;
    @Autowired
    IResourceUnitDao resourceUnitDao;
    @Autowired
    IRedBookCourseDao redBookCourseDao;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IQuestionSuitDao suitMapper;


    @Override
    public List<SentenceBean> getArticleContentList(Integer articleId) {
        return articleDao.getArticleContentList(articleId);
    }

    @Override
    public boolean updateArticleContentList(List<SentenceBean> contentList) {
        boolean flag = false;
        if (contentList != null){
            for (SentenceBean sentence : contentList) {
                if (sentence.getId().equals(-1)){
                    /*Integer disorder = articleDao.getMaxSentenceOrder(sentence.getArticleId());
                    sentence.setDisplayOrder(disorder+1);
                    Integer id = articleDao.insertArticleSentence(sentence);
                    Integer sentenceCount =  articleDao.getArticleSentenceCount(sentence.getArticleId());
                    articleDao.updateArticleSentenceCount(sentence.getArticleId());*/
                    sentence.setId(null);
                    flag=articleDao.replaceIntoArticleSentence(sentence);
                    /*try {
                        redBookPublishService.reloadArticleById(sentence.getArticleId());
                    }catch (Exception e){

                    }*/
//                    flag = true;
                }else {
                    flag = articleDao.replaceIntoArticleSentence(sentence);
                    /*try {
                        redBookPublishService.reloadArticleById(sentence.getArticleId());
                    }catch (Exception e){

                    }*/
                }
            }
            //更新文章句子数量。
            articleDao.updateArticleSentenceCount(contentList.get(0).getArticleId());
        }
        return flag;
    }

    @Override
    public boolean insertContentWord(Integer resourceUnitId, Integer level,String programName, String unitName, String showName, Integer wordCount, Integer hasExampleSentence) {
        List<Map<String, Object>> wordShowName = resourceContentDao.getWordShowName(resourceUnitId);
        if(wordShowName.isEmpty()){
            resourceUnitDao.updateUnitContentNum(1,resourceUnitId);
        }
        return  resourceContentDao.insertContentWord(resourceUnitId,level,programName,unitName,showName,wordCount,hasExampleSentence);
    }

    @Override
    public boolean insertContentSentence(Integer resourceUnitId, String programName, String unitName, String showName,Integer sentenceCount) {
        Map<String, Object> sentenceShowName = resourceContentDao.getSentenceShowName(resourceUnitId);
        if(sentenceShowName==null){
            resourceUnitDao.updateUnitContentNum(1,resourceUnitId);
        }
        return resourceContentDao.insertContentSentence(resourceUnitId,programName,unitName,showName,sentenceCount);
    }

    @Override
    public boolean insertContentGrammar(Integer resourceUnitId, Integer grammarCourseId, Integer grammarUnitId, String showName, Integer knowledgeCount) {
        Map<String, Object> grammarShowName = resourceContentDao.getGrammarShowName(resourceUnitId);
        if (grammarShowName==null){
            resourceUnitDao.updateUnitContentNum(1,resourceUnitId);
        }
        return resourceContentDao.insertContentGrammar(resourceUnitId,grammarCourseId,grammarUnitId,showName,knowledgeCount);
    }

    @Override
    public boolean insertContentQuestion(Integer resourceUnitId, Integer level, Integer syncQuestionCourseId, Integer syncQuestionUnitId, String showName, Integer paperId) {
        List<Map<String, Object>> questionShowName = resourceContentDao.getQuestionShowName(resourceUnitId);
        if(questionShowName.isEmpty()){
            resourceUnitDao.updateUnitContentNum(1,resourceUnitId);
        }
        RedBookCourse course = redBookCourseDao.getCourseByUnitId(resourceUnitId);
        if (course!=null&&!course.isContainQuestion()){
            resourceCourseDao.updateCourseContentContainStatus(course.getId(), RedBookContentTypeEnum.QUERSTION, true);
        }
        List<Map<String, Object>> unitQuestion = resourceUnitDao.getUnitQuestion(paperId);
        //查询之前是否已经有过关联
        if (unitQuestion!=null){

        }


        return resourceContentDao.insertContentQuestion(resourceUnitId,level,syncQuestionCourseId,syncQuestionUnitId,showName,paperId);
    }


    @Override
    public boolean updateContentWord(String programName, String unitName, String showName, Integer wordCount, Integer hasExampleSentence, Integer id) {
        return resourceContentDao.updateContentWord(programName,unitName,showName,wordCount,hasExampleSentence,id);
    }

    @Override
    public boolean updateContentSentence(String programName, String unitName, String showName, Integer sentenceCount, Integer id) {
        return resourceContentDao.updateContentSentence(programName,unitName,showName,sentenceCount,id);
    }

    @Override
    public boolean updateContentGrammar(Integer grammarCourseId, Integer grammarUnitId, String showName, Integer knowledgeCount, Integer id) {
        return resourceContentDao.updateContentGrammar(grammarCourseId,grammarUnitId,showName,knowledgeCount,id);
    }

    @Override
    public boolean updateContentQuestion(Integer syncQuestionCourseId, Integer syncQuestionUnitId, String showName, Integer paperId, Integer id) {
        return resourceContentDao.updateContentQuestion(syncQuestionCourseId,syncQuestionUnitId,showName,paperId,id);
    }

    @Override
    public boolean delContentWord(Integer id) {
        return resourceContentDao.delContentWord(id);
    }

    @Override
    public boolean delContentSentence(Integer id) {
        return resourceContentDao.delContentSentence(id);
    }

    @Override
    public boolean delContentQuestion(Integer id) {
        return resourceContentDao.delContentQuestion(id);
    }

    @Override
    public boolean delContentGrammar(Integer id) {
        return resourceContentDao.delContentGrammar(id);
    }


    /*@Override
    public List<Map<String, Object>> getWordShowName(Integer resourceUnitId) {
        return resourceContentDao.getWordShowName(resourceUnitId);
    }

    @Override
    public List<Map<String, Object>> getQuestionShowName(Integer resourceUnitId) {
        return resourceContentDao.getQuestionShowName(resourceUnitId);
    }

    @Override
    public Map<String, Object> getGrammarShowName(Integer resourceUnitId) {
        return resourceContentDao.getGrammarShowName(resourceUnitId);
    }

    @Override
    public Map<String, Object> getSentenceShowName(Integer resourceUnitId) {
        return resourceContentDao.getSentenceShowName(resourceUnitId);
    }*/


    public int copySuit(int originalSuitId) {
        // 1. 插入 suit 数据并获取新主键 ID
        Map<String, Object> newSuit = new HashMap<>();
        suitMapper.insertSuit(originalSuitId, newSuit);
        int newSuitId = (int) newSuit.get("id");

        // 2. 插入 suit_section 数据
        suitMapper.insertSuitSections(originalSuitId, newSuitId);

        // 3. 获取新旧 section_id 映射关系
        List<Map<String, Integer>> sectionMappings = suitMapper.getSectionIdMapping(newSuitId);
        Map<Integer, Integer> sectionIdMapping = new HashMap<>();
        for (Map<String, Integer> mapping : sectionMappings) {
            sectionIdMapping.put(mapping.get("oldSectionId"), mapping.get("newSectionId"));
        }

        // 4. 查询原始 suit_section_question 数据
        List<Map<String, Integer>> originalQuestions = suitMapper.getOriginalSectionQuestions(originalSuitId);

        // 5. 构造新的数据
        List<Map<String, Integer>> newQuestions = new ArrayList<>();
        for (Map<String, Integer> question : originalQuestions) {
            Integer oldSectionId = question.get("sectionId");
            Integer newSectionId = sectionIdMapping.get(oldSectionId);
            if (newSectionId != null) {
                Map<String, Integer> newQuestion = new HashMap<>();
                newQuestion.put("newSectionId", newSectionId);
                newQuestion.put("questionId", question.get("questionId"));
                newQuestions.add(newQuestion);
            }
        }

        // 6. 批量插入新数据
        suitMapper.batchInsertSectionQuestions(newQuestions);

        return newSuitId;
    }

}
