package com.woxue.resourcemanage.service.impl;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.redBook.RedBookVersionStage;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.dao.IResourceVersionDao;
import com.woxue.resourcemanage.service.IResourceVersionService;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-26 14:28
 */
@Service("resourceVersionService")
public class ResourceVersionServiceImpl implements IResourceVersionService {
    
    @Autowired
    IResourceVersionDao resourceVersionDao;
    @Autowired
    IResourceCourseDao resourceCourseDao;
    @Autowired
    IRedBookCourseDao redBookCourseDao;
    @Override
    public Map<String, Object> getVersionList(Integer versionType,Integer stage,String search, Integer pageStart, Integer pageSize) {
        List<Map<String, Object>> versionList = resourceVersionDao.getVersionList(versionType,stage,search, pageStart, pageSize);
        Integer versionListCount = resourceVersionDao.getVersionListCount(versionType,stage);
        Map<String,Object> map=new HashMap<>();
        String phase="";
        for (Map<String, Object> stringObjectMap : versionList) {
            String otherPhaseVersion="";
            Integer id = (Integer) stringObjectMap.get("id");
            Integer type = (Integer) stringObjectMap.get("type");
            Integer courseCount = resourceCourseDao.getCourseCount(id,null);
            stringObjectMap.put("courseCount",courseCount);
            Integer stageInteger = (Integer) stringObjectMap.get("stage");
            if(stageInteger==1){
                phase="小学";
            }else if(stageInteger==2){
                phase="初中";
            }else{
                phase="高中";
            }
            stringObjectMap.put("phaseName",phase);
            if(type!=-1){
                List<Map<String, Object>> versionByType = resourceVersionDao.getVersionByType(type, id);
                if(versionByType.size()>0){
                    String  otherPhase="";
                    for (Map<String, Object> objectMap : versionByType) {
                        Integer otherStageInteger = (Integer) objectMap.get("stage");
                        if(otherStageInteger==1){
                            otherPhase="小学";
                        }else if(otherStageInteger==2){
                            otherPhase="初中";
                        }else{
                            otherPhase="高中";
                        }
                        otherPhaseVersion+=otherPhase+"-"+objectMap.get("name_cn").toString()+"、";
                        stringObjectMap.put("otherPhaseVersion",otherPhaseVersion);
                    }
                }else{
                    stringObjectMap.put("otherPhaseVersion","无");
                }
            }else{
                stringObjectMap.put("otherPhaseVersion","无");
            }
            if(RedBookCourseManager.getRedBookVersion(id)!=null){
                stringObjectMap.put("publishStatus",1);//已发布
            }else{
                stringObjectMap.put("publishStatus",0);//未发布
            }
            List<RedBookVersionStage> stageList = redBookCourseDao.getVersionStageList(id);
            if(!stageList.isEmpty()){
                stringObjectMap.put("stageList",stageList);
                stringObjectMap.put("courseCount",resourceVersionDao.selectVersionCount(id,stage));
            }
        }

        map.put("versionList",versionList);
        map.put("allCount",versionListCount);
        return map;
    }

    @Override
    public boolean updateVersionDisplayOrder(Integer pageStart, Integer pageSize, Integer newIndex,Integer newId, Integer oldIndex,Integer oldId) {
        if (pageStart>1){
            newIndex = pageSize*pageStart + newIndex;
            oldIndex = pageSize*pageStart+oldIndex;
        }
        resourceVersionDao.updateVersionDisplayOrder(newIndex,newId);
        resourceVersionDao.updateVersionDisplayOrder(oldIndex,oldId);
        return true;
    }

    /**
     * 获取添加版本时 下拉框内 关联其他学段对应版本的内容
     * @return
     */
    @Override
    public List<Map<String,Object>> getVersionTypeList() {
        List<Map<String,Object>> versionTypeList = resourceVersionDao.getVersionTypeList();
        String phase="";
        List<Map<String,Object>> otherPhaseVersionList=new ArrayList<>();
        List<Map<String, Object>> versionByType=new ArrayList<>();
        List<Integer> relationTypeList=new ArrayList<>();
        for (Map<String,Object> versionMap : versionTypeList) {
            String otherPhaseVersion="";
            Map<String,Object> map = new HashMap<>();
            if(versionMap.get("relation_type").equals(-1)){
              Map<String ,Object>  versionByType2 = resourceVersionDao.getVersionById((Integer) versionMap.get("id"));
                Integer stageInteger = (Integer) versionByType2.get("stage");
                if(stageInteger==1){
                    phase="小学";
                }else if(stageInteger==2){
                    phase="初中";
                }else{
                    phase="高中";
                }
                otherPhaseVersion+=phase+"-"+versionByType2.get("name_cn").toString()+"、";
            }else{
                 versionByType = resourceVersionDao.getVersionByType((Integer) versionMap.get("relation_type"), null);
                for (Map<String, Object> objectMap : versionByType) {
                    Integer stageInteger = (Integer) objectMap.get("stage");
                    if(stageInteger==1){
                        phase="小学";
                    }else if(stageInteger==2){
                        phase="初中";
                    }else{
                        phase="高中";
                    }
                    otherPhaseVersion+=phase+"-"+objectMap.get("name_cn").toString()+"、";
                }
            }
            map.put("otherPhaseVersion",otherPhaseVersion);
            map.put("type",versionMap.get("relation_type"));
            if(!versionMap.get("relation_type").equals(-1)){
                map.put("id",versionMap.get("relation_type"));
            }else{
                map.put("id",versionMap.get("id"));
            }

            boolean relation_type = relationTypeList.contains(versionMap.get("relation_type"));
            if(!relation_type||versionMap.get("relation_type").equals(-1)){
                otherPhaseVersionList.add(map);
                relationTypeList.add((Integer) versionMap.get("relation_type"));
            }
        }
        return otherPhaseVersionList;
    }

    /**
     * 添加版本信息
     * @param nameEn
     * @param nameCn
     * @param type
     * @param stage
     * @return
     */
    @Override
    public boolean insertVersion(String nameEn, String nameCn, Integer versionType,Integer type, Integer stage,Integer price,String briefIntroduction) {
        Integer maxDisplayOrder = resourceVersionDao.getMaxDisplayOrder();
        if(type==-1){
            return  resourceVersionDao.insertVersion(nameEn,nameCn,versionType,type,stage,price,briefIntroduction,maxDisplayOrder + 1);
        }else{
            Map<String, Object> versionMap = resourceVersionDao.getVersionById(type);
            if(versionMap.get("relation_type").equals(-1)){
                resourceVersionDao.updateRelationType((Integer) versionMap.get("id"),(Integer) versionMap.get("id"));
                return  resourceVersionDao.insertVersion(nameEn,nameCn,versionType,(Integer) versionMap.get("id"),stage,price,briefIntroduction,maxDisplayOrder + 1);
            }else{
                return  resourceVersionDao.insertVersion(nameEn,nameCn,versionType,(Integer) versionMap.get("relation_type"),stage,price,briefIntroduction,maxDisplayOrder + 1);
            }
        }
    }
    @Override
    public Map<String, Object> getVersionById(Integer id) {
        return resourceVersionDao.getVersionById(id);
    }

    @Override
    public boolean updateVersionById(String nameEn, String nameCn,Integer versionType, Integer type,Integer price,String briefIntroduction ,Integer displayOrder,Integer id) {
        if(type==-1){
            return  resourceVersionDao.updateVersionById(nameEn,nameCn,versionType,type,price,briefIntroduction,displayOrder,id);
        }else{
            Map<String, Object> versionMap = resourceVersionDao.getVersionById(type);
            if(versionMap==null){
                return false;
            }else{
                if(versionMap.get("relation_type").equals(-1)){
                    resourceVersionDao.updateRelationType((Integer) versionMap.get("id"),(Integer) versionMap.get("id"));
                    return resourceVersionDao.updateVersionById(nameEn,nameCn,versionType,(Integer) versionMap.get("id"),price,briefIntroduction,displayOrder,id);
                }else{
                    return resourceVersionDao.updateVersionById(nameEn,nameCn,versionType,(Integer) versionMap.get("relation_type"),price,briefIntroduction,displayOrder,id);
                }
            }
        }
    }

    @Override
    public Boolean insertVersionStage(Integer versionId,Integer... stageList) {
        resourceVersionDao.deleteVersionStage(versionId);
        for (Integer stage : stageList) {
            resourceVersionDao.insertVersionStage(versionId,stage);
        }
        RedBookCourseManager.updateVersionInfo(versionId);
        return true;
    }

}
