package com.woxue.resourcemanage.service;


import com.redbook.kid.common.model.*;

import java.util.List;

public interface ResourceKidService {
    // resource_kid_course相关方法
    void insertCourse(ResourceKidCourse course);
    ResourceKidCourse getCourseById(int id);
    void updateCourse(ResourceKidCourse course);
    void deleteCourse(int id);
    List<ResourceKidCourse> getAllCourses();

    // resource_kid_picture_book相关方法
    void insertPictureBook(ResourceKidPictureBook pictureBook);
    ResourceKidPictureBook getPictureBookById(int id);
    void updatePictureBook(ResourceKidPictureBook pictureBook);
    void deletePictureBook(int id);
    List<ResourceKidPictureBook> getAllPictureBooks();

    // resource_kid_picture_book_content相关方法
    void insertPictureBookContent(ResourceKidPictureBookContent content);
    ResourceKidPictureBookContent getPictureBookContentById(int id);
    void updatePictureBookContent(ResourceKidPictureBookContent content);
    void deletePictureBookContent(int id);
    List<ResourceKidPictureBookContent> getAllPictureBookContents();
    List<ResourceKidPictureBookContent> getPictureBookContentByPictureBookId(int pictureBookId);

    // resource_kid_picture_book_sentence相关方法
    void insertPictureBookSentence(ResourceKidPictureBookSentence sentence);
    ResourceKidPictureBookSentence getPictureBookSentenceById(int id);
    void updatePictureBookSentence(ResourceKidPictureBookSentence sentence);
    void deletePictureBookSentence(int id);
    List<ResourceKidPictureBookSentence> getAllPictureBookSentences();
    List<ResourceKidPictureBookSentence> getPictureBookSentenceByPictureBookId(int pictureBookId);

    // resource_kid_scene相关方法
    void insertScene(ResourceKidScene scene);
    ResourceKidScene getSceneById(int id);
    void updateScene(ResourceKidScene scene);
    void deleteScene(int id);
    List<ResourceKidScene> getAllScenes();
    List<ResourceKidScene> getSceneByCourseId(int courseId);

    // resource_kid_unit相关方法
    void insertUnit(ResourceKidUnit unit);
    ResourceKidUnit getUnitById(int id);
    void updateUnit(ResourceKidUnit unit);
    void deleteUnit(int id);
    List<ResourceKidUnit> getUnitBySceneId(int sceneId);
    List<ResourceKidUnit> getUnitByCourseId(int courseId);
    List<ResourceKidUnit> getAllUnits();
    List<KidLetterUnit> getLetterUnitVOByCourseId(int courseId);

    // resource_kid_video相关方法
    void insertVideo(ResourceKidVideo video);
    ResourceKidVideo getVideoById(int id);
    void updateVideo(ResourceKidVideo video);
    void deleteVideo(int id);
    List<ResourceKidVideo> getAllVideos();
    ResourceKidVideo getVideoByUnitId(int unitId);

    // resource_kid_word相关方法
    void insertWord(ResourceKidWord word);
    ResourceKidWord getWordById(int id);
    void updateWord(ResourceKidWord word);
    void deleteWord(int id);
    List<ResourceKidWord> getAllWords();
    List<ResourceKidWord> getWordByUnitId(int unitId);

    ResourceKidPictureBook getPictureBookByUnitId(int unitId);

    // resource_kid_letter相关方法
    void addLetterUnit(String letter,Integer unitId,Integer courseId);
    void updateLetterUnit(String letter,Integer unitId);

    void addLetterExample(ResourceKidLetterExample kidLetterExample);

    void updateLetterExample(ResourceKidLetterExample kidLetterExample);

    void deleteLetterExample(Integer id);

    void setUnitOnLine(Integer unitId, Boolean onLine);

}