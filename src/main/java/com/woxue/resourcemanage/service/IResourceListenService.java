package com.woxue.resourcemanage.service;

import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.common.model.redBook.listen.ResourceUnitListenDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceUnitTopicIdPostDTO;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-01-15 16:58
 */
public interface IResourceListenService {

    Map<String, Object> listByCourseId(Integer courseId, Integer pageNum, Integer pageSize);

    ResourceUnitListenDTO edit(Integer unitId);

    int deleteSentence(Integer id);
    int deleteQuestion(Integer questionId);

    int saveOrUpdate(ResourceUnitListenDTO resourceUnitListenDTO);
    Map<String, Object> uploadPhoto(Integer unitId, MultipartFile upload, HttpServletRequest request);

    List<ResourceTopic> topicList();
    int saveTopicUnitListenList(ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO);

    int insertTopic(ResourceTopic resourceTopicBean);


    String importExcel(MultipartFile file,Integer courseId, Integer startReadLine);
    String importQuestionExcel(MultipartFile file,Integer courseId, Integer startReadLine);


}
