package com.woxue.resourcemanage.service;


import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.wordUse.WordUseBean;
import com.woxue.resourcemanage.entity.WordEntity;
import com.woxue.resourcemanage.entity.vo.MatchResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface IWordService {

    /**
     * 导入单词/句子
     * @return
     * @throws SQLException
     */
    HSSJsonReulst insertWordInfo(MultipartFile[] files, Integer courseId, RedBookContentTypeEnum contentType);



    /**
     * 查询单词
     * @return
     * @throws SQLException
     * @throws UnsupportedEncodingException
     */
    List<WordEntity> queryWords(Integer courseId, Integer wordId, String spelling, Integer pageIndex, Integer pageSize, String orderBy,RedBookContentTypeEnum type);
    WordEntity queryWord(Integer wordId);
    /**
     * 查询记录总数
     * @return
     * @throws SQLException
     */
    Integer queryWordsCount(Integer courseId, Integer wordId, String spelling,RedBookContentTypeEnum type);
    /**
     * 查询句子
     * @return
     * @throws SQLException
     * @throws UnsupportedEncodingException
     */
    List<WordEntity> querySentences(Integer courseId, Integer wordId, String spelling,Integer divide, Integer pageIndex, Integer pageSize, String orderBy);
    /**
     * 查询句子记录总数
     * @return
     * @throws SQLException
     */
    Integer querySentencesCount(Integer courseId, Integer wordId, String spelling,Integer divide);

    /**
     * 模糊查询例句的总数
     * @return
     * @throws SQLException
     */
    Integer queryExampleCount(String spelling,Integer courseId,String searchType);

    /**
     * 模糊查询例句列表
     * @param spelling
     * @param pageIndex
     * @param pageSize
     * @return
     */
    List<WordEntity> queryExamplesList(String spelling,Integer courseId,Integer pageIndex, Integer pageSize,String searcFrom);

    /**
     * 更新单词详细信息
     * @throws SQLException
     */
    int updateWordDetail(Integer wordId, String spelling, String syllable, String meaning_en_US , String meaning_zh_CN, String example_en_US, String example_zh_CN);

    int updateSentenceDetail(WordEntity wordEntity);

    /**
     * 匹配句子成分
     * @param courseId
     * @return
     */
    MatchResultVO matchSentenceElement(Integer courseId);

    /**
     * 查询无音标单词
     * @param courseId
     * @return
     * @throws SQLException
     */
    List<WordEntity> queryWordNoSyllable(Integer courseId, Integer pageIndex, Integer pageSize);
    Integer queryWordNoSyllableCount(Integer courseId);
    /**
     * 查询无例句单词
     * @param courseId
     * @return
     * @throws SQLException
     */
    List<WordEntity> queryWordNoExample(Integer courseId, Integer pageIndex, Integer pageSize);
    Integer queryWordNoExampleCount(Integer courseId);

    /**
     * 获取单词助记信息
     * @param spelling
     * @return
     * @throws SQLException
     */
    Map<String, String> getWordMnemonics(String spelling);
    List<Map<String, String>> getWordMnemonicsList(Integer pageIndex, Integer pageSize);
    Integer getWordMnemonicsCount();
    void insertWordMnemonics(Map<String, String> wordMnemonics);
    void updateWordMnemonics(Map<String, String> wordMnemonics);

    /**
     * 获取分音节单词列表
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<Map<String, Object>> getSoundMarkList(String spelling, Integer pageNum, Integer pageSize);

    /**
     * 修改单词或音节
     * @param soundMark
     * @return
     */
    boolean updateSoundMark(String oldSpelling, String newSpelling, String soundMark);

    /**
     * 添加单词和音节
     * @return
     */
    boolean insertSoundMark(String spelling, String soundMark);

    /**
     * 音节条目
     * @return
     */
    Integer getSoundMarkCount();


    List<Map<String, Object>> getWordUseList(String spelling, Integer status,Integer pageNum, Integer pageSize);


    Integer getWordUseCount(String spelling,Integer status);

    Integer updateWordUse(String spelling, String sentence, WordUseBean wordUseBean);

    boolean generateWordUse(Integer courseId);

}
