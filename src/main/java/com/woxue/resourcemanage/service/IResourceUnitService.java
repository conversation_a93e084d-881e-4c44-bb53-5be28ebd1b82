package com.woxue.resourcemanage.service;


import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 15:03
 */
public interface IResourceUnitService {

    Map<String,Object> getUnitList(Integer courseId, Integer pageStart, Integer pageSize);

    Integer getUnitByCourse(Integer courseId,String nameEn);

    boolean insertUnit(String nameEn, String nameCn, Integer courseId);
    Map<String,Object> getUnitById(Integer id);

    boolean updateUnit(String nameEn, String nameCn, Integer id);

    boolean updateUnitContentNum(Integer contentNum, Integer resourceUnitId);


//    Map<String,Object> getUnitQuestion(Integer paperId);

    Map<String,Object> getUnitQuestion(Integer courseId, Integer paperId);

    boolean insertUnits(String[] nameEns, Integer courseId);
}
