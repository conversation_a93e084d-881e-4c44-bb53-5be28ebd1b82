package com.woxue.resourcemanage.service;

import com.woxue.common.model.redBook.RedBookVersionStage;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-26 14:27
 */
public interface IResourceVersionService {

    Map<String,Object> getVersionList(Integer manageType, Integer stage, String search, Integer pageStart, Integer pageSize);
    boolean updateVersionDisplayOrder(Integer pageStart, Integer pageSize, Integer newIndex, Integer newId, Integer oldIndex, Integer oldId);


    List<Map<String,Object>> getVersionTypeList();

    boolean insertVersion(String nameEn, String nameCn, Integer versionType, Integer type, Integer stage, Integer price, String briefIntroduction);

    Map<String,Object> getVersionById(Integer id);

    boolean updateVersionById(String nameEn, String nameCn, Integer versionType, Integer type, Integer price, String briefIntroduction,Integer displayOrder, Integer id);

    Boolean insertVersionStage(Integer versionId,Integer... stageList);
}
