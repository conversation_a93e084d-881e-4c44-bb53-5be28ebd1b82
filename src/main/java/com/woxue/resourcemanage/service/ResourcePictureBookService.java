package com.woxue.resourcemanage.service;

import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.PictureBook;
import com.woxue.common.model.redBook.PictureBookContent;
import com.woxue.common.model.redBook.PictureBookSentence;
import com.woxue.common.model.redBook.PictureBookWord;

import java.util.List;

public interface ResourcePictureBookService {

    List<PictureBook>getPictureBookList();

    List<PictureBookContent>getPictureBookContentList(Integer pictureBookId);

    List<PictureBookSentence>getPictureBookSentenceList(Integer pictureBookContentId);

    Boolean insertPictureBook(PictureBook pictureBook);

    Boolean updatePictureBookById(PictureBook pictureBook);

    Boolean insertPictureBookContent(PictureBookContent pictureBookContent);

    Boolean deletePictureBookById(Integer pictureBookId);


    Boolean updatePictureBookContentById(PictureBookContent pictureBookContent);

    Boolean insertPictureBookSentence(PictureBookSentence pictureBookSentence);

    Boolean deletePictureBookSentence(Integer pictureBookSentenceId);
    Boolean updatePictureBookSentenceById(PictureBookSentence pictureBookSentence);

    Boolean publishPictureBook();


    List<PictureBookWord> getWordListByPictureBookId(Integer pictureBookId);

    Boolean insertPictureBookWord(Integer pictureBookId, String spelling, String syllable, String meaningEnUs, String meaningZhCn, String exampleEnUs, String exampleZhCn,String imgUrl);

    Boolean updatePictureBookWord(Integer wordId, Integer pictureBookId, String spelling, String syllable, String meaningEnUs, String meaningZhCn, String exampleEnUs, String exampleZhCn,String imgUrl);

    Boolean deletePictureBookWord(Integer wordId);

}
