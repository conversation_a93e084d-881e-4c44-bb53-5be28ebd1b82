package com.woxue.resourcemanage.service;

import java.util.List;

import com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean;
import com.woxue.resourcemanage.entity.dto.ReadExpandKnowledgeQuestionParamsDTO;

/**
 * 扩展阅读知识重点-试题Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public interface IReadExpandKnowledgeQuestionService 
{
    /**
     * 查询扩展阅读知识重点-试题
     * 
     * @param id 扩展阅读知识重点-试题主键
     * @return 扩展阅读知识重点-试题
     */
    ReadExpandKnowledgeQuestionBean selectReadExpandKnowledgeQuestionById(Long id);

    /**
     * 查询扩展知识重点-试题列表
     * 
     * @return 扩展知识重点-试题集合
     */
     List<ReadExpandKnowledgeQuestionBean> selectReadExpandKnowledgeQuestionList(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO);

    int count(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO);
    /**
     * 查询扩展知识重点-试题列表
     *
     * @param articleId
     * @return 扩展知识重点-试题集合
     */
    List<ReadExpandKnowledgeQuestionBean> getListByArticleId(Integer articleId);


    /**
     * 新增扩展知识重点-试题
     * 
     * @param ReadExpandKnowledgeQuestionBean 扩展知识重点-试题
     * @return 结果
     */
     int insertReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean ReadExpandKnowledgeQuestionBean);

    /**
     * 修改扩展知识重点-试题
     * 
     * @param ReadExpandKnowledgeQuestionBean 扩展知识重点-试题
     * @return 结果
     */
     int updateReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean ReadExpandKnowledgeQuestionBean);

    /**
     * 批量删除扩展知识重点-试题
     * 
     * @param ids 需要删除的扩展知识重点-试题主键集合
     * @return 结果
     */
     int deleteReadExpandKnowledgeQuestionByIds(Long[] ids);

    /**
     * 删除扩展知识重点-试题信息
     * 
     * @param id 扩展知识重点-试题主键
     * @return 结果
     */
     int deleteReadExpandKnowledgeQuestionById(Long id);
}
