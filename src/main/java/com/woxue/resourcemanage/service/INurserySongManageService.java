package com.woxue.resourcemanage.service;

import com.redbook.kid.common.model.NurserySongDO;
import com.redbook.kid.common.model.NurserySongSentenceDO;
import com.redbook.kid.common.model.NurserySongWithSentencesDTO;
import com.woxue.resourcemanage.util.NurserySongValidatorUtil;
import com.woxue.resourcemanage.util.SubtitleParserUtil;
import com.woxue.common.util.HSSJsonReulst;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 儿歌管理服务接口
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
public interface INurserySongManageService {

    /**
     * 添加儿歌
     *
     * @param song 儿歌信息
     * @return 处理结果
     */
    HSSJsonReulst<Integer> addSong(NurserySongDO song);

    /**
     * 更新儿歌信息
     *
     * @param song 儿歌信息
     * @return 处理结果
     */
    HSSJsonReulst<Boolean> updateSong(NurserySongDO song);

    /**
     * 删除儿歌
     *
     * @param id 儿歌ID
     * @return 处理结果
     */
    HSSJsonReulst<Boolean> deleteSong(Integer id);

    /**
     * 根据ID获取儿歌详情
     *
     * @param id 儿歌ID
     * @return 儿歌详情
     */
    HSSJsonReulst<NurserySongWithSentencesDTO> getSongDetail(Integer id);

    /**
     * 分页查询儿歌列表
     *
     * @param params 查询参数
     * @return 儿歌列表
     */
    HSSJsonReulst<Map<String, Object>> getSongList(Map<String, Object> params);

    /**
     * 上传并解析字幕文件
     *
     * @param songId 儿歌ID
     * @param subtitleFile 字幕文件
     * @return 解析结果
     */
    HSSJsonReulst<List<SubtitleParserUtil.SentenceTimeline>> uploadAndParseSubtitle(Integer songId, MultipartFile subtitleFile);

    /**
     * 处理儿歌资源（解析字幕并生成句子）
     *
     * @param songId 儿歌ID
     * @return 处理结果
     */
    HSSJsonReulst<ProcessingResult> processSongResources(Integer songId);

    /**
     * 更新儿歌状态
     *
     * @param id 儿歌ID
     * @param status 新状态
     * @return 处理结果
     */
    HSSJsonReulst<Boolean> updateSongStatus(Integer id, Integer status);

    /**
     * 批量更新状态
     *
     * @param ids 儿歌ID列表
     * @param status 新状态
     * @return 处理结果
     */
    HSSJsonReulst<Boolean> batchUpdateStatus(List<Integer> ids, Integer status);

    /**
     * 获取句子列表
     *
     * @param songId 儿歌ID
     * @return 句子列表
     */
    HSSJsonReulst<List<NurserySongSentenceDO>> getSentencesBySongId(Integer songId);

    /**
     * 更新句子信息
     *
     * @param sentence 句子信息
     * @return 处理结果
     */
    HSSJsonReulst<Boolean> updateSentence(NurserySongSentenceDO sentence);

    /**
     * 删除句子
     *
     * @param sentenceId 句子ID
     * @return 处理结果
     */
    HSSJsonReulst<Boolean> deleteSentence(Integer sentenceId);

    /**
     * 验证儿歌数据
     *
     * @param song 儿歌信息
     * @param sentences 句子列表
     * @return 验证结果
     */
    HSSJsonReulst<NurserySongValidatorUtil.ValidationResult> validateSongData(NurserySongDO song, List<NurserySongSentenceDO> sentences);

    /**
     * 重新处理儿歌资源
     *
     * @param songId 儿歌ID
     * @return 处理结果
     */
    HSSJsonReulst<ProcessingResult> reprocessSongResources(Integer songId);

    /**
     * 批量处理儿歌资源
     *
     * @param songIds 儿歌ID列表
     * @return 批量处理结果
     */
    HSSJsonReulst<BatchProcessingResult> batchProcessSongs(List<Integer> songIds);

    /**
     * 处理结果类
     */
    class ProcessingResult {
        private boolean success;
        private String message;
        private Integer songId;
        private Integer sentenceCount;
        private NurserySongValidatorUtil.ValidationResult validationResult;
        private Exception exception;

        public ProcessingResult() {}

        public ProcessingResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public static ProcessingResult success(Integer songId, Integer sentenceCount) {
            ProcessingResult result = new ProcessingResult();
            result.success = true;
            result.songId = songId;
            result.sentenceCount = sentenceCount;
            result.message = "处理成功";
            return result;
        }

        public static ProcessingResult failure(String message, Exception exception) {
            ProcessingResult result = new ProcessingResult();
            result.success = false;
            result.message = message;
            result.exception = exception;
            return result;
        }

        // Getter 和 Setter 方法
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Integer getSongId() { return songId; }
        public void setSongId(Integer songId) { this.songId = songId; }
        public Integer getSentenceCount() { return sentenceCount; }
        public void setSentenceCount(Integer sentenceCount) { this.sentenceCount = sentenceCount; }
        public NurserySongValidatorUtil.ValidationResult getValidationResult() { return validationResult; }
        public void setValidationResult(NurserySongValidatorUtil.ValidationResult validationResult) { this.validationResult = validationResult; }
        public Exception getException() { return exception; }
        public void setException(Exception exception) { this.exception = exception; }
    }

    /**
     * 批量处理结果类
     */
    class BatchProcessingResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<ProcessingResult> results;

        public BatchProcessingResult() {}

        public BatchProcessingResult(int totalCount, int successCount, int failureCount, List<ProcessingResult> results) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.results = results;
        }

        // Getter 和 Setter 方法
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        public List<ProcessingResult> getResults() { return results; }
        public void setResults(List<ProcessingResult> results) { this.results = results; }
        public boolean isAllSuccess() { return failureCount == 0; }
        public double getSuccessRate() { return totalCount > 0 ? (double) successCount / totalCount : 0.0; }
    }
}
