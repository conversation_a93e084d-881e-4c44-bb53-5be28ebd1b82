package com.woxue.resourcemanage.service;



import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean;

import java.util.List;

/**
 * <p>
 * 扩展阅读-文章句句对应 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface IReadExpandReadArticleCorrelationService  {
    List<ReadExpandReadArticleCorrelationBean> list();

    ReadExpandReadArticleCorrelationBean edit(Integer correlationId);

    ReadExpandReadArticleCorrelationBean editByArticleId(Integer articleId);

    int save(ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean);

    int update(ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean);
}
