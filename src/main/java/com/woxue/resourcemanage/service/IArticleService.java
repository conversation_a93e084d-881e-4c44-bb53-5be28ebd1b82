package com.woxue.resourcemanage.service;

import com.woxue.common.util.HSSJsonReulst;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 课文相关
 *
 * <AUTHOR>
 * @date 2022/5/30 12:08
 */
public interface IArticleService {
    /**
     * 导入课文
     * @param files
     * @param courseId
     * @return
     */
    HSSJsonReulst insertArticleInfo(MultipartFile[] files, Integer courseId);

    Map<String,Object> getUnitList(Integer courseId, Integer pageIndex, Integer pageSize);

    void export(Integer courseId, HttpServletResponse response);
}
