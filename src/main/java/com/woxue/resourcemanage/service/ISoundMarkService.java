package com.woxue.resourcemanage.service;

import java.util.List;
import java.util.Map;

public interface ISoundMarkService {

    List<Map<String, Object>> list(String spelling, int index, Integer pageSize);

    Boolean add(String spelling, String soundmark);

    Boolean update(String oldSpelling,String spelling, String soundmark);

    Boolean delete(String spelling);

    Integer count(String spelling);

}
