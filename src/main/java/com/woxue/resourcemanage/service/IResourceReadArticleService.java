package com.woxue.resourcemanage.service;

import com.woxue.ai.model.AnalysisResponse;
import com.woxue.common.model.redBook.read.ResourceReadArticleBean;
import com.woxue.common.model.redBook.read.ResourceReadArticleDTO;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.read.AIGenerateArticleDTO;
import com.woxue.resourcemanage.entity.dto.read.ReadCorrelationDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceReadArticleNewDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceUnitTopicIdPostDTO;
import com.woxue.resourcemanage.entity.vo.read.TopicUnitArticleListVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:42
 */
public interface IResourceReadArticleService {

    Map<String, Object> listByCourseId(Integer courseId, Integer pageNum, Integer pageSize);

    List<ResourceReadArticleBean> list(Integer unitId);
    ResourceReadArticleNewDTO edit(Integer articleId);

    int saveOrUpdate(ResourceReadArticleNewDTO resourceReadArticleDTO);
    Map<String, Object> uploadPhoto(Integer unitId, MultipartFile upload, HttpServletRequest request);

    List<ResourceTopic> topicList();
    List<TopicUnitArticleListVO> getTopicUnitArticleList(Integer topicId);
    int saveTopicUnitArticleList(ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO);
    ResourceReadArticleDTO aiGenerateArticle(AIGenerateArticleDTO aiGenerateArticleDTO);
    AnalysisResponse aiArticleAnalysis(AIGenerateArticleDTO aiGenerateArticleDTO);
    String aiGenerateImages(AIGenerateArticleDTO aiGenerateArticleDTO);
    int insertTopic(ResourceTopic resourceTopicBean);

    ReadCorrelationDTO patternSplitContent(ReadCorrelationDTO readCorrelationDTO);
    ReadCorrelationDTO patternSplitContentV2(ReadCorrelationDTO readCorrelationDTO);

    String importExcel(MultipartFile file,Integer versionId,Integer courseId, Integer startReadLine);

}
