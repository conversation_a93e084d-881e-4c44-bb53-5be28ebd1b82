package com.woxue.resourcemanage.service;

import com.redbook.kid.common.model.phonics.*;

import java.util.Date;
import java.util.List;

/**
 * 少儿自然拼读资源管理服务接口
 */
public interface KidPhonicsService {

    // 课程管理
    /**
     * 创建课程
     * @param course 课程对象
     */
    void insertCourse(KidPhonicsCourse course);

    /**
     * 根据ID获取课程
     * @param id 课程ID
     * @return 课程对象
     */
    KidPhonicsCourse getCourseById(Integer id);

    /**
     * 更新课程
     * @param course 课程对象
     */
    void updateCourse(KidPhonicsCourse course);

    /**
     * 删除课程
     * @param id 课程ID
     */
    void deleteCourse(Integer id);

    /**
     * 获取所有课程
     * @return 课程列表
     */
    List<KidPhonicsCourse> getAllCourses();

    // 单元管理
    /**
     * 创建单元
     * @param unit 单元对象
     */
    void insertUnit(KidPhonicsUnit unit);

    /**
     * 根据ID获取单元
     * @param id 单元ID
     * @return 单元对象
     */
    KidPhonicsUnit getUnitById(Integer id);

    /**
     * 更新单元
     * @param unit 单元对象
     */
    void updateUnit(KidPhonicsUnit unit);

    /**
     * 删除单元
     * @param id 单元ID
     */
    void deleteUnit(Integer id);

    /**
     * 根据课程ID获取单元列表
     * @param courseId 课程ID
     * @return 单元列表
     */
    List<KidPhonicsUnit> getUnitsByCourseId(Integer courseId);

    /**
     * 更新单元状态
     * @param id 单元ID
     * @param status 状态(0-禁用，1-启用)
     */
    void updateUnitStatus(Integer id, Integer status);

    // 音素管理
    /**
     * 创建音素
     * @param letter 音素对象
     */
    void insertLetter(KidPhonicsLetter letter);

    /**
     * 根据ID获取音素
     * @param id 音素ID
     * @return 音素对象
     */
    KidPhonicsLetter getLetterById(Integer id);

    /**
     * 更新音素
     * @param letter 音素对象
     */
    void updateLetter(KidPhonicsLetter letter);

    /**
     * 删除音素
     * @param id 音素ID
     */
    void deleteLetter(Integer id);

    /**
     * 根据单元ID获取音素列表
     * @param unitId 单元ID
     * @return 音素列表
     */
    List<KidPhonicsLetter> getLettersByUnitId(Integer unitId);

    // 音素组件管理
    /**
     * 创建音素组件
     * @param component 音素组件对象
     */
    void insertLetterComponent(KidPhonicsLetterComponent component);

    /**
     * 根据ID获取音素组件
     * @param id 音素组件ID
     * @return 音素组件对象
     */
    KidPhonicsLetterComponent getLetterComponentById(Integer id);

    /**
     * 更新音素组件
     * @param component 音素组件对象
     */
    void updateLetterComponent(KidPhonicsLetterComponent component);

    /**
     * 删除音素组件
     * @param id 音素组件ID
     */
    void deleteLetterComponent(Integer id);

    /**
     * 根据音素ID获取组件列表
     * @param letterId 音素ID
     * @return 组件列表
     */
    List<KidPhonicsLetterComponent> getComponentsByLetterId(Integer letterId);

    // 例词管理
    /**
     * 创建例词
     * @param word 例词对象
     */
    void insertWord(KidPhonicsWord word);

    /**
     * 根据ID获取例词
     * @param id 例词ID
     * @return 例词对象
     */
    KidPhonicsWord getWordById(Integer id);

    /**
     * 更新例词
     * @param word 例词对象
     */
    void updateWord(KidPhonicsWord word);

    /**
     * 删除例词
     * @param id 例词ID
     */
    void deleteWord(Integer id);

    /**
     * 根据音素ID获取例词列表
     * @param letterId 音素ID
     * @return 例词列表
     */
    List<KidPhonicsWord> getWordsByLetterId(Integer letterId);

    // 儿歌管理
    /**
     * 创建儿歌
     * @param rhyme 儿歌对象
     */
    void insertRhyme(KidPhonicsRhyme rhyme);

    /**
     * 根据ID获取儿歌
     * @param id 儿歌ID
     * @return 儿歌对象
     */
    KidPhonicsRhyme getRhymeById(Integer id);

    /**
     * 更新儿歌
     * @param rhyme 儿歌对象
     */
    void updateRhyme(KidPhonicsRhyme rhyme);

    /**
     * 删除儿歌
     * @param id 儿歌ID
     */
    void deleteRhyme(Integer id);

    /**
     * 根据单元ID获取儿歌列表
     * @param unitId 单元ID
     * @return 儿歌列表
     */
    List<KidPhonicsRhyme> getRhymesByUnitId(Integer unitId);

    // 绘本管理
    /**
     * 创建绘本
     * @param pictureBook 绘本对象
     */
    void insertPictureBook(KidPhonicsPictureBook pictureBook);

    /**
     * 根据ID获取绘本
     * @param id 绘本ID
     * @return 绘本对象
     */
    KidPhonicsPictureBook getPictureBookById(Integer id);

    /**
     * 更新绘本
     * @param pictureBook 绘本对象
     */
    void updatePictureBook(KidPhonicsPictureBook pictureBook);

    /**
     * 删除绘本
     * @param id 绘本ID
     */
    void deletePictureBook(Integer id);

    /**
     * 根据单元ID获取绘本列表
     * @param unitId 单元ID
     * @return 绘本列表
     */
    List<KidPhonicsPictureBook> getPictureBooksByUnitId(Integer unitId);

    // 绘本内容管理
    /**
     * 创建绘本内容
     * @param content 绘本内容对象
     */
    void insertPictureBookContent(KidPhonicsPictureBookContent content);

    /**
     * 根据ID获取绘本内容
     * @param id 绘本内容ID
     * @return 绘本内容对象
     */
    KidPhonicsPictureBookContent getPictureBookContentById(Integer id);

    /**
     * 更新绘本内容
     * @param content 绘本内容对象
     */
    void updatePictureBookContent(KidPhonicsPictureBookContent content);

    /**
     * 删除绘本内容
     * @param id 绘本内容ID
     */
    void deletePictureBookContent(Integer id);

    /**
     * 根据绘本ID获取内容列表
     * @param pictureBookId 绘本ID
     * @return 内容列表
     */
    List<KidPhonicsPictureBookContent> getPictureBookContentsByPictureBookId(Integer pictureBookId);

    // 绘本句子管理
    /**
     * 创建绘本句子
     * @param sentence 绘本句子对象
     */
    void insertPictureBookSentence(KidPhonicsPictureBookSentence sentence);

    /**
     * 根据ID获取绘本句子
     * @param id 绘本句子ID
     * @return 绘本句子对象
     */
    KidPhonicsPictureBookSentence getPictureBookSentenceById(Integer id);

    /**
     * 更新绘本句子
     * @param sentence 绘本句子对象
     */
    void updatePictureBookSentence(KidPhonicsPictureBookSentence sentence);

    /**
     * 删除绘本句子
     * @param id 绘本句子ID
     */
    void deletePictureBookSentence(Integer id);

    /**
     * 根据内容ID获取句子列表
     * @param contentId 内容ID
     * @return 句子列表
     */
    List<KidPhonicsPictureBookSentence> getPictureBookSentencesByContentId(Integer contentId);

    // 发布管理
    /**
     * 创建发布记录
     * @param record 发布记录对象
     */
    void insertPublishRecord(KidPhonicsPublishRecord record);

    /**
     * 根据ID获取发布记录
     * @param id 发布记录ID
     * @return 发布记录对象
     */
    KidPhonicsPublishRecord getPublishRecordById(Integer id);

    /**
     * 根据课程ID获取发布记录列表
     * @param courseId 课程ID
     * @return 发布记录列表
     */
    List<KidPhonicsPublishRecord> getPublishRecordsByCourseId(Integer courseId);

    /**
     * 发布课程
     * @param courseId 课程ID
     * @param publisherName 发布人姓名
     * @param remark 备注
     */
    void publishCourse(Integer courseId, String publisherName, String remark);

    /**
     * 加载单元的完整数据，包括音素、例词、儿歌、绘本等关联资源
     * @param unit 单元对象
     * @return 包含完整数据的单元对象
     */
    KidPhonicsUnit loadUnitWithResources(KidPhonicsUnit unit);

    /**
     * 加载绘本的完整数据，包括内容和句子
     * @param pictureBook 绘本对象
     * @return 包含完整数据的绘本对象
     */
    KidPhonicsPictureBook loadPictureBookWithContents(KidPhonicsPictureBook pictureBook);
} 