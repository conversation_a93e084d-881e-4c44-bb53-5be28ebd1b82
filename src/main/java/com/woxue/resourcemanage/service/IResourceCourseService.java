package com.woxue.resourcemanage.service;


import com.woxue.common.model.redBook.RedBookCourseStage;
import com.woxue.resourcemanage.entity.vo.RedBookCourseVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 13:47
 */
public interface IResourceCourseService {


    List<RedBookCourseVO> getCourseList(Integer versionId,Integer stage);

    boolean insertCourse(Integer versionId, String nameEn, String nameCn, Integer stage, Integer grade, String paramString, Integer resourceCourseId);

    Map<String, Object> getCourseById(Integer id);

    boolean updateCourse(String nameEn, String nameCn, Integer grade, Integer id);

    Map<String, Object> getCourseRelevant(Integer courseId);

    Map<String, Object> isHaveCourseWord(String programName);

    Map<String, Object> isHaveCourseSentence(String programName);

    Map<String, Object> isHaveCourseArticle(String programName);

    Map<String, Object> isHaveCourseQuestion(Integer programId);

    Map<String, Object> isHaveCourseGrammar(Integer programId);

    boolean insertCourseStage(Integer courseId,Integer... stageList);
}
