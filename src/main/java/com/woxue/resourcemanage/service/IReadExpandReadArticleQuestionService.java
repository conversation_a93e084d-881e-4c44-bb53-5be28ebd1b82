package com.woxue.resourcemanage.service;



import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean;

import java.util.List;

/**
 * <p>
 * 扩展阅读-文章问题 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface IReadExpandReadArticleQuestionService {
    List<ReadExpandReadArticleQuestionBean> list(Integer articleId);

    ReadExpandReadArticleQuestionBean edit(Integer questionId);

    int save(ReadExpandReadArticleQuestionBean readexpandReadArticleQuestionBean);

    int batchSave(List<ReadExpandReadArticleQuestionBean> readexpandReadArticleQuestionBeanList);

    int update(ReadExpandReadArticleQuestionBean readexpandReadArticleQuestionBean);
}
