package com.woxue.resourcemanage.service;


import com.woxue.resourcemanage.entity.grammar.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022 -02-23 10:45
 */
public interface INewGrammarManagerService {

    /**
     * 获取课程下的试题列表
     * @param courseId
     * @return
     */
    List<SimulationQuestionBean> getCourseQuestionList(Integer courseId, Integer pageNum, Integer pageSize);

    /**
     * 获取课程下的试题数
     * @param courseId
     * @return
     */
    List<Map<String, Object>> getCourseQuestionNum(Integer courseId);

    /**
     * 获取单元列表
     * @param courseId
     * @return
     */
    List<GrammarUnitBean> getUnitList(Integer courseId);

    Boolean addGrammarUnit(Integer unitId);

    /**
     * 获取课程全名
     * @param courseId
     * @return
     */
    Map<String, Object> getCourseFullNameById(Integer courseId);

    /**
     * 获取本章检测题questionid最小值
     * @return
     */
    Integer getCourseQuestionIdMinNum(Integer courseId);

    /**
     * 批量添加试题
     * @param questionList
     * @return
     */
    boolean addCourseQuestionList(List<SimulationQuestionBean> questionList);

    /**
     * 修改试题
     * @param 
     * @return
     */
    boolean updateCourseQuestion(SimulationQuestionBean question);

    /**
     * 批量删除课程下面的试题
     * @param deleteIdArr
     * @return
     */
    boolean deleteCourseQuestion(String teachId, Integer courseId, Integer[] deleteIdArr);

    /**
     * 获取满足条件的语法试题
     * @param aid
     * @param teachId
     * @param sourceTypeArr
     * @param gradeArr
     * @param questionTypeArr
     * @param difficultyArr
     * @return
     */
    Map<String, Object> getSimulationQuestionList(String aid,
                                                  String teachId, Integer[] sourceTypeArr, Integer gradePhase, Integer[] gradeArr,
                                                  String[] knowledgePointArr, Integer[] questionTypeArr,
                                                  Integer[] difficultyArr, String orderBy, Integer pageNum, Integer pageSize);

    /**
     * 添加所有本章检测试题
     * @param aid
     * @param sourceTypeArr
     * @param gradeArr
     * @param questionTypeArr
     * @param difficultyArr
     * @return
     */
    boolean addAllCourseQuestion(String aid, String teachId,Integer courseId,
                                 Integer[] sourceTypeArr, Integer gradePhase, Integer[] gradeArr,
                                 String[] knowledgePointArr, Integer[] questionTypeArr,
                                 Integer[] difficultyArr);


    /**
     * 添加所有本章检测试题
     * @param aid
     * @param userId
     * @param sourceTypeArr
     * @param gradeArr
     * @param grammarPointArr
     * @param questionTypeArr
     * @param difficultyArr
     * @return
     */
    boolean addAllUnitQuestion(String aid, String teachId,Integer unitId,
                               Integer[] sourceTypeArr, Integer gradePhase, Integer[] gradeArr,
                               String[] knowledgePointArr, Integer[] questionTypeArr,
                               Integer[] difficultyArr);

    /**
     * 添加选中的本章检测试题
     * @param courseId
     * @param questionIdArr
     * @return
     */
    boolean addSelectUnitQuestion(String teachId, Integer unitId, Integer[] questionIdArr);
    /**
     * 添加选中的本章检测试题
     * @param courseId
     * @param questionIdArr
     * @return
     */
    boolean addSelectCourseQuestion(String teachId, Integer courseId, Integer[] questionIdArr);

    /**
     * 获取语法卡片的详细信息
     * @param unitId
     * @return
     */
    GrammarNewUnitBean getGrammarCardDetail(Integer unitId);

    boolean deleteContent(Integer unitId,Integer questionType,Integer contentId);

    /**
     * 获取剪切板的内容
     * @param idArr
     * @return
     */
    List<GrammarUnitContentBean> getCopiedContentList(String[] idArr);

    /**
     * 获取单元名
     * @param unitId
     * @return
     */
    Map<String, Object> getUnitName(Integer unitId);

    /**
     * 获取知识点列表
     * @param unitId
     * @return
     */
    Map<String, Object> getKnowledgeListWithNum(Integer unitId);

    /**
     * 获取试题列表
     * @return
     */
    Map<String, Object> getQuestionList(Integer unitId, Integer pageNum, Integer pageSize, String titleIds);

    /**
     * 修改试题
     * @param question
     * @return
     */
    boolean updateQuestion(GrammarQuestionBean question);
    /**
     * 批量添加试题
     * @param questionList
     * @return
     */
    boolean addQuestionList(List<GrammarQuestionBean> questionList);

    /**
     * 获取应用试题列表
     * @param unitId
     * @return
     */
    List<Map<String, Object>> getApplyQuestionList(Integer unitId, Integer titleId);

    /**
     * 根据试题id删除试题
     * @param teachId
     * @return
     */
    boolean deleteQuestion(String teachId, Integer courseId, Integer unitId, Integer[] questionIdArr);
    /**
     * 获取地区信息
     * @return
     */
    List<Map<String, Object>> getAreaList();

    /**
     * 添加语法卡片
     * @param grammarCard
     * @return
     */
    Map<String, Object> saveGrammarCard(String contextPath, GrammarNewUnitBean grammarCard);



    /**
     * 上传图片
     * @param userId
     * @param upload
     * @param request
     * @return
     */
    Map<String, String> uploadImage(Integer unitId, MultipartFile upload,
                                    HttpServletRequest request);

    /**
     * 通过粘贴或抓取的方式上传图片
     * @param userId
     * @param unitId
     * @param upload
     * @param request
     * @return
     */
    Map<String, Object> textUploadByPaste(Integer unitId,
                                          MultipartFile upload, HttpServletRequest request);
    Map<String, Object> audioUploadByPaste(Integer unitId,
                                          MultipartFile upload, HttpServletRequest request);
    void textUpload(Integer unitId, MultipartFile upload, HttpServletResponse response, HttpServletRequest request);

    boolean updateUnitKnowledgeCount(Integer unitId, Integer count);
}
