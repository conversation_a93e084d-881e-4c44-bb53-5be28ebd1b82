package com.woxue.resourcemanage.service;

import com.woxue.common.model.redBook.RedBookUnit;
import com.woxue.common.model.redBook.RedBookUnitContentWrite;
import com.woxue.common.model.redBook.ResourceUnitWriteSentence;
import com.woxue.common.model.redBook.WriteUnitTitleBean;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.write.EditSampleDto;
import com.woxue.resourcemanage.entity.dto.write.EditWordDto;
import com.woxue.resourcemanage.entity.dto.write.SaveUnitTopicDto;
import com.woxue.resourcemanage.entity.write.TopicRelatedUnitBean;
import com.woxue.resourcemanage.entity.write.WriteNewUnitBean;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IResourceWriteService {
    //保存写作方法
    Map<String, Object> saveWriteMethodCard(String contextPath, WriteNewUnitBean writeNewUnitBean);
    //富文本文件上传
    Map<String, Object> textUploadByPaste(Integer unitId, MultipartFile upload, HttpServletRequest request);
    //保存主题
    HSSJsonReulst<Integer> addTopic(String topic, String type);
    //获取主题列表
    HSSJsonReulst<List<ResourceTopic>> getTopicListByType(String type);
    //获取写作方法
    HSSJsonReulst<List<WriteUnitTitleBean>> getWriteMethodByUnitId(Integer unitId);
    //保存句型训练
    HSSJsonReulst<Integer> addSentenceTrain(ResourceUnitWriteSentence resourceUnitWriteSentence);
    //获取句型训练
    HSSJsonReulst<List<ResourceUnitWriteSentence>> getSentenceTrainListByUnitId(Integer unitId);
    //编辑句型训练
    HSSJsonReulst<Boolean> editSentenceTrain(ResourceUnitWriteSentence resourceUnitWriteSentence);
    //编辑词汇
    HSSJsonReulst<Boolean> editUnitWords(EditWordDto editWordDto);
    //获取范文
    HSSJsonReulst<RedBookUnitContentWrite> getSampleByUnitId(Integer unitId);
    //编辑范文
    HSSJsonReulst<Boolean> editSample(EditSampleDto editSampleDto);
    //获取单元列表
    HSSJsonReulst<List<RedBookUnit>> getUnitListByCourseId(Integer courseId);
    //获取主题关联的单元列表
    HSSJsonReulst<List<TopicRelatedUnitBean>> getTopicRelatedUnitList(Integer topicId);
    //保存单元主题
    HSSJsonReulst<Boolean> saveUnitTopic(SaveUnitTopicDto saveUnitTopicDto);
    //导入句型训练
    HSSJsonReulst<Boolean> importSentence(MultipartFile file, Integer courseId) throws IOException;
    //导入范文
    HSSJsonReulst<Boolean> importSample(MultipartFile file, Integer courseId)throws IOException;
}
