package com.woxue.resourcemanage.service;


import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleBean;
import com.woxue.resourcemanage.entity.vo.ReadExpandReadArticleVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 扩展阅读-文章 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface IReadExpandReadArticleService  {

    Map<String, Object> listByCourseId(Integer courseId, Integer pageNum, Integer pageSize);

    List<ReadExpandReadArticleBean> list(Integer unitId);

    ReadExpandReadArticleVO edit(Integer articleId);

    int save(ReadExpandReadArticleBean readexpandReadArticleBean);

    int update(ReadExpandReadArticleBean readexpandReadArticleBean);
    String importExcel(MultipartFile file,Integer versionId,Integer courseId, Integer startReadLine);

}
