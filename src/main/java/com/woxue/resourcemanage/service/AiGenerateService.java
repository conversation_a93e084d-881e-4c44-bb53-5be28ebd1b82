package com.woxue.resourcemanage.service;

import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.AiMnemonicDTO;
import com.woxue.resourcemanage.entity.AiMnemonicParent;
import io.swagger.annotations.ApiParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-05-12 11:31
 */
public interface AiGenerateService {


    Map<String, Object> list(AiGenerateQuery aiGenerateQuery, HttpServletResponse response);
    void execute(Integer id,String moduleType,HttpServletResponse response);
    String executeWord(Integer id,HttpServletResponse response);
    Integer update(AiMnemonicParent aiMnemonicParent);
    Integer handleIgnore(Integer id,String moduleType);
    Integer saveOrUpdate(AiMnemonicDTO aiMnemonicDTO);


}
