package com.woxue.resourcemanage.config;

/**
 * <AUTHOR>
 * @date 2022 -05-11 10:53
 */

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfiguration {

    @Bean
    public Docket defaultApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(true)//非正式环境开启
                .apiInfo(groupApiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.woxue.resourcemanage.controller"))
                .paths(PathSelectors.any())
               .build();
    }

    private ApiInfo groupApiInfo(){
        return new ApiInfoBuilder()
                .title("小红本内容管理系统接口文档")
                .description("redbook RESTful APIs")
                .version("2.1")
                .build();
    }
}
