package com.woxue.resourcemanage.config;

import com.google.common.base.Charsets;
import com.google.gson.GsonBuilder;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.GsonHttpMessageConverter;
import org.springframework.stereotype.Component;

import java.io.Writer;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Component
public class MmoJsonConverter extends GsonHttpMessageConverter {
	public MmoJsonConverter() {
		List<MediaType> fastMediaTypes = new ArrayList<>();
		fastMediaTypes.add(MediaType.APPLICATION_JSON);
		super.setSupportedMediaTypes(fastMediaTypes);
		super.setDefaultCharset(Charsets.UTF_8);

		//更换Gson转换器
		super.setGson(new GsonBuilder()
				//null值属性也需要序列化
				.serializeNulls()
				//设置日期转换
				.setDateFormat("yyyy-MM-dd HH:mm:ss")
						.disableHtmlEscaping()
				.create());
	}

	/**
	 * 这个位置是处理swagger json序列化失败的问题
	 * @param object
	 * @param type
	 * @param writer
	 * @throws Exception
	 */
	@Override
	protected void writeInternal(Object object, Type type, Writer writer) throws Exception {
		if (object.getClass().equals(springfox.documentation.spring.web.json.Json.class)) {
			object = ((springfox.documentation.spring.web.json.Json)object).value();
		}
		super.writeInternal(object, type, writer);
	}
}
