package com.woxue.resourcemanage.config;

import com.google.common.base.Charsets;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class MmoStringConverter extends StringHttpMessageConverter {

	public MmoStringConverter() {
		List<MediaType> fastMediaTypes = new ArrayList<>();
		fastMediaTypes.add(MediaType.TEXT_PLAIN);
		fastMediaTypes.add(MediaType.TEXT_HTML);
		super.setSupportedMediaTypes(fastMediaTypes);
		super.setDefaultCharset(Charsets.UTF_8);
	}
}
