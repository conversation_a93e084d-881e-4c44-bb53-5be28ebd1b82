package com.woxue.resourcemanage.entity;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * 父类
 */
public class AiMnemonicParent implements Serializable {

    private static final long serialVersionUID = 1L;
    /** id */
    private Integer id;

    @ApiModelProperty(name = "moduleType",value= "模块类型：resourceWord、word、sentence、write、article")
    private String moduleType;

    /** 处理状态：0未处理，1已处理，2忽略 */
    @ApiModelProperty(name = "status",value= "处理状态：0未处理，1已处理，2忽略" )
    private Integer status;

    @ApiModelProperty(name = "likeNum",value= "点赞数量" )
    private Integer likeNum;

    @ApiModelProperty(name = "dislikeNum",value= "点踩数量" )
    private Integer dislikeNum;

    /** 反馈内容 */
    @ApiModelProperty(name = "feedContent",value= "反馈内容" )
    private String feedContent;


    /** 关联内容 */
    @ApiModelProperty(name = "relationContent",value= "关联内容" )
    private String relationContent;


    /** 课程详情 */
    @ApiModelProperty(name = "courseDetails",value= "课程详情" )
    private String courseDetails;


    /** 单元名称 */
    @ApiModelProperty(name = "unitName",value= "单元名称" )
    private String unitName;

    @ApiModelProperty(name = "updateTime",value= "修改时间" )
    private Date updateTime;

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getDislikeNum() {
        return dislikeNum;
    }

    public void setDislikeNum(Integer dislikeNum) {
        this.dislikeNum = dislikeNum;
    }

    public String getFeedContent() {
        return feedContent;
    }

    public void setFeedContent(String feedContent) {
        this.feedContent = feedContent;
    }

    public String getRelationContent() {
        return relationContent;
    }

    public void setRelationContent(String relationContent) {
        this.relationContent = relationContent;
    }

    public String getCourseDetails() {
        return courseDetails;
    }

    public void setCourseDetails(String courseDetails) {
        this.courseDetails = courseDetails;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
