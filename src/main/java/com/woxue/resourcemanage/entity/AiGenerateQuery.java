package com.woxue.resourcemanage.entity;


import io.swagger.annotations.ApiModelProperty;

public class AiGenerateQuery {

    @ApiModelProperty(name = "pageNum",value= "页码" )
    private Integer pageNum;
    @ApiModelProperty(name = "pageSize",value= "每页显示数" )
    private Integer pageSize;
    @ApiModelProperty(name = "moduleType",value= "模块类型：resourceWord、word、sentence、write、article" )
    private String moduleType;
    @ApiModelProperty(name = "status",value= "处理状态：0未处理，1已处理，2忽略" )
    private Integer status;
    @ApiModelProperty(name = "relationContent",value= "关联内容" )
    private String relationContent;
    @ApiModelProperty(name = "courseDetails",value= "课程信息" )
    private String courseDetails;
    @ApiModelProperty(name = "sort",value= "正序倒序:asc desc" )
    private String sort;
    @ApiModelProperty(name = "order",value= "排序字段" )
    private String order;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRelationContent() {
        return relationContent;
    }

    public void setRelationContent(String relationContent) {
        this.relationContent = relationContent;
    }

    public String getCourseDetails() {
        return courseDetails;
    }

    public void setCourseDetails(String courseDetails) {
        this.courseDetails = courseDetails;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
