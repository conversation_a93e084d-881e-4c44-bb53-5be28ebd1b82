package com.woxue.resourcemanage.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@ApiModel(value = "单词")
public class WordEntity implements Serializable {

	@ApiModelProperty("单词ID")
	private int wordId=-1;

	@ApiModelProperty("拼写")
	private String spelling = "";   //拼写

	@ApiModelProperty("音标")
	private String syllable = "";   //音标

	@ApiModelProperty("声音文件")
	private String soundFile;	//聲音文件

	@ApiModelProperty("英文解释")
	private String meaning_en_US = "";	//英文解释

	@ApiModelProperty("中文解释")
	private String meaning_zh_CN = "";   //中文解释

	@ApiModelProperty("例句英文")
	private String example_en_US;   //例句英文

	@ApiModelProperty("例句中文")
	private String example_zh_CN;

	@ApiModelProperty("句子成分英文，用|分割")
	private String sentenceCompositionEn;
	@ApiModelProperty("句子成分中文，用|分割")
	private String sentenceCompositionCn;
	@ApiModelProperty("句子从句成分英文，用|分割")
	private String sentenceClauseCompositionEn;
	@ApiModelProperty("句子从句成分中文，用|分割")
	private String sentenceClauseCompositionCn;
	@ApiModelProperty("同义词（暂未使用）")
	private String synonymous;
	@ApiModelProperty("句子从句成分确认标识：0未确认，1已确认")
	private Integer confirmFlag=0;

	@ApiModelProperty("修改时间")
	private Date modify_time;

	@ApiModelProperty("课程id")
	private Integer courseId;
	@ApiModelProperty("课程名")
	private String courseName;

	@ApiModelProperty("单词名")
	private Integer unitId;

	@ApiModelProperty("单词名")
	private String unitName;

	@ApiModelProperty("词汇（词汇一二三四五，用数字表示）")
	private int level;

	@ApiModelProperty("单词声音文件是否存在")
	private boolean hasSpellingSound=false;
	@ApiModelProperty("例句声音文件是否存在")
	private boolean hasExampleSound=false;
	@ApiModelProperty("例句声音文件地址")
	private String exampleSoundFile="";

	@ApiModelProperty("助记内容")
	private String mnemonics;
	@ApiModelProperty("其他助记内容1")
	private String other_mnemonics1;
	@ApiModelProperty("其他助记内容2")
	private String other_mnemonics2;

	public String getMnemonics() {
		return mnemonics;
	}

	public void setMnemonics(String mnemonics) {
		this.mnemonics = mnemonics;
	}

	public String getOther_mnemonics1() {
		return other_mnemonics1;
	}

	public void setOther_mnemonics1(String other_mnemonics1) {
		this.other_mnemonics1 = other_mnemonics1;
	}

	public String getOther_mnemonics2() {
		return other_mnemonics2;
	}

	public void setOther_mnemonics2(String other_mnemonics2) {
		this.other_mnemonics2 = other_mnemonics2;
	}

	public boolean isHasExampleSound() {
		return hasExampleSound;
	}

	public void setHasExampleSound(boolean hasExampleSound) {
		this.hasExampleSound = hasExampleSound;
	}

	public String getExampleSoundFile() {
		return exampleSoundFile;
	}

	public void setExampleSoundFile(String exampleSoundFile) {
		this.exampleSoundFile = exampleSoundFile;
	}

	public boolean isHasSpellingSound() {
		return hasSpellingSound;
	}

	public void setHasSpellingSound(boolean hasSpellingSound) {
		this.hasSpellingSound = hasSpellingSound;
	}

	public Date getModify_time() {
		return modify_time;
	}

	public void setModify_time(Date modify_time) {
		this.modify_time = modify_time;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public String getSoundFile() {
		return soundFile;
	}

	public void setSoundFile(String soundFile) {
		this.soundFile = soundFile;
	}
	

	public int getWordId() {
		return wordId;
	}

	public void setWordId(int wordId) {
		this.wordId = wordId;
	}

	public String getSpelling()
	{
		return spelling;
	}

	public void setSpelling(String spelling)
	{
		this.spelling = spelling;
	}

	public String getSyllable()
	{
		return syllable;
	}

	public void setSyllable(String syllable)
	{
		this.syllable = syllable;
	}

	public String getMeaning_en_US()
	{
		return meaning_en_US;
	}

	public void setMeaning_en_US(String meaning_en_US)
	{
		this.meaning_en_US = meaning_en_US;
	}

	public String getMeaning_zh_CN()
	{
		return meaning_zh_CN;
	}

	public void setMeaning_zh_CN(String meaning_zh_CN)
	{
		this.meaning_zh_CN = meaning_zh_CN;
	}

	public String getExample_en_US()
	{
		return example_en_US;
	}

	public void setExample_en_US(String example_en_US)
	{
		this.example_en_US = example_en_US;
	}

	public String getExample_zh_CN()
	{
		return example_zh_CN;
	}

	public void setExample_zh_CN(String example_zh_CN)
	{
		this.example_zh_CN = example_zh_CN;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getSynonymous() {
		return synonymous;
	}

	public void setSynonymous(String synonymous) {
		this.synonymous = synonymous;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public Integer getCourseId() {
		return courseId;
	}

	public void setCourseId(Integer courseId) {
		this.courseId = courseId;
	}

	public String getSentenceCompositionEn() {
		return sentenceCompositionEn;
	}

	public void setSentenceCompositionEn(String sentenceCompositionEn) {
		this.sentenceCompositionEn = sentenceCompositionEn;
	}

	public String getSentenceCompositionCn() {
		return sentenceCompositionCn;
	}

	public void setSentenceCompositionCn(String sentenceCompositionCn) {
		this.sentenceCompositionCn = sentenceCompositionCn;
	}

	public String getSentenceClauseCompositionEn() {
		return sentenceClauseCompositionEn;
	}

	public void setSentenceClauseCompositionEn(String sentenceClauseCompositionEn) {
		this.sentenceClauseCompositionEn = sentenceClauseCompositionEn;
	}

	public String getSentenceClauseCompositionCn() {
		return sentenceClauseCompositionCn;
	}

	public void setSentenceClauseCompositionCn(String sentenceClauseCompositionCn) {
		this.sentenceClauseCompositionCn = sentenceClauseCompositionCn;
	}

	public Integer getConfirmFlag() {
		return confirmFlag;
	}

	public void setConfirmFlag(Integer confirmFlag) {
		this.confirmFlag = confirmFlag;
	}
}
