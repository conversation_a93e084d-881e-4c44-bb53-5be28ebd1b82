package com.woxue.resourcemanage.entity;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021 -06-03 14:22
 */
public class ResourceCourse implements Serializable {
    private static final long serialVersionUID = 3052426091320348454L;
    private Integer id;
    private Integer versionId;
    private String nameEn;
    private String nameCn;
    private Integer unitNum;
    private Integer stage;
    private Integer grade;
    private Integer displayOrder;
    private Integer branchId;
    private List<ResourceUnit> unitList;

    private Integer count;

    public Integer getBranchId() {
        return branchId;
    }

    public void setBranchId(Integer branchId) {
        this.branchId = branchId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<ResourceUnit> getUnitList() {
        return unitList;
    }

    public void setUnitList(List<ResourceUnit> unitList) {
        this.unitList = unitList;
    }

    public ResourceCourse() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public Integer getUnitNum() {
        return unitNum;
    }

    public void setUnitNum(Integer unitNum) {
        this.unitNum = unitNum;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public ResourceCourse(Integer id, Integer versionId, String nameEn, String nameCn, Integer unitNum, Integer stage, Integer grade, Integer displayOrder, List<ResourceUnit> unitList, Integer count) {
        this.id = id;
        this.versionId = versionId;
        this.nameEn = nameEn;
        this.nameCn = nameCn;
        this.unitNum = unitNum;
        this.stage = stage;
        this.grade = grade;
        this.displayOrder = displayOrder;
        this.unitList = unitList;
        this.count = count;
    }
}
