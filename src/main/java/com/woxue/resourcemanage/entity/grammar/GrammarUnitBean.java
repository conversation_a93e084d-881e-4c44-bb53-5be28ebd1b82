package com.woxue.resourcemanage.entity.grammar;

import io.swagger.annotations.ApiModelProperty;

public class GrammarUnitBean {
    @ApiModelProperty(value = "单元id")
    private Integer unitId;
    @ApiModelProperty(value = "单元名称")
    private String unitName;
    @ApiModelProperty(value = "语法单元ID -1代表没有")
    private Integer grammarUnitId;
    @ApiModelProperty(value = "语法习题单元ID")
    private Integer questionUnitId;
    @ApiModelProperty(value = "语法问题总数")
    private Integer questionTotalNum;
    @ApiModelProperty(value = "语法问题简单")
    private Integer questionLevel1Num;
    @ApiModelProperty(value = "语法问题中等")
    private Integer questionLevel2Num;
    @ApiModelProperty(value = "语法问题较难")
    private Integer questionLevel3Num;
    @ApiModelProperty(value = "知识点数量")
    private Integer knowledgeCount;
    @ApiModelProperty(value = "语法单元信息")
    GrammarNewUnitBean grammarNewUnitBean;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getGrammarUnitId() {
        return grammarUnitId;
    }

    public void setGrammarUnitId(Integer grammarUnitId) {
        this.grammarUnitId = grammarUnitId;
    }

    public Integer getQuestionUnitId() {
        return questionUnitId;
    }

    public void setQuestionUnitId(Integer questionUnitId) {
        this.questionUnitId = questionUnitId;
    }

    public Integer getQuestionTotalNum() {
        return questionTotalNum;
    }

    public void setQuestionTotalNum(Integer questionTotalNum) {
        this.questionTotalNum = questionTotalNum;
    }

    public Integer getQuestionLevel1Num() {
        return questionLevel1Num;
    }

    public void setQuestionLevel1Num(Integer questionLevel1Num) {
        this.questionLevel1Num = questionLevel1Num;
    }

    public Integer getQuestionLevel2Num() {
        return questionLevel2Num;
    }

    public void setQuestionLevel2Num(Integer questionLevel2Num) {
        this.questionLevel2Num = questionLevel2Num;
    }

    public Integer getQuestionLevel3Num() {
        return questionLevel3Num;
    }

    public void setQuestionLevel3Num(Integer questionLevel3Num) {
        this.questionLevel3Num = questionLevel3Num;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getKnowledgeCount() {
        return knowledgeCount;
    }

    public void setKnowledgeCount(Integer knowledgeCount) {
        this.knowledgeCount = knowledgeCount;
    }

    public GrammarNewUnitBean getGrammarNewUnitBean() {
        return grammarNewUnitBean;
    }

    public void setGrammarNewUnitBean(GrammarNewUnitBean grammarNewUnitBean) {
        this.grammarNewUnitBean = grammarNewUnitBean;
    }
}
