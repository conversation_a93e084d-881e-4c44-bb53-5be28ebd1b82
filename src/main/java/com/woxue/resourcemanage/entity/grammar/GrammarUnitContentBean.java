package com.woxue.resourcemanage.entity.grammar;

import java.io.Serializable;

public class GrammarUnitContentBean   implements Serializable{
	private static final long serialVersionUID = -4102957468669066491L;
	private Integer id;
	private String teachId;
	private Integer unitId;
	private Integer subtitleId;
	private Integer questionType;
	private String question;
	private String optionA;
	private String optionB;
	private String optionC;
	private String optionD;
	private String optionE;
	private String correctOption;
	private String wrongTips1;
	private String wrongTips2;
	private String wrongTips3;
	private String parse;
	private Integer disporder;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getTeachId() {
		return teachId;
	}
	public void setTeachId(String teachId) {
		this.teachId = teachId;
	}
	public Integer getUnitId() {
		return unitId;
	}
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	public Integer getSubtitleId() {
		return subtitleId;
	}
	public void setSubtitleId(Integer subtitleId) {
		this.subtitleId = subtitleId;
	}
	public Integer getQuestionType() {
		return questionType;
	}
	public void setQuestionType(Integer questionType) {
		this.questionType = questionType;
	}
	public String getQuestion() {
		return question;
	}
	public void setQuestion(String question) {
		this.question = question;
	}
	public String getOptionA() {
		return optionA;
	}
	public void setOptionA(String optionA) {
		this.optionA = optionA;
	}
	public String getOptionB() {
		return optionB;
	}
	public void setOptionB(String optionB) {
		this.optionB = optionB;
	}
	public String getOptionC() {
		return optionC;
	}
	public void setOptionC(String optionC) {
		this.optionC = optionC;
	}
	public String getOptionD() {
		return optionD;
	}
	public void setOptionD(String optionD) {
		this.optionD = optionD;
	}
	public String getOptionE() {
		return optionE;
	}
	public void setOptionE(String optionE) {
		this.optionE = optionE;
	}
	public String getCorrectOption() {
		return correctOption;
	}
	public void setCorrectOption(String correctOption) {
		this.correctOption = correctOption;
	}
	public String getWrongTips1() {
		return wrongTips1;
	}
	public void setWrongTips1(String wrongTips1) {
		this.wrongTips1 = wrongTips1;
	}
	public String getWrongTips2() {
		return wrongTips2;
	}
	public void setWrongTips2(String wrongTips2) {
		this.wrongTips2 = wrongTips2;
	}
	public String getWrongTips3() {
		return wrongTips3;
	}
	public void setWrongTips3(String wrongTips3) {
		this.wrongTips3 = wrongTips3;
	}
	public String getParse() {
		return parse;
	}
	public void setParse(String parse) {
		this.parse = parse;
	}
	public Integer getDisporder() {
		return disporder;
	}
	public void setDisporder(Integer disporder) {
		this.disporder = disporder;
	}
	
}
