package com.woxue.resourcemanage.entity.grammar;

import com.woxue.common.model.redBook.GrammarQuestionContentBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


public class GrammarQuestionBean {
	private Integer id;
	private String teachId;
	private Integer unitId;
	private Integer questionType;
	private Integer difficulty;
	private String question;
	private String optionA;
	private String optionB;
	private String optionC;
	private String optionD;
	private String optionE;
	private String correctOption;
	private String parse;
	private Integer source;
	private Integer year;
	private Integer areaId;
	private Integer contentId;
	private List<Integer> titleIdList;
	private Integer status;//答题结果，-1：未答； 0：答错； 1：答对
	@ApiModelProperty("答案类型 0:文本,1:图片")
	private Integer answerType = 0;
	@ApiModelProperty("题目内容,技能讲练的资源等")
	private List<GrammarQuestionContentBean> questionContentBeanList;

	public Integer getAnswerType() {
		return answerType;
	}

	public void setAnswerType(Integer answerType) {
		this.answerType = answerType;
	}

	public List<GrammarQuestionContentBean> getQuestionContentBeanList() {
		return questionContentBeanList;
	}

	public void setQuestionContentBeanList(List<GrammarQuestionContentBean> questionContentBeanList) {
		this.questionContentBeanList = questionContentBeanList;
	}

	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getTeachId() {
		return teachId;
	}
	public void setTeachId(String teachId) {
		this.teachId = teachId;
	}
	public Integer getUnitId() {
		return unitId;
	}
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	public Integer getQuestionType() {
		return questionType;
	}
	public void setQuestionType(Integer questionType) {
		this.questionType = questionType;
	}
	public Integer getDifficulty() {
		return difficulty;
	}
	public void setDifficulty(Integer difficulty) {
		this.difficulty = difficulty;
	}
	public String getQuestion() {
		return question;
	}
	public void setQuestion(String question) {
		this.question = question;
	}
	public String getOptionA() {
		return optionA;
	}
	public void setOptionA(String optionA) {
		this.optionA = optionA;
	}
	public String getOptionB() {
		return optionB;
	}
	public void setOptionB(String optionB) {
		this.optionB = optionB;
	}
	public String getOptionC() {
		return optionC;
	}
	public void setOptionC(String optionC) {
		this.optionC = optionC;
	}
	public String getOptionD() {
		return optionD;
	}
	public void setOptionD(String optionD) {
		this.optionD = optionD;
	}
	public String getOptionE() {
		return optionE;
	}
	public void setOptionE(String optionE) {
		this.optionE = optionE;
	}
	public String getCorrectOption() {
		return correctOption;
	}
	public void setCorrectOption(String correctOption) {
		this.correctOption = correctOption;
	}
	public String getParse() {
		return parse;
	}
	public void setParse(String parse) {
		this.parse = parse;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getSource() {
		return source;
	}
	public void setSource(Integer source) {
		this.source = source;
	}
	public Integer getYear() {
		return year;
	}
	public void setYear(Integer year) {
		this.year = year;
	}
	public Integer getAreaId() {
		return areaId;
	}
	public void setAreaId(Integer areaId) {
		this.areaId = areaId;
	}
	public List<Integer> getTitleIdList() {
		return titleIdList;
	}
	public void setTitleIdList(List<Integer> titleIdList) {
		this.titleIdList = titleIdList;
	}
	public Integer getContentId() {
		return contentId;
	}
	public void setContentId(Integer contentId) {
		this.contentId = contentId;
	}
	
}
