package com.woxue.resourcemanage.entity.grammar;

import java.io.Serializable;
import java.util.List;

public class GrammarUnitSubtitleBean implements Serializable{
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer unitId;
	private Integer titleId;
	private String name;
	private Integer type;
	private Integer disporder;
	List<GrammarUnitContentBean> contentList;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getUnitId() {
		return unitId;
	}
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	public Integer getTitleId() {
		return titleId;
	}
	public void setTitleId(Integer titleId) {
		this.titleId = titleId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Integer getDisporder() {
		return disporder;
	}
	public void setDisporder(Integer disporder) {
		this.disporder = disporder;
	}
	public List<GrammarUnitContentBean> getContentList() {
		return contentList;
	}
	public void setContentList(List<GrammarUnitContentBean> contentList) {
		this.contentList = contentList;
	}
}
