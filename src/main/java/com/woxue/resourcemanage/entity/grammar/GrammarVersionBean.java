package com.woxue.resourcemanage.entity.grammar;


/**
 * <AUTHOR>
 *
 */
public class GrammarVersionBean {
	private Integer id;
	private String name;
	private Integer grade;
	private Integer versionType;
	private Integer disporder;
	private String modifyTime;
	private String publishTime;
	private int courseNum;

	public Integer getVersionType() {
		return versionType;
	}

	public void setVersionType(Integer versionType) {
		this.versionType = versionType;
	}

	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getGrade() {
		return grade;
	}
	public void setGrade(Integer grade) {
		this.grade = grade;
	}
	public Integer getDisporder() {
		return disporder;
	}
	public void setDisporder(Integer disporder) {
		this.disporder = disporder;
	}
	public int getCourseNum() {
		return courseNum;
	}
	public void setCourseNum(int courseNum) {
		this.courseNum = courseNum;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getPublishTime() {
		return publishTime;
	}
	public void setPublishTime(String publishTime) {
		this.publishTime = publishTime;
	}
	
}
