package com.woxue.resourcemanage.entity.grammar;

import java.util.Date;


public class GrammarCourseBean {
	private Integer id;
	private String aid;
	private String teachId;
	private Integer versionId;
	private Integer grade;
	private String name;
	private int unitNum;
	private int levelQuestionNum1;
	private int levelQuestionNum2;
	private int levelQuestionNum3;
	private Date modifyTime;
	private Date publishTime;
	private Boolean isApply;
	private Integer disporder;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getAid() {
		return aid;
	}
	public void setAid(String aid) {
		this.aid = aid;
	}
	public String getTeachId() {
		return teachId;
	}
	public void setTeachId(String teachId) {
		this.teachId = teachId;
	}
	public Integer getVersionId() {
		return versionId;
	}
	public void setVersionId(Integer versionId) {
		this.versionId = versionId;
	}
	public Integer getGrade() {
		return grade;
	}
	public void setGrade(Integer grade) {
		this.grade = grade;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public int getUnitNum() {
		return unitNum;
	}
	public void setUnitNum(int unitNum) {
		this.unitNum = unitNum;
	}
	public int getLevelQuestionNum1() {
		return levelQuestionNum1;
	}
	public void setLevelQuestionNum1(int levelQuestionNum1) {
		this.levelQuestionNum1 = levelQuestionNum1;
	}
	public int getLevelQuestionNum2() {
		return levelQuestionNum2;
	}
	public void setLevelQuestionNum2(int levelQuestionNum2) {
		this.levelQuestionNum2 = levelQuestionNum2;
	}
	public int getLevelQuestionNum3() {
		return levelQuestionNum3;
	}
	public void setLevelQuestionNum3(int levelQuestionNum3) {
		this.levelQuestionNum3 = levelQuestionNum3;
	}
	public Date getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}
	public Date getPublishTime() {
		return publishTime;
	}
	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}
	public Boolean getIsApply() {
		return isApply;
	}
	public void setIsApply(Boolean isApply) {
		this.isApply = isApply;
	}
	public Integer getDisporder() {
		return disporder;
	}
	public void setDisporder(Integer disporder) {
		this.disporder = disporder;
	}
}
