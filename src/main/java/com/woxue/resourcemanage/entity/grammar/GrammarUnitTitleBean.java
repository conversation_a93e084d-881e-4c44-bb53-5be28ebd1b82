package com.woxue.resourcemanage.entity.grammar;

import java.io.Serializable;
import java.util.List;

public class GrammarUnitTitleBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer unitId;
	private String name;
	private Integer parentId;
	private Integer level;
	private boolean isBasic;
	private Integer disporder;
	private List<GrammarUnitTitleBean> titleList;
	private List<GrammarUnitSubtitleBean> subtitleList;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getUnitId() {
		return unitId;
	}
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getParentId() {
		return parentId;
	}
	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public boolean isBasic() {
		return isBasic;
	}
	public void setBasic(boolean isBasic) {
		this.isBasic = isBasic;
	}
	public Integer getDisporder() {
		return disporder;
	}
	public void setDisporder(Integer disporder) {
		this.disporder = disporder;
	}
	public List<GrammarUnitTitleBean> getTitleList() {
		return titleList;
	}
	public void setTitleList(List<GrammarUnitTitleBean> titleList) {
		this.titleList = titleList;
	}
	public List<GrammarUnitSubtitleBean> getSubtitleList() {
		return subtitleList;
	}
	public void setSubtitleList(List<GrammarUnitSubtitleBean> subtitleList) {
		this.subtitleList = subtitleList;
	}
	
}
