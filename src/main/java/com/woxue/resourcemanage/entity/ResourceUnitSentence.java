package com.woxue.resourcemanage.entity;

import java.io.Serializable;

public class ResourceUnitSentence implements Serializable {
    private static final long serialVersionUID = -2679810800894284857L;

    private Integer id;

    private Integer unitId;
    private Integer wordId;
    private Integer level;
    private Integer displayOrder;

    public ResourceUnitSentence() {
    }

    public ResourceUnitSentence(Integer unitId, Integer level,Integer wordId, Integer displayOrder) {
        this.unitId = unitId;
        this.wordId = wordId;
        this.level = level;
        this.displayOrder = displayOrder;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getWordId() {
        return wordId;
    }

    public void setWordId(Integer wordId) {
        this.wordId = wordId;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
