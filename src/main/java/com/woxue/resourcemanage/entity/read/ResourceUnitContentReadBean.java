package com.woxue.resourcemanage.entity.read;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;


/**
 * 单元同步阅读内容
 */
public class ResourceUnitContentReadBean implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private Integer id;
    @ApiModelProperty("文章id")
    private Integer resourceUnitId;
    @ApiModelProperty("主题id")
    private Integer topicId;
    @ApiModelProperty("主题名称")
    private String topicName;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getResourceUnitId() {
        return resourceUnitId;
    }

    public void setResourceUnitId(Integer resourceUnitId) {
        this.resourceUnitId = resourceUnitId;
    }

    public Integer getTopicId() {
        return topicId;
    }

    public void setTopicId(Integer topicId) {
        this.topicId = topicId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
