package com.woxue.resourcemanage.entity;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022 -05-12 13:45
 */
public class ResourceUnit implements Serializable {

    private static final long serialVersionUID = 3265871547151102929L;
    private Integer id;
    private Integer courseId;
    private String nameEn;
    private String  nameCn;
    private Integer contentNum;
    private Integer displayOrder;

    private List<WordEntity> wordList;

    public List<WordEntity> getWordList() {
        return wordList;
    }

    public void setWordList(List<WordEntity> wordList) {
        this.wordList = wordList;
    }

    @Override
    public String toString() {
        return "ResourceUnit{" +
                "id=" + id +
                ", courseId=" + courseId +
                ", nameEn='" + nameEn + '\'' +
                ", nameCn='" + nameCn + '\'' +
                ", contentNum=" + contentNum +
                ", displayOrder=" + displayOrder +
                '}';
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCourseId() {
        return courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public Integer getContentNum() {
        return contentNum;
    }

    public void setContentNum(Integer contentNum) {
        this.contentNum = contentNum;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public ResourceUnit() {
    }

    public ResourceUnit(Integer courseId, String nameEn, String nameCn, Integer contentNum, Integer displayOrder) {
        this.courseId = courseId;
        this.nameEn = nameEn;
        this.nameCn = nameCn;
        this.contentNum = contentNum;
        this.displayOrder = displayOrder;
    }
}
