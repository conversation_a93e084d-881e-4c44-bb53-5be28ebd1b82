package com.woxue.resourcemanage.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(value = "干扰项",description = "干扰项")
public class WordDisturb {
    @ApiModelProperty(value = "单词拼写",example = "apple")
    private String spelling;

    @ApiModelProperty(value = "干扰单词id",example = "1")
    private Integer disturbWordId;

    @ApiModelProperty(value = "干扰单词拼写")
    private String disturbSpelling;

    @ApiModelProperty(value = "干扰单词中文")
    private String disturbMeaning;

    @ApiModelProperty(value = "修改时间")
    private String modifyTime;

    public String getSpelling() {
        return spelling;
    }

    public void setSpelling(String spelling) {
        this.spelling = spelling;
    }

    public Integer getDisturbWordId() {
        return disturbWordId;
    }

    public void setDisturbWordId(Integer disturbWordId) {
        this.disturbWordId = disturbWordId;
    }

    public String getDisturbSpelling() {
        return disturbSpelling;
    }

    public void setDisturbSpelling(String disturbSpelling) {
        this.disturbSpelling = disturbSpelling;
    }

    public String getDisturbMeaning() {
        return disturbMeaning;
    }

    public void setDisturbMeaning(String disturbMeaning) {
        this.disturbMeaning = disturbMeaning;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}
