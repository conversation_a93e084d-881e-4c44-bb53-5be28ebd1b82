package com.woxue.resourcemanage.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022 -05-11 13:30
 */
public class ResourceVersio implements Serializable {

    private static final long serialVersionUID = -318696121369525773L;
    private String nameEn;
    private String nameCn;
    private Integer versionType;        //版本内容类型
    private Integer relationType;   //版本类型
    private Integer stage;
    private Integer price;
    private String briefIntroduction;
    private Integer displayOrder;

    @Override
    public String toString() {
        return "ResourceVersio{" +
                "nameEn='" + nameEn + '\'' +
                ", nameCn='" + nameCn + '\'' +
                ", versionType=" + versionType +
                ", relationType=" + relationType +
                ", stage=" + stage +
                ", price=" + price +
                ", briefIntroduction='" + briefIntroduction + '\'' +
                ", displayOrder=" + displayOrder +
                '}';
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public Integer getVersionType() {
        return versionType;
    }

    public void setVersionType(Integer versionType) {
        this.versionType = versionType;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getBriefIntroduction() {
        return briefIntroduction;
    }

    public void setBriefIntroduction(String briefIntroduction) {
        this.briefIntroduction = briefIntroduction;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public ResourceVersio() {
    }

    public ResourceVersio(String nameEn, String nameCn, Integer versionType, Integer relationType, Integer stage, Integer price, String briefIntroduction, Integer displayOrder) {
        this.nameEn = nameEn;
        this.nameCn = nameCn;
        this.versionType = versionType;
        this.relationType = relationType;
        this.stage = stage;
        this.price = price;
        this.briefIntroduction = briefIntroduction;
        this.displayOrder = displayOrder;
    }
}
