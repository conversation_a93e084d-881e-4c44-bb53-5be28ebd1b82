package com.woxue.resourcemanage.entity;

import com.woxue.common.util.GsonManager;
import com.woxue.common.util.wordUse.WordUseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WordUseDto {
    @ApiModelProperty(value = "单词拼写",required = true)
    private String spelling;
    @ApiModelProperty(value = "例句/句子",required = true)
    private String sentence;
    @ApiModelProperty(value = "单词用词数据",required = true)
    private WordUseBean wordUseBean;
    @ApiModelProperty(value = "单词用词数据Str",required = true)
    private String content;
    @ApiModelProperty(value = "状态",required = true)
    private Integer status;

    public WordUseBean getWordUseBean() {
        if (wordUseBean == null&& content != null){
           return GsonManager.fromJson(content, WordUseBean.class);
        }
        return wordUseBean;
    }
}
