package com.woxue.resourcemanage.entity;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @date 2022 -05-10 15:59
 */
public class AdminBean implements Serializable {

    private static final long serialVersionUID = 4951471547150903929L;
    private String name;
    private String password;
    private Integer role;

    private String token;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPassword() {
        return password;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return "Admin{" +
                "name='" + name + '\'' +
                ", password='" + password + '\'' +
                ", role=" + role +
                ", token='" + token + '\'' +
                '}';
    }

    public AdminBean() {}

    public AdminBean(String name, String password, Integer role, String token) {
        this.name = name;
        this.password = password;
        this.role = role;
        this.token = token;
    }
}
