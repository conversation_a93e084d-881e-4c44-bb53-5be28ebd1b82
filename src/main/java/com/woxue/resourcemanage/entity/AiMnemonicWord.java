package com.woxue.resourcemanage.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;


/**
 * AI词汇助记对象 ai_mnemonic_word
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@ApiModel("AI词汇助记")
public class AiMnemonicWord implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** id */
    private Integer id;

    /** 课程id */
    @ApiModelProperty(name = "courseId",value= "课程id" )
    private Integer courseId;


    /** 单元id */
    @ApiModelProperty(name = "unitId",value= "单元id" )
    private Integer unitId;


    /** 单词id */
    @ApiModelProperty(name = "wordId",value= "单词id" )
    private Integer wordId;


    /** 处理状态：0未处理，1已处理，2忽略 */
    @ApiModelProperty(name = "status",value= "处理状态：0未处理，1已处理，2忽略" )
    private Integer status;
    @ApiModelProperty(name = "likeNum",value= "点赞数量" )
    private Integer likeNum;

    @ApiModelProperty(name = "dislikeNum",value= "点踩数量" )
    private Integer dislikeNum;


    /** 反馈内容 */
    @ApiModelProperty(name = "feedContent",value= "反馈内容" )
    private String feedContent;


    /** 关联内容 */
    @ApiModelProperty(name = "relationContent",value= "关联内容" )
    private String relationContent;


    /** 课程详情 */
    @ApiModelProperty(name = "courseDetails",value= "课程详情" )
    private String courseDetails;


    /** 单元名称 */
    @ApiModelProperty(name = "unitName",value= "单元名称" )
    private String unitName;

    @ApiModelProperty(name = "createTime",value= "创建时间" )
    private Date createTime;
    @ApiModelProperty(name = "updateTime",value= "修改时间" )
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCourseId() {
        return courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getWordId() {
        return wordId;
    }

    public void setWordId(Integer wordId) {
        this.wordId = wordId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFeedContent() {
        return feedContent;
    }

    public void setFeedContent(String feedContent) {
        this.feedContent = feedContent;
    }

    public String getRelationContent() {
        return relationContent;
    }

    public void setRelationContent(String relationContent) {
        this.relationContent = relationContent;
    }

    public String getCourseDetails() {
        return courseDetails;
    }

    public void setCourseDetails(String courseDetails) {
        this.courseDetails = courseDetails;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getDislikeNum() {
        return dislikeNum;
    }

    public void setDislikeNum(Integer dislikeNum) {
        this.dislikeNum = dislikeNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("courseId", getCourseId())
            .append("unitId", getUnitId())
            .append("wordId", getWordId())
            .append("status", getStatus())
            .append("feedContent", getFeedContent())
            .append("relationContent", getRelationContent())
            .append("courseDetails", getCourseDetails())
            .append("unitName", getUnitName())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
