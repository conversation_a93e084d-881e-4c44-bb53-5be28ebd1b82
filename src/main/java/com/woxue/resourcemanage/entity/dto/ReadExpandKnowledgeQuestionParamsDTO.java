package com.woxue.resourcemanage.entity.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 扩展阅读知识重点-试题请求参数DTO
 * <AUTHOR>
 * @date 2024-06-06 15:54
 */
@Data
@Api("扩展阅读知识重点-试题请求参数DTO")
public class ReadExpandKnowledgeQuestionParamsDTO {

    @ApiModelProperty("页码")
    private Integer pageIndex;

    @ApiModelProperty("每页展示数")
    private Integer pageSize;

    @ApiModelProperty("文章id")
    private Integer articleId;

    private Long[] ids;
}
