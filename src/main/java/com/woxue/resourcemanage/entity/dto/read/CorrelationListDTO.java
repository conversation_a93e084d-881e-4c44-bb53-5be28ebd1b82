package com.woxue.resourcemanage.entity.dto.read;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-12 11:07
 */
public class CorrelationListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("句句对应分句-内容列表")
    private List<String> correlationContentList;
    @ApiModelProperty("句句对应分句-翻译列表")
    private List<String> correlationTranslateList;

    public List<String> getCorrelationContentList() {
        return correlationContentList;
    }

    public void setCorrelationContentList(List<String> correlationContentList) {
        this.correlationContentList = correlationContentList;
    }

    public List<String> getCorrelationTranslateList() {
        return correlationTranslateList;
    }

    public void setCorrelationTranslateList(List<String> correlationTranslateList) {
        this.correlationTranslateList = correlationTranslateList;
    }
}
