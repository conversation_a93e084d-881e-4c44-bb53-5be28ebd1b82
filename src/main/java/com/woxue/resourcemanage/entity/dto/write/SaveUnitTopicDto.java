package com.woxue.resourcemanage.entity.dto.write;

import io.swagger.annotations.ApiModelProperty;

public class SaveUnitTopicDto {
    @ApiModelProperty(value = "单元id",required = true)
    private Integer resourceUnitId;
    @ApiModelProperty(value = "主题id",required = true)
    private Integer topicId;
    @ApiModelProperty(value = "是否关联内容",required = true)
    private Boolean related=false;
    @ApiModelProperty(value = "关联的写作方法单元id",required = true)
    private Integer relatedWriteMethodUnitId;

    @ApiModelProperty(value = "关联的句型训练单元id",required = true)
    private Integer relatedSentenceUnitId;

    @ApiModelProperty(value = "关联的范文单元id",required = true)
    private Integer relatedSampleUnitId;

    public Integer getResourceUnitId() {
        return resourceUnitId;
    }

    public void setResourceUnitId(Integer resourceUnitId) {
        this.resourceUnitId = resourceUnitId;
    }

    public Integer getTopicId() {
        return topicId;
    }

    public void setTopicId(Integer topicId) {
        this.topicId = topicId;
    }

    public Boolean getRelated() {
        return related;
    }

    public void setRelated(Boolean related) {
        this.related = related;
    }

    public Integer getRelatedWriteMethodUnitId() {
        return relatedWriteMethodUnitId;
    }

    public void setRelatedWriteMethodUnitId(Integer relatedWriteMethodUnitId) {
        this.relatedWriteMethodUnitId = relatedWriteMethodUnitId;
    }

    public Integer getRelatedSentenceUnitId() {
        return relatedSentenceUnitId;
    }

    public void setRelatedSentenceUnitId(Integer relatedSentenceUnitId) {
        this.relatedSentenceUnitId = relatedSentenceUnitId;
    }

    public Integer getRelatedSampleUnitId() {
        return relatedSampleUnitId;
    }

    public void setRelatedSampleUnitId(Integer relatedSampleUnitId) {
        this.relatedSampleUnitId = relatedSampleUnitId;
    }
}
