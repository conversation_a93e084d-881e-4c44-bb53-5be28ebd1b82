package com.woxue.resourcemanage.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 导入使用，文章DTO
 */
@Data
public class ReadExpandReadArticleDTO {

    @ApiModelProperty("单元名称")
    private String unitName;

    @ApiModelProperty("标识：文章标识填1、阅读训练填2、拔高训练填3")
    private Integer typeId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("单词个数")
    private Integer wordCount;

    @ApiModelProperty("试题难度：1:易、2:中上、3:中、4：中下、5：难")
    private Integer difficulty;

    @ApiModelProperty("年份")
    private String year;

    @ApiModelProperty("序列编码：A B C D")
    private String serialCode;

    @ApiModelProperty("文章类型:记叙文、说明文等")
    private String articleName;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("翻译")
    private String translate;

    @ApiModelProperty("多个单词拼接")
    private String keyWords;

    @ApiModelProperty("主题")
    private String theme;

    @ApiModelProperty("来源")
    private String source;

    //题目选项list
    List<ReadExpandReadArticleQuestionDTO> questionDTOList;

}
