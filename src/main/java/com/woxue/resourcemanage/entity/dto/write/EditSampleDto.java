package com.woxue.resourcemanage.entity.dto.write;

import io.swagger.annotations.ApiModelProperty;

public class EditSampleDto {
    @ApiModelProperty(value = "单元id", required = true)
    private Integer unitId;
    @ApiModelProperty(value = "范文主题", required = true)
    private String sampleTitle;
    @ApiModelProperty(value = "范文内容", required = true)
    private String sampleContent;
    @ApiModelProperty(value = "建议用时", required = true)
    private Integer limitTime;
    @ApiModelProperty(value = "最大用时", required = true)
    private Integer maxTime;
    @ApiModelProperty(value = "范文段落数", required = true)
    private Integer sampleParagraphNum;
    @ApiModelProperty(value = "建议字数", required = true)
    private Integer limitWord;
    @ApiModelProperty(value = "最大字数", required = true)
    private Integer maxWord;
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getSampleTitle() {
        return sampleTitle;
    }

    public void setSampleTitle(String sampleTitle) {
        this.sampleTitle = sampleTitle;
    }

    public String getSampleContent() {
        return sampleContent;
    }

    public void setSampleContent(String sampleContent) {
        this.sampleContent = sampleContent;
    }

    public Integer getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(Integer limitTime) {
        this.limitTime = limitTime;
    }

    public Integer getSampleParagraphNum() {
        return sampleParagraphNum;
    }

    public void setSampleParagraphNum(Integer sampleParagraphNum) {
        this.sampleParagraphNum = sampleParagraphNum;
    }

    public Integer getLimitWord() {
        return limitWord;
    }

    public void setLimitWord(Integer limitWord) {
        this.limitWord = limitWord;
    }

    public Integer getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(Integer maxTime) {
        this.maxTime = maxTime;
    }

    public Integer getMaxWord() {
        return maxWord;
    }

    public void setMaxWord(Integer maxWord) {
        this.maxWord = maxWord;
    }
}
