package com.woxue.resourcemanage.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 导入使用，题目DTO
 */
@Data
public class ReadExpandReadArticleQuestionDTO {

    @ApiModelProperty("标识：文章标识填1、阅读训练填2、拔高训练填3")
    private Integer typeId;

    @ApiModelProperty("题号")
    private Integer number;

    @ApiModelProperty("题目问题")
    private String question;

    @ApiModelProperty("题干译文")
    private String questionTranslation;

    @ApiModelProperty("四个选项组合")
    private String optionABCD;

    @ApiModelProperty("四项译文组合")
    private String optionABCDTranslation;

    @ApiModelProperty("正确回答")
    private String answer;

}
