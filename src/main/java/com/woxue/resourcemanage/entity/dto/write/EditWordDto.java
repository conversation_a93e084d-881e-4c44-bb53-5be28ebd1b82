package com.woxue.resourcemanage.entity.dto.write;

import io.swagger.annotations.ApiModelProperty;

public class EditWordDto {
    @ApiModelProperty(value = "单元id", required = true)
    Integer unitId;
    @ApiModelProperty(value = "词汇", required = true)
    String words;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getWords() {
        return words;
    }

    public void setWords(String words) {
        this.words = words;
    }
}
