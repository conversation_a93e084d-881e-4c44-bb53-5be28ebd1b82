package com.woxue.resourcemanage.entity.dto.read;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 单元主题保存
 * <AUTHOR>
 * @date 2024-07-22 14:04
 */
@Data
public class ResourceUnitTopicIdPostDTO {

    @ApiModelProperty("单元id")
    private Integer unitId;
    @ApiModelProperty("主题id")
    private Integer topicId;
    @ApiModelProperty("复制关联的阅读文章列表(高中录入时，忽略)")
    private List<Integer> articleIdList;

}
