package com.woxue.resourcemanage.entity.dto.listen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * 同步听力文章句子对象 resource_listen_sentence
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@ApiModel(value = " 同步听力单元句子",description = " 同步听力单元句子")
public class ResourceListenSentenceImport implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 单元id */
    @ApiModelProperty(value = "单元名称", example = "")
    private String unitName;

    /** 句子英文 */
    @ApiModelProperty(value = "句子英文", example = "")
    private String sentenceEnUs;

    /** 句子翻译 */
    @ApiModelProperty(value = "句子翻译", example = "")
    private String sentenceZhCn;

    /** 对话人名 */
    @ApiModelProperty(value = "对话人名", example = "")
    private String speaker;

    /** 声音文件 */
    @ApiModelProperty(value = "声音文件", example = "")
    private String soundFile;

    @ApiModelProperty(value = "听文填词", example = "name|good#hello|hi")
    private String fillWordList;


    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getFillWordList() {
        return fillWordList;
    }

    public void setFillWordList(String fillWordList) {
        this.fillWordList = fillWordList;
    }


    public void setSentenceEnUs(String sentenceEnUs)
    {
        this.sentenceEnUs = sentenceEnUs;
    }

    public String getSentenceEnUs() 
    {
        return sentenceEnUs;
    }
    public void setSentenceZhCn(String sentenceZhCn) 
    {
        this.sentenceZhCn = sentenceZhCn;
    }

    public String getSentenceZhCn() 
    {
        return sentenceZhCn;
    }
    public void setSpeaker(String speaker) 
    {
        this.speaker = speaker;
    }

    public String getSpeaker() 
    {
        return speaker;
    }
    public void setSoundFile(String soundFile) 
    {
        this.soundFile = soundFile;
    }

    public String getSoundFile() 
    {
        return soundFile;
    }


}
