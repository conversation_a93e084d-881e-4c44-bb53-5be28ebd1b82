package com.woxue.resourcemanage.entity.dto.read;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 请求AI生成文章接口传参
 * <AUTHOR>
 * @date 2024-07-23 10:37
 */
@Data
public class ReadCorrelationDTO {

    @ApiModelProperty("文章内容")
    private String content;
    @ApiModelProperty("文章内容list")
    private List<String> contentList;

    @ApiModelProperty("文章翻译")
    private String translation;
    @ApiModelProperty("文章翻译list")
    private List<String> translationList;

}
