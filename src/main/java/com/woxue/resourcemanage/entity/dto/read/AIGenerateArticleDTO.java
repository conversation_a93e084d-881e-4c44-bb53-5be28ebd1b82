package com.woxue.resourcemanage.entity.dto.read;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 请求AI生成文章接口传参
 * <AUTHOR>
 * @date 2024-07-23 10:37
 */
@Data
public class AIGenerateArticleDTO {
    @ApiModelProperty("单元id")
    private Integer unitId;
    @ApiModelProperty("主题名称")
    private String topicName;
    @ApiModelProperty("参考单词数组")
    private String[] wordList;
    @ApiModelProperty("生成文章的单词个数")
    private Integer wordCount;
    @ApiModelProperty("提示词")
    private String requireText;
    @ApiModelProperty("参考文章（AI全文解析、AI生成图片传此参数）")
    private String content;

}
