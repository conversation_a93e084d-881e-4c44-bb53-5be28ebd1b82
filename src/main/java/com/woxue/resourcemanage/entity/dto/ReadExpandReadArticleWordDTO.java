package com.woxue.resourcemanage.entity.dto;


import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ReadExpandReadArticleWordDTO对象", description = "重点单词DTO")
public class ReadExpandReadArticleWordDTO {

    @ApiModelProperty("wordBeanList")
    List<ReadExpandReadArticleWordBean> wordBeanList;
}
