package com.woxue.resourcemanage.entity.dto.read;

import com.woxue.ai.model.AnalysisResponse;
import com.woxue.common.model.LetterDirectionEnum;
import com.woxue.common.model.redBook.read.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-12 10:50
 */
@ApiModel(
        description = "同步阅读-文章和相关子类信息"
)
public class ResourceReadArticleNewDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("文章id")
    private Integer articleId;
    @ApiModelProperty("版本id")
    private Integer versionId;
    @ApiModelProperty("课程id")
    private Integer courseId;
    @ApiModelProperty("单元id")
    private Integer unitId;
    @ApiModelProperty("文章类型:应用文 PRACTICAL_ESSAY,记叙文 NARRATIVE_ESSAY、说明文 EXPOSITORY_ESSAY、议论文 EXPOSITORY_TALK")
    private String articleType;
    @ApiModelProperty("序列编码：A B C D E")
    private String serialCode;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty("翻译")
    private String translate;
    @ApiModelProperty("配图")
    private String photo;
    @ApiModelProperty("试题难度：1:易、2:中、3:难")
    private Integer difficulty;
    @ApiModelProperty("单词个数")
    private Integer wordCount;
    @ApiModelProperty("建议用时：单位分")
    private Integer time;
    @ApiModelProperty("选句-句子")
    private String sentenceList;
    @ApiModelProperty("选句-干扰项")
    private String sentenceDisturbance;
    @ApiModelProperty("选句-文章内容")
    private String sentenceContent;
    @ApiModelProperty("全文解析")
    private String fullTextParse;
    @ApiModelProperty("AI生成文章的参考词")
    private String aiGenWord;
    @ApiModelProperty("AI生成文章的参考词列表")
    private List<String> aiGenWordList;
    @ApiModelProperty("句句对应分句-内容列表")
    private List<String> correlationContentList;
    @ApiModelProperty("句句对应分句-翻译列表")
    private List<String> correlationTranslateList;

    @ApiModelProperty("文章标题")
    private String articleTitle;
    @ApiModelProperty("文章标题翻译")
    private String titleTranslation;
    @ApiModelProperty("书信署名-中文")
    private String letterSignatureCn;
    @ApiModelProperty("书信署名-英文")
    private String letterSignatureEn;
    @ApiModelProperty("书信署名方位：左LEFT、右RIGHT、中CENTRE")
    private LetterDirectionEnum letterDirection;
    @ApiModelProperty("全文解析-实体类")
    private AnalysisResponse analysisResponse;
    @ApiModelProperty("初中-句句对应bean")
    private ResourceReadArticleCorrelationBean correlationBean;
    @ApiModelProperty("初高中-问题beanList")
    private List<ResourceReadArticleQuestionBean> questionBeanList;
    @ApiModelProperty("初中-选词填空bean")
    private ResourceReadArticleSelectWordBean selectWordBean;
    @ApiModelProperty("高中-多个难词")
    private List<ResourceReadArticleHighWordBean> highWordBeanList;
    @ApiModelProperty("高中-多个难句")
    private List<ResourceReadArticleHighSentenceBean> highSentenceBeanList;

    //导入添加参数
    @ApiModelProperty("单元名称")
    private String unitName;
    @ApiModelProperty("多个难词")
    private String keyWords;
    @ApiModelProperty("多个难句")
    private String keySentences;
    @ApiModelProperty("多个选句")
    private String chooseSentences;

    public ResourceReadArticleNewDTO() {
    }


    public String getChooseSentences() {
        return chooseSentences;
    }

    public void setChooseSentences(String chooseSentences) {
        this.chooseSentences = chooseSentences;
    }

    public String getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
    }

    public String getKeySentences() {
        return keySentences;
    }

    public void setKeySentences(String keySentences) {
        this.keySentences = keySentences;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public List<String> getAiGenWordList() {
        return aiGenWordList;
    }

    public void setAiGenWordList(List<String> aiGenWordList) {
        this.aiGenWordList = aiGenWordList;
    }

    public List<String> getCorrelationContentList() {
        return correlationContentList;
    }

    public void setCorrelationContentList(List<String> correlationContentList) {
        this.correlationContentList = correlationContentList;
    }

    public List<String> getCorrelationTranslateList() {
        return correlationTranslateList;
    }

    public void setCorrelationTranslateList(List<String> correlationTranslateList) {
        this.correlationTranslateList = correlationTranslateList;
    }

    public String getAiGenWord() {
        return aiGenWord;
    }

    public void setAiGenWord(String aiGenWord) {
        this.aiGenWord = aiGenWord;
    }

    public String getLetterSignatureCn() {
        return this.letterSignatureCn;
    }

    public void setLetterSignatureCn(String letterSignatureCn) {
        this.letterSignatureCn = letterSignatureCn;
    }

    public String getLetterSignatureEn() {
        return this.letterSignatureEn;
    }

    public void setLetterSignatureEn(String letterSignatureEn) {
        this.letterSignatureEn = letterSignatureEn;
    }

    public LetterDirectionEnum getLetterDirection() {
        return this.letterDirection;
    }

    public void setLetterDirection(LetterDirectionEnum letterDirection) {
        this.letterDirection = letterDirection;
    }

    public String getArticleTitle() {
        return this.articleTitle;
    }

    public void setArticleTitle(String articleTitle) {
        this.articleTitle = articleTitle;
    }

    public String getTitleTranslation() {
        return this.titleTranslation;
    }

    public void setTitleTranslation(String titleTranslation) {
        this.titleTranslation = titleTranslation;
    }

    public AnalysisResponse getAnalysisResponse() {
        return this.analysisResponse;
    }

    public void setAnalysisResponse(AnalysisResponse analysisResponse) {
        this.analysisResponse = analysisResponse;
    }

    public String getSentenceContent() {
        return this.sentenceContent;
    }

    public void setSentenceContent(String sentenceContent) {
        this.sentenceContent = sentenceContent;
    }

    public String getFullTextParse() {
        return this.fullTextParse;
    }

    public void setFullTextParse(String fullTextParse) {
        this.fullTextParse = fullTextParse;
    }

    public Integer getArticleId() {
        return this.articleId;
    }

    public void setArticleId(Integer articleId) {
        this.articleId = articleId;
    }

    public Integer getVersionId() {
        return this.versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Integer getCourseId() {
        return this.courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getArticleType() {
        return this.articleType;
    }

    public void setArticleType(String articleType) {
        this.articleType = articleType;
    }

    public String getSerialCode() {
        return this.serialCode;
    }

    public void setSerialCode(String serialCode) {
        this.serialCode = serialCode;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTranslate() {
        return this.translate;
    }

    public void setTranslate(String translate) {
        this.translate = translate;
    }

    public String getPhoto() {
        return this.photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public Integer getDifficulty() {
        return this.difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public Integer getWordCount() {
        return this.wordCount;
    }

    public void setWordCount(Integer wordCount) {
        this.wordCount = wordCount;
    }

    public Integer getTime() {
        return this.time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }

    public String getSentenceList() {
        return this.sentenceList;
    }

    public void setSentenceList(String sentenceList) {
        this.sentenceList = sentenceList;
    }

    public String getSentenceDisturbance() {
        return this.sentenceDisturbance;
    }

    public void setSentenceDisturbance(String sentenceDisturbance) {
        this.sentenceDisturbance = sentenceDisturbance;
    }

    public ResourceReadArticleCorrelationBean getCorrelationBean() {
        return this.correlationBean;
    }

    public void setCorrelationBean(ResourceReadArticleCorrelationBean correlationBean) {
        this.correlationBean = correlationBean;
    }

    public List<ResourceReadArticleQuestionBean> getQuestionBeanList() {
        return this.questionBeanList;
    }

    public void setQuestionBeanList(List<ResourceReadArticleQuestionBean> questionBeanList) {
        this.questionBeanList = questionBeanList;
    }

    public ResourceReadArticleSelectWordBean getSelectWordBean() {
        return this.selectWordBean;
    }

    public void setSelectWordBean(ResourceReadArticleSelectWordBean selectWordBean) {
        this.selectWordBean = selectWordBean;
    }

    public List<ResourceReadArticleHighWordBean> getHighWordBeanList() {
        return this.highWordBeanList;
    }

    public void setHighWordBeanList(List<ResourceReadArticleHighWordBean> highWordBeanList) {
        this.highWordBeanList = highWordBeanList;
    }

    public List<ResourceReadArticleHighSentenceBean> getHighSentenceBeanList() {
        return this.highSentenceBeanList;
    }

    public void setHighSentenceBeanList(List<ResourceReadArticleHighSentenceBean> highSentenceBeanList) {
        this.highSentenceBeanList = highSentenceBeanList;
    }

}
