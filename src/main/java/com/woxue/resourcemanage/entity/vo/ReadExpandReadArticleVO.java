package com.woxue.resourcemanage.entity.vo;


import com.woxue.common.model.LetterDirectionEnum;
import com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean;
import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean;
import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean;
import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.time.Year;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 扩展阅读-文章
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Getter
@Setter
@ApiModel(value = "ReadExpandReadArticle对象", description = "扩展阅读-文章")
public class ReadExpandReadArticleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文章id")
    private Integer articleId;

    @ApiModelProperty("版本id")
    private Integer versionId;

    @ApiModelProperty("课程id")
    private Integer courseId;

    @ApiModelProperty("单元id")
    private Integer unitId;

    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("标题翻译")
    private String titleTranslation;

    @ApiModelProperty("年份")
    private String year;

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("序列编码：A B C D")
    private String serialCode;

    @ApiModelProperty("文章类型:应用文 PRACTICAL_ESSAY,记叙文 NARRATIVE_ESSAY、说明文 EXPOSITORY_ESSAY")
    private String articleType;

    @ApiModelProperty("主题")
    private String theme;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("翻译")
    private String translate;

    @ApiModelProperty("单词个数")
    private Integer wordCount;

    @ApiModelProperty("建议用时：单位秒")
    private Integer time;

    @ApiModelProperty("年级：1 2 3")
    private Integer grade;

    @ApiModelProperty("试题难度：1:易、2:中上、3:中、4：中下、5：难")
    private Integer difficulty;

    @ApiModelProperty("书信署名-中文")
    private String letterSignatureCn;
    @ApiModelProperty("书信署名-英文")
    private String letterSignatureEn;
    @ApiModelProperty("书信署名方位：左LEFT、右RIGHT、中CENTRE")
    private LetterDirectionEnum letterDirection;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("基础训练")
    List<ReadExpandReadArticleQuestionBean> basicsQuestionBeanList;
    @ApiModelProperty("拔高训练")
    List<ReadExpandReadArticleQuestionBean> raiseQuestionBeanList;
    @ApiModelProperty("重点单词")
    List<ReadExpandReadArticleWordBean> wordBeanList;
    @ApiModelProperty("句句对应")
    ReadExpandReadArticleCorrelationBean correlationBean;
    @ApiModelProperty("知识重点")
    List<ReadExpandKnowledgeQuestionBean> knowledgeQuestionBeanList;


}
