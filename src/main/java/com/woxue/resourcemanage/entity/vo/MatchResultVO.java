package com.woxue.resourcemanage.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "匹配结果",description = "匹配结果")
public class MatchResultVO {
    @ApiModelProperty(value = "共有多少条需要匹配")
    private int count=0;
    @ApiModelProperty(value = "匹配成功数量")
    private int matchCount=0;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getMatchCount() {
        return matchCount;
    }

    public void setMatchCount(int matchCount) {
        this.matchCount = matchCount;
    }
}
