package com.woxue.resourcemanage.entity.vo;

import com.woxue.resourcemanage.entity.WordDisturb;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "干扰项结果列表",description = "干扰项结果列表")
public class WordDisturbListVO {
    @ApiModelProperty(value = "共有多少条")
    private Integer count=0;
    @ApiModelProperty(value = "有干扰项单词列表")
    private List<WordDisturb> list = new ArrayList<>();

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<WordDisturb> getList() {
        return list;
    }

    public void setList(List<WordDisturb> list) {
        this.list = list;
    }

    public void addList(WordDisturb wordDisturb) {
        this.list.add(wordDisturb);
    }


}
