package com.woxue.resourcemanage.entity.vo.read;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-07-20 17:26
 */
@Data
@ApiModel(value = "同步阅读-主题关联的阅读文章list",description = "同步阅读-主题关联的阅读文章list")
public class TopicUnitArticleListVO {

    @ApiModelProperty("版本课程单元名")
    private String versionCourseUnitName;

    @ApiModelProperty("单元id")
    private Integer unitId;


    @ApiModelProperty("文章信息")
    private List<ArticleVO> articleVOList;

    @Data
    public static class ArticleVO{
        @ApiModelProperty("文章id")
        private Integer articleId;
        @ApiModelProperty("文章内容")
        private String content;
        @ApiModelProperty("序列编码：A B C D")
        private String serialCode;
    }


}
