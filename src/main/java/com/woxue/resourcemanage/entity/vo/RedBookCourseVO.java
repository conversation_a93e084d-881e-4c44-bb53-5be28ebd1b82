package com.woxue.resourcemanage.entity.vo;

import com.woxue.common.model.redBook.RedBookCourse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel(value = "课程",description = "课程")
public class RedBookCourseVO extends RedBookCourse {

    @ApiModelProperty(value = "发布状态 1发布 0未发布 -1版本未发布（版本未发布课程不能发布）")
    private Integer publishStatus = 0;

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }
}
