package com.woxue.resourcemanage.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class ResourceTopic implements Serializable {
    /**
     *   主键
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     *   主题名称
     */
    @ApiModelProperty("主题名称")
    private String name;

    /**
     *   类型 1：阅读 2：写作
     */
    @ApiModelProperty("类型 1：阅读 2：写作 3 听力")
    private String type;


    private static final long serialVersionUID = 1L;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        String sb = getClass().getSimpleName() +
                " [" +
                "Hash = " + hashCode() +
                ", id=" + id +
                ", name=" + name +
                ", type=" + type +
                ", serialVersionUID=" + serialVersionUID +
                "]";
        return sb;
    }
}