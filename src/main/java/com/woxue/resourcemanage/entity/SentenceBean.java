package com.woxue.resourcemanage.entity;

import com.woxue.common.model.redBook.RedBookArticleSentenceShowType;

import java.io.Serializable;

public class SentenceBean implements Serializable {
    private Integer id;
    private Integer articleId;
    private String sentence_en_US;
    private String sentence_zh_CN;
    private String speaker;
    private String soundFile;
    private RedBookArticleSentenceShowType showType;
    private String url;
    private Integer displayOrder;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getArticleId() {
        return articleId;
    }

    public void setArticleId(Integer articleId) {
        this.articleId = articleId;
    }

    public String getSentence_en_US() {
        return sentence_en_US;
    }

    public void setSentence_en_US(String sentence_en_US) {
        this.sentence_en_US = sentence_en_US;
    }

    public String getSentence_zh_CN() {
        return sentence_zh_CN;
    }

    public void setSentence_zh_CN(String sentence_zh_CN) {
        this.sentence_zh_CN = sentence_zh_CN;
    }

    public String getSpeaker() {
        return speaker;
    }

    public void setSpeaker(String speaker) {
        this.speaker = speaker;
    }

    public String getSoundFile() {
        return soundFile;
    }

    public void setSoundFile(String soundFile) {
        this.soundFile = soundFile;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public RedBookArticleSentenceShowType getShowType() {
        return showType;
    }

    public void setShowType(RedBookArticleSentenceShowType showType) {
        this.showType = showType;
    }
}
