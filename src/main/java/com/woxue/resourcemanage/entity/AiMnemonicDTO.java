package com.woxue.resourcemanage.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-12 17:00
 */
public class AiMnemonicDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(name = "moduleType",value= "模块类型：resourceWord 助记、word AI记法、sentence AI句子学法、write AI写作辅导、article AI背法" )
    private  String moduleType;

    /** 课程id */
    @ApiModelProperty(name = "courseId",value= "课程id" )
    private Integer courseId;


    /** 单元id */
    @ApiModelProperty(name = "unitId",value= "单元id" )
    private Integer unitId;


    /** 课文id */
    @ApiModelProperty(name = "articleId",value= "课文id" )
    private Integer articleId;


    /** 句子id */
    @ApiModelProperty(name = "sentenceId",value= "句子id" )
    private Integer sentenceId;

    /** 单词id */
    @ApiModelProperty(name = "wordId",value= "单词id" )
    private Integer wordId;

    @ApiModelProperty(name = "likeNum",value= "点赞数量" )
    private Integer likeNum=0;

    @ApiModelProperty(name = "dislikeNum",value= "点踩数量" )
    private Integer dislikeNum=0;


    /** 反馈内容 */
    @ApiModelProperty(name = "feedContent",value= "反馈内容" )
    private String feedContent;


    /** 关联内容
     * 包含课程、单元、课文、句子、单词内容
     * */
    @ApiModelProperty(name = "relationContent",value= "关联内容" )
    private String relationContent;


    /** 课程详情 */
    @ApiModelProperty(name = "courseDetails",value= "课程详情" )
    private String courseDetails;


    /** 单元名称 */
    @ApiModelProperty(name = "unitName",value= "单元名称（忽略传值）" )
    private String unitName;
    @ApiModelProperty(name = "gradeName",value= "所属年级（忽略传值）" )
    private String gradeName;

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }


    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public Integer getCourseId() {
        return courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getArticleId() {
        return articleId;
    }

    public void setArticleId(Integer articleId) {
        this.articleId = articleId;
    }

    public Integer getSentenceId() {
        return sentenceId;
    }

    public void setSentenceId(Integer sentenceId) {
        this.sentenceId = sentenceId;
    }

    public Integer getWordId() {
        return wordId;
    }

    public void setWordId(Integer wordId) {
        this.wordId = wordId;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getDislikeNum() {
        return dislikeNum;
    }

    public void setDislikeNum(Integer dislikeNum) {
        this.dislikeNum = dislikeNum;
    }

    public String getFeedContent() {
        return feedContent;
    }

    public void setFeedContent(String feedContent) {
        this.feedContent = feedContent;
    }

    public String getRelationContent() {
        return relationContent;
    }

    public void setRelationContent(String relationContent) {
        this.relationContent = relationContent;
    }

    public String getCourseDetails() {
        return courseDetails;
    }

    public void setCourseDetails(String courseDetails) {
        this.courseDetails = courseDetails;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Override
    public String toString() {
        return "AiMnemonicDTO{" +
                "moduleType='" + moduleType + '\'' +
                ", courseId=" + courseId +
                ", unitId=" + unitId +
                ", articleId=" + articleId +
                ", sentenceId=" + sentenceId +
                ", wordId=" + wordId +
                ", likeNum=" + likeNum +
                ", dislikeNum=" + dislikeNum +
                ", feedContent='" + feedContent + '\'' +
                ", relationContent='" + relationContent + '\'' +
                ", courseDetails='" + courseDetails + '\'' +
                ", unitName='" + unitName + '\'' +
                '}';
    }
}
