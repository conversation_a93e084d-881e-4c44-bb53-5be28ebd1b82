package com.woxue.resourcemanage.entity;

import java.io.Serializable;

/**
 *<AUTHOR>
 *@date 2022 -05-13 09:03
 */
public class ResourceUnitWord implements Serializable {
    private static final long serialVersionUID = -2679810800894284857L;

    private Integer unitWordId;

    private Integer unitId;

    private Integer level;

    private Integer wordId;

    private Integer displayOrder;


    public Integer getUnitWordId() {
        return unitWordId;
    }

    public void setUnitWordId(Integer unitWordId) {
        this.unitWordId = unitWordId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getWordId() {
        return wordId;
    }

    public void setWordId(Integer wordId) {
        this.wordId = wordId;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    @Override
    public String toString() {
        return "ResourceUnitWord{" +
                "unitWordId=" + unitWordId +
                ", unitId=" + unitId +
                ", level=" + level +
                ", wordId=" + wordId +
                ", displayOrder=" + displayOrder +
                '}';
    }

    public ResourceUnitWord(Integer unitId, Integer level, Integer wordId, Integer displayOrder) {
        this.unitId = unitId;
        this.level = level;
        this.wordId = wordId;
        this.displayOrder = displayOrder;
    }

    public ResourceUnitWord() {
        super();
    }
}
