package com.woxue.resourcemanage.entity.write;

public class TopicRelatedUnitBean {
    private Integer resourceVersionId;
    private String resourceVersionNameCn;
    private Integer resourceCourseId;
    private String resourceCourseNameCn;
    private Integer resourceUnitId;
    private String resourceUnitName;

    public Integer getResourceVersionId() {
        return resourceVersionId;
    }

    public void setResourceVersionId(Integer resourceVersionId) {
        this.resourceVersionId = resourceVersionId;
    }

    public String getResourceVersionNameCn() {
        return resourceVersionNameCn;
    }

    public void setResourceVersionNameCn(String resourceVersionNameCn) {
        this.resourceVersionNameCn = resourceVersionNameCn;
    }

    public Integer getResourceCourseId() {
        return resourceCourseId;
    }

    public void setResourceCourseId(Integer resourceCourseId) {
        this.resourceCourseId = resourceCourseId;
    }

    public String getResourceCourseNameCn() {
        return resourceCourseNameCn;
    }

    public void setResourceCourseNameCn(String resourceCourseNameCn) {
        this.resourceCourseNameCn = resourceCourseNameCn;
    }

    public Integer getResourceUnitId() {
        return resourceUnitId;
    }

    public void setResourceUnitId(Integer resourceUnitId) {
        this.resourceUnitId = resourceUnitId;
    }

    public String getResourceUnitName() {
        return resourceUnitName;
    }

    public void setResourceUnitName(String resourceUnitName) {
        this.resourceUnitName = resourceUnitName;
    }
}
