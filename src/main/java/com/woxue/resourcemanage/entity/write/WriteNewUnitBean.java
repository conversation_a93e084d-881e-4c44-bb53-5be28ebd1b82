package com.woxue.resourcemanage.entity.write;

import com.woxue.common.model.redBook.WriteUnitContentBean;
import com.woxue.common.model.redBook.WriteUnitTitleBean;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class WriteNewUnitBean {
    private Integer id;
    private String teachId;
    private String name;
    private String contentOrder;
    private Integer courseId;
    private int inteNum;
    private Date modifyTime;
    private Integer disporder;
    private int questionNum;
    private int questionNum1;
    private int questionNum2;
    private int questionNum3;
    private Integer questionDifficulty;
    List<WriteUnitTitleBean> titleList;
    private List<WriteUnitContentBean> writeUnitContentBeanList;
    private List<WriteUnitContentBean> targetContentList;
    private List<WriteUnitContentBean> summaryContentList;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTeachId() {
        return teachId;
    }

    public void setTeachId(String teachId) {
        this.teachId = teachId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContentOrder() {
        return contentOrder;
    }

    public void setContentOrder(String contentOrder) {
        this.contentOrder = contentOrder;
    }

    public Integer getCourseId() {
        return courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public int getInteNum() {
        return inteNum;
    }

    public void setInteNum(int inteNum) {
        this.inteNum = inteNum;
    }

    public Integer getDisporder() {
        return disporder;
    }

    public void setDisporder(Integer disporder) {
        this.disporder = disporder;
    }

    public int getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(int questionNum) {
        this.questionNum = questionNum;
    }

    public int getQuestionNum1() {
        return questionNum1;
    }

    public void setQuestionNum1(int questionNum1) {
        this.questionNum1 = questionNum1;
    }

    public int getQuestionNum2() {
        return questionNum2;
    }

    public void setQuestionNum2(int questionNum2) {
        this.questionNum2 = questionNum2;
    }

    public int getQuestionNum3() {
        return questionNum3;
    }

    public void setQuestionNum3(int questionNum3) {
        this.questionNum3 = questionNum3;
    }

    public Integer getQuestionDifficulty() {
        return questionDifficulty;
    }

    public void setQuestionDifficulty(Integer questionDifficulty) {
        this.questionDifficulty = questionDifficulty;
    }

    public List<WriteUnitTitleBean> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<WriteUnitTitleBean> titleList) {
        this.titleList = titleList;
    }

    public List<WriteUnitContentBean> getWriteContentList() {
        return writeUnitContentBeanList;
    }

    public void setWriteContentList(List<WriteUnitContentBean> writeUnitContentBeanList) {
        this.writeUnitContentBeanList = writeUnitContentBeanList;
    }

    public List<WriteUnitContentBean> getTargetContentList() {
        return targetContentList;
    }

    public void setTargetContentList(List<WriteUnitContentBean> targetContentList) {
        this.targetContentList = targetContentList;
    }

    public List<WriteUnitContentBean> getSummaryContentList() {
        return summaryContentList;
    }

    public void setSummaryContentList(List<WriteUnitContentBean> summaryContentList) {
        this.summaryContentList = summaryContentList;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
