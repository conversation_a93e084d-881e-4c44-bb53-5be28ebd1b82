package com.woxue.resourcemanage.job;

import com.woxue.common.model.WordBean;
import com.woxue.common.util.GsonManager;
import com.woxue.common.util.MD5;
import com.woxue.redbookresource.service.IRedBookCourseService;
import com.woxue.resourcemanage.entity.AiMnemonicDTO;
import com.woxue.resourcemanage.service.AiGenerateService;
import com.woxue.resourcemanage.service.JobService;
import com.woxue.resourceservice.util.RedisManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class AiMnemonicJob {


    @Autowired
    JobService jobService;

    /**
     * 定时任务
     * 每天1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void aiMnemonicJob() {
        jobService.aiMnemonicJob();
    }



}
