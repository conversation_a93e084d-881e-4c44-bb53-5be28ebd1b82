package com.woxue.resourcemanage.filter;


import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.util.GsonManager;
import com.woxue.common.util.UtilControl;
import com.woxue.resourcemanage.dao.IAdminDao;
import com.woxue.resourcemanage.entity.AdminBean;
import com.woxue.resourcemanage.util.AdminManager;
import com.woxue.resourcemanage.util.SysUserUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class MonitorFilter implements Filter {
    List<String> excludesType = Arrays.asList("html","jpg", "css", "js", "png", "gif", "swf", "mp3", "ico",
            "jpeg", "woff", "woff2", "ttf", "eot", "svg", "otf", "map", "flv", "xml", "txt", "zip", "apk", "jsp");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":"
                + request.getServerPort() + path;
        String urlKey = request.getRequestURI();
        String requestType = (urlKey.indexOf(".") > 0) ? urlKey.substring(urlKey.lastIndexOf(".") + 1) : "";

        if (this.excludesType.contains(requestType.toLowerCase())) {
            chain.doFilter(request, response);
            return;
        }
        //swagger
        if (request.getRequestURI().endsWith("/v2/api-docs") ||
                request.getRequestURI().endsWith("/swagger-resources") ||
                request.getRequestURI().endsWith("/swagger-resources/configuration/security") ||
                request.getRequestURI().endsWith("/swagger-resources/configuration/ui") ) {
            chain.doFilter(request, response);
            return;
        }

        //登陆的请求过滤掉
        if (request.getRequestURI().contains("/login")) {
//            if (true) {
            chain.doFilter(request, response);
            return;
        }
        //上传的请求过滤掉
        if (request.getRequestURI().contains("textUpload") || request.getRequestURI().contains("uploadImage")|| request.getRequestURI().contains("audioUploadByPaste")) {
            chain.doFilter(request, response);
            return;
        }

        AdminBean adminBean = SysUserUtils.getAdminBean(request);
        if(adminBean==null||adminBean.getToken()==null){
            try {
                AdminManager.delToken(adminBean.getName());
            } catch (Exception e) {
            }
            response.setStatus(403);
            return;
        }else{
            request.setAttribute("userName", adminBean.getName());
            chain.doFilter(request, response);
        }

        //记录操作记录
        SpringActionSupport.getSpringBean("adminDao", null, IAdminDao.class)
                .insertOperationRecord(adminBean.getName(), UtilControl.getIpAddress(request),urlKey,request.getMethod(),GsonManager.toJson(request.getParameterMap()));

    }

    @Override
    public void destroy() {
    }


}
