<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceReadArticleHighWordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.read.ResourceReadArticleHighWordBean">
        <id column="word_id" property="wordId" />
        <result column="article_id" property="articleId" />
        <result column="spelling" property="spelling" />
        <result column="syllable" property="syllable" />
        <result column="meaning_zh_CN" property="meaningZhCN" />
        <result column="sort" property="sort" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="listByArticleId" resultMap="BaseResultMap">
        select word_id,article_id,spelling,syllable,meaning_zh_CN,sort,update_time
        from resource_read_article_high_word
        where article_id = #{articleId}
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select word_id,article_id,spelling,syllable,meaning_zh_CN,sort,update_time
        from resource_read_article_high_word
        where correlation_id = #{correlationId}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleHighWordBean"
            useGeneratedKeys="true"  keyProperty="correlationId">
        insert into resource_read_article_high_word (article_id,spelling,syllable,meaning_zh_CN,sort)
        values (#{articleId},#{spelling},#{syllable},#{meaningZhCN},#{sort})
    </insert>

    <insert id="batchSave" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleHighWordBean">
        insert into resource_read_article_high_word (article_id,spelling,syllable,meaning_zh_CN,sort)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.articleId},#{item.spelling},#{item.syllable},#{item.meaningZhCN},#{item.sort})
        </foreach>
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleHighWordBean">
        update resource_read_article_high_word
        <set>
            <if test="spelling != null">
                spelling=#{spelling},
            </if>
            <if test="syllable != null and syllable != ''">
                syllable=#{syllable},
            </if>
            <if test="meaningZhCN != null and meaningZhCN != ''">
                meaning_zh_CN=#{meaningZhCN},
            </if>
            <if test="sort != null">
                sort=#{sort},
            </if>
        </set>
        where word_id = #{wordId}
    </update>

    <delete id="delete">
        delete from resource_read_article_high_word where word_id = #{wordId}
    </delete>

    <delete id="deleteByArticleId">
        delete from resource_read_article_high_word where article_id = #{articleId}
    </delete>
</mapper>
