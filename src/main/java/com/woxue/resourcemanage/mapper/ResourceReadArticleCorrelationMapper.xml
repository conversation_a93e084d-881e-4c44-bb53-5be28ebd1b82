<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceReadArticleCorrelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.read.ResourceReadArticleCorrelationBean">
        <id column="correlation_id" property="correlationId" />
        <result column="article_id" property="articleId" />
        <result column="content" property="content" />
        <result column="translate" property="translate" />
        <result column="content_list" property="contentList" />
        <result column="translate_list" property="translateList" />
        <result column="update_time" property="updateTime" />
    </resultMap>


    <select id="edit" resultMap="BaseResultMap">
        select correlation_id,article_id,content,translate,content_list,translate_list,update_time
        from resource_read_article_correlation
        where correlation_id = #{correlationId}
    </select>

    <select id="editByArticleId" resultMap="BaseResultMap">
        select correlation_id,article_id,content,translate,content_list,translate_list,update_time
        from resource_read_article_correlation
        where article_id = #{articleId}
    </select>

    <insert id="save" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleCorrelationBean"
            useGeneratedKeys="true"  keyProperty="correlationId">
        insert into resource_read_article_correlation (article_id,content,translate,content_list,translate_list)
        values (#{articleId},#{content},#{translate},#{contentList},#{translateList})
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleCorrelationBean">
        update resource_read_article_correlation
        <set>
            <if test="articleId != null">
                article_id=#{articleId},
            </if>
            <if test="content != null and content != ''">
                content=#{content},
            </if>
            <if test="translate != null and translate != ''">
                translate=#{translate},
            </if>
            <if test="contentList != null and contentList != ''">
                content_list=#{contentList},
            </if>
            <if test="translateList != null and translateList != ''">
                translate_list=#{translateList},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where correlation_id = #{correlationId}
    </update>

    <delete id="deleteByArticleId">
        delete from resource_read_article_correlation where article_id =#{articleId}
    </delete>
</mapper>
