<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceReadArticleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.read.ResourceReadArticleBean">
        <id column="article_id" property="articleId" />
        <result column="version_id" property="versionId" />
        <result column="course_id" property="courseId" />
        <result column="unit_id" property="unitId" />
        <result column="article_type" property="articleType" />
        <result column="serial_code" property="serialCode" />
        <result column="article_type" property="articleType" />
        <result column="content" property="content" />
        <result column="translate" property="translate" />
        <result column="word_count" property="wordCount" />
        <result column="time" property="time" />
        <result column="photo" property="photo" />
        <result column="difficulty" property="difficulty" />
        <result column="sentence_list" property="sentenceList" />
        <result column="sentence_disturbance" property="sentenceDisturbance" />
        <result column="sentence_content" property="sentenceContent" />
        <result column="full_text_parse" property="fullTextParse" />
        <result column="article_title" property="articleTitle" />
        <result column="title_translation" property="titleTranslation" />
        <result column="letter_signature_cn" property="letterSignatureCn" />
        <result column="letter_signature_en" property="letterSignatureEn" />
        <result column="letter_direction" property="letterDirection" />
        <result column="ai_gen_word" property="aiGenWord" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="selectList">
        article_id,version_id,course_id,unit_id,article_type,serial_code,content,translate,word_count,time,photo,
            difficulty,sentence_list,sentence_disturbance,update_time,sentence_content,full_text_parse,article_title,title_translation,letter_signature_cn,letter_signature_en,letter_direction,
            ai_gen_word
    </sql>

    <select id="listByUnitId" resultMap="BaseResultMap">
        select <include refid="selectList"/>
        from resource_read_article
        where unit_id = #{unitId}
    </select>

    <select id="edit" resultMap="BaseResultMap">
        select <include refid="selectList"/>
        from resource_read_article
        where article_id = #{articleId}
    </select>

    <select id="editBySerialCode" resultMap="BaseResultMap">
        select <include refid="selectList"/>
        from resource_read_article
        where unit_id = #{unitId} and serial_code = #{serialCode}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleBean"
            useGeneratedKeys="true"  keyProperty="articleId">
        insert into resource_read_article (version_id,course_id,unit_id,article_type,serial_code,content,translate,word_count,time,photo,difficulty,sentence_list,sentence_disturbance,sentence_content,full_text_parse,article_title,title_translation,letter_signature_cn,letter_signature_en,letter_direction,ai_gen_word)
        values (#{versionId},#{courseId},#{unitId},#{articleType},#{serialCode},#{content},#{translate},#{wordCount},#{time},#{photo},#{difficulty},#{sentenceList},#{sentenceDisturbance},#{sentenceContent},#{fullTextParse},#{articleTitle},#{titleTranslation},#{letterSignatureCn},#{letterSignatureEn},#{letterDirection},#{aiGenWord})
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleBean">
        update resource_read_article
        <set>
            <if test="versionId != null">
                version_id=#{versionId},
            </if>
            <if test="courseId != null">
                course_id=#{courseId},
            </if>
            <if test="unitId != null">
                unit_id=#{unitId},
            </if>
            <if test="articleType != null">
                article_type=#{articleType},
            </if>
            <if test="serialCode != null">
                serial_code=#{serialCode},
            </if>
            <if test="content != null">
                content=#{content},
            </if>
            <if test="translate != null">
                translate=#{translate},
            </if>
            <if test="wordCount != null">
                word_count=#{wordCount},
            </if>
            <if test="time != null">
                time=#{time},
            </if>
            <if test="photo != null">
                photo=#{photo},
            </if>
            <if test="difficulty != null">
                difficulty=#{difficulty},
            </if>
            <if test="sentenceList != null">
                sentence_list=#{sentenceList},
            </if>
            <if test="sentenceDisturbance != null">
                sentence_disturbance=#{sentenceDisturbance},
            </if>
            <if test="sentenceContent != null">
                sentence_content=#{sentenceContent},
            </if>
            <if test="fullTextParse != null">
                full_text_parse=#{fullTextParse},
            </if>
            article_title=#{articleTitle},
            title_translation=#{titleTranslation},
            letter_signature_cn=#{letterSignatureCn},
            letter_signature_en=#{letterSignatureEn},
            ai_gen_word=#{aiGenWord},
            <if test="letterDirection != null">
                letter_direction=#{letterDirection}
            </if>
        </set>
        where article_id = #{articleId}
    </update>

    <delete id="deleteByUnitId">
        delete from resource_read_article where unit_id = #{unitId}
    </delete>

    <select id="topicList" resultType="com.woxue.resourcemanage.entity.ResourceTopic">
        select id,name,type from resource_topic where type = '1'
    </select>

    <select id="getTopicUnitArticleList" resultMap="BaseResultMap">
        select a.article_id as articleId,a.unit_id as unitId,a.serial_code as serialCode,a.content
        from resource_read_article a
        left join resource_unit_content_read cr on a.unit_id = cr.resource_unit_id
        left join resource_topic t on cr.topic_id = t.id
        where t.type = '1' and t.id = #{topicId}
    </select>

    <insert id="insertTopic" parameterType="com.woxue.resourcemanage.entity.ResourceTopic">
        insert into resource_topic (name,type) values (#{name},#{type})
    </insert>


</mapper>
