<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IAdminDao">

	<select id="getAdmin" resultType="com.woxue.resourcemanage.entity.AdminBean">
        select
			`name`,
			password,
			role
			from admin
			where `name`=#{name} and password=#{password}
    </select>


	<insert id="insertOperationRecord">
		insert into `hssword_content_red_book`.`admin_operation_record`
			(`admin_name`,`ip`,`url`,`http_method`,`params`)
			VALUES
			(#{admin_name},#{ip},#{url},#{http_method},#{params})
	</insert>
</mapper>