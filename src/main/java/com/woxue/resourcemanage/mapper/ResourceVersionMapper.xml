<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourceVersionDao">

<!--版本-->
    <!--获取版本列表 分页-->
    <select id="getVersionList" resultType="map">
       SELECT Distinct v.id,v.name_en AS nameEn,v.name_cn AS nameCn,v.version_type AS versionType,v.relation_type AS  `type`, v.stage,
        v.price,  v.brief_introduction AS briefIntroduction,display_order displayOrder
       FROM  `resource_version` v
        left join resource_version_stage vs on v.id=vs.version_id
        WHERE
       <if test="versionType==1 and stage!=null">
           (v.stage=#{stage} or vs.stage=#{stage})
               AND v.version_type=#{versionType}
       </if>
       <if test="versionType!=1 and stage==null">
            v.version_type=#{versionType}
       </if>
       <if test="search!=null and stage !=''">
           AND v.name_en like concat(#{search},'%')
       </if>
        ORDER BY v.display_order ASC
       <if test="pageStart!=null">
           LIMIT #{pageStart}, #{pageSize}
       </if>
    </select>
    <update id="updateVersionDisplayOrder">
        UPDATE `resource_version`
        SET display_order = #{index} Where id = #{Id}
    </update>
    <select id="getVersionListCount" resultType="Integer">
       SELECT COUNT(*) FROM  `resource_version`  WHERE
        <if test="versionType==1 and stage!=null">
            stage=#{stage}
            AND version_type=#{versionType}
        </if>
        <if test="versionType!=1 and stage==null">
            version_type=#{versionType}
        </if>
    </select>
    <!--显示关联其他学段对应版本 -->
    <select id="getVersionByType" resultType="map">
        SELECT * FROM `resource_version` WHERE `relation_type`=#{type} AND `version_type`=1
        <if test="id!=null">
            AND id!=#{id}
        </if>
    </select>
    <select id="getVersionTypeList" resultType="map">
        SELECT `relation_type`,id FROM `resource_version` WHERE `version_type`=1;
    </select>
     <!--添加本版信息-->
    <insert id="insertVersion">
       INSERT `resource_version` (name_en,name_cn,version_type,`relation_type`,stage,price,brief_introduction,display_order)VALUES(#{nameEn},#{nameCn},#{versionType},#{type},#{stage},#{price},#{briefIntroduction},#{displayOrder});
    </insert>
    <!--回显某版本信息-->
    <select id="getVersionById" resultType="map">
      SELECT * FROM  `resource_version`  WHERE id=#{id}
    </select>

    <!--修改版本信息-->
    <update id="updateVersionById">
       UPDATE  `resource_version` SET name_en=#{nameEn},name_cn=#{nameCn},version_type=#{versionType},`relation_type`=#{type},
        price=#{price},brief_introduction=#{briefIntroduction},display_order=#{displayOrder}
        WHERE id=#{id};
    </update>

    <update id="updateRelationType">
        UPDATE  `resource_version` SET `relation_type`=#{type}  WHERE id=#{id};
    </update>

    <select id="getMaxDisplayOrder" resultType="java.lang.Integer">
        select MAX(display_order) from `resource_version`
    </select>

    <delete id="deleteVersionStage">
        delete from `resource_version_stage` where version_id=#{versionId}
    </delete>

    <insert id="insertVersionStage">
        insert into `resource_version_stage` (version_id,stage) values (#{versionId},#{stage})
    </insert>

    <delete id="deleteCourseStage">
        delete from `resource_course_stage` where course_id=#{courseId}
    </delete>

    <insert id="insertCourseStage">
        insert into `resource_course_stage` (course_id,stage) values (#{courseId},#{stage})
    </insert>

    <select id="selectVersionCount" resultType="int">
        select count(1)
         from resource_course
                 LEFT JOIN resource_course_stage ON resource_course.id = resource_course_stage.course_id
        where version_id = #{versionId}
          AND (resource_course.stage = #{stage} or resource_course_stage.stage = #{stage})
    </select>
</mapper>