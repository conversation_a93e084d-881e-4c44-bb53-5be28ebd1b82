<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourceContentDao">
    <!--新增关联内容-->
    <!--词汇-->
     <insert id="insertContentWord">
         INSERT `resource_unit_content_word` (`resource_unit_id`,`level`,`program_name`,`unit_name`,show_name,word_count,has_example_sentence)
         VALUES(#{resourceUnitId},#{level},#{programName},#{unitName},#{showName},#{wordCount},#{hasExampleSentence});
     </insert>
    <!--句子-->
    <insert id="insertContentSentence">
         INSERT `resource_unit_content_sentence`(`resource_unit_id`,`program_name`,`unit_name`,`show_name`,`sentence_count`)
          VALUES(#{resourceUnitId},#{programName},#{unitName},#{showName},#{sentenceCount})
    </insert>
    <!--语法-->
    <insert id="insertContentGrammar">
         INSERT `resource_unit_content_grammar`(`resource_unit_id`,`grammar_course_id`,`grammar_unit_id`,`show_name`,`knowledge_count`)
         VALUES(#{resourceUnitId},#{grammarCourseId},#{grammarUnitId},#{showName},#{knowledgeCount});
    </insert>
    <!--优题-->
    <insert id="insertContentQuestion">
        INSERT `resource_unit_content_question`(`resource_unit_id`,`level`,`sync_question_course_id`,`sync_question_unit_id`,`show_name`,`paper_id`)
        VALUES(#{resourceUnitId},#{level},#{syncQuestionCourseId},#{syncQuestionUnitId},#{showName},#{paperId});
    </insert>



    <!--修改关联内容-->
    <!--词汇-->
    <update id="updateContentWord">
        UPDATE `resource_unit_content_word` SET
        program_name=#{programName},unit_name=#{unitName},
        show_name=#{showName},word_count=#{wordCount},
        has_example_sentence=#{hasExampleSentence}
        WHERE id=#{id}
    </update>
    <!--句子-->
    <update id="updateContentSentence">
         UPDATE `resource_unit_content_sentence` SET  `program_name`=#{programName},`unit_name`=#{unitName},`show_name`=#{showName},`sentence_count`=#{sentenceCount}
         WHERE id=#{id}
     </update>
    <!--语法-->
    <update id="updateContentGrammar">
         UPDATE `resource_unit_content_grammar` SET  grammar_course_id=#{grammarCourseId} ,grammar_unit_id=#{grammarUnitId},show_name=#{showName},  knowledge_count=#{knowledgeCount}
         WHERE  id=#{id}
     </update>
    <!--优题-->
    <update id="updateContentQuestion">
          UPDATE `resource_unit_content_question`  SET sync_question_course_id=#{syncQuestionCourseId}, sync_question_unit_id=#{syncQuestionUnitId} ,show_name=#{showName} ,paper_id=#{paperId}
          WHERE id=#{id}
     </update>


    <select id="getWordShowName" resultType="map">
         SELECT id,`level`,show_name AS showName FROM `resource_unit_content_word`  WHERE resource_unit_id=#{resourceUnitId};
    </select>
    <select id="getSentenceShowName" resultType="map">
         SELECT id,show_name AS showName FROM `resource_unit_content_sentence` WHERE resource_unit_id=#{resourceUnitId};
    </select>
    <select id="getQuestionShowName" resultType="map">
         SELECT id,`level`,show_name AS showName  FROM `resource_unit_content_question` WHERE resource_unit_id=#{resourceUnitId};
    </select>
    <select id="getGrammarShowName" resultType="map">
        SELECT id,show_name AS showName  FROM  `resource_unit_content_grammar` WHERE resource_unit_id=#{resourceUnitId};
    </select>

    <!--取消关联 -->
    <delete id="delContentWord">
        DELETE FROM  `resource_unit_content_word`  WHERE id=#{id}
    </delete>
    <delete id="delContentSentence">
        DELETE FROM `resource_unit_content_sentence`  WHERE id=#{id};
    </delete>
    <delete id="delContentQuestion">
        DELETE FROM `resource_unit_content_question`  WHERE id=#{id};
    </delete>
    <delete id="delContentGrammar">
        DELETE FROM `resource_unit_content_grammar`  WHERE id=#{id};
    </delete>

</mapper>