<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ResourceListenSentenceSelectWordDao">
    
    <resultMap type="com.woxue.common.model.redBook.listen.ResourceListenSentenceSelectWordBean" id="ResourceListenSentenceSelectWordResult">
        <result property="id"    column="id"    />
        <result property="unitId"    column="unit_id"    />
        <result property="sentenceId"    column="sentence_id"    />
        <result property="sentenceContent"    column="sentence_content"    />
        <result property="word"    column="word"    />
        <result property="wordDisturbance"    column="word_disturbance"    />
    </resultMap>

    <sql id="selectResourceListenSentenceSelectWordVo">
        select id, unit_id, sentence_id, sentence_content, word, word_disturbance,type from resource_listen_sentence_select_word
    </sql>

    <select id="selectResourceListenSentenceSelectWordList" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceSelectWordBean" resultMap="ResourceListenSentenceSelectWordResult">
        <include refid="selectResourceListenSentenceSelectWordVo"/>
        <where>  
            <if test="unitId != null "> and unit_id = #{unitId}</if>
            <if test="sentenceId != null "> and sentence_id = #{sentenceId}</if>
            <if test="sentenceContent != null  and sentenceContent != ''"> and sentence_content = #{sentenceContent}</if>
            <if test="word != null  and word != ''"> and word = #{word}</if>
            <if test="wordDisturbance != null  and wordDisturbance != ''"> and word_disturbance = #{wordDisturbance}</if>
        </where>
    </select>
    
    <select id="selectResourceListenSentenceSelectWordById" resultMap="ResourceListenSentenceSelectWordResult">
        <include refid="selectResourceListenSentenceSelectWordVo"/>
        where id = #{id}
    </select>

    <select id="listByUnitId" resultMap="ResourceListenSentenceSelectWordResult">
        <include refid="selectResourceListenSentenceSelectWordVo"/>
        where unit_id = #{untiId}
    </select>

    <insert id="insertResourceListenSentenceSelectWord" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceSelectWordBean" useGeneratedKeys="true" keyProperty="id">
        insert into resource_listen_sentence_select_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitId != null">unit_id,</if>
            <if test="sentenceId != null">sentence_id,</if>
            <if test="sentenceContent != null">sentence_content,</if>
            <if test="word != null">word,</if>
            <if test="wordDisturbance != null">word_disturbance,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitId != null">#{unitId},</if>
            <if test="sentenceId != null">#{sentenceId},</if>
            <if test="sentenceContent != null">#{sentenceContent},</if>
            <if test="word != null">#{word},</if>
            <if test="wordDisturbance != null">#{wordDisturbance},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <insert id="batchInsert" >
        insert into resource_listen_sentence_select_word( unit_id, sentence_id, sentence_content, word, word_disturbance,type)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.unitId},#{item.sentenceId},#{item.sentenceContent},#{item.word},#{item.wordDisturbance},#{item.type})
        </foreach>
    </insert>

    <update id="updateResourceListenSentenceSelectWord" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceSelectWordBean">
        update resource_listen_sentence_select_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="sentenceId != null">sentence_id = #{sentenceId},</if>
            <if test="sentenceContent != null">sentence_content = #{sentenceContent},</if>
            <if test="word != null">word = #{word},</if>
            <if test="wordDisturbance != null">word_disturbance = #{wordDisturbance},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResourceListenSentenceSelectWordById">
        delete from resource_listen_sentence_select_word where id = #{id}
    </delete>

    <delete id="deleteByUnitId">
        delete from resource_listen_sentence_select_word where unit_id = #{unitId}
    </delete>

    <delete id="deleteResourceListenSentenceSelectWordByIds" parameterType="String">
        delete from resource_listen_sentence_select_word where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>