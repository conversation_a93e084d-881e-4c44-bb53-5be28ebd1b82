<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IArticleDao">
    <insert id="insertArticle" useGeneratedKeys="true" keyProperty="id">
        insert into resource_article
            (article_name,sentence_count,article_type)
        values
            (#{articleName},#{sentenceCount},#{articleType})
    </insert>

    <insert id="insertUnitArticle">
        insert into `resource_unit_article` values(null,#{unitId},#{articleId},#{displayOrder})
    </insert>
    <update id="updateArticleSentenceCount">
        UPDATE `resource_article`
        SET
            `sentence_count` = (SELECT COUNT(id) FROM `resource_article_sentence` WHERE `article_id`=#{articleId})
        WHERE `id`=#{articleId}
    </update>

    <!--课文-->
    <select id="getArticleShowName" resultType="map">
        SELECT ra.id articleId,ra.article_name articleName, CAST(ra.article_type AS UNSIGNED ) articleType
        FROM resource_unit_article rua
                 left join resource_article ra on rua .article_id = ra.id
        WHERE rua.resource_unit_id = #{resourceUnitId}
    </select>
    <!--根据课文Id获取课文分段句子-->
    <select id="getArticleContentList" resultType="com.woxue.resourcemanage.entity.SentenceBean">
        select id,article_id articleId,sentence_en_US ,sentence_zh_CN,speaker,sound_file soundFile,show_type showType,display_order displayOrder from resource_article_sentence
        WHERE article_id = #{articleId} order by display_order
    </select>
    <!--修改课文-->
    <update id="updateArticleContent" parameterType="com.woxue.resourcemanage.entity.SentenceBean">
        UPDATE resource_article_sentence SET
                                             sentence_en_US = #{sentence.sentence_en_US},
                                             sentence_zh_CN = #{sentence.sentence_zh_CN},
                                             speaker =#{sentence.speaker},
                                             sound_file =#{sentence.soundFile},
                                             display_order =#{sentence.displayOrder}
        WHERE id=#{sentence.id}
    </update>
    <!--批量添加课文句子-->
    <insert id="insertArticleSentence">
        INSERT into resource_article_sentence
        (article_id,sentence_en_US,sentence_zh_CN,speaker,sound_file,show_type,display_order)
        values
        (#{sentence.articleId},#{sentence.sentence_en_US},#{sentence.sentence_zh_CN},
        #{sentence.speaker},#{sentence.soundFile},#{sentence.showType},#{sentence.displayOrder})
    </insert>
    <insert id="replaceIntoArticleSentence">
        replace into resource_article_sentence
        (id,article_id,sentence_en_US,sentence_zh_CN,speaker,sound_file,show_type,display_order)
        values
        (#{sentence.id},#{sentence.articleId},#{sentence.sentence_en_US},#{sentence.sentence_zh_CN},
        #{sentence.speaker},#{sentence.soundFile},#{sentence.showType},#{sentence.displayOrder})
    </insert>

    <!--获取最大顺序-->
    <select id="getMaxSentenceOrder" resultType="java.lang.Integer">
        select MAX(display_order) from resource_article_sentence WHERE article_id = #{articleId}
    </select>

    <!--获取句子个数-->
    <select id="getArticleSentenceCount" resultType="java.lang.Integer">
        SELECT sentence_count from resource_article WHERE id = #{articleId}
    </select>
</mapper>