<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ReadExpandReadArticleQuestionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean">
        <id column="question_id" property="questionId" />
        <result column="article_id" property="articleId" />
        <result column="train_phase" property="trainPhase" />
        <result column="number" property="number" />
        <result column="question" property="question" />
        <result column="option_a" property="optionA" />
        <result column="option_b" property="optionB" />
        <result column="option_c" property="optionC" />
        <result column="option_d" property="optionD" />
        <result column="answer" property="answer" />
        <result column="question_type" property="questionType" />
        <result column="think_method" property="thinkMethod" />
        <result column="topic_point" property="topicPoint" />
        <result column="content_point" property="contentPoint" />
        <result column="content_point_translate" property="contentPointTranslate" />
        <result column="option_point" property="optionPoint" />
        <result column="analyze_judgment" property="analyzeJudgment" />
        <result column="question_translation" property="questionTranslation" />
        <result column="optionA_translation" property="optionATranslation" />
        <result column="optionB_translation" property="optionBTranslation" />
        <result column="optionC_translation" property="optionCTranslation" />
        <result column="optionD_translation" property="optionDTranslation" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>


    <select id="list" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_question
        where article_id = #{articleId}
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_question
        where question_id = #{questionId}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean"
            useGeneratedKeys="true"  keyProperty="questionId">
        insert into readexpand_read_article_question (article_id,train_phase,number,question,option_a,option_b,option_c,option_d,answer,
                                                 question_type,think_method,topic_point,content_point,content_point_translate,
                                                 option_point,analyze_judgment,question_translation,optionA_translation,optionB_translation,
                                                 optionC_translation,optionD_translation,create_time,update_time)
        values (#{articleId},#{trainPhase},#{number},#{question},#{optionA},#{optionB},#{optionC},#{optionD},#{answer},
                #{questionType},#{thinkMethod},#{topicPoint},#{contentPoint},#{contentPointTranslate},
                #{optionPoint},#{analyzeJudgment},#{questionTranslation},#{optionATranslation},#{optionBTranslation},
                #{optionCTranslation},#{optionDTranslation},#{createTime},#{updateTime})
    </insert>


    <insert id="batchSave" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean">
        insert into readexpand_read_article_question (article_id,train_phase,number,question,option_a,option_b,option_c,option_d,answer,
                                                 question_type,think_method,topic_point,content_point,content_point_translate,
                                                 option_point,analyze_judgment,question_translation,optionA_translation,optionB_translation,
                                                 optionC_translation,optionD_translation,create_time,update_time)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.articleId},#{item.trainPhase},#{item.number},#{item.question},#{item.optionA},#{item.optionB},#{item.optionC},
             #{item.optionD},#{item.answer},#{item.questionType},#{item.thinkMethod},#{item.topicPoint},#{item.contentPoint},
             #{item.contentPointTranslate},#{item.optionPoint},#{item.analyzeJudgment},#{item.questionTranslation},
             #{item.optionATranslation},#{item.optionBTranslation},#{item.optionCTranslation},#{item.optionDTranslation},
             #{item.createTime},#{item.updateTime})
        </foreach>

    </insert>


    <update id="update" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleQuestionBean">
        update readexpand_read_article_question
        <set>
            <if test="articleId != null">
                article_id=#{articleId},
            </if>
            <if test="trainPhase != null">
                train_phase=#{trainPhase},
            </if>
            <if test="number != null">
                number=#{number},
            </if>
            <if test="question != null">
                question=#{question},
            </if>
            <if test="optionA != null">
                option_a=#{optionA},
            </if>
            <if test="optionB != null">
                option_b=#{optionB},
            </if>
            <if test="optionC != null">
                option_c=#{optionC},
            </if>
            <if test="optionD != null">
                option_d=#{optionD},
            </if>
            <if test="answer != null">
                answer=#{answer},
            </if>
            <if test="questionType != null">
                question_type=#{questionType},
            </if>
            <if test="thinkMethod != null">
                think_method=#{thinkMethod},
            </if>
            <if test="topicPoint != null">
                topic_point=#{topicPoint},
            </if>
            <if test="contentPoint != null">
                content_point=#{contentPoint},
            </if>
            <if test="contentPointTranslate != null">
                content_point_translate=#{contentPointTranslate},
            </if>
            <if test="optionPoint != null">
                option_point=#{optionPoint},
            </if>
            <if test="analyzeJudgment != null">
                analyze_judgment=#{analyzeJudgment},
            </if>
            <if test="questionTranslation != null">
                question_translation=#{questionTranslation},
            </if>
            <if test="optionATranslation != null">
                optionA_translation=#{optionATranslation},
            </if>
            <if test="optionBTranslation != null">
                optionB_translation=#{optionBTranslation},
            </if>
            <if test="optionCTranslation != null">
                optionC_translation=#{optionCTranslation},
            </if>
            <if test="optionDTranslation != null">
                optionD_translation=#{optionDTranslation},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where question_id = #{questionId}
    </update>

</mapper>
