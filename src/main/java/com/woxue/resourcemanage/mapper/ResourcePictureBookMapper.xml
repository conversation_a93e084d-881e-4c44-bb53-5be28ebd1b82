<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourcePictureBookDao">
    <insert id="insertPictureBook">
        insert into picture_book(course_id, title,en_title, thumbnail, desc_ch, desc_en, level, sort)
        values (#{pictureBook.courseId}, #{pictureBook.title},#{pictureBook.enTitle}, #{pictureBook.thumbnail}, #{pictureBook.desc_Ch}, #{pictureBook.desc_En}, #{pictureBook.level}, #{pictureBook.sort})
    </insert>

    <update id="updatePictureBookById">
        update picture_book
        set course_id=#{pictureBook.courseId},
            title=#{pictureBook.title},
            en_title=#{pictureBook.enTitle},
            thumbnail=#{pictureBook.thumbnail},
            desc_ch=#{pictureBook.desc_Ch},
            desc_en=#{pictureBook.desc_En},
            level=#{pictureBook.level},
            sort=#{pictureBook.sort}
        where id = #{pictureBook.id}
    </update>

    <update id="updatePictureBookContentById">
        update picture_book_content
        set course_id=#{pictureBookContent.courseId},
            picture_book_id=#{pictureBookContent.pictureBookId},
            pic_url=#{pictureBookContent.picUrl},
            sentence_num=#{pictureBookContent.sentenceNum},
            sort=#{pictureBookContent.sort}
        where id = #{pictureBookContent.id}
    </update>
    <delete id="deletePictureBookById" parameterType="java.lang.Integer">
        delete from picture_book where id = #{pictureBookId}
    </delete>
    <delete id="deletePictureBookContentByPictureBookId" parameterType="java.lang.Integer">
        delete from picture_book_content where picture_book_id = #{pictureBookId}
    </delete>
    <delete id="deletePictureBookSentenceByPictureBookId" parameterType="java.lang.Integer">
        delete from picture_book_sentence where picture_book_id = #{pictureBookId}
    </delete>

    <insert id="insertPictureBookContent">
        insert into picture_book_content(course_id, picture_book_id, pic_url, sentence_num, sort)
        values (#{pictureBookContent.courseId}, #{pictureBookContent.pictureBookId}, #{pictureBookContent.picUrl}, #{pictureBookContent.sentenceNum}, #{pictureBookContent.sort})
    </insert>

    <insert id="insertPictureBookSentence">
        insert into picture_book_sentence(course_id,picture_book_id, content_id, example_en_us, example_en_us_element,
                                                   example_zh_cn, example_zh_py, speaker, sound_file, sort)
        values (#{pictureBookSentence.courseId},#{pictureBookSentence.pictureBookId}, #{pictureBookSentence.contentId}, #{pictureBookSentence.exampleEnUS}, #{pictureBookSentence.exampleEnUSElement}, #{pictureBookSentence.exampleZhCN}, #{pictureBookSentence.exampleZhPY},
                #{pictureBookSentence.speaker}, #{pictureBookSentence.soundFile}, #{pictureBookSentence.sort})
    </insert>

    <update id="updatePictureBookSentenceById">
        update picture_book_sentence
        set course_id=#{pictureBookSentence.courseId},
            content_id=#{pictureBookSentence.contentId},
            picture_book_id=#{pictureBookSentence.pictureBookId},
            example_en_us=#{pictureBookSentence.exampleEnUS},
            example_en_us_element=#{pictureBookSentence.exampleEnUSElement},
            example_zh_cn=#{pictureBookSentence.exampleZhCN},
            example_zh_py=#{pictureBookSentence.exampleZhPY},
            speaker=#{pictureBookSentence.speaker},
            sound_file=#{pictureBookSentence.soundFile},
            sort=#{pictureBookSentence.sort}
        where id = #{pictureBookSentence.id}
    </update>

    <delete id="deletePictureBookSentenceByPictureBookSentenceId">
        delete from picture_book_sentence where id = #{pictureBookSentenceId}
    </delete>

    <insert id="insertPictureBookWord">
        insert into picture_book_word(picture_book_id, spelling, syllable, meaning_en_US, meaning_zh_CN, example_en_US, example_zh_CN,img_url)
        values (#{pictureBookId}, #{spelling}, #{syllable}, #{meaningEnUs}, #{meaningZhCn}, #{exampleEnUs}, #{exampleZhCn},#{imgUrl})
    </insert>

    <update id="updatePictureBookWord">
        update picture_book_word
        set picture_book_id=#{pictureBookId},
            spelling=#{spelling},
            syllable=#{syllable},
            meaning_en_US=#{meaningEnUs},
            meaning_zh_CN=#{meaningZhCn},
            example_en_US=#{exampleEnUs},
            example_zh_CN=#{exampleZhCn},
            img_url=#{imgUrl}
        where id = #{wordId}
    </update>

    <delete id="deletePictureBookWord">
        delete from picture_book_word where id = #{wordId}
    </delete>
</mapper>