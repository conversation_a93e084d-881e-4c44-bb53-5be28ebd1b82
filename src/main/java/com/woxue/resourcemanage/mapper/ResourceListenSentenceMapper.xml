<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ResourceListenSentenceDao">
    
    <resultMap type="com.woxue.common.model.redBook.listen.ResourceListenSentenceBean" id="ResourceListenSentenceResult">
        <result property="id"    column="id"    />
        <result property="unitId"    column="unit_id"    />
        <result property="sentenceEnUs"    column="sentence_en_US"    />
        <result property="sentenceZhCn"    column="sentence_zh_CN"    />
        <result property="speaker"    column="speaker"    />
        <result property="soundFile"    column="sound_file"    />
        <result property="modtime"    column="modtime"    />
        <result property="displayOrder"    column="display_order"    />
    </resultMap>

    <sql id="selectResourceListenSentenceVo">
        select id, unit_id, sentence_en_US, sentence_zh_CN, speaker, sound_file, modtime, display_order from resource_listen_sentence
    </sql>

    <select id="selectResourceListenSentenceList" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceBean" resultMap="ResourceListenSentenceResult">
        <include refid="selectResourceListenSentenceVo"/>
        <where>  
            <if test="unitId != null "> and unit_id = #{unitId}</if>
            <if test="sentenceEnUs != null  and sentenceEnUs != ''"> and sentence_en_US = #{sentenceEnUs}</if>
            <if test="sentenceZhCn != null  and sentenceZhCn != ''"> and sentence_zh_CN = #{sentenceZhCn}</if>
            <if test="speaker != null  and speaker != ''"> and speaker = #{speaker}</if>
            <if test="soundFile != null  and soundFile != ''"> and sound_file = #{soundFile}</if>
            <if test="modtime != null "> and modtime = #{modtime}</if>
            <if test="displayOrder != null "> and display_order = #{displayOrder}</if>
        </where>
    </select>
    
    <select id="selectResourceListenSentenceById" resultMap="ResourceListenSentenceResult">
        <include refid="selectResourceListenSentenceVo"/>
        where id = #{id}
    </select>
    <select id="listByUnitId" resultMap="ResourceListenSentenceResult">
        <include refid="selectResourceListenSentenceVo"/>
        where unit_id = #{unitId}
        order by display_order
    </select>
        
    <insert id="insertResourceListenSentence" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceBean" useGeneratedKeys="true" keyProperty="id">
        insert into resource_listen_sentence
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitId != null">unit_id,</if>
            <if test="sentenceEnUs != null and sentenceEnUs != ''">sentence_en_US,</if>
            <if test="sentenceZhCn != null and sentenceZhCn != ''">sentence_zh_CN,</if>
            <if test="speaker != null">speaker,</if>
            <if test="soundFile != null">sound_file,</if>
            <if test="modtime != null">modtime,</if>
            <if test="displayOrder != null">display_order,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitId != null">#{unitId},</if>
            <if test="sentenceEnUs != null and sentenceEnUs != ''">#{sentenceEnUs},</if>
            <if test="sentenceZhCn != null and sentenceZhCn != ''">#{sentenceZhCn},</if>
            <if test="speaker != null">#{speaker},</if>
            <if test="soundFile != null">#{soundFile},</if>
            <if test="modtime != null">#{modtime},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
         </trim>
    </insert>

    <insert id="replaceInsert" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceBean" useGeneratedKeys="true" keyProperty="id">
        replace into resource_listen_sentence
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="sentenceEnUs != null and sentenceEnUs != ''">sentence_en_US,</if>
            <if test="sentenceZhCn != null and sentenceZhCn != ''">sentence_zh_CN,</if>
            <if test="speaker != null">speaker,</if>
            <if test="soundFile != null">sound_file,</if>
            <if test="modtime != null">modtime,</if>
            <if test="displayOrder != null">display_order,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="sentenceEnUs != null and sentenceEnUs != ''">#{sentenceEnUs},</if>
            <if test="sentenceZhCn != null and sentenceZhCn != ''">#{sentenceZhCn},</if>
            <if test="speaker != null">#{speaker},</if>
            <if test="soundFile != null">#{soundFile},</if>
            <if test="modtime != null">#{modtime},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert into resource_listen_sentence (unit_id,sentence_en_US,sentence_zh_CN,speaker,sound_file,modtime,display_order)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.unitId},#{item.sentenceEnUs},#{item.sentenceZhCn},#{item.speaker},#{item.soundFile},#{item.modtime},#{item.displayOrder})
        </foreach>
    </insert>

    <update id="updateResourceListenSentence" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceBean">
        update resource_listen_sentence
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="sentenceEnUs != null and sentenceEnUs != ''">sentence_en_US = #{sentenceEnUs},</if>
            <if test="sentenceZhCn != null and sentenceZhCn != ''">sentence_zh_CN = #{sentenceZhCn},</if>
            <if test="speaker != null">speaker = #{speaker},</if>
            <if test="soundFile != null">sound_file = #{soundFile},</if>
            <if test="modtime != null">modtime = #{modtime},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResourceListenSentenceById">
        delete from resource_listen_sentence where id = #{id}
    </delete>

    <delete id="deleteResourceListenSentenceByIds" parameterType="String">
        delete from resource_listen_sentence where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>