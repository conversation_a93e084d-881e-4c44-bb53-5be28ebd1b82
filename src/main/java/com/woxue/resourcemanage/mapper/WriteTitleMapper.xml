<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IWriteTitleDao">
  <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.WriteUnitTitleBean">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="level" property="level" />
    <result column="is_basic" jdbcType="BIT" property="isBasic" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unit_id, name, parent_id, level, is_basic, display_order
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from write_title
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByUnitId">
    delete from write_title
    where unit_id = #{unitId}
  </delete>
  <select id="getWriteParientTitleByUnitId" resultMap="BaseResultMap">
    select * from write_title where unit_id = #{unitId} and parent_id = 0 limit 1
  </select>
  <select id="getTitleListByUnitIdAndParientId" resultMap="BaseResultMap">
    select * from write_title where unit_id = #{unitId} and parent_id = #{parientId}
  </select>
  <!-- 添加单元内容的标题 -->
  <insert id="addUnitTitle" parameterType="com.woxue.common.model.redBook.WriteUnitTitleBean" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO `write_title`(id, unit_id, `name`, parent_id, `level`, is_basic, display_order)
      VALUE(NULL, #{unitId}, #{name}, #{parentId}, #{level}, #{isBasic}, #{displayOrder})
  </insert>

  <!-- 更新单元内容的标题 -->
  <update id="updateUnitTitle" parameterType="com.woxue.common.model.redBook.WriteUnitTitleBean">
    UPDATE `write_title`
    SET unit_id = #{unitId},
        `name` = #{name},
        `parent_id` = #{parentId},
        `level` = #{level},
        `is_basic` = #{isBasic},
        `display_order` = #{displayOrder}
    WHERE id = #{id}
  </update>
  <!-- 更新标题是最小知识点 -->
  <update id="updateTitleIsBasic">
    UPDATE `write_title` SET is_basic = TRUE WHERE id = #{titleId}
  </update>
</mapper>