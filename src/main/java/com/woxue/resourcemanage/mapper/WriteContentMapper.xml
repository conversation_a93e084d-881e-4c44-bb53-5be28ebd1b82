<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IWriteContentDao">
  <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.WriteUnitContentBean">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="title_id" jdbcType="INTEGER" property="titleId" />
    <result column="question_type"  property="questionType" />
    <result column="option_a" jdbcType="VARCHAR" property="optionA" />
    <result column="option_b" jdbcType="VARCHAR" property="optionB" />
    <result column="option_c" jdbcType="VARCHAR" property="optionC" />
    <result column="option_d" jdbcType="VARCHAR" property="optionD" />
    <result column="option_e" jdbcType="VARCHAR" property="optionE" />
    <result column="correct_option" jdbcType="VARCHAR" property="correctOption" />
    <result column="wrong_tips_1" jdbcType="VARCHAR" property="wrongTips1" />
    <result column="wrong_tips_2" jdbcType="VARCHAR" property="wrongTips2" />
    <result column="wrong_tips_3" jdbcType="VARCHAR" property="wrongTips3" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.woxue.common.model.redBook.WriteUnitContentBean">
    <result column="question" jdbcType="LONGVARCHAR" property="question" />
    <result column="parse" jdbcType="LONGVARCHAR" property="parse" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unit_id, title_id, question_type, option_a, option_b, option_c, option_d, option_e, 
    correct_option, wrong_tips_1, wrong_tips_2, wrong_tips_3, display_order
  </sql>
  <sql id="Blob_Column_List">
    question, parse
  </sql>

  <delete id="deleteByUnitId">
    delete from write_content
    where unit_id = #{unitId}
  </delete>
  <select id="getWriteContentList" resultMap="BaseResultMap">
    select * from write_content
    where id in
    <foreach collection="array" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <select id="getWriteContentListByUnitIdAndTitleId" resultMap="BaseResultMap">
    select * from write_content
    where unit_id = #{unitId} and title_id = #{titleId} order by display_order asc
  </select>
  <select id="selectListByUnitId" resultMap="ResultMapWithBLOBs">
    select * from write_content
    where unit_id = #{unitId} order by display_order asc
  </select>

  <insert id="addUnitContent" parameterType="com.woxue.common.model.redBook.WriteUnitContentBean" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO `write_content`(id, unit_id, title_id,question_type, question,
                                  option_a, option_b, option_c, option_d, option_e, correct_option,
                                  wrong_tips_1, wrong_tips_2, wrong_tips_3, parse, display_order)
      VALUE(NULL, #{unitId},#{titleId}, #{questionType}, #{question},
      #{optionA}, #{optionB}, #{optionC}, #{optionD}, #{optionE}, #{correctOption},
      #{wrongTips1}, #{wrongTips2}, #{wrongTips3}, #{parse}, #{displayOrder})
  </insert>
  <update id="updateUnitContent" parameterType="com.woxue.common.model.redBook.WriteUnitContentBean">
    UPDATE `write_content`
    SET
      title_id = #{titleId},
      question_type = #{questionType},
      question = #{question},
      option_a = #{optionA},
      option_b = #{optionB},
      option_c = #{optionC},
      option_d = #{optionD},
      option_e = #{optionE},
      correct_option = #{correctOption},
      wrong_tips_1 = #{wrongTips1},
      wrong_tips_2 = #{wrongTips2},
      wrong_tips_3 = #{wrongTips3},
      parse = #{parse},
      display_order = #{displayOrder}
    WHERE id = #{id}
  </update>
</mapper>