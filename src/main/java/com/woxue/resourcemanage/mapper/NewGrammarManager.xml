<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.INewGrammarManagerDao">

    <resultMap type="com.woxue.resourcemanage.entity.grammar.SimulationQuestionBean" id="simulationQuestionBeanMap">
        <id column="id" property="id"/>
        <result column="teach_id" property="teachId"/>
        <result column="area_id" property="aid"/>
        <result column="course_id" property="courseId"/>
        <!-- <result column="teachName" property="teachName"/>
        <result column="aid" property="aid"/>
        <result column="agentName" property="agentName"/> -->
        <result column="grade" property="grade"/>
        <result column="knowledge_point" property="knowledgePoint"/>
        <result column="question_type" property="questionType"/>
        <result column="question" property="question"/>
        <result column="option_a" property="optionA"/>
        <result column="option_b" property="optionB"/>
        <result column="option_c" property="optionC"/>
        <result column="option_d" property="optionD"/>
        <result column="option_e" property="optionE"/>
        <result column="correct_option" property="correctOption"/>
        <result column="parse" property="parse"/>
        <result column="difficulty" property="difficulty"/>
        <result column="answer_time" property="answerTime"/>
        <result column="is_share" property="isShare"/>
        <result column="use_num" property="useNum"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap type="com.woxue.resourcemanage.entity.grammar.GrammarUnitContentBean" id="grammarContentMap">
        <id column="id" property="id"/>
        <result column="unit_id" property="unitId"/>
        <result column="subtitle_id" property="subtitleId"/>
        <result column="question_type" property="questionType"/>
        <result column="question" property="question"/>
        <result column="option_a" property="optionA"/>
        <result column="option_b" property="optionB"/>
        <result column="option_c" property="optionC"/>
        <result column="option_d" property="optionD"/>
        <result column="option_e" property="optionE"/>
        <result column="correct_option" property="correctOption"/>
        <result column="wrong_tips_1" property="wrongTips1"/>
        <result column="wrong_tips_2" property="wrongTips2"/>
        <result column="wrong_tips_3" property="wrongTips3"/>
        <result column="parse" property="parse"/>
        <result column="disporder" property="disporder"/>
    </resultMap>
    <resultMap type="com.woxue.resourcemanage.entity.grammar.GrammarQuestionBean" id="grammarQuestionMap">
        <id column="id" property="id"/>
        <result column="teach_id" property="teachId"/>
        <result column="unit_id" property="unitId"/>
        <result column="question_type" property="questionType"/>
        <result column="difficulty" property="difficulty"/>
        <result column="question" property="question"/>
        <result column="option_a" property="optionA"/>
        <result column="option_b" property="optionB"/>
        <result column="option_c" property="optionC"/>
        <result column="option_d" property="optionD"/>
        <result column="option_e" property="optionE"/>
        <result column="correct_option" property="correctOption"/>
        <result column="parse" property="parse"/>
        <result column="source" property="source"/>
        <result column="year" property="year"/>
        <result column="area_id" property="areaId"/>
        <result column="answer_type" property="answerType"/>
        <collection property="titleIdList" ofType="Integer" javaType="java.util.List">
            <result column="title_id"/>
        </collection>
    </resultMap>

    <resultMap id="grammarUnitMap" type="com.woxue.resourcemanage.entity.grammar.GrammarUnitBean">
        <result column="unit_id" property="unitId"/>
        <result column="unit_name" property="unitName"/>
        <result column="knowledge_count" property="knowledgeCount"/>
        <result column="grammar_unit_id" property="grammarUnitId"/>
        <result column="question_unit_id" property="questionUnitId"/>
        <result column="total" property="questionTotalNum"/>
        <result column="easy" property="questionLevel1Num"/>
        <result column="mid" property="questionLevel2Num"/>
        <result column="difficulty" property="questionLevel3Num"/>
    </resultMap>
    <!-- 获取课程下的试题列表 -->
    <select id="getCourseQuestionList" resultMap="simulationQuestionBeanMap">
        SELECT * FROM `grammar_course_question`
        WHERE course_id=#{courseId} and is_delete!=1
        <if test="pageSize>0">
            LIMIT #{pageStart}, #{pageSize}
        </if>
    </select>

    <!-- 获取课程下的试题总数 -->
    <select id="getCourseQuestionNum" parameterType="Integer" resultType="java.util.HashMap">
		SELECT `question_type` questionType, `difficulty` difficulty, COUNT(`id`) num FROM
		grammar_course_question
		WHERE `course_id`=#{courseId} and is_delete!=1
		GROUP BY `question_type`, `difficulty`
		ORDER BY `question_type`, `difficulty`
	</select>
    <!-- 根据版本id获取版本 -->
    <select id="getVersion" parameterType="Integer" resultType="com.woxue.resourcemanage.entity.grammar.GrammarVersionBean">
		SELECT id,`name_cn` name,stage grade,version_type as versionType
		FROM `resource_version`
		WHERE id=#{versionId}
	</select>

    <!-- 根据课程Id获取课程 -->
    <select id="getCourse" parameterType="Integer" resultType="com.woxue.resourcemanage.entity.grammar.GrammarCourseBean">
		SELECT id,`name_cn` name, version_id versionId,
		unit_num unitNum,stage,grade,display_order disporder
		FROM `resource_course`
		WHERE id=#{id} 
	</select>


    <select id="getCourseQuestionIdMinNum" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select min(question_id) questionId from grammar_course_question
	</select>
    <!-- 批量添加本章测试题 -->
    <insert id="addCourseQuestion" parameterType="com.woxue.resourcemanage.entity.grammar.SimulationQuestionBean" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO `grammar_course_question`(id,teach_id, grade, course_id, question_id, question_type, difficulty, question,option_a,option_b,
			option_c,option_d,option_e,correct_option,parse,`year`, `knowledge_point`, `area_id`, `answer_time`)
		VALUE (NULL, #{teachId}, #{grade}, #{courseId}, #{questionId}, #{questionType}, #{difficulty}, #{question},#{optionA},#{optionB},
			#{optionC},#{optionD},#{optionE},#{correctOption},#{parse},#{year}, #{knowledgePoint}, #{aid}, #{answerTime})
	</insert>

    <!-- 获取指定课程更新详情 -->
    <select id="getCourseUpdateDetail" resultType="com.woxue.resourcemanage.entity.grammar.CourseUpdateDetailBean">
        SELECT id, teach_id teachId, course_id courseId,  unit_id unitId, `type`, `comment`, update_time updateTime
        FROM `grammar_new_course_update_detail`
        WHERE course_id = #{courseId}
        <if test="unitId==null">
            AND unit_id IS NULL
        </if>
        <if test="unitId!=null">
            AND unit_id = #{unitId}
        </if>
        AND `type` = #{type}
    </select>
    <!-- 添加课程详情 -->
    <insert id="addCourseUpdateDetail">
		INSERT INTO `grammar_new_course_update_detail`(id, teach_id, course_id, unit_id, `type`)
		VALUE(null, #{teachId}, #{courseId}, #{unitId}, #{type})
	</insert>
    <select id="getCourseQuestionById" parameterType="java.lang.Integer" resultType="com.woxue.resourcemanage.entity.grammar.SimulationQuestionBean">
		select id
			,course_id courseId
			,question_id questionId
			,teach_id teachId
			,grade
			,year
			,knowledge_point knowledgePoint
			,question_type questionType
			,question
			,option_a optionA
			,option_b optionB
			,option_b optionB
			,option_e optionC
			,option_d optionD
			,option_e optionE
			,correct_option correctOption
			,parse
			,difficulty
			,answer_time answerTime
			,create_time createTime
			,area_id areaId
			,is_delete idDelete
		from grammar_course_question where id=#{courseQuestionId}
	</select>
    <!-- 修改本章检测题 -->
    <update id="updateCourseQuestion" parameterType="com.woxue.resourcemanage.entity.grammar.SimulationQuestionBean">
		update `grammar_course_question` set difficulty=#{difficulty}
			,question=#{question}
			,option_a=#{optionA}
			,option_b=#{optionB}
			,option_c=#{optionC}
			,option_d=#{optionD}
			,option_e=#{optionE}
			,correct_option=#{correctOption}
			,parse=#{parse}
			,year=#{year}
			,knowledge_point=#{knowledgePoint}
			,area_id=#{aid}
			,answer_time=#{answerTime} where id=#{id}
	</update>

    <!-- 删除课程的下的试题 -->
    <delete id="deleteCourseQuestion" parameterType="java.lang.Integer">
        update `grammar_course_question` set `is_delete`=1
        WHERE `id` IN
        <foreach collection="array" open="(" close=")" separator="," item="questionId">
            #{questionId}
        </foreach>
    </delete>

    <!-- 获取题库中满足条件的系统试题 -->
    <select id="getSystemQuestionList" resultMap="simulationQuestionBeanMap">
        SELECT * FROM ${DB_QUESTION}.`question`
        WHERE teach_id='system' and question_type!=3
        <if test="gradeArr!=null and gradeArr.length>0">
            AND grade IN
            <foreach collection="gradeArr" open="(" close=")" separator="," item="grade">
                #{grade}
            </foreach>
        </if>
        <if test="gradeArr==null or gradeArr.length==0">
            <if test="gradePhase!=null and gradePhase==30">
                AND  grade &gt;= 30  AND grade &lt; 40
            </if>
            <if test="gradePhase!=null and gradePhase==40">
                AND  grade &gt;= 40  AND grade &lt; 50
            </if>
            <if test="gradePhase!=null and gradePhase==50">
                AND  grade &gt;= 50  AND grade &lt; 59
            </if>
        </if>
        <if test="knowledgePointList!=null and knowledgePointList.size>0">
            AND
            <foreach collection="knowledgePointList" open="(" close=")" separator=" OR " item="knowledgePointMap">
                knowledge_point &gt;= #{knowledgePointMap.start} AND knowledge_point &lt;= #{knowledgePointMap.end}
            </foreach>
        </if>
        <if test="questionTypeArr!=null and questionTypeArr.length>0">
            AND question_type IN
            <foreach collection="questionTypeArr" open="(" close=")" separator="," item="questionType">
                #{questionType}
            </foreach>
        </if>
        <if test="difficultyArr!=null and difficultyArr.length>0">
            AND difficulty IN
            <foreach collection="difficultyArr" open="(" close=")" separator="," item="difficulty">
                #{difficulty}
            </foreach>
        </if>
    </select>
    <!-- 获取题库中满足条件的共享试题 -->
    <select id="getShareQuestionList" resultMap="simulationQuestionBeanMap">
        SELECT sq.* FROM ${DB_QUESTION}.`question` sq
        LEFT JOIN ${DB_OPERATION}.`agent_user` u ON sq.`teach_id`=u.`user_id`
        WHERE sq.is_share=TRUE and question_type!=3
        <if test="noAid!=null and noAid!=''">
            AND u.aid!=#{noAid}
        </if>
        <if test="noTeachId!=null and noTeachId!=''">
            AND sq.`teach_id`!=#{noTeachId}
        </if>
        <if test="gradeArr!=null and gradeArr.length>0">
            AND sq.grade IN
            <foreach collection="gradeArr" open="(" close=")" separator="," item="grade">
                #{grade}
            </foreach>
        </if>
        <if test="gradeArr==null or gradeArr.length==0">
            <if test="gradePhase!=null and gradePhase==30">
                AND sq.grade &gt;= 30  AND sq.grade &lt; 40
            </if>
            <if test="gradePhase!=null and gradePhase==40">
                AND sq.grade &gt;= 40  AND sq.grade &lt; 50
            </if>
            <if test="gradePhase!=null and gradePhase==50">
                AND sq.grade &gt;= 50  AND sq.grade &lt; 59
            </if>
        </if>
        <if test="knowledgePointList!=null and knowledgePointList.size>0">
            AND
            <foreach collection="knowledgePointList" open="(" close=")" separator=" OR " item="knowledgePointMap">
                sq.knowledge_point &gt;= #{knowledgePointMap.start} AND sq.knowledge_point &lt;= #{knowledgePointMap.end}
            </foreach>
        </if>
        <if test="questionTypeArr!=null and questionTypeArr.length>0">
            AND sq.question_type IN
            <foreach collection="questionTypeArr" open="(" close=")" separator="," item="questionType">
                #{questionType}
            </foreach>
        </if>
        <if test="difficultyArr!=null and difficultyArr.length>0">
            AND sq.difficulty IN
            <foreach collection="difficultyArr" open="(" close=")" separator="," item="difficulty">
                #{difficulty}
            </foreach>
        </if>
    </select>

    <!-- 获取题库中满足条件的分校试题 -->
    <select id="getAgentQuestionList" resultMap="simulationQuestionBeanMap">
        SELECT sq.* FROM ${DB_QUESTION}.`question` sq
        LEFT JOIN ${DB_OPERATION}.`agent_user` u ON sq.`teach_id`=u.`user_id`
        WHERE
        u.aid=#{aid} and question_type!=3
        <if test="gradeArr!=null and gradeArr.length>0">
            AND sq.grade IN
            <foreach collection="gradeArr" open="(" close=")" separator="," item="grade">
                #{grade}
            </foreach>
        </if>
        <if test="gradeArr==null or gradeArr.length==0">
            <if test="gradePhase!=null and gradePhase==30">
                AND sq.grade &gt;= 30  AND sq.grade &lt; 40
            </if>
            <if test="gradePhase!=null and gradePhase==40">
                AND sq.grade &gt;= 40  AND sq.grade &lt; 50
            </if>
            <if test="gradePhase!=null and gradePhase==50">
                AND sq.grade &gt;= 50  AND sq.grade &lt; 59
            </if>
        </if>
        <if test="knowledgePointList!=null and knowledgePointList.size>0">
            AND
            <foreach collection="knowledgePointList" open="(" close=")" separator=" OR " item="knowledgePointMap">
                sq.knowledge_point &gt;= #{knowledgePointMap.start} AND sq.knowledge_point &lt;= #{knowledgePointMap.end}
            </foreach>
        </if>
        <if test="questionTypeArr!=null and questionTypeArr.length>0">
            AND sq.question_type IN
            <foreach collection="questionTypeArr" open="(" close=")" separator="," item="questionType">
                #{questionType}
            </foreach>
        </if>
        <if test="difficultyArr!=null and difficultyArr.length>0">
            AND sq.difficulty IN
            <foreach collection="difficultyArr" open="(" close=")" separator="," item="difficulty">
                #{difficulty}
            </foreach>
        </if>
    </select>

    <!-- 获取满足条件的自建试题 -->
    <select id="getTeachQuestionList" resultMap="simulationQuestionBeanMap">
        SELECT * FROM ${DB_QUESTION}.`question`
        WHERE
        teach_id = #{teachId} and question_type!=3
        <if test="gradeArr!=null and gradeArr.length>0">
            AND grade IN
            <foreach collection="gradeArr" open="(" close=")" separator="," item="grade">
                #{grade}
            </foreach>
        </if>
        <if test="gradeArr==null or gradeArr.length==0">
            <if test="gradePhase!=null and gradePhase==30">
                AND  grade &gt;= 30  AND grade &lt; 40
            </if>
            <if test="gradePhase!=null and gradePhase==40">
                AND  grade &gt;= 40  AND grade &lt; 50
            </if>
            <if test="gradePhase!=null and gradePhase==50">
                AND  grade &gt;= 50  AND grade &lt; 59
            </if>
        </if>
        <if test="knowledgePointList!=null and knowledgePointList.size>0">
            AND
            <foreach collection="knowledgePointList" open="(" close=")" separator=" OR " item="knowledgePointMap">
                knowledge_point &gt;= #{knowledgePointMap.start} AND knowledge_point &lt;= #{knowledgePointMap.end}
            </foreach>
        </if>
        <if test="questionTypeArr!=null and questionTypeArr.length>0">
            AND question_type IN
            <foreach collection="questionTypeArr" open="(" close=")" separator="," item="questionType">
                #{questionType}
            </foreach>
        </if>
        <if test="difficultyArr!=null and difficultyArr.length>0">
            AND difficulty IN
            <foreach collection="difficultyArr" open="(" close=")" separator="," item="difficulty">
                #{difficulty}
            </foreach>
        </if>
    </select>

    <!-- 根据id列表获取内容 -->
    <select id="getQuestionByIdFromProgram" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM `grammar_course_question` where question_id=#{questionId} and course_id=#{courseId}
	</select>
    <update id="updateQuestionDelete" parameterType="java.lang.Integer">
		update `grammar_course_question` set is_delete = 0 where question_id=#{questionId}
	</update>

    <!-- 根据id列表获取内容 -->
    <select id="getQuestionById" resultMap="simulationQuestionBeanMap">
		SELECT * FROM ${DB_QUESTION}.`question` where id=#{questionId}
	</select>
    <insert id="addNewCourseQuestion">
        replace into grammar_course_question
        (
        course_id
        ,question_id
        ,teach_id
        ,grade
        ,`year`
        ,knowledge_point
        ,question_type
        ,question
        ,option_a
        ,option_b
        ,option_c
        ,option_d
        ,option_e
        ,option_f
        ,option_g
        ,correct_option
        ,parse
        ,difficulty
        ,answer_time
        ,create_time
        ,area_id
        )
        VALUES
        <foreach collection="questionList" index="index" item="item" separator=",">
            (
            #{courseId},
            #{item.id},
            #{item.teachId},
            #{item.grade},
            #{item.year},
            #{item.knowledgePoint},
            #{item.questionType},
            #{item.question},
            #{item.optionA},
            #{item.optionB},
            #{item.optionC},
            #{item.optionD},
            #{item.optionE},
            #{item.optionF},
            #{item.optionG},
            #{item.correctOption},
            #{item.parse},
            #{item.difficulty},
            #{item.answerTime},
            #{item.createTime},
            #{item.aid}
            )
        </foreach>
    </insert>

    <!-- 获取语法卡片信息 -->
    <select id="getGrammarCard" resultType="com.woxue.resourcemanage.entity.grammar.GrammarNewUnitBean">
		SELECT ru.id id,
			ru.name_en name_en,
			ru.name_cn name,
            rucg.inte_num inteNum,
            rucg.knowledge_count knowNum,
			ru.course_id courseId,
			ru.display_order disporder
		FROM `resource_unit` ru
        left join resource_unit_content_grammar rucg on ru.id=rucg.resource_unit_id
		WHERE ru.id=#{unitId}
	</select>

    <!-- 获取子标题下的问题 -->
    <select id="getUnitContentList" parameterType="Integer" resultMap="grammarContentMap">
		SELECT *
		FROM grammar_content
		WHERE unit_id=#{unitId} AND subtitle_id = #{subtitleId}
		ORDER BY disporder, id
	</select>
    <!-- 获取节下的标题 -->
    <select id="getUnitTitleListByParentId" parameterType="Integer" resultType="com.woxue.resourcemanage.entity.grammar.GrammarUnitTitleBean">
		SELECT id,unit_id unitId,`name`, parent_id parentId, `level`, is_basic isBasic, disporder
		FROM grammar_title
		WHERE unit_id = #{unitId} AND parent_id = #{parentId}
		ORDER BY disporder, id
	</select>
    <!-- 获取子标题 -->
    <select id="getUnitSubtitleList" parameterType="Integer" resultType="com.woxue.resourcemanage.entity.grammar.GrammarUnitSubtitleBean">
		SELECT id,unit_id unitId,title_id titleId,`name`,`type`, disporder
		FROM `grammar_subtitle`
		WHERE unit_id = #{unitId} AND title_id = #{titleId}
		ORDER BY disporder, id
	</select>

    <!-- 根据id列表获取标题 -->
    <select id="getUnitTitleListByIdList" resultType="com.woxue.resourcemanage.entity.grammar.GrammarUnitTitleBean">
        SELECT id, unit_id unitId, `name`, parent_id parentId, `level`, is_basic isBasic, disporder
        FROM `grammar_title`
        WHERE id IN
        <foreach collection="titleIdList" item="titleId" open="(" close=")" separator=",">
            #{titleId}
        </foreach>
    </select>

    <!-- 根据id列表获取子标题 -->
    <select id="getUnitSubtitleListByIdList" resultType="com.woxue.resourcemanage.entity.grammar.GrammarUnitSubtitleBean">
        SELECT id, `unit_id` unitId, title_id titleId, `name`, `type`, disporder
        FROM `grammar_subtitle`
        WHERE id IN
        <foreach collection="subtitleIdList" item="subtitleId" open="(" close=")" separator=",">
            #{subtitleId}
        </foreach>
    </select>

    <!-- 根据id列表获取内容 -->
    <select id="getUnitContentListByIdList" resultMap="grammarContentMap">
        SELECT * FROM `grammar_content`
        WHERE id IN
        <foreach collection="contentIdList" item="contentId" open="(" close=")" separator=",">
            #{contentId}
        </foreach>
    </select>


    <!-- 获取知识点列表 -->
    <select id="getKnowledgeList" resultType="java.util.HashMap">
		SELECT ut.id, ut.unit_id unitId, ut.`name`, ut.parent_id parentId, ut.`level`, ut.is_basic isBasic, COUNT(tq.`question_id`) questionNum
		FROM `grammar_title` ut
		LEFT JOIN `grammar_title_question` tq ON ut.id = tq.`title_id`
		WHERE ut.`unit_id` = #{unitId}
		GROUP BY ut.id
		ORDER BY ut.disporder
	</select>

    <!-- 获取试题数量 -->
    <select id="getKnowQuestionNum" resultType="int">
        SELECT COUNT(gq.id)
        FROM `grammar_question` gq
        LEFT JOIN `grammar_title_question` tq ON gq.`id` = tq.`question_id`
        WHERE gq.unit_id = #{unitId} AND
        <if test="allKnow">
            tq.`title_id` IS NOT NULL
        </if>
        <if test="not allKnow">
            tq.`title_id` IS NULL
        </if>
    </select>
    <!-- 获取试题列表 -->
    <select id="getQuestionList" resultMap="grammarQuestionMap">
    SELECT temp.*, tq2.title_id FROM
    (SELECT gq.*
    FROM `grammar_question` gq
    LEFT JOIN `grammar_title_question` tq ON gq.id = tq.question_id
    WHERE gq.unit_id=#{unitId}
    <choose>
        <when test="titleIds=='allKnow'">
            AND tq.title_id IS NOT NULL
        </when>
        <when test="titleIds=='noKnow'">
            AND tq.title_id IS NULL
        </when>
        <when test="titleIds!=null and titleIds!=''">
            AND tq.title_id IN (${titleIds})
        </when>
    </choose>
    GROUP BY gq.id) AS temp
    LEFT JOIN `grammar_title_question` tq2 ON temp.id = tq2.question_id
    ORDER BY temp.id
    </select>

    <!-- 修改试题 -->
    <update id="updateQuestion" >
		UPDATE `grammar_question`
		SET teach_id=#{teachId},
			difficulty=#{difficulty},
			question=#{question},
			option_a=#{optionA},
			option_b=#{optionB},
			option_c=#{optionC},
			option_d=#{optionD},
			option_e=#{optionE},
			correct_option=#{correctOption},
            answer_type=#{answerType},
			parse=#{parse},
			`source`=#{source},
			`year`=#{year},
			`area_id`=#{areaId}
		WHERE id=#{id}
	</update>
    <!--删除试题知识点关联信息 -->
  <delete id="deleteTitleQuestion">
      DELETE FROM `grammar_title_question`
      WHERE question_id IN
      <foreach collection="questionIdArr" open="(" close=")" item="questionId" separator=",">
          #{questionId}
      </foreach>
  </delete>
    <!--替换知识点的关联信息 -->
  <insert id="addTitleQuestion">
      REPLACE INTO `grammar_title_question`(title_id, question_id)
      VALUES
      <foreach collection="titleQuestionList" separator="," item="item">
          (#{item.titleId}, #{item.questionId})
      </foreach>
  </insert>

  <!-- 添加试题 -->
    <insert id="addQuestion"  useGeneratedKeys="true" keyProperty="id">
		INSERT INTO `grammar_question`(id,teach_id,unit_id,question_type, difficulty, question,option_a,option_b,
			option_c,option_d,option_e,correct_option,parse, `source`, `year`, `area_id`,`answer_type`)
		VALUE (NULL, #{teachId}, #{unitId}, #{questionType}, #{difficulty}, #{question},#{optionA},#{optionB},
			#{optionC},#{optionD},#{optionE},#{correctOption},#{parse}, #{source}, #{year}, #{areaId}, #{answerType})
	</insert>
    <!-- 批量添加试题 -->
    <insert id="addQuestionList" >
        INSERT INTO `grammar_question`(id,teach_id,unit_id,question_type, difficulty, question,option_a,option_b,
        option_c,option_d,option_e,correct_option,parse, `source`, `year`, `area_id`,`answer_type`)
        VALUES
        <foreach collection="list" separator="," item="item">
            (NULL, #{item.teachId}, #{item.unitId}, #{item.questionType}, #{item.difficulty}, #{item.question},#{item.optionA},#{item.optionB},
            #{item.optionC},#{item.optionD},#{item.optionE},#{item.correctOption},#{item.parse}, #{item.source}, #{item.year}, #{item.areaId}, #{item.answerType})
        </foreach>
    </insert>
    <!-- 批量添加试题-多份阅读材料 -->
    <insert id="addQuestionContentList">
        INSERT INTO `grammar_question_content`(question_id,img_content,text_content, display_order)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{questionId}, #{item.imgContent}, #{item.textContent}, #{item.displayOrder})
        </foreach>
    </insert>

    <update id="updateQuestionContent">
        update grammar_question_content set img_content = #{imgContent}, text_content = #{textContent}, display_order = #{displayOrder}
        where id = #{id}
    </update>
    <delete id="deleteQuestionContentById">
        delete from grammar_question_content where id = #{id}
    </delete>
    <delete id="deleteQuestionContentByQuestionId">
        delete from grammar_question_content where question_id = #{questionId}
    </delete>

    <!-- 获取应用题列表 -->
    <select id="getApplyQuestionList" resultType="java.util.HashMap">
        SELECT uc.id , uc.`unit_id` unitId, ust.`title_id` titleId, uc.`question_type` questionType, uc.`question`,
        uc.`option_a` optionA, uc.`option_b` optionB, uc.`option_c` optionC, uc.`option_d` optionD, uc.`option_e` optionE,
        uc.correct_option correctOption, uc.`parse`, NOT ISNULL(gq.`id`) copied
        FROM `grammar_content` uc
        LEFT JOIN `grammar_subtitle` ust ON uc.`subtitle_id` = ust.`id`
        LEFT JOIN `grammar_title_question` gtq ON ust.`title_id` = gtq.`title_id`
        LEFT JOIN `grammar_question` gq ON gtq.question_id = gq.`id`
        WHERE uc.`unit_id`=#{unitId} AND ust.`type` = 4
        <if test="titleId!=null and titleId &gt; 0">
            AND ust.title_id = #{titleId}
        </if>
    </select>

    <!-- 根据试题id删除试题 -->
    <delete id="deleteQuestion">
        DELETE FROM `grammar_question`
        WHERE id IN
        <foreach collection="questionIdArr" open="(" close=")" item="questionId" separator=",">
            #{questionId}
        </foreach>
    </delete>
    <delete id="deleteTitle">
        DELETE FROM `grammar_title` WHERE id=#{id}
    </delete>
    <delete id="deleteSubTitle">
        DELETE FROM `grammar_subtitle` WHERE id=#{id}
    </delete>
    <delete id="deleteContent">
        DELETE FROM `grammar_content` WHERE id=#{id}
    </delete>

    <!-- 获取省份列表 -->
    <select id="getAreaList" resultType="java.util.Map">
		SELECT id, `name`, parent_id parentId FROM ${DB_QUESTION}.`area`  where parent_id=0  ORDER BY id
	</select>
    <!-- 更新课程修改详情的补充说明 -->
    <update id="updateCourseUpdateDetailComment">
        UPDATE `grammar_new_course_update_detail` SET `comment` = #{comment}
        WHERE course_id = #{courseId}
        <if test="unitId==null">
            AND unit_id IS NULL
        </if>
        <if test="unitId!=null">
            AND unit_id = #{unitId}
        </if>
        AND `type` = #{type}
    </update>
    <!-- 获取语法内容列表 -->
    <select id="getGrammarContentList" parameterType="Integer" resultMap="grammarContentMap">
        SELECT * FROM `grammar_content`
        WHERE id IN
        <foreach collection="array" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="getUnitList" resultMap="grammarUnitMap">
        SELECT
            *
        FROM
            (
                SELECT
                    a.id unit_id,
                    IFNULL( b.grammar_unit_id,- 1 ) grammar_unit_id,
                    b.knowledge_count knowledge_count,
                    a.name_cn unit_name,
                    a.display_order display_order
                FROM
                    resource_unit a
                        LEFT JOIN resource_unit_content_grammar b ON a.id = b.resource_unit_id
                WHERE
                    a.course_id = #{courseId}
            ) tmp1
                LEFT JOIN (
                SELECT
                    unit_id question_unit_id,
                    COUNT( 1 ) total,
                    COUNT( CASE WHEN difficulty = 1 THEN 1 END ) easy,
                    COUNT( CASE WHEN difficulty = 2 THEN 1 END ) mid,
                    COUNT( CASE WHEN difficulty = 3 THEN 1 END ) difficulty
                FROM
                    grammar_question
                GROUP BY
                    unit_id
            ) tmp2 ON tmp1.unit_id = tmp2.question_unit_id
        ORDER BY tmp1.display_order
    </select>
    <!-- 添加单元卡片的内容 -->
    <insert id="addUnitContent" parameterType="com.woxue.resourcemanage.entity.grammar.GrammarUnitContentBean" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO `grammar_content`(id, unit_id, subtitle_id, question_type, question,
			option_a, option_b, option_c, option_d, option_e, correct_option,
			wrong_tips_1, wrong_tips_2, wrong_tips_3, parse, disporder)
		VALUE(NULL, #{unitId}, #{subtitleId}, #{questionType}, #{question}, 
			#{optionA}, #{optionB}, #{optionC}, #{optionD}, #{optionE}, #{correctOption},
			#{wrongTips1}, #{wrongTips2}, #{wrongTips3}, #{parse}, #{disporder})
	</insert>
    <!-- 更新单元卡片的内容 unit_id = #{unitId},-->
    <update id="updateUnitContent" parameterType="com.woxue.resourcemanage.entity.grammar.GrammarUnitContentBean">
		UPDATE `grammar_content`
		SET
			subtitle_id = #{subtitleId},
			question_type = #{questionType},
			question = #{question},
			option_a = #{optionA},
			option_b = #{optionB},
			option_c = #{optionC},
			option_d = #{optionD},
			option_e = #{optionE},
			correct_option = #{correctOption},
			wrong_tips_1 = #{wrongTips1},
			wrong_tips_2 = #{wrongTips2},
			wrong_tips_3 = #{wrongTips3},
			parse = #{parse},
			disporder = #{disporder}
		WHERE id = #{id}
	</update>
    <!-- 更新标题是最小知识点 -->
    <update id="updateTitleIsBasic">
		UPDATE `grammar_title` SET is_basic = TRUE WHERE id = #{titleId}
	</update>

    <!-- 添加单元内容的标题 -->
    <insert id="addUnitTitle" parameterType="com.woxue.resourcemanage.entity.grammar.GrammarUnitTitleBean" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO `grammar_title`(id, unit_id, `name`, parent_id, `level`, is_basic, disporder)
		VALUE(NULL, #{unitId}, #{name}, #{parentId}, #{level}, #{isBasic}, #{disporder})
	</insert>

    <!-- 更新单元内容的标题 -->
    <update id="updateUnitTitle" parameterType="com.woxue.resourcemanage.entity.grammar.GrammarUnitTitleBean">
		UPDATE `grammar_title`
		SET unit_id = #{unitId},
			`name` = #{name},
			`parent_id` = #{parentId},
			`level` = #{level},
			`is_basic` = #{isBasic},
			`disporder` = #{disporder}
		WHERE id = #{id}
	</update>

    <!-- 添加单元内容的子标题 -->
    <insert id="addUnitSubtitle" parameterType="com.woxue.resourcemanage.entity.grammar.GrammarUnitSubtitleBean" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO `grammar_subtitle`(id, unit_id, title_id, `name`, `type`, disporder)
		VALUE(NULL, #{unitId}, #{titleId}, #{name}, #{type}, #{disporder})
	</insert>
    <insert id="addGrammarUnit">
        INSERT INTO `resource_unit_content_grammar` ( `resource_unit_id`, `grammar_course_id`, `grammar_unit_id`, `knowledge_count`, `show_name`) VALUES (#{unitId}, -1, #{unitId}, 0, '');
    </insert>

    <!-- 更新单元内容的子标题 -->
    <update id="updateUnitSubtitle" parameterType="com.woxue.resourcemanage.entity.grammar.GrammarUnitSubtitleBean">
		UPDATE `grammar_subtitle`
		SET unit_id = #{unitId},
			`title_id` = #{titleId},
			`name` = #{name},
			`type` = #{type},
			`disporder` = #{disporder}
		WHERE id = #{id}
	</update>

    <!-- 更新单元的互动步数 -->
    <update id="updateUnitInteNum">
		UPDATE `resource_unit_content_grammar` SET inte_num = #{inteNum}
		WHERE resource_unit_id = #{unitId}
	</update>

    <!-- 更新单元知识点数量 -->
    <update id="updateUnitKnowNum">
		UPDATE `resource_unit_content_grammar` u
		SET u.knowledge_count = (SELECT COUNT(ut.id) FROM `grammar_title` ut WHERE ut.`unit_id`=u.`resource_unit_id` AND ut.`is_basic`=TRUE)
		WHERE u.resource_unit_id = #{unitId}
	</update>
    <update id="updateUnitKnowledgeCount">
        update resource_unit_content_grammar
        set knowledge_count=#{count} where resource_unit_id=#{unitId}
    </update>

    <insert id="replaceUnitContent">
        REPLACE INTO `grammar_content`(id, unit_id, subtitle_id, question_type, question,
        option_a, option_b, option_c, option_d, option_e, correct_option,
        wrong_tips_1, wrong_tips_2, wrong_tips_3, parse, disporder)
        VALUE(#{id}, #{unitId}, #{subtitleId}, #{questionType}, #{question},
        #{optionA}, #{optionB}, #{optionC}, #{optionD}, #{optionE}, #{correctOption},
        #{wrongTips1}, #{wrongTips2}, #{wrongTips3}, #{parse}, #{disporder})
    </insert>

    <insert id="replaceUnitTitle">
        REPLACE INTO `grammar_title`(id, unit_id, `name`, parent_id, `level`, is_basic, disporder)
        VALUE(#{id}, #{unitId}, #{name}, #{parentId}, #{level}, #{isBasic}, #{disporder})
    </insert>
</mapper>