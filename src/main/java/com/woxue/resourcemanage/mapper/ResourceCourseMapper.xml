<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourceCourseDao">

    <select id="getCourseList" resultType="com.woxue.common.model.redBook.RedBookCourse">
        select id
             ,version_id versionId
             ,name_en nameEn
             ,name_cn nameCn
             ,unit_num unitNum
             ,contain_word containWord
             ,contain_phrase containPhrase
             ,contain_sentence containSentence
             ,contain_grammar containGrammar
             ,contain_question containQuestion
             ,contain_article containArticle
             ,contain_read containRead
             ,contain_write containWrite
             ,resource_course.stage
             ,grade
             ,display_order displayOrder
             ,resource_course.branch_id branchId
        from resource_course
        LEFT JOIN resource_course_stage ON resource_course.id = resource_course_stage.course_id
        where
        version_id = #{versionId}
        <if test="stage!=null and stage!=''">
            AND (resource_course.stage=#{stage} or resource_course_stage.stage=#{stage})
        </if>
        order by display_order
    </select>

       <select id="getCourseCount" resultType="Integer">
           SELECT COUNT(*) FROM  `resource_course`  WHERE version_id=#{versionId}
           <if test="stage!=null and stage!=''">
               AND  stage=#{stage}
           </if>
           ;
       </select>
        <select id="getMaxDisplayOrder" resultType="Integer">
            SELECT MAX(display_order) maxDisplayOrder FROM   `resource_course` ;
        </select>
        <insert id="insertCourse" parameterType="com.woxue.resourcemanage.entity.ResourceCourse" keyProperty="id" useGeneratedKeys="true">
            INSERT `resource_course`(version_id,name_en,name_cn,unit_num,stage,grade,display_order)VALUES
            (#{versionId},#{nameEn},#{nameCn},#{unitNum},#{stage},#{grade},#{displayOrder})
        </insert>
        <select id="getCourseById" resultType="map">
            SELECT * FROM  `resource_course`  WHERE id=#{id}
        </select>
        <update id="updateCourse">
             UPDATE  `resource_course`  SET name_en=#{nameEn},name_cn=#{nameCn},grade=#{grade} WHERE id=#{id};
        </update>
         <update id="updateCourseUnitNum">
           UPDATE  `resource_course`  SET unit_num=#{unitNum} WHERE id=#{id}
         </update>

        <select id="getCourseWord" resultType="map">
            SELECT id,resource_course_id AS resourceCourseId,program_name AS programName,show_name AS showName
            FROM `resource_course_word` WHERE resource_course_id=#{courseId}
        </select>

    <!--查询课程下关联的其他产品的课程-->
    <select id="getCourseSentence" resultType="map">
            SELECT id,resource_course_id AS resourceCourseId,program_name AS programName,show_name AS showName
            FROM `resource_course_sentence` WHERE resource_course_id=#{courseId}
        </select>
    <select id="getCourseQuestion" resultType="map">
            SELECT id,resource_course_id AS resourceCourseId,sync_question_course_id AS programId,show_name AS showName
            FROM `resource_course_question` WHERE resource_course_id=#{courseId}
         </select>
    <update id="updateCourseContentContainStatus">
        UPDATE  `resource_course`
        SET
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@WORD">
            contain_word=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@WORD_PHRASE">
            contain_phrase=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@SENTENCE">
            contain_sentence=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@GRAMMAR">
            contain_grammar=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@ARTICLE">
            contain_article=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@QUERSTION">
            contain_question=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@READ">
            contain_read=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@WRITE">
            contain_write=#{flag}
        </if>
        <if test="contentType == @com.woxue.common.model.redBook.RedBookContentTypeEnum@LISTEN">
            contain_listen=#{flag}
        </if>
        WHERE id=#{courseId}
    </update>
    <select id="getCourseGrammar" resultType="map">
            SELECT id,resource_course_id AS resourceCourseId,grammar_course_id AS programId,show_name AS showName
            FROM `resource_course_grammar` WHERE resource_course_id=#{courseId}
         </select>
        <select id="getCourseArticle" resultType="map">
             SELECT id,resource_course_id AS resourceCourseId,program_name AS programName,show_name AS showName
            FROM `resource_course_article` WHERE resource_course_id=#{courseId}
        </select>

        <!--关联课程-->
        <insert id="insertCourseWord">
             INSERT `resource_course_word`(`resource_course_id`,`program_name`,`show_name`)
             VALUES(#{resourceCourseId},#{programName},#{showName});
        </insert>
        <insert id="insertCourseSentence">
             INSERT  `resource_course_sentence`(`resource_course_id`,`program_name`,`show_name`)
             VALUES(#{resourceCourseId},#{programName},#{showName});
        </insert>
        <insert id="insertCourseQuestion">
             INSERT  `resource_course_question`(`resource_course_id`,`sync_question_course_id`,`show_name`)
             VALUES(#{resourceCourseId},#{programId},#{showName});
        </insert>
        <insert id="insertCourseGrammar">
              INSERT  `resource_course_grammar`(`resource_course_id`,`grammar_course_id`,`show_name`)
             VALUES(#{resourceCourseId},#{programId},#{showName});
        </insert>
        <insert id="insertCourseArticle">
            INSERT  `resource_course_article`(`resource_course_id`,`program_name`,`show_name`)
             VALUES(#{resourceCourseId},#{programName},#{showName});
        </insert>

        <delete id="delOldCourseWord">
             DELETE FROM   `resource_course_word` WHERE resource_course_id=#{resourceCourseId} AND  program_name=#{oldProgramName};
        </delete>
        <delete id="delOldCourseSentence">
             DELETE FROM   `resource_course_sentence` WHERE resource_course_id=#{resourceCourseId};
        </delete>
        <delete id="delOldCourseQuestion">
             DELETE FROM   `resource_course_question` WHERE resource_course_id=#{resourceCourseId};
        </delete>
        <delete id="delOldCourseGrammar">
             DELETE FROM   `resource_course_grammar` WHERE resource_course_id=#{resourceCourseId};
        </delete>
        <delete id="delOldCourseArticle">
             DELETE FROM   `resource_course_article` WHERE resource_course_id=#{resourceCourseId};
        </delete>


        <select id="isHaveCourseWord" resultType="map">
            SELECT * FROM `resource_course_word` WHERE program_name=#{programName};
        </select>
    
        <select id="isHaveCourseSentence" resultType="map">
            SELECT * FROM `resource_course_sentence` WHERE program_name=#{programName};
        </select>
        <select id="isHaveCourseArticle" resultType="map">
            SELECT * FROM `resource_course_article` WHERE program_name=#{programName};
        </select>

        <select id="isHaveCourseQuestion" resultType="map">
            SELECT * FROM `resource_course_question` WHERE sync_question_course_id=#{programId};
        </select>
        <select id="isHaveCourseGrammar" resultType="map">
            SELECT * FROM `resource_course_grammar` WHERE grammar_course_id=#{programId};
        </select>


</mapper>