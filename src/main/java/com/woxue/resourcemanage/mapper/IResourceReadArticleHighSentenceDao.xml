<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceReadArticleHighSentenceDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.read.ResourceReadArticleHighSentenceBean">
        <id column="sentence_id" property="sentenceId" />
        <result column="article_id" property="articleId" />
        <result column="content" property="content" />
        <result column="photo" property="photo" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="listByArticleId" resultMap="BaseResultMap">
        select sentence_id,article_id,content,photo,update_time
        from resource_read_article_high_sentence
        where article_id = #{articleId}
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select sentence_id,article_id,content,photo,update_time
        from resource_read_article_high_sentence
        where sentence_id = #{sentenceId}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleHighSentenceBean"
            useGeneratedKeys="true"  keyProperty="articleId">
        insert into resource_read_article_high_sentence (article_id,content,photo,update_time)
        values (#{articleId},#{content},#{photo},#{updateTime})
    </insert>

    <insert id="batchSave" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleHighSentenceBean">
        insert into resource_read_article_high_sentence (article_id,content,photo)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.articleId},#{item.content},#{item.photo})
        </foreach>

    </insert>



    <update id="update" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleHighSentenceBean">
        update resource_read_article_high_sentence
        <set>
            <if test="content != null">
                content=#{content},
            </if>
            <if test="photo != null">
                photo=#{photo},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where sentence_id = #{sentenceId}
    </update>


    <delete id="deleteByArticleId">
        delete from resource_read_article_high_sentence where article_id = #{articleId}
    </delete>

</mapper>
