<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ReadExpandReadArticleCorrelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean">
        <id column="correlation_id" property="correlationId" />
        <result column="article_id" property="articleId" />
        <result column="content" property="content" />
        <result column="translate" property="translate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="list" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_correlation
        where article_id = #{articleId}
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_correlation
        where correlation_id = #{correlationId}
    </select>

    <select id="editByArticleId" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_correlation
        where article_id = #{articleId}
    </select>

    <insert id="save" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean"
            useGeneratedKeys="true"  keyProperty="correlationId">
        insert into readexpand_read_article_correlation (article_id,content,translate,create_time,update_time)
        values (#{articleId},#{content},#{translate},#{createTime},#{updateTime})
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean">
        update readexpand_read_article_correlation
        <set>
            <if test="articleId != null">
                article_id=#{articleId},
            </if>
            <if test="content != null and content != ''">
                content=#{content},
            </if>
            <if test="translate != null and translate != ''">
                translate=#{translate},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where correlation_id = #{correlationId}
    </update>

</mapper>
