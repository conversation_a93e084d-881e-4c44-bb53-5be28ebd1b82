<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ReadExpandReadArticleWordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean">
        <id column="word_id" property="wordId" />
        <result column="article_id" property="articleId" />
        <result column="spelling" property="spelling" />
        <result column="syllable" property="syllable" />
        <result column="meaning_zh_CN" property="meaningZhCn" />
        <result column="sound_file" property="soundFile" />
        <result column="sort" property="sort" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="list" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_word
        where article_id = #{articleId}
        order by sort,word_id
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select *
        from readexpand_read_article_word
        where word_id = #{wordId}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean"
            useGeneratedKeys="true"  keyProperty="wordId">
        insert into readexpand_read_article_word (article_id,spelling,syllable,meaning_zh_CN,sound_file,sort)
        values (#{articleId},#{spelling},#{syllable},#{meaningZhCn},#{soundFile},#{sort})
    </insert>

    <insert id="batchSave" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean">
        insert into readexpand_read_article_word (article_id,spelling,syllable,meaning_zh_CN,sound_file,sort)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.articleId},#{item.spelling},#{item.syllable},#{item.meaningZhCn},#{item.soundFile},
             #{item.sort})
        </foreach>
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean">
        update readexpand_read_article_word
        <set>
            <if test="articleId != null">
                article_id=#{articleId},
            </if>
            <if test="spelling != null">
                spelling=#{spelling},
            </if>
            <if test="syllable != null">
                syllable=#{syllable},
            </if>
            <if test="meaningZhCn != null">
                meaning_zh_CN=#{meaningZhCn},
            </if>
            <if test="soundFile != null">
                sound_file=#{soundFile},
            </if>
            <if test="sort != null">
                sort=#{sort}
            </if>
        </set>
        where word_id = #{wordId}
    </update>


    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update readexpand_read_article_word
            <set>
                <if test="item.articleId != null">
                    article_id=#{item.articleId},
                </if>
                <if test="item.spelling != null and item.spelling != ''">
                    spelling=#{item.spelling},
                </if>
                <if test="item.syllable != null and item.spelling != ''">
                    syllable=#{item.syllable},
                </if>
                <if test="item.meaningZhCn != null and item.spelling != ''">
                    meaning_zh_CN=#{item.meaningZhCn},
                </if>
                <if test="item.soundFile != null">
                    sound_file=#{item.soundFile},
                </if>
                <if test="item.sort != null">
                    sort=#{item.sort}
                </if>
            </set>
            where word_id = #{item.wordId}
        </foreach>
    </update>


    <delete id="delete">
        delete from readexpand_read_article_word where word_id = #{wordId}
    </delete>

</mapper>
