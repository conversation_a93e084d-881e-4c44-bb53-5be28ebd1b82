<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourceUnitDao">
        <select id="getUnitList" resultType="map">
            SELECT id,course_id AS courseId,name_en AS nameEn,name_cn AS nameCn,
            content_num AS contentNum,display_order AS displayOrder
             FROM  `resource_unit` WHERE  course_id=#{courseId}
            order by display_order ASC
            <if test="pageStart!=null">
                LIMIT #{pageStart}, #{pageSize}
            </if>
            ;
        </select>
        <select id="getUnitCount" resultType="Integer">
            SELECT COUNT(*) FROM  `resource_unit` WHERE course_id=#{courseId};
        </select>

        <select id="getUnitByCourse" resultType="Integer">
            SELECT id FROM `resource_unit`
            WHERE course_id=#{courseId} and name_en=#{nameEn}
        </select>

        <select id="getUnitMaxDisplayOrder" resultType="Integer">
            SELECT MAX(display_order) maxDisplayOrder FROM   `resource_unit` ;
        </select>
        <insert id="insertUnit">
            INSERT  `resource_unit` (course_id,name_en,name_cn,content_num,display_order)VALUES(#{courseId},#{nameEn},#{nameCn},0,#{displayOrder});
        </insert>
        
        <insert id="insertUnits">
            INSERT into `resource_unit` (course_id,name_en,name_cn,content_num,display_order)value
            <foreach collection="resourceUnits" item="nameEn" index="index" separator=",">
                (#{courseId},#{nameEn},#{nameEn},0,#{index})
            </foreach>
        </insert>
        <select id="getUnitById" resultType="map">
            SELECT * FROM  `resource_unit` WHERE id=#{id};
        </select>
        <update id="updateUnit">
           UPDATE  `resource_unit`  SET   name_en=#{nameEn},name_cn=#{nameCn} WHERE id=#{id};
        </update>
        <update id="updateUnitContentNum">
            UPDATE  `resource_unit`  SET content_num=content_num+#{contentNum} WHERE id=#{resourceUnitId}
        </update>


    <select id="selectByName" resultType="java.lang.Integer">
        select id from `resource_unit` where name_en = #{unitName} and course_id = #{courseId}
    </select>
    <select id="getUnitContentWordIdList" resultType="Integer">
             SELECT id FROM `resource_unit`
            WHERE   course_id=#{resourceCourseId}
    </select>
    <select id="getUnitContentSentenceIdList" resultType="Integer">
           SELECT ucw.id FROM  `resource_unit_content_sentence` ucw
           LEFT JOIN  `resource_unit` u ON  ucw.resource_unit_id=u.id
           LEFT JOIN  `resource_course_sentence` cw  ON cw.resource_course_id=u.course_id
           WHERE  cw.resource_course_id=#{resourceCourseId}  AND cw.program_name!=#{programName};
    </select>
    <select id="getUnitContentArticleIdList" resultType="Integer">
           SELECT ucw.id articleId FROM  `resource_unit_article` ucw
           LEFT JOIN  `resource_unit` u ON  ucw.resource_unit_id=u.id
           LEFT JOIN  `resource_course_article` cw  ON cw.resource_course_id=u.course_id
           WHERE  cw.resource_course_id=#{resourceCourseId}  AND cw.program_name!=#{programName};
    </select>

    <select id="getUnitContentQuestionIdList" resultType="Integer">
           SELECT ucw.id FROM  `resource_unit_content_question` ucw
           LEFT JOIN  `resource_unit` u ON  ucw.resource_unit_id=u.id
           LEFT JOIN  `resource_course_question` cw  ON cw.resource_course_id=u.course_id
           WHERE  cw.resource_course_id=#{resourceCourseId}  AND cw.sync_question_course_id!=#{programId};
    </select>
    <select id="getUnitContentGrammarIdList" resultType="Integer">
           SELECT ucw.id FROM  `resource_unit_content_grammar` ucw
           LEFT JOIN  `resource_unit` u ON  ucw.resource_unit_id=u.id
           LEFT JOIN  `resource_course_grammar` cw  ON cw.resource_course_id=u.course_id
           WHERE  cw.resource_course_id=#{resourceCourseId}  AND cw.grammar_course_id!=#{programId};
    </select>

    <delete id="delUnitContentWord">
         DELETE  FROM  `resource_unit_content_word` WHERE id=#{id}  AND  program_name=#{oldProgramName}
    </delete>
    <delete id="delUnitContentSentence">
         DELETE  FROM  `resource_unit_content_sentence` WHERE id=#{id}
    </delete>
    <delete id="delUnitContentQuestion">
         DELETE  FROM  `resource_unit_content_question` WHERE id=#{id}
    </delete>
    <delete id="delUnitContentGrammar">
         DELETE  FROM  `resource_unit_content_grammar` WHERE id=#{id}
    </delete>
    <delete id="delUnitContentArticle">
         DELETE  FROM  `resource_unit_article` WHERE id=#{id}
    </delete>



    <select id="getUnitQuestion" resultType="map">
       SELECT * FROM  `resource_unit_content_question`  WHERE  paper_id=#{paperId};
    </select>
    <select id="getUnitQuestionByCourseID" resultType="map">
       SELECT * FROM  `resource_unit_content_question` rucq
                left join resource_unit ru on ru.id=rucq.resource_unit_id
                WHERE  rucq.paper_id=#{paperId} and ru.course_id=#{courseId} ;
    </select>


    <insert id="insertUnitContentWord">
        insert into `resource_unit_content_word`
            (resource_unit_id,`level`,word_count,has_example_sentence)
        values (#{unitId},#{level},1,#{hasExample});
    </insert>

    <update id="updateUnitContentWordCount">
        update `resource_unit_content_word`
        set word_count=word_count+1
        where resource_unit_id=#{unitId} and `level`=#{level}
    </update>

    <insert id="insertUnitContentSentence">
        insert into `resource_unit_content_sentence`
            (resource_unit_id,`level`,sentence_count)
        values (#{unitId},#{level},1);
    </insert>

    <update id="updateUnitContentSentenceCount">
        update `resource_unit_content_sentence`
        set sentence_count=sentence_count+1
        where resource_unit_id=#{unitId} and `level`=#{level}
    </update>


</mapper>