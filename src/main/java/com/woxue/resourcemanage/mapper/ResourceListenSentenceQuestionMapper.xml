<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ResourceListenSentenceQuestionDao">
    
    <resultMap type="com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean" id="ResourceListenSentenceQuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="unitId"    column="unit_id"    />
        <result property="type"    column="type"    />
        <result property="question"    column="question"    />
        <result property="optionA"    column="option_a"    />
        <result property="optionB"    column="option_b"    />
        <result property="optionC"    column="option_c"    />
        <result property="optionD"    column="option_d"    />
        <result property="answer"    column="answer"    />
        <result property="parse"    column="parse"    />
    </resultMap>

    <sql id="selectResourceListenSentenceQuestionVo">
        select question_id, unit_id, type, question, option_a, option_b, option_c, option_d, answer, parse from resource_listen_sentence_question
    </sql>

    <select id="selectResourceListenSentenceQuestionList" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean" resultMap="ResourceListenSentenceQuestionResult">
        <include refid="selectResourceListenSentenceQuestionVo"/>
        <where>  
            <if test="unitId != null "> and unit_id = #{unitId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="question != null  and question != ''"> and question = #{question}</if>
            <if test="optionA != null  and optionA != ''"> and option_a = #{optionA}</if>
            <if test="optionB != null  and optionB != ''"> and option_b = #{optionB}</if>
            <if test="optionC != null  and optionC != ''"> and option_c = #{optionC}</if>
            <if test="optionD != null  and optionD != ''"> and option_d = #{optionD}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="parse != null  and parse != ''"> and parse = #{parse}</if>
        </where>
    </select>
    
    <select id="selectResourceListenSentenceQuestionByQuestionId" resultMap="ResourceListenSentenceQuestionResult">
        <include refid="selectResourceListenSentenceQuestionVo"/>
        where question_id = #{questionId}
    </select>
    <select id="listByUnitId" resultMap="ResourceListenSentenceQuestionResult">
        <include refid="selectResourceListenSentenceQuestionVo"/>
        where unit_id = #{unitId}
    </select>
        
    <insert id="insertResourceListenSentenceQuestion" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean" useGeneratedKeys="true" keyProperty="questionId">
        insert into resource_listen_sentence_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitId != null">unit_id,</if>
            <if test="type != null">type,</if>
            <if test="question != null">question,</if>
            <if test="optionA != null">option_a,</if>
            <if test="optionB != null">option_b,</if>
            <if test="optionC != null">option_c,</if>
            <if test="optionD != null">option_d,</if>
            <if test="answer != null">answer,</if>
            <if test="parse != null">parse,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitId != null">#{unitId},</if>
            <if test="type != null">#{type},</if>
            <if test="question != null">#{question},</if>
            <if test="optionA != null">#{optionA},</if>
            <if test="optionB != null">#{optionB},</if>
            <if test="optionC != null">#{optionC},</if>
            <if test="optionD != null">#{optionD},</if>
            <if test="answer != null">#{answer},</if>
            <if test="parse != null">#{parse},</if>
         </trim>
    </insert>

    <insert id="replaceInsert" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean" useGeneratedKeys="true" keyProperty="questionId">
        replace into resource_listen_sentence_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionId != null">question_id,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="type != null">type,</if>
            <if test="question != null">question,</if>
            <if test="optionA != null">option_a,</if>
            <if test="optionB != null">option_b,</if>
            <if test="optionC != null">option_c,</if>
            <if test="optionD != null">option_d,</if>
            <if test="answer != null">answer,</if>
            <if test="parse != null">parse,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionId != null">#{questionId},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="type != null">#{type},</if>
            <if test="question != null">#{question},</if>
            <if test="optionA != null">#{optionA},</if>
            <if test="optionB != null">#{optionB},</if>
            <if test="optionC != null">#{optionC},</if>
            <if test="optionD != null">#{optionD},</if>
            <if test="answer != null">#{answer},</if>
            <if test="parse != null">#{parse},</if>
        </trim>
    </insert>

    <update id="updateResourceListenSentenceQuestion" parameterType="com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean">
        update resource_listen_sentence_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="question != null">question = #{question},</if>
            <if test="optionA != null">option_a = #{optionA},</if>
            <if test="optionB != null">option_b = #{optionB},</if>
            <if test="optionC != null">option_c = #{optionC},</if>
            <if test="optionD != null">option_d = #{optionD},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="parse != null">parse = #{parse},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteResourceListenSentenceQuestionByQuestionId">
        delete from resource_listen_sentence_question where question_id = #{questionId}
    </delete>

    <delete id="deleteResourceListenSentenceQuestionByQuestionIds" parameterType="String">
        delete from resource_listen_sentence_question where question_id in 
        <foreach item="questionId" collection="array" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>

</mapper>