<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceReadArticleQuestionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.read.ResourceReadArticleQuestionBean">
        <id column="question_id" property="questionId" />
        <result column="article_id" property="articleId" />
        <result column="question" property="question" />
        <result column="option_a" property="optionA" />
        <result column="option_b" property="optionB" />
        <result column="option_c" property="optionC" />
        <result column="option_d" property="optionD" />
        <result column="answer" property="answer" />
        <result column="parse" property="parse" />
        <result column="update_time" property="updateTime" />
    </resultMap>


    <select id="listByArticleId" resultMap="BaseResultMap">
        select question_id,article_id,question,option_a,option_b,option_c,option_d,answer,parse,update_time
        from resource_read_article_question
        where article_id = #{articleId}
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select question_id,article_id,question,option_a,option_b,option_c,option_d,answer,parse,update_time
        from resource_read_article_question
        where question_id = #{questionId}
    </select>


    <insert id="batchSave" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleQuestionBean">
        insert into resource_read_article_question (article_id,question,option_a,option_b,option_c,option_d,answer,parse)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.articleId},#{item.question},#{item.optionA},#{item.optionB},#{item.optionC},
            #{item.optionD},#{item.answer},#{item.parse})
        </foreach>

    </insert>


    <update id="update" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleQuestionBean">
        update resource_read_article_question
        <set>
            <if test="articleId != null">
                article_id=#{articleId},
            </if>
            <if test="question != null">
                question=#{question},
            </if>
            <if test="optionA != null">
                option_a=#{optionA},
            </if>
            <if test="optionB != null">
                option_b=#{optionB},
            </if>
            <if test="optionC != null">
                option_c=#{optionC},
            </if>
            <if test="optionD != null">
                option_d=#{optionD},
            </if>
            <if test="answer != null">
                answer=#{answer},
            </if>
            <if test="parse != null">
                parse=#{parse},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where question_id = #{questionId}
    </update>


    <delete id="deleteByArticleId">
        delete from resource_read_article_question where article_id = #{articleId}
    </delete>
</mapper>
