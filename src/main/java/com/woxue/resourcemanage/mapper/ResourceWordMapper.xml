<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourceWordDao">
    <delete id="deleteSoundMark">
        delete from soundmark
        where spelling = #{spelling}
    </delete>
    <select id="querySyllableBySpelling" resultType="string">
        select w.syllable
        from resource_word w
        where w.spelling = #{spelling}
          and w.syllable is not null
          and w.syllable!='' limit 1
    </select>

    <insert id="insertUnitWord" parameterType="com.woxue.resourcemanage.entity.ResourceUnitWord" useGeneratedKeys="true"
            keyProperty="unitWordId">
        insert into `resource_unit_word`(resource_unit_id, `level`, word_id, display_order)
        values (#{unitId}, #{level}, #{wordId}, #{displayOrder})
    </insert>
    <insert id="insertUnitSentence" parameterType="com.woxue.resourcemanage.entity.ResourceUnitSentence"
            useGeneratedKeys="true" keyProperty="id">
        insert into `resource_unit_sentence`(resource_unit_id,`level`, word_id, display_order)
        values (#{unitId},#{level}, #{wordId}, #{displayOrder})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.woxue.resourcemanage.entity.WordEntity">
        update resource_word
        <set>
            <if test="spelling != null">
                spelling = #{spelling,jdbcType=VARCHAR},
            </if>
            <if test="syllable != null">
                syllable = #{syllable,jdbcType=VARCHAR},
            </if>
            <if test="meaning_en_US != null">
                meaning_en_US = #{meaning_en_US,jdbcType=VARCHAR},
            </if>
            <if test="meaning_zh_CN != null">
                meaning_zh_CN = #{meaning_zh_CN,jdbcType=VARCHAR},
            </if>
            <if test="example_en_US != null">
                example_en_US = #{example_en_US,jdbcType=VARCHAR},
            </if>
            <if test="example_zh_CN != null">
                example_zh_CN = #{example_zh_CN,jdbcType=VARCHAR},
            </if>
            <if test="soundFile != null">
                sound_file = #{soundFile,jdbcType=VARCHAR},
            </if>
            <if test="synonymous != null">
                synonymous = #{synonymous,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{wordId,jdbcType=INTEGER}
    </update>

    <insert id="insertWordInfo" useGeneratedKeys="true" keyProperty="wordId">
        insert into `resource_word`(spelling, syllable, sound_file, meaning_en_US, meaning_zh_CN, example_en_US,
                                    example_zh_CN)
        values (#{spelling}, #{syllable}, #{soundFile}, #{meaning_en_US}, #{meaning_zh_CN}, #{example_en_US},
                #{example_zh_CN});
    </insert>

    <select id="getSentenceElementBySentence" resultType="java.util.Map">
        select se.example_en_US, se.element_jz, se.example_clause_en_US, se.element_clause_jz,se.confirm_flag
        from resource_word w
                 left join sentence_element se on w.id = se.word_id
        where se.element_jz is not null
          and se.element_jz!='' and w.example_en_US=#{sentenceEn} limit 1
    </select>

    <!-- 插入sentence_element记录 -->
    <insert id="insertSentenceElement">
        insert into `sentence_element`(word_id, example_en_US, element_jz, example_clause_en_US,element_clause_jz)
        values (#{wordId}, #{example_en_US}, #{element_jz},#{example_clause_en_US},#{element_clause_jz})
    </insert>
    <update id="updateSentenceElement">
        update `sentence_element`
        set example_en_US=#{example_en_US},
            element_jz=#{element_jz},
        example_clause_en_US=#{example_clause_en_US},
        element_clause_jz=#{element_clause_jz},
        confirm_flag=#{confirmFlag}
        where word_id = #{wordId}
    </update>

    <select id="getWordList" resultType="com.woxue.resourcemanage.entity.WordEntity">
        select
        w.id wordId,
        w.spelling,
        w.syllable,
        w.sound_file soundFile,
        w.meaning_en_US,
        w.meaning_zh_CN,
        w.example_en_US,
        w.example_zh_CN,
        c.id courseId,
        concat(v.name_cn,"-",c.name_cn) courseName,
        u.id unitId,
        u.name_cn unitName,
        w.modify_time,
        uw.level
        from resource_word w
        LEFT JOIN resource_unit_word uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        left join resource_course c on u.course_id = c.id
        left join resource_version v on c.version_id = v.id
        where w.id!=0
        <if test="spelling!=null and spelling!='' ">
            and w.spelling=#{spelling}
        </if>
        <if test="wordId!=null">
            and w.id=#{wordId}
        </if>
        <if test="courseId!=null">
            and c.id = #{courseId}
        </if>
        <if test="type ==  @com.woxue.common.model.redBook.RedBookContentTypeEnum@WORD ">
            and uw.level not in (7,8,9)
        </if>
        <if test="type ==  @com.woxue.common.model.redBook.RedBookContentTypeEnum@WORD_PHRASE ">
            and uw.level in (7,8,9)
        </if>
        order by u.display_order,uw.display_order
        <if test="pageIndex!=null">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="getWordCount" resultType="int">
        select
        count(w.id)
        from resource_word w
        LEFT JOIN resource_unit_word uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        where w.id!=0
        <if test="spelling!=null and spelling!='' ">
            and w.spelling=#{spelling}
        </if>
        <if test="wordId!=null">
            and w.id=#{wordId}
        </if>
        <if test="courseId!=null">
            and u.course_id = #{courseId}
        </if>
        <if test="type ==  @com.woxue.common.model.redBook.RedBookContentTypeEnum@WORD ">
            and uw.level not in (7,8,9)
        </if>
        <if test="type ==  @com.woxue.common.model.redBook.RedBookContentTypeEnum@WORD_PHRASE ">
            and uw.level in (7,8,9)
        </if>
    </select>

    <select id="queryExamplesList" resultType="com.woxue.resourcemanage.entity.WordEntity">
        select
        w.id wordId,
        w.spelling,
        w.syllable,
        w.sound_file soundFile,
        w.meaning_en_US,
        w.meaning_zh_CN,
        w.example_en_US,
        w.example_zh_CN,
        c.id courseId,
        concat(v.name_cn,"-",c.name_cn) courseName,
        u.id unitId,
        u.name_cn unitName,
        w.modify_time,
        uw.level
        from resource_word w
        LEFT JOIN resource_unit_word uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        left join resource_course c on u.course_id = c.id
        left join resource_version v on c.version_id = v.id
        where
        <if test="searchFrom=='spelling'">
            w.spelling=#{spelling}
        </if>
        <if test="searchFrom!='spelling'">
            w.example_en_US like concat('%',#{spelling},'%')
        </if>
        <if test="courseId!=null">
            and u.course_id = #{courseId}
        </if>
        <if test="pageIndex!=null">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="queryExampleCount" resultType="int">
        select
        count(w.id)
        from resource_word w
        LEFT JOIN resource_unit_word uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        where
        <if test="searchFrom=='spelling'">
            w.spelling=#{spelling}
        </if>
        <if test="searchFrom!='spelling'">
            w.example_en_US like concat('%',#{spelling},'%')
        </if>
        <if test="courseId!=null">
            and u.course_id = #{courseId}
        </if>
    </select>

    <select id="getNoSyllableWordList" resultType="com.woxue.resourcemanage.entity.WordEntity">
        select
        w.id wordId,
        w.spelling,
        w.syllable,
        w.sound_file soundFile,
        w.meaning_en_US,
        w.meaning_zh_CN,
        w.example_en_US,
        w.example_zh_CN,
        c.id courseId,
        concat(v.name_cn,"-",c.name_cn) courseName,
        u.id unitId,
        u.name_cn unitName,
        w.modify_time,
        uw.level
        from resource_word w
        LEFT JOIN resource_unit_word uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        left join resource_course c on u.course_id = c.id
        left join resource_version v on c.version_id = v.id
        where (w.syllable is NULL or w.syllable='')
        and u.course_id = #{courseId}
        order by u.display_order,uw.display_order
        <if test="pageIndex!=null">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="getNoSyllableWordCount" resultType="int">
        select count(w.id)
        from resource_word w
                 LEFT JOIN resource_unit_word uw on w.id = uw.word_id
                 left join resource_unit u on u.id = uw.resource_unit_id
        where (w.syllable is NULL or w.syllable = '')
          and u.course_id = #{courseId}
    </select>

    <select id="getNoExampleWordList" resultType="com.woxue.resourcemanage.entity.WordEntity">
        select
        w.id wordId,
        w.spelling,
        w.syllable,
        w.sound_file soundFile,
        w.meaning_en_US,
        w.meaning_zh_CN,
        w.example_en_US,
        w.example_zh_CN,
        c.id courseId,
        concat(v.name_cn,"-",c.name_cn) courseName,
        u.id unitId,
        u.name_cn unitName,
        w.modify_time,
        uw.level
        from resource_word w
        LEFT JOIN resource_unit_word uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        left join resource_course c on u.course_id = c.id
        left join resource_version v on c.version_id = v.id
        where (w.example_en_US is NULL or w.example_en_US='')
        and u.course_id = #{courseId}
        order by u.display_order,uw.display_order
        <if test="pageIndex!=null">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="getNoExampleWordCount" resultType="int">
        select count(w.id)
        from resource_word w
                 LEFT JOIN resource_unit_word uw on w.id = uw.word_id
                 left join resource_unit u on u.id = uw.resource_unit_id
        where (w.example_en_US is NULL or w.example_en_US = '')
          and u.course_id = #{courseId}
    </select>

    <select id="getSentenceList" resultType="com.woxue.resourcemanage.entity.WordEntity">
        select
        w.id wordId,
        w.spelling,
        w.syllable,
        w.sound_file soundFile,
        w.meaning_en_US,
        w.meaning_zh_CN,
        w.example_en_US,
        w.example_zh_CN,
        c.id courseId,
        concat(v.name_cn,"-",c.name_cn) courseName,
        u.id unitId,
        u.name_cn unitName,
        w.modify_time,
        se.example_en_US sentenceCompositionEn,
        se.element_jz sentenceCompositionCn,
        se.example_clause_en_US sentenceClauseCompositionEn,
        se.element_clause_jz sentenceClauseCompositionCn,
        se.confirm_flag as confirmFlag,
        uw.level
        from resource_word w
        LEFT JOIN resource_unit_sentence uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        left join resource_course c on u.course_id = c.id
        left join resource_version v on c.version_id = v.id
        left join sentence_element se on w.id = se.word_id
        where w.id!=0
        and c.id is not null
        <if test="spelling!=null and spelling!='' ">
            and w.example_en_US like concat('%',#{spelling},'%')
        </if>
        <if test="wordId!=null">
            and w.id=#{wordId}
        </if>
        <if test="courseId!=null">
            and c.id = #{courseId}
        </if>
        <if test="divide!=null and divide==1">
            and se.element_jz is not null
        </if>
        <if test="divide!=null and divide==2">
            and se.element_jz is null
        </if>
        order by u.display_order,uw.display_order
        <if test="pageIndex!=null">
            limit #{pageIndex},#{pageSize}
        </if>
    </select>

    <select id="getSentenceCount" resultType="int">
        select
        count(w.id)
        from resource_word w
        LEFT JOIN resource_unit_sentence uw on w.id = uw.word_id
        left join resource_unit u on u.id = uw.resource_unit_id
        left join sentence_element se on w.id = se.word_id
        where w.id!=0
        <if test="spelling!=null and spelling!='' ">
            and w.example_en_US like concat('%',#{spelling},'%')
        </if>
        <if test="wordId!=null">
            and w.id=#{wordId}
        </if>
        <if test="courseId!=null">
            and u.course_id = #{courseId}
        </if>
        <if test="divide!=null and divide==1">
            and se.element_jz is not null
        </if>
        <if test="divide!=null and divide==2">
            and se.element_jz is null
        </if>
    </select>

    <update id="updateWordDetail" parameterType="java.util.HashMap">
        update resource_word
        set spelling=#{spelling},
            syllable=#{syllable},
            meaning_en_US=#{meaning_en_US},
            meaning_zh_CN=#{meaning_zh_CN},
            example_en_US=#{example_en_US},
            example_zh_CN=#{example_zh_CN},
            modify_time=now()
        where id = #{wordId}
    </update>
    <update id="updateSentence">
        update resource_word
        set example_en_US=#{example_en_US},
            example_zh_CN=#{example_zh_CN},
            modify_time=now()
        where id = #{wordId}
    </update>

    <!--获取单词助记信息-->
    <select id="getWordMnemonics" resultType="java.util.HashMap" parameterType="java.lang.String">
        SELECT *
        FROM `word_mnemonics`
        WHERE spelling = #{value}
    </select>
    <select id="getWordMnemonicsList" resultType="java.util.HashMap">
        SELECT *
        FROM `word_mnemonics` limit #{pageIndex},#{pageSize}
    </select>
    <select id="getWordMnemonicsCount" resultType="int">
        SELECT count(id)
        FROM `word_mnemonics`
    </select>
    <insert id="insertWordMnemonics" parameterType="java.util.HashMap">
        insert into word_mnemonics(spelling, mnemonics, other_mnemonics1, other_word_ids1, other_mnemonics2,
                                   other_word_ids2, no_mnemonics_word_ids)
        values (#{spelling}, #{mnemonics}, #{other_mnemonics1}, #{other_word_ids1}, #{other_mnemonics2},
                #{other_word_ids2}, #{no_mnemonics_word_ids})
    </insert>
    <update id="updateWordMnemonics" parameterType="java.util.HashMap">
        update word_mnemonics
        SET mnemonics=#{mnemonics},
            other_mnemonics1=#{other_mnemonics1},
            other_word_ids1=#{other_word_ids1},
            other_mnemonics2=#{other_mnemonics2},
            other_word_ids2=#{other_word_ids2},
            no_mnemonics_word_ids=#{no_mnemonics_word_ids}
        WHERE spelling = #{spelling}
    </update>

    <!--获取分音节单词列表-->
    <select id="getSoundMarkList" resultType="java.util.Map">
        SELECT spelling,soundmark from soundmark
        <if test="spelling!=null and spelling!=''">
            where spelling = #{spelling}
        </if>
        <if test="pageNum!=null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <!--修改单词或音节-->
    <update id="updateSoundMark">
        update soundmark
        set spelling  =#{newSpelling},
            soundmark =#{soundMark}
        WHERE spelling = #{oldSpelling}
    </update>

    <!--是否有这个音标-->
    <select id="getSoundMark" resultType="java.lang.String">
        SELECT spelling
        from soundmark
        where spelling = #{spelling}
    </select>


    <!--添加音标-->
    <insert id="insertSoundMark">
        INSERT into soundmark (spelling, soundmark)
        values (#{spelling}, #{soundMark})
    </insert>

    <select id="getSoundMarkCount" resultType="java.lang.Integer">
        select Count(1)
        from soundmark
    </select>

    <select id="getWordDisturbList" resultType="com.woxue.resourcemanage.entity.WordDisturb">
        SELECT spelling,
        disturb_word_id disturbWordId,
        disturb_spelling disturbSpelling,
        disturb_meaning disturbMeaning,
        modify_time modifyTime
        FROM `word_disturb`
        <if test="spelling!=null and spelling!='' ">
            WHERE spelling=#{spelling}
        </if>
        order by spelling
        limit #{pageIndex},#{pageSize}
    </select>
    <select id="countWordDisturbSpelling" resultType="int">
        SELECT count(id) FROM `word_disturb`
        <if test="spelling!=null and spelling!='' ">
            WHERE spelling=#{spelling}
        </if>
    </select>
    <select id="queryUnitMaxDisplayOrder" resultType="java.lang.Integer"
            parameterType="com.woxue.resourcemanage.entity.ResourceUnitWord">
        select ifnull(max(display_order), 0)
        from resource_unit_word
        where resource_unit_id = #{unitId}
    </select>

    <update id="updateWordDisturb">
        update word_disturb
        set disturb_word_id=#{disturbWordId},
            disturb_spelling=#{disturbSpelling},
            disturb_meaning=#{disturbMeaning}
        where spelling = #{spelling}

    </update>

    <select id="queryWordAbbr" resultType="com.woxue.common.model.redBook.WordAbbr">
        select *
        from word_abbr
    </select>
    <select id="countSoundMark" resultType="java.lang.Integer">
        select count(1)
        from soundmark
        <if test="spelling!=null and spelling!=''">
            where spelling = #{spelling}
        </if>
    </select>

    <insert id="addWordAbbr">
        insert into word_abbr(abbr, spelling, syllable)
        values (#{abbr}, #{spelling}, #{syllable})
    </insert>

    <update id="updateWordAbbr">
        update word_abbr
        set spelling=#{spelling},
            syllable=#{syllable}
        where abbr = #{abbr}
    </update>
    <!--CREATE TABLE `resource_word_use` (
    `spelling` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `example_en_US` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `status` int NOT NULL DEFAULT '0' COMMENT '是否已包含解析',
    PRIMARY KEY (`spelling`,`example_en_US`),
    UNIQUE KEY `word_idx` (`spelling`,`example_en_US`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='用词资源';-->
    <insert id="insertWordUse">
        insert into resource_word_use(spelling, example_en_US, content)
        values (#{spelling}, #{sentence}, #{content})
    </insert>

    <update id="updateWordUseStatus">
        update resource_word_use
        set status = #{status}
        where spelling = #{spell}
          and example_en_US = #{sentence}
    </update>

    <select id="getWordUseList" resultType="java.util.Map">
        select spelling, example_en_US, content, status
        from resource_word_use
        <where>
            <if test="spelling!=null and spelling!=''">
                spelling like  concat('%',#{spelling},'%')
            </if>
            <if test="status != null">
                status = #{status}
            </if>
        </where>
        limit #{pageIndex},#{pageSize}
    </select>

    <select id="getWordUseCount" resultType="java.lang.Integer">
        select count(1)
        from resource_word_use
        <where>
            <if test="spelling!=null and spelling!=''">
                spelling like  concat('%',#{spelling},'%')
            </if>
            <if test="status != null">
                status = #{status}
            </if>
        </where>
    </select>

    <update id="updateWordUse">
        update resource_word_use
        set content=#{content}
        where spelling = #{spelling}
          and example_en_US = #{sentence}
    </update>
</mapper>