<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ResourceMnemonicWordDao">
    
    <resultMap type="com.woxue.resourcemanage.entity.ResourceMnemonicWord" id="ResourceMnemonicWordResult">
        <result property="id"    column="id"    />
        <result property="courseId"    column="course_id"    />
        <result property="unitId"    column="unit_id"    />
        <result property="wordId"    column="word_id"    />
        <result property="status"    column="status"    />
        <result property="likeNum"    column="like_num"    />
        <result property="dislikeNum"    column="dislike_num"    />
        <result property="feedContent"    column="feed_content"    />
        <result property="relationContent"    column="relation_content"    />
        <result property="courseDetails"    column="course_details"    />
        <result property="unitName"    column="unit_name"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectResourceMnemonicWordVo">
        select id, course_id, unit_id, word_id, status,like_num, dislike_num, feed_content, relation_content, course_details, unit_name,create_time, update_time from resource_mnemonic_word
    </sql>

    <select id="selectResourceMnemonicWordList" resultMap="ResourceMnemonicWordResult">
        <include refid="selectResourceMnemonicWordVo"/>
        <where>
            <if test="status != null "> and status = #{status}</if>
            <if test="relationContent != null  and relationContent != ''"> and relation_content like concat('%', #{relationContent}, '%')</if>
            <if test="courseDetails != null  and courseDetails != ''"> and course_details like concat('%', #{courseDetails}, '%')</if>
        </where>
        <choose>
            <when test="sort != null and sort != '' and order != null and order != ''">
                ORDER BY ${order} ${sort}
            </when>
            <otherwise>
                ORDER BY create_time desc
            </otherwise>
        </choose>
        LIMIT #{pageNum}, #{pageSize}
    </select>

    <select id="count" resultType="int">
        select count(*) from resource_mnemonic_word
        <where>
            <if test="status != null "> and status = #{status}</if>
            <if test="relationContent != null  and relationContent != ''"> and relation_content like concat('%', #{relationContent}, '%')</if>
            <if test="courseDetails != null  and courseDetails != ''"> and course_details like concat('%', #{courseDetails}, '%')</if>
        </where>
    </select>
    
    <select id="selectResourceMnemonicWordById" resultMap="ResourceMnemonicWordResult">
        <include refid="selectResourceMnemonicWordVo"/>
        where id = #{id}
    </select>
    <select id="selectByWordId" resultMap="ResourceMnemonicWordResult">
        <include refid="selectResourceMnemonicWordVo"/>
        where word_id = #{wordId}
    </select>
        
    <insert id="insertResourceMnemonicWord" parameterType="com.woxue.resourcemanage.entity.ResourceMnemonicWord" useGeneratedKeys="true" keyProperty="id">
        insert into resource_mnemonic_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="wordId != null">word_id,</if>
            <if test="status != null">status,</if>
            <if test="likeNum != null">like_num,</if>
            <if test="dislikeNum != null">dislike_num,</if>
            <if test="feedContent != null">feed_content,</if>
            <if test="relationContent != null">relation_content,</if>
            <if test="courseDetails != null">course_details,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="wordId != null">#{wordId},</if>
            <if test="status != null">#{status},</if>
            <if test="likeNum != null">#{likeNum},</if>
            <if test="dislikeNum != null">#{dislikeNum},</if>
            <if test="feedContent != null">#{feedContent},</if>
            <if test="relationContent != null">#{relationContent},</if>
            <if test="courseDetails != null">#{courseDetails},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateResourceMnemonicWord" parameterType="com.woxue.resourcemanage.entity.ResourceMnemonicWord">
        update resource_mnemonic_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="wordId != null">word_id = #{wordId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="likeNum != null">like_num = #{likeNum},</if>
            <if test="dislikeNum != null">dislike_num = #{dislikeNum},</if>
            <if test="feedContent != null">feed_content = #{feedContent},</if>
            <if test="relationContent != null">relation_content = #{relationContent},</if>
            <if test="courseDetails != null">course_details = #{courseDetails},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResourceMnemonicWordById">
        delete from resource_mnemonic_word where id = #{id}
    </delete>

    <delete id="deleteResourceMnemonicWordByIds" parameterType="String">
        delete from resource_mnemonic_word where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>