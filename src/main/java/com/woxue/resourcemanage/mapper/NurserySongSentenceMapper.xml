<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.INurserySongSentenceDao">

    <resultMap id="BaseResultMap" type="com.redbook.kid.common.model.NurserySongSentenceDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="song_id" property="songId" jdbcType="INTEGER"/>
        <result column="sentence_index" property="sentenceIndex" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="INTEGER"/>
        <result column="end_time" property="endTime" jdbcType="INTEGER"/>
        <result column="lyrics_text" property="lyricsText" jdbcType="VARCHAR"/>
        <result column="lyrics_pinyin" property="lyricsPinyin" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="difficulty_score" property="difficultyScore" jdbcType="DECIMAL"/>
        <result column="reference_audio_url" property="referenceAudioUrl" jdbcType="VARCHAR"/>
        <result column="phonetic_data" property="phoneticData" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, song_id, sentence_index, start_time, end_time, lyrics_text, lyrics_pinyin,
        duration, difficulty_score, reference_audio_url, phonetic_data,
        create_time, update_time, is_deleted
    </sql>

    <insert id="insertSentence" parameterType="com.redbook.kid.common.model.NurserySongSentenceDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO nursery_song_sentence (
            song_id, sentence_index, start_time, end_time, lyrics_text, lyrics_pinyin,
            duration, difficulty_score, reference_audio_url, phonetic_data
        ) VALUES (
            #{songId}, #{sentenceIndex}, #{startTime}, #{endTime}, #{lyricsText}, #{lyricsPinyin},
            #{duration}, #{difficultyScore}, #{referenceAudioUrl}, #{phoneticData}
        )
    </insert>

    <insert id="batchInsertSentences" parameterType="java.util.List">
        INSERT INTO nursery_song_sentence (
            song_id, sentence_index, start_time, end_time, lyrics_text, lyrics_pinyin,
            duration, difficulty_score, reference_audio_url, phonetic_data
        ) VALUES
        <foreach collection="sentences" item="sentence" separator=",">
            (
                #{sentence.songId}, #{sentence.sentenceIndex}, #{sentence.startTime}, #{sentence.endTime},
                #{sentence.lyricsText}, #{sentence.lyricsPinyin}, #{sentence.duration},
                #{sentence.difficultyScore}, #{sentence.referenceAudioUrl}, #{sentence.phoneticData}
            )
        </foreach>
    </insert>

    <select id="getSentenceById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song_sentence
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <select id="getSentencesBySongId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song_sentence
        WHERE song_id = #{songId} AND is_deleted = 0
        ORDER BY sentence_index ASC
    </select>

    <select id="getSentenceBySongIdAndIndex" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song_sentence
        WHERE song_id = #{songId} AND sentence_index = #{sentenceIndex} AND is_deleted = 0
    </select>

    <update id="updateSentence" parameterType="com.redbook.kid.common.model.NurserySongSentenceDO">
        UPDATE nursery_song_sentence
        SET sentence_index = #{sentenceIndex},
            start_time = #{startTime},
            end_time = #{endTime},
            lyrics_text = #{lyricsText},
            lyrics_pinyin = #{lyricsPinyin},
            duration = #{duration},
            difficulty_score = #{difficultyScore},
            reference_audio_url = #{referenceAudioUrl},
            phonetic_data = #{phoneticData},
            update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <update id="deleteSentence" parameterType="java.lang.Integer">
        UPDATE nursery_song_sentence
        SET is_deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="deleteSentencesBySongId" parameterType="java.lang.Integer">
        UPDATE nursery_song_sentence
        SET is_deleted = 1, update_time = NOW()
        WHERE song_id = #{songId}
    </update>

    <select id="countSentencesBySongId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM nursery_song_sentence
        WHERE song_id = #{songId} AND is_deleted = 0
    </select>

    <update id="batchDeleteSentences">
        UPDATE nursery_song_sentence
        SET is_deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateSentenceIndex">
        UPDATE nursery_song_sentence
        SET sentence_index = #{newIndex}, update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

</mapper>
