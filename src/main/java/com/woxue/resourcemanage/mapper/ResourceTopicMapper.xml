<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceTopicDao">
  <resultMap id="BaseResultMap" type="com.woxue.resourcemanage.entity.ResourceTopic">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="CHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, type
  </sql>

  <insert id="insertSelective" parameterType="com.woxue.resourcemanage.entity.ResourceTopic">
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into resource_topic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
    <insert id="addTopic">
      insert into resource_topic (`name`, `type`)
      values (#{topicName}, #{type})
    </insert>
    <select id="selectTopicByNameAndType" resultMap="BaseResultMap">
    select id from resource_topic where `type` = #{type} and `name` = #{topicName} limit 1
  </select>
  <select id="selectTopicListByType" resultType="com.woxue.resourcemanage.entity.ResourceTopic">
    select * from resource_topic where `type` = #{type} order by id asc
  </select>
    <select id="getTopicNameById" resultType="java.lang.String">
      select name from resource_topic where id = #{topicId} limit 1
    </select>
</mapper>