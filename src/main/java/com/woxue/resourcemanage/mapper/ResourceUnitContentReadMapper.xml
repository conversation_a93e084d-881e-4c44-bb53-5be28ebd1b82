<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceUnitContentReadDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.resourcemanage.entity.read.ResourceUnitContentReadBean">
        <id column="id" property="id" />
        <result column="resource_unit_id" property="resourceUnitId" />
        <result column="topic_id" property="topicId" />
        <result column="topic_name" property="topicName" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="editByUnitId" resultMap="BaseResultMap">
        select r.id,r.resource_unit_id,r.topic_id,r.update_time,t.name as topicName
        from resource_unit_content_read r
        left join resource_topic t on t.id = r.topic_id and t.type = 1
        where r.resource_unit_id = #{unitId}
    </select>

    <select id="editByTopicId" resultMap="BaseResultMap">
        select r.id,r.resource_unit_id,r.topic_id,r.update_time,t.name as topicName
        from resource_unit_content_read r
        left join resource_topic t on t.id = r.topic_id and t.type = 1
        where r.topic_id = #{topicId}
    </select>

    <insert id="save" parameterType="com.woxue.resourcemanage.entity.read.ResourceUnitContentReadBean"
            useGeneratedKeys="true"  keyProperty="correlationId">
        insert into resource_unit_content_read (resource_unit_id,topic_id)
        values (#{resourceUnitId},#{topicId})
    </insert>

    <update id="update" parameterType="com.woxue.resourcemanage.entity.read.ResourceUnitContentReadBean">
        update resource_unit_content_read
        <set>
            <if test="topicId != null">
                topic_id=#{topicId},
            </if>
        </set>
        where resource_unit_id = #{resourceUnitId}
    </update>

</mapper>
