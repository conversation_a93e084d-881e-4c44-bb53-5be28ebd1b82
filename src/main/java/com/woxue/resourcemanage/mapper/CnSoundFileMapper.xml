<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourceservice.dao.ICnSoundFileDao">
    
    <select id="getSoundFileByCn" resultType="java.lang.String">
        select sound_file
        from cn_sound_file
        where cn = #{cn}
    </select>
    
    <insert id="saveCnSoundFile">
        insert into cn_sound_file (cn, sound_file)
        values (#{cn}, #{soundFile})
    </insert>
    
</mapper> 