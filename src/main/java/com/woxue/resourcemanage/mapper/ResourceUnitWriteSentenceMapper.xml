<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceUnitWriteSentenceDao">
  <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.ResourceUnitWriteSentence">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.woxue.common.model.redBook.ResourceUnitWriteSentence">
    <result column="sentence_en_US" jdbcType="LONGVARCHAR" property="sentenceEnUs" />
    <result column="sentence_zh_CN" jdbcType="LONGVARCHAR" property="sentenceZhCn" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unit_id, display_order
  </sql>
  <sql id="Blob_Column_List">
    sentence_en_US, sentence_zh_CN
  </sql>
  <!-- 添加单元内容的标题 -->
  <insert id="addSentence" parameterType="com.woxue.common.model.redBook.ResourceUnitWriteSentence" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO `resource_unit_write_sentence`(id, unit_id, `display_order`, sentence_en_US, `sentence_zh_CN`)
      VALUE(NULL, #{unitId}, #{displayOrder}, #{sentenceEnUs}, #{sentenceZhCn})
  </insert>

  <!-- 更新单元内容的标题 -->
  <update id="updateSentence" parameterType="com.woxue.common.model.redBook.ResourceUnitWriteSentence">
    UPDATE `resource_unit_write_sentence`
    SET unit_id = #{unitId},
        `display_order` = #{displayOrder},
        `sentence_en_US` = #{sentenceEnUs},
        `sentence_zh_CN` = #{sentenceZhCn}
    WHERE id = #{id}
  </update>

  <delete id="deleteByUnitId">
    delete from resource_unit_write_sentence where unit_id = #{unitId}
  </delete>
  <insert id="batchInsert" parameterType="java.util.List">
    insert into resource_unit_write_sentence (
    unit_id, sentence_en_US,sentence_zh_CN)
    values
    <foreach collection="lists" item="item" separator=",">
      (#{item.unitId}, #{item.sentenceEnUs},#{item.sentenceZhCn})
    </foreach>
  </insert>
  <select id="getSentenceTrainListByUnitId" resultMap="ResultMapWithBLOBs">
    select * from resource_unit_write_sentence where unit_id = #{unitId} order by display_order asc
  </select>
</mapper>