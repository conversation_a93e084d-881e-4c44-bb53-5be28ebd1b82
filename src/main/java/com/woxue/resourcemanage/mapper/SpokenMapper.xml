<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.ISpokenDao">


	<insert id="insertTopic" keyProperty="id">
		insert into resource_spoken_topic(name,sense,cover,video_url,`desc`,displayorder)
		values(#{name},#{sense},#{cover},#{videoUrl},#{desc},#{displayorder})
	</insert>

	<update id="updateTopic">
		update resource_spoken_topic
		<set>
			<if test="name!=null">name=#{name},</if>
			<if test="sense!=null">sense=#{sense},</if>
			<if test="cover!=null">cover=#{cover},</if>
			<if test="videoUrl!=null">video_url=#{videoUrl},</if>
			<if test="desc!=null">`desc`=#{desc},</if>
			<if test="displayorder!=null">displayorder=#{displayorder},</if>
		</set>
		where id=#{id}
	</update>
	<update id="updateTopicContent"
			parameterType="com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent">
		update resource_spoken_topic_content
		<set>
			<if test="spelling!=null">spelling=#{spelling},</if>
			<if test="syllable!=null">syllable=#{syllable},</if>
			<if test="type!=null">type=#{type},</if>
			<if test="meaningEnUS!=null">meaning_en_US=#{meaningEnUS},</if>
			<if test="meaningZhCN!=null">meaning_zh_CN=#{meaningZhCN},</if>
		</set>
		where id=#{id}
	</update>
	<delete id="deleteTopicContent" parameterType="int">
		delete from resource_spoken_topic_content where id=#{id}
	</delete>
	<delete id="deleteTopic" parameterType="int">
		delete from resource_spoken_topic where id=#{id}
	</delete>

	<select id="getAllResourceSpokenTopic"
			resultType="com.woxue.common.model.redBook.spoken.ResourceSpokenTopic">
		select `id`,`name`, `sense`,`cover`,`video_url`as videoUrl,`desc`, `displayorder` from resource_spoken_topic order by displayorder
	</select>

	<resultMap id="topicContent" type="com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent">
		<id column="id" property="id"/>
		<result column="topic_id" property="topicId"/>
		<result column="spelling" property="spelling"/>
		<result column="syllable" property="syllable"/>
		<result column="type" property="type"/>
		<result column="meaning_en_US" property="meaningEnUs"/>
		<result column="meaning_zh_CN" property="meaningZhCn"/>
	</resultMap>

	<select id="getResourceSpokenTopicContentByTopicId"
			resultMap="topicContent" parameterType="int">
		select * from resource_spoken_topic_content where topic_id=#{topicId}
	</select>

	<insert id="insertTopicContent"
			parameterType="com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent">
		insert into resource_spoken_topic_content(topic_id,spelling,syllable,type,meaning_en_US,meaning_zh_CN)
		values(#{topicId},#{spelling},#{syllable},#{type},#{meaningEnUS},#{meaningZhCN})
	</insert>



</mapper>