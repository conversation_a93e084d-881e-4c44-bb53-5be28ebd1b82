<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ReadExpandReadArticleDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleBean">
        <id column="article_id" property="articleId" />
        <result column="version_id" property="versionId" />
        <result column="course_id" property="courseId" />
        <result column="unit_id" property="unitId" />
        <result column="title" property="title" />
        <result column="title_translation" property="titleTranslation" />
        <result column="year" property="year" />
        <result column="source" property="source" />
        <result column="serial_code" property="serialCode" />
        <result column="article_type" property="articleType" />
        <result column="theme" property="theme" />
        <result column="content" property="content" />
        <result column="translate" property="translate" />
        <result column="word_count" property="wordCount" />
        <result column="time" property="time" />
        <result column="grade" property="grade" />
        <result column="difficulty" property="difficulty" />
        <result column="letter_signature_cn" property="letterSignatureCn" />
        <result column="letter_signature_en" property="letterSignatureEn" />
        <result column="letter_direction" property="letterDirection" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="list" resultMap="BaseResultMap">
        select *
        from readexpand_read_article
        where unit_id = #{unitId}
    </select>


    <select id="edit" resultMap="BaseResultMap">
        select *
        from readexpand_read_article
        where article_id = #{articleId}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleBean"
            useGeneratedKeys="true"  keyProperty="articleId">
        insert into readexpand_read_article (version_id,course_id,unit_id,title,title_translation,year,source,serial_code,article_type,theme,content,
                                        translate,word_count,time,grade,difficulty,letter_signature_cn,letter_signature_en,letter_direction,create_time,update_time)
        values (#{versionId},#{courseId},#{unitId},#{title},#{titleTranslation},#{year},#{source},#{serialCode},#{articleType},#{theme},#{content},
                #{translate},#{wordCount},#{time},#{grade},#{difficulty},#{letterSignatureCn},#{letterSignatureEn},#{letterDirection},#{createTime},#{updateTime})
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleBean">
        update readexpand_read_article
        <set>
            <if test="versionId != null">
                version_id=#{versionId},
            </if>
            <if test="courseId != null">
                course_id=#{courseId},
            </if>
            <if test="unitId != null">
                unit_id=#{unitId},
            </if>
            <if test="title != null">
                title=#{title},
            </if>
            <if test="titleTranslation != null">
                title_translation=#{titleTranslation},
            </if>
            <if test="year != null">
                year=#{year},
            </if>
            <if test="source != null">
                source=#{source},
            </if>
            <if test="serialCode != null">
                serial_code=#{serialCode},
            </if>
            <if test="articleType != null">
                article_type=#{articleType},
            </if>
            <if test="theme != null">
                theme=#{theme},
            </if>
            <if test="content != null">
                content=#{content},
            </if>
            <if test="translate != null">
                translate=#{translate},
            </if>
            <if test="wordCount != null">
                word_count=#{wordCount},
            </if>
            <if test="time != null">
                time=#{time},
            </if>
            <if test="grade != null">
                grade=#{grade},
            </if>
            <if test="difficulty != null">
                difficulty=#{difficulty},
            </if>
            <if test="letterSignatureCn != null">
                letter_signature_cn=#{letterSignatureCn},
            </if>
            <if test="letterSignatureEn != null">
                letter_signature_en=#{letterSignatureEn},
            </if>
            <if test="letterDirection != null">
                letter_direction=#{letterDirection},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where article_id = #{articleId}
    </update>

</mapper>
