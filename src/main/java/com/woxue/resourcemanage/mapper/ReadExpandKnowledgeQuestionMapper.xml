<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ReadExpandKnowledgeQuestionDao">
    
    <resultMap type="com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean" id="ReadExpandKnowledgeQuestionResult">
        <result property="id"    column="id"    />
        <result property="unitId"    column="unit_id"    />
        <result property="articleId"    column="article_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="difficulty"    column="difficulty"    />
        <result property="title"    column="title"    />
        <result property="titleDescribe"    column="title_describe"    />
        <result property="question"    column="question"    />
        <result property="optionA"    column="option_a"    />
        <result property="optionB"    column="option_b"    />
        <result property="optionC"    column="option_c"    />
        <result property="optionD"    column="option_d"    />
        <result property="optionE"    column="option_e"    />
        <result property="correctOption"    column="correct_option"    />
        <result property="parse"    column="parse"    />
        <result property="source"    column="source"    />
        <result property="year"    column="year"    />
        <result property="areaId"    column="area_id"    />
        <result property="sort"    column="sort"    />
    </resultMap>

    <sql id="selectReadExpandKnowledgeQuestionVo">
        select id, unit_id, article_id, question_type, difficulty,title,title_describe, question, option_a, option_b, option_c, option_d, option_e, correct_option, parse, source, year, area_id, sort from readexpand_knowledge_question
    </sql>

    <select id="selectReadExpandKnowledgeQuestionList" resultMap="ReadExpandKnowledgeQuestionResult">
        <include refid="selectReadExpandKnowledgeQuestionVo"/>
        <where>  
            <if test="articleId != null "> and article_id = #{articleId}</if>
        </where>
        order by sort asc,id asc
        <if test="pageSize != null and pageSize != ''">
            limit #{pageIndex}, #{pageSize}
        </if>
    </select>

    <select id="count" resultType="int">
        select count(*) from readexpand_knowledge_question
        <where>
            <if test="articleId != null "> and article_id = #{articleId}</if>
        </where>
    </select>
    
    <select id="selectReadExpandKnowledgeQuestionById" parameterType="Long" resultMap="ReadExpandKnowledgeQuestionResult">
        <include refid="selectReadExpandKnowledgeQuestionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertReadExpandKnowledgeQuestion" useGeneratedKeys="true" keyProperty="id">
        insert into readexpand_knowledge_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitId != null">unit_id,</if>
            <if test="articleId != null">article_id,</if>
            <if test="questionType != null">question_type,</if>
            <if test="difficulty != null">difficulty,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="titleDescribe != null and titleDescribe != ''">title_describe,</if>
            <if test="question != null and question != ''">question,</if>
            <if test="optionA != null">option_a,</if>
            <if test="optionB != null">option_b,</if>
            <if test="optionC != null">option_c,</if>
            <if test="optionD != null">option_d,</if>
            <if test="optionE != null">option_e,</if>
            <if test="correctOption != null">correct_option,</if>
            <if test="parse != null">parse,</if>
            <if test="source != null">source,</if>
            <if test="year != null">year,</if>
            <if test="areaId != null">area_id,</if>
            <if test="sort != null">sort,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitId != null">#{unitId},</if>
            <if test="articleId != null">#{articleId},</if>
            <if test="questionType != null">#{questionType},</if>
            <if test="difficulty != null">#{difficulty},</if>
            <if test="title != null">#{title},</if>
            <if test="titleDescribe != null">#{titleDescribe},</if>
            <if test="question != null and question != ''">#{question},</if>
            <if test="optionA != null">#{optionA},</if>
            <if test="optionB != null">#{optionB},</if>
            <if test="optionC != null">#{optionC},</if>
            <if test="optionD != null">#{optionD},</if>
            <if test="optionE != null">#{optionE},</if>
            <if test="correctOption != null">#{correctOption},</if>
            <if test="parse != null">#{parse},</if>
            <if test="source != null">#{source},</if>
            <if test="year != null">#{year},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>

    <update id="updateReadExpandKnowledgeQuestion" parameterType="com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean">
        update readexpand_knowledge_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="articleId != null">article_id = #{articleId},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="difficulty != null">difficulty = #{difficulty},</if>
            title = #{title},
            title_describe = #{titleDescribe},
            <if test="question != null and question != ''">question = #{question},</if>
            <if test="optionA != null">option_a = #{optionA},</if>
            <if test="optionB != null">option_b = #{optionB},</if>
            <if test="optionC != null">option_c = #{optionC},</if>
            <if test="optionD != null">option_d = #{optionD},</if>
            <if test="optionE != null">option_e = #{optionE},</if>
            <if test="correctOption != null">correct_option = #{correctOption},</if>
            <if test="parse != null">parse = #{parse},</if>
            <if test="source != null">source = #{source},</if>
            <if test="year != null">year = #{year},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReadExpandKnowledgeQuestionById" parameterType="Long">
        delete from readexpand_knowledge_question where id = #{id}
    </delete>

    <delete id="deleteReadExpandKnowledgeQuestionByIds" parameterType="String">
        delete from readexpand_knowledge_question where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>