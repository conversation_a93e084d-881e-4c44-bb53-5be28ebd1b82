<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IQuestionBankConfigDao">
	<!-- 获取学段列表 -->
	<select id="getGradePhaseList" resultType="java.util.Map">
		SELECT id, `name` FROM ${DB_QUESTION}.`config_grade_phase` ORDER BY id
	</select>

	<!-- 获取年级列表 -->
	<select id="getGradeList" resultType="java.util.Map">
		SELECT id, `name`, disporder FROM ${DB_QUESTION}.`config_grade` ORDER BY disporder
	</select>
	
	<!-- 获取卷子类型列表 -->
	<select id="getPaperTypeList" resultType="java.util.Map">
		SELECT id, `name` FROM ${DB_QUESTION}.`config_paper_type` ORDER BY id
	</select>
	
	
	<!-- 获取知识点列表 -->
	<select id="getKnowledgePointList" resultType="java.util.HashMap">
		SELECT id, `name`, parent_id parentId, disporder FROM ${DB_QUESTION}.`config_knowledge_point`
		WHERE is_show = TRUE
		ORDER BY `disporder`
	</select>
	
	<!-- 获取试题列表 -->
	<select id="getQuestionTypeList" resultType="java.util.Map">
		SELECT id, `name` FROM ${DB_QUESTION}.`config_question_type` ORDER BY id
	</select>
	
	<!-- 获取试题难度列表 -->
	<select id="getQuestionDifficultyList" resultType="java.util.Map">
		SELECT id, `name` FROM ${DB_QUESTION}.`config_question_difficulty` ORDER BY id
	</select>
	
	<!-- 获取地区列表 -->
	<select id="getAreaList" resultType="java.util.Map">
		SELECT id, `name`, parent_id parentId FROM ${DB_QUESTION}.`area` ORDER BY id
	</select>
	
	<!-- 加载试题和知识点的关联信息-->
	<select id="getQuestionKnowledgeList" resultType="java.util.Map">
		SELECT qkp.`question_type` questionType, qkp.`knowledge_point` knowledgePoint,
			kp.`name`, kp.`parent_id` parentId
		FROM ${DB_QUESTION}.`config_question_knowledge_point` qkp
		LEFT JOIN ${DB_QUESTION}.`config_knowledge_point` kp ON qkp.`knowledge_point` = kp.`id`
		WHERE kp.`is_show` = TRUE 
		ORDER BY kp.`disporder`
	</select>
	
	<!-- 获取话题列表 -->
	<select id="getQuestionTopicList" resultType="java.util.Map">
		SELECT id, grade_phase gradePhase, question_type questionType, `name`, disporder 
		FROM ${DB_QUESTION}.`config_topic` ORDER BY grade_phase, question_type, disporder
	</select>
	
	<!-- 获取话题列表 -->
	<select id="getQuizPointList" resultType="java.util.Map">
		SELECT id, grade_phase gradePhase, question_type questionType, `name`, disporder 
		FROM ${DB_QUESTION}.`config_quiz_point` ORDER BY grade_phase, question_type, disporder; 
	</select>
</mapper>
