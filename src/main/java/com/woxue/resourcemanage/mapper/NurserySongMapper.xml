<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.INurserySongDao">

    <resultMap id="BaseResultMap" type="com.redbook.kid.common.model.NurserySongDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="video_url" property="videoUrl" jdbcType="VARCHAR"/>
        <result column="original_audio_url" property="originalAudioUrl" jdbcType="VARCHAR"/>
        <result column="background_audio_url" property="backgroundAudioUrl" jdbcType="VARCHAR"/>
        <result column="subtitle_file_url" property="subtitleFileUrl" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="INTEGER"/>
        <result column="sentence_count" property="sentenceCount" jdbcType="INTEGER"/>
        <result column="difficulty_level" property="difficultyLevel" jdbcType="INTEGER"/>
        <result column="thumbnail_url" property="thumbnailUrl" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="play_count" property="playCount" jdbcType="INTEGER"/>
        <result column="recording_count" property="recordingCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, title, video_url, original_audio_url, background_audio_url, subtitle_file_url,
        duration, sentence_count, difficulty_level, thumbnail_url, description, tags,
        play_count, recording_count, status, sort_order, create_time, update_time, is_deleted
    </sql>

    <insert id="insertSong" parameterType="com.redbook.kid.common.model.NurserySongDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO nursery_song (
            title, video_url, original_audio_url, background_audio_url, subtitle_file_url,
            duration, sentence_count, difficulty_level, thumbnail_url, description, tags,
            play_count, recording_count, status, sort_order
        ) VALUES (
            #{title}, #{videoUrl}, #{originalAudioUrl}, #{backgroundAudioUrl}, #{subtitleFileUrl},
            #{duration}, #{sentenceCount}, #{difficultyLevel}, #{thumbnailUrl}, #{description}, #{tags},
            #{playCount}, #{recordingCount}, #{status}, #{sortOrder}
        )
    </insert>

    <select id="getSongById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <update id="updateSong" parameterType="com.redbook.kid.common.model.NurserySongDO">
        UPDATE nursery_song
        SET title = #{title},
            video_url = #{videoUrl},
            original_audio_url = #{originalAudioUrl},
            background_audio_url = #{backgroundAudioUrl},
            subtitle_file_url = #{subtitleFileUrl},
            duration = #{duration},
            sentence_count = #{sentenceCount},
            difficulty_level = #{difficultyLevel},
            thumbnail_url = #{thumbnailUrl},
            description = #{description},
            tags = #{tags},
            status = #{status},
            sort_order = #{sortOrder},
            update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <update id="deleteSong" parameterType="java.lang.Integer">
        UPDATE nursery_song
        SET is_deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="getSongList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song
        WHERE is_deleted = 0
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="difficultyLevel != null">
            AND difficulty_level = #{difficultyLevel}
        </if>
        ORDER BY sort_order ASC, create_time DESC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

    <select id="getSongCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM nursery_song
        WHERE is_deleted = 0
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="difficultyLevel != null">
            AND difficulty_level = #{difficultyLevel}
        </if>
    </select>

    <select id="getSongsByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song
        WHERE status = #{status} AND is_deleted = 0
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <select id="getSongsByDifficulty" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM nursery_song
        WHERE difficulty_level = #{difficultyLevel} AND is_deleted = 0
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <update id="updateSongStatus">
        UPDATE nursery_song
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <update id="updateSentenceCount">
        UPDATE nursery_song
        SET sentence_count = #{sentenceCount}, update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <update id="incrementPlayCount">
        UPDATE nursery_song
        SET play_count = play_count + 1, update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <update id="incrementRecordingCount">
        UPDATE nursery_song
        SET recording_count = recording_count + 1, update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <update id="batchUpdateStatus">
        UPDATE nursery_song
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>

</mapper>
