<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.ResourceUnitContentListenDao">
    
    <resultMap type="com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean" id="ResourceUnitContentListenResult">
        <result property="id"    column="id"    />
        <result property="resourceUnitId"    column="resource_unit_id"    />
        <result property="topicId"    column="topic_id"    />
        <result property="topicName"    column="topicName"    />
    </resultMap>

    <sql id="selectResourceUnitContentListenVo">
        select id, resource_unit_id, topic_id from resource_unit_content_listen
    </sql>

    <select id="selectResourceUnitContentListenList" parameterType="com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean" resultMap="ResourceUnitContentListenResult">
        <include refid="selectResourceUnitContentListenVo"/>
        <where>  
            <if test="resourceUnitId != null "> and resource_unit_id = #{resourceUnitId}</if>
            <if test="topicId != null "> and topic_id = #{topicId}</if>
        </where>
    </select>
    
    <select id="selectResourceUnitContentListenById" resultMap="ResourceUnitContentListenResult">
        <include refid="selectResourceUnitContentListenVo"/>
        where id = #{id}
    </select>

    <select id="editByUnitId" resultMap="ResourceUnitContentListenResult">
        select r.id,r.resource_unit_id,r.topic_id,t.name as topicName
        from resource_unit_content_listen r
        left join resource_topic t on t.id = r.topic_id and t.type = 3
        where r.resource_unit_id = #{unitId}
    </select>

    <select id="topicList" resultType="com.woxue.resourcemanage.entity.ResourceTopic">
        select id,name,type from resource_topic where type = '3'
    </select>
        
    <insert id="insertResourceUnitContentListen" parameterType="com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean" useGeneratedKeys="true" keyProperty="id">
        insert into resource_unit_content_listen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resourceUnitId != null">resource_unit_id,</if>
            <if test="topicId != null">topic_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resourceUnitId != null">#{resourceUnitId},</if>
            <if test="topicId != null">#{topicId},</if>
         </trim>
    </insert>

    <update id="updateResourceUnitContentListen" parameterType="com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean">
        update resource_unit_content_listen
        <trim prefix="SET" suffixOverrides=",">
            <if test="resourceUnitId != null">resource_unit_id = #{resourceUnitId},</if>
            <if test="topicId != null">topic_id = #{topicId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResourceUnitContentListenById">
        delete from resource_unit_content_listen where id = #{id}
    </delete>

    <delete id="deleteResourceUnitContentListenByIds" parameterType="String">
        delete from resource_unit_content_listen where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>