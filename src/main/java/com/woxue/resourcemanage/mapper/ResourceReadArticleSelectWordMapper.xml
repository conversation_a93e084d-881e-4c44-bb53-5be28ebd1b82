<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceReadArticleSelectWordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.read.ResourceReadArticleSelectWordBean">
        <id column="id" property="id" />
        <result column="article_id" property="articleId" />
        <result column="content" property="content" />
        <result column="word_content" property="wordContent" />
        <result column="word_list" property="wordList" />
        <result column="word_disturbance" property="wordDisturbance" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="editByArticleId" resultMap="BaseResultMap">
        select id,article_id,content,word_list,word_disturbance,update_time,word_content
        from resource_read_article_select_word
        where article_id = #{articleId}
    </select>


    <insert id="save" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleSelectWordBean"
            useGeneratedKeys="true"  keyProperty="correlationId">
        insert into resource_read_article_select_word (article_id,content,word_content,word_list,word_disturbance)
        values (#{articleId},#{content},#{wordContent},#{wordList},#{wordDisturbance})
    </insert>

    <update id="update" parameterType="com.woxue.common.model.redBook.read.ResourceReadArticleSelectWordBean">
        update resource_read_article_select_word
        <set>
            <if test="articleId != null">
                article_id=#{articleId},
            </if>
            <if test="wordContent != null and wordContent != ''">
                word_content=#{wordContent},
            </if>
            <if test="content != null and content != ''">
                content=#{content},
            </if>
            <if test="wordList != null and wordList != ''">
                word_list=#{wordList},
            </if>
            <if test="wordDisturbance != null">
                word_disturbance=#{wordDisturbance},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime}
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByArticleId">
        delete from resource_read_article_select_word
        where article_id = #{articleId}
    </delete>
</mapper>
