<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woxue.resourcemanage.dao.IResourceUnitContentWriteDao">
  <resultMap id="BaseResultMap" type="com.woxue.common.model.redBook.RedBookUnitContentWrite">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="resource_unit_id" jdbcType="INTEGER" property="resourceUnitId" />
    <result column="topic_id" jdbcType="INTEGER" property="topicId" />
    <result column="limit_time" jdbcType="INTEGER" property="limitTime" />
    <result column="max_time" jdbcType="INTEGER" property="maxTime" />
    <result column="sample_paragraph_num" jdbcType="INTEGER" property="sampleParagraphNum" />
    <result column="limit_word" jdbcType="INTEGER" property="limitWord" />
    <result column="max_word" jdbcType="INTEGER" property="maxWord" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.woxue.common.model.redBook.RedBookUnitContentWrite">
    <result column="words" jdbcType="LONGVARCHAR" property="words" />
    <result column="sample_title" jdbcType="LONGVARCHAR" property="sampleTitle" />
    <result column="sample_content" jdbcType="LONGVARCHAR" property="sampleContent" />
  </resultMap>
  <sql id="Base_Column_List">
    id, resource_unit_id, topic_id,limit_time,max_time,sample_paragraph_num,limit_word,max_word
  </sql>
  <sql id="Blob_Column_List">
    words, sample_title, sample_content
  </sql>

  <insert id="addUnitContent" parameterType="com.woxue.common.model.redBook.RedBookUnitContentWrite">
    insert into resource_unit_content_write (resource_unit_id, topic_id,limit_time,max_time,words,sample_title,sample_content,sample_paragraph_num,limit_word,max_word)
    values (#{resourceUnitId}, #{topicId}, #{limitTime},#{maxTime},#{words},#{sampleTitle},#{sampleContent},#{sampleParagraphNum},#{limitWord},#{maxWord})
  </insert>
  <update id="updateUnitContent" parameterType="com.woxue.common.model.redBook.RedBookUnitContentWrite">
    UPDATE `resource_unit_content_write`
    SET
      resource_unit_id = #{resourceUnitId},
      topic_id = #{topicId},
      limit_time = #{limitTime},
      max_time = #{maxTime},
      words = #{words},
      sample_title = #{sampleTitle},
      sample_content = #{sampleContent},
      sample_paragraph_num = #{sampleParagraphNum},
      limit_word = #{limitWord},
      max_word = #{maxWord}
    WHERE id = #{id}
  </update>
  <select id="getRecordByUnitId" resultMap="ResultMapWithBLOBs">
      select * from resource_unit_content_write where resource_unit_id = #{resourceUnitId} limit 1
    </select>
  <select id="getTopicRelatedUnitIdList" resultType="Integer">
    select resource_unit_id from resource_unit_content_write where topic_id = #{topicId} order by resource_unit_id asc
  </select>
</mapper>