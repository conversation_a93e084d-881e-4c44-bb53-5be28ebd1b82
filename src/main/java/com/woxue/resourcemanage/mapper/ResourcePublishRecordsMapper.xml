<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.woxue.resourcemanage.dao.IResourcePublishRecordsDao">

    <insert id="insertResourcePublishRecords" parameterType="com.woxue.redbookresource.model.ResourcePublishRecords" keyProperty="id" useGeneratedKeys="true">
        insert into resource_publish_records
        (publish_type,relation_column,relation_value,publish_time)
        values
        (#{publishType},#{relationColumn},#{relationValue},now())
    </insert>


   <select id="getResourcePublishRecordsList" resultType="com.woxue.redbookresource.model.ResourcePublishRecords">
       select
           id,
           publish_type publishType,
           relation_column relationColumn,
           relation_value relationValue,
           publish_time publishTime
       from resource_publish_records
        where id >  #{lastPublishId}
        order by id
   </select>


</mapper>