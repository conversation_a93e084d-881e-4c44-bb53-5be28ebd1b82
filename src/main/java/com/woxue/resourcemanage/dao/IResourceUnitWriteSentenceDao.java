package com.woxue.resourcemanage.dao;


import com.woxue.common.model.redBook.ResourceUnitWriteSentence;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface IResourceUnitWriteSentenceDao {

    List<ResourceUnitWriteSentence> getSentenceTrainListByUnitId(@Param("unitId") Integer unitId);

    void batchInsert(@Param("lists") List<ResourceUnitWriteSentence> lists);

    void deleteByUnitId(@Param("unitId") Integer unitId);

    int addSentence(ResourceUnitWriteSentence resourceUnitWriteSentence);
    int updateSentence(ResourceUnitWriteSentence resourceUnitWriteSentence);

}