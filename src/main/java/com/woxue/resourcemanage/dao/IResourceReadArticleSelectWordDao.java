package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.read.ResourceReadArticleSelectWordBean;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
public interface IResourceReadArticleSelectWordDao {

    ResourceReadArticleSelectWordBean editByArticleId(Integer articleId);
    int save(ResourceReadArticleSelectWordBean resourceReadArticleSelectWordBean);
    int update(ResourceReadArticleSelectWordBean resourceReadArticleSelectWordBean);

    int deleteByArticleId(Integer articleId);
}
