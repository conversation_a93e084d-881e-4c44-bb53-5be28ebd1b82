package com.woxue.resourcemanage.dao;



import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleWordBean;

import java.util.List;

/**
 * <p>
 * 扩展阅读-文章重点单词 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface ReadExpandReadArticleWordDao {
    List<ReadExpandReadArticleWordBean> list(Integer articleId);

    ReadExpandReadArticleWordBean edit(Integer wordId);

    int save(ReadExpandReadArticleWordBean readexpandReadArticleWordBean);
    int batchSave(List<ReadExpandReadArticleWordBean> wordBeanList);
    int update(ReadExpandReadArticleWordBean readexpandReadArticleWordBean);

    int batchUpdate(List<ReadExpandReadArticleWordBean> wordBeanList);

    int delete(Integer wordId);

}
