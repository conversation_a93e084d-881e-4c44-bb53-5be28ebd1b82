package com.woxue.resourcemanage.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-31 09:48
 */
public interface IResourceContentDao {

    List<Map<String,Object>> getWordShowName(Integer resourceUnitId);
    List<Map<String,Object>> getQuestionShowName(Integer resourceUnitId);

    Map<String,Object> getGrammarShowName(Integer resourceUnitId);
    Map<String,Object> getSentenceShowName(Integer resourceUnitId);

    boolean insertContentWord(@Param("resourceUnitId") Integer resourceUnitId, @Param("level") Integer level, @Param("programName") String programName, @Param("unitName") String unitName, @Param("showName") String showName, @Param("wordCount") Integer wordCount, @Param("hasExampleSentence") Integer hasExampleSentence);
    boolean insertContentSentence(@Param("resourceUnitId") Integer resourceUnitId, @Param("programName") String programName, @Param("unitName") String unitName, @Param("showName") String showName, @Param("sentenceCount") Integer sentenceCount);
    boolean insertContentGrammar(@Param("resourceUnitId") Integer resourceUnitId, @Param("grammarCourseId") Integer grammarCourseId, @Param("grammarUnitId") Integer grammarUnitId, @Param("showName") String showName, @Param("knowledgeCount") Integer knowledgeCount);
    boolean insertContentQuestion(@Param("resourceUnitId") Integer resourceUnitId, @Param("level") Integer level, @Param("syncQuestionCourseId") Integer syncQuestionCourseId, @Param("syncQuestionUnitId") Integer syncQuestionUnitId, @Param("showName") String showName, @Param("paperId") Integer paperId);


    boolean updateContentWord(@Param("programName") String programName, @Param("unitName") String unitName, @Param("showName") String showName, @Param("wordCount") Integer wordCount, @Param("hasExampleSentence") Integer hasExampleSentence, @Param("id") Integer id);
    boolean updateContentSentence(@Param("programName") String programName, @Param("unitName") String unitName, @Param("showName") String showName, @Param("sentenceCount") Integer sentenceCount, @Param("id") Integer id);
    boolean updateContentGrammar(@Param("grammarCourseId") Integer grammarCourseId, @Param("grammarUnitId") Integer grammarUnitId, @Param("showName") String showName, @Param("knowledgeCount") Integer knowledgeCount, @Param("id") Integer id);
    boolean updateContentQuestion(@Param("syncQuestionCourseId") Integer syncQuestionCourseId, @Param("syncQuestionUnitId") Integer syncQuestionUnitId, @Param("showName") String showName, @Param("paperId") Integer paperId, @Param("id") Integer id);


    boolean delContentWord(Integer id);
    boolean delContentSentence(Integer id);
    boolean delContentQuestion(Integer id);
    boolean delContentGrammar(Integer id);


}
