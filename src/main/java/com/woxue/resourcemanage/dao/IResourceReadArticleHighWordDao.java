package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.read.ResourceReadArticleHighWordBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
public interface IResourceReadArticleHighWordDao {
    ResourceReadArticleHighWordBean edit(Integer wordId);
    List<ResourceReadArticleHighWordBean> listByArticleId(Integer articleId);
    int save(ResourceReadArticleHighWordBean resourceReadArticleHighWordBean);
    int batchSave(List<ResourceReadArticleHighWordBean> list);
    int update(ResourceReadArticleHighWordBean resourceReadArticleHighWordBean);
    int delete(Integer wordId);
    int deleteByArticleId(Integer articleId);

}
