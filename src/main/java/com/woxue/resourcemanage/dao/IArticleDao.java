package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.RedBookArticle;
import com.woxue.resourcemanage.entity.SentenceBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 课文相关
 * <AUTHOR>
 * @date 2022/5/30 11:53
 */
public interface IArticleDao {
    int insertArticle(RedBookArticle article);
    int insertUnitArticle(@Param("unitId") Integer unitId,@Param("articleId") Integer articleId,@Param("displayOrder") Integer displayOrder);
    int updateArticleSentenceCount(@Param("articleId") Integer articleId);

    //-----------------以下方法从IResourceContentDao移植过来的

    List<Map<String,Object>> getArticleShowName(Integer resourceUnitId);
    /**
     * 根据课文Id获取课文分段句子
     * @param articleId
     * @return
     */
    List<SentenceBean> getArticleContentList(@Param("articleId") Integer articleId);

    /**
     * 批量修改课文
     * @param sentence
     * @return
     */
    boolean updateArticleContent(@Param("sentence") SentenceBean sentence);

    /**
     * 批量添加课文句子
     * @param sentence
     */
    Integer insertArticleSentence(@Param("sentence") SentenceBean sentence);

    Boolean replaceIntoArticleSentence(@Param("sentence") SentenceBean sentence);

    /**
     * 获取最大顺序
     * @param articleId
     * @return
     */
    Integer getMaxSentenceOrder(@Param("articleId") Integer articleId);

    /**
     * 获取句子个数
     * @param articleId
     */
    Integer getArticleSentenceCount(@Param("articleId") Integer articleId);
}
