package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean;
import com.woxue.resourcemanage.entity.dto.ReadExpandKnowledgeQuestionParamsDTO;

import java.util.List;
/**
 * 扩展阅读知识重点-试题Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public interface ReadExpandKnowledgeQuestionDao
{
    /**
     * 查询扩展阅读知识重点-试题
     * 
     * @param id 扩展阅读知识重点-试题主键
     * @return 扩展阅读知识重点-试题
     */
    ReadExpandKnowledgeQuestionBean selectReadExpandKnowledgeQuestionById(Long id);

    /**
     * 查询扩展阅读知识重点-试题列表
     * 
     * @param ReadExpandKnowledgeQuestionParamsDTO 扩展阅读知识重点-试题
     * @return 扩展阅读知识重点-试题集合
     */
    List<ReadExpandKnowledgeQuestionBean> selectReadExpandKnowledgeQuestionList(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO);
    int count(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO);

    /**
     * 新增扩展阅读知识重点-试题
     * 
     * @param ReadExpandKnowledgeQuestionBean 扩展阅读知识重点-试题
     * @return 结果
     */
    int insertReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean ReadExpandKnowledgeQuestionBean);

    /**
     * 修改扩展阅读知识重点-试题
     * 
     * @param ReadExpandKnowledgeQuestionBean 扩展阅读知识重点-试题
     * @return 结果
     */
    int updateReadExpandKnowledgeQuestion(ReadExpandKnowledgeQuestionBean ReadExpandKnowledgeQuestionBean);

    /**
     * 删除扩展阅读知识重点-试题
     * 
     * @param id 扩展阅读知识重点-试题主键
     * @return 结果
     */
    int deleteReadExpandKnowledgeQuestionById(Long id);

    /**
     * 批量删除扩展阅读知识重点-试题
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteReadExpandKnowledgeQuestionByIds(Long[] ids);
}
