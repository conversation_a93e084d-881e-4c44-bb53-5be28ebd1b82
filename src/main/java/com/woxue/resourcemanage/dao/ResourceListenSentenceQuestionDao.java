package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.listen.ResourceListenSentenceQuestionBean;

import java.util.List;

/**
 * 同步听力句子题目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ResourceListenSentenceQuestionDao
{
    /**
     * 查询同步听力句子题目
     * 
     * @param questionId 同步听力句子题目主键
     * @return 同步听力句子题目
     */
    ResourceListenSentenceQuestionBean selectResourceListenSentenceQuestionByQuestionId(Integer questionId);

    List<ResourceListenSentenceQuestionBean> listByUnitId(Integer unitId);

    /**
     * 查询同步听力句子题目列表
     * 
     * @param resourceListenSentenceQuestionBean 同步听力句子题目
     * @return 同步听力句子题目集合
     */
    List<ResourceListenSentenceQuestionBean> selectResourceListenSentenceQuestionList(ResourceListenSentenceQuestionBean resourceListenSentenceQuestionBean);

    /**
     * 新增同步听力句子题目
     * 
     * @param resourceListenSentenceQuestionBean 同步听力句子题目
     * @return 结果
     */
    int insertResourceListenSentenceQuestion(ResourceListenSentenceQuestionBean resourceListenSentenceQuestionBean);
    int replaceInsert(ResourceListenSentenceQuestionBean resourceListenSentenceQuestionBean);

    /**
     * 修改同步听力句子题目
     * 
     * @param resourceListenSentenceQuestionBean 同步听力句子题目
     * @return 结果
     */
    int updateResourceListenSentenceQuestion(ResourceListenSentenceQuestionBean resourceListenSentenceQuestionBean);

    /**
     * 删除同步听力句子题目
     * 
     * @param questionId 同步听力句子题目主键
     * @return 结果
     */
    int deleteResourceListenSentenceQuestionByQuestionId(Integer questionId);

    /**
     * 批量删除同步听力句子题目
     * 
     * @param questionIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteResourceListenSentenceQuestionByQuestionIds(String[] questionIds);
}
