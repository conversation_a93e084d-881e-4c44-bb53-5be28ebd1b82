package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.read.ResourceUnitContentReadBean;

/**
 * <AUTHOR>
 * @date 2024-07-20 16:34
 */
public interface IResourceUnitContentReadDao {

    ResourceUnitContentReadBean editByUnitId(Integer unitId);
    ResourceUnitContentReadBean editByTopicId(Integer unitId);
    int save(ResourceUnitContentReadBean resourceUnitContentReadBean);
    int update(ResourceUnitContentReadBean resourceUnitContentReadBean);


}
