package com.woxue.resourcemanage.dao;

import com.redbook.kid.common.model.NurserySongSentenceDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 儿歌句子数据访问接口
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
public interface INurserySongSentenceDao {

    /**
     * 插入句子
     *
     * @param sentence 句子信息
     * @return 影响行数
     */
    int insertSentence(NurserySongSentenceDO sentence);

    /**
     * 批量插入句子
     *
     * @param sentences 句子列表
     * @return 影响行数
     */
    int batchInsertSentences(@Param("sentences") List<NurserySongSentenceDO> sentences);

    /**
     * 根据ID查询句子
     *
     * @param id 句子ID
     * @return 句子信息
     */
    NurserySongSentenceDO getSentenceById(@Param("id") Integer id);

    /**
     * 根据儿歌ID查询所有句子
     *
     * @param songId 儿歌ID
     * @return 句子列表
     */
    List<NurserySongSentenceDO> getSentencesBySongId(@Param("songId") Integer songId);

    /**
     * 根据儿歌ID和句子序号查询句子
     *
     * @param songId 儿歌ID
     * @param sentenceIndex 句子序号
     * @return 句子信息
     */
    NurserySongSentenceDO getSentenceBySongIdAndIndex(@Param("songId") Integer songId, 
                                                      @Param("sentenceIndex") Integer sentenceIndex);

    /**
     * 更新句子信息
     *
     * @param sentence 句子信息
     * @return 影响行数
     */
    int updateSentence(NurserySongSentenceDO sentence);

    /**
     * 删除句子
     *
     * @param id 句子ID
     * @return 影响行数
     */
    int deleteSentence(@Param("id") Integer id);

    /**
     * 根据儿歌ID删除所有句子
     *
     * @param songId 儿歌ID
     * @return 影响行数
     */
    int deleteSentencesBySongId(@Param("songId") Integer songId);

    /**
     * 统计儿歌的句子数量
     *
     * @param songId 儿歌ID
     * @return 句子数量
     */
    int countSentencesBySongId(@Param("songId") Integer songId);

    /**
     * 批量删除句子
     *
     * @param ids 句子ID列表
     * @return 影响行数
     */
    int batchDeleteSentences(@Param("ids") List<Integer> ids);

    /**
     * 更新句子序号
     *
     * @param id 句子ID
     * @param newIndex 新序号
     * @return 影响行数
     */
    int updateSentenceIndex(@Param("id") Integer id, @Param("newIndex") Integer newIndex);
}
