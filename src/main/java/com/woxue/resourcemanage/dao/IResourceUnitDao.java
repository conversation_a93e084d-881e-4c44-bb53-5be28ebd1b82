package com.woxue.resourcemanage.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 14:28
 */
public interface IResourceUnitDao {

    List<Map<String,Object>> getUnitList(@Param("courseId") Integer courseId, @Param("pageStart") Integer pageStart, @Param("pageSize") Integer pageSize);
    Integer getUnitCount(Integer courseId);

    Integer getUnitByCourse(@Param("courseId") Integer courseId, @Param("nameEn") String nameEn);

    Map<String,Object> getUnitById(Integer id);
    Integer getUnitMaxDisplayOrder();
    boolean insertUnit(@Param("courseId") Integer courseId, @Param("nameEn") String nameEn, @Param("nameCn") String nameCn, @Param("displayOrder") Integer displayOrder);
    boolean updateUnit(@Param("nameEn") String nameEn, @Param("nameCn") String nameCn, @Param("id") Integer id);
    boolean updateUnitContentNum(@Param("contentNum") Integer contentNum, @Param("resourceUnitId") Integer resourceUnitId);



    List<Integer> getUnitContentWordIdList(@Param("resourceCourseId") Integer resourceCourseId);
    List<Integer> getUnitContentSentenceIdList(@Param("resourceCourseId") Integer resourceCourseId, @Param("programName") String programName);
    List<Integer> getUnitContentArticleIdList(@Param("resourceCourseId") Integer resourceCourseId, @Param("programName") String programName);
    List<Integer> getUnitContentQuestionIdList(@Param("resourceCourseId") Integer resourceCourseId, @Param("programId") Integer programId);
    List<Integer> getUnitContentGrammarIdList(@Param("resourceCourseId") Integer resourceCourseId, @Param("programId") Integer programId);

    //取消单元内容的关联
    boolean delUnitContentWord(@Param("id") Integer id, @Param("oldProgramName") String oldProgramName);
    boolean delUnitContentSentence(Integer id);
    boolean delUnitContentQuestion(Integer id);
    boolean delUnitContentGrammar(Integer id);
    boolean delUnitContentArticle(Integer id);
    //查是否关联过该课程单元
    List<Map<String,Object>> getUnitQuestion(@Param("paperId") Integer paperId);

    Map<String,Object> getUnitQuestionByCourseID(@Param("courseId") Integer courseId,@Param("paperId") Integer paperId);

    /**
     * 批量添加单元
     * @param resourceUnits
     * @param courseId
     * @return
     */
    boolean insertUnits(@Param("resourceUnits") String [] resourceUnits,@Param("courseId") Integer courseId);

    /**
     * 查unit_id：根据单元名和课程id
     * @param unitName
     * @param courseId
     * @return
     */
    Integer selectByName(@Param("unitName") String unitName,@Param("courseId") Integer courseId);

    /**
     * 添加：resource_unit_content_word添加记录
     * @param unitId
     * @param level
     * @param hasExample
     * @return
     */
    boolean insertUnitContentWord(@Param("unitId") Integer unitId,
                                  @Param("level") Integer level,
                                  @Param("hasExample") Boolean hasExample);
    int updateUnitContentWordCount(@Param("unitId") Integer unitId,
                                   @Param("level") Integer level);

//    boolean insertUnitContentSentence(@Param("unitId") Integer unitId);

    int insertUnitContentSentence(@Param("unitId") Integer unitId,
                                   @Param("level") Integer level);

//    int updateUnitContentSentenceCount(@Param("unitId") Integer unitId);

    int updateUnitContentSentenceCount(@Param("unitId") Integer unitId,
                                   @Param("level") Integer level);
}
