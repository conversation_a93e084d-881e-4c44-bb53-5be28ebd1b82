package com.woxue.resourcemanage.dao;


import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleCorrelationBean;

import java.util.List;

/**
 * <p>
 * 扩展阅读-文章句句对应 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface ReadExpandReadArticleCorrelationDao {
    List<ReadExpandReadArticleCorrelationBean> list();

    ReadExpandReadArticleCorrelationBean edit(Integer correlationId);


    ReadExpandReadArticleCorrelationBean editByArticleId(Integer articleId);

    int save(ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean);

    int update(ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean);
}
