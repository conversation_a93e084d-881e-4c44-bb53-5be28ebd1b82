package com.woxue.resourcemanage.dao;

import java.util.List;
import java.util.Map;

public interface IQuestionBankConfigDao {
	
	/**
	 * 获取学段列表
	 * @return
	 */
	List<Map<String, Object>> getGradePhaseList();
	
	/**
	 * 获取年级列表
	 * @return
	 */
	List<Map<String, Object>> getGradeList();
	
	/**
	 * 获取卷子类型列表
	 * @return
	 */
	List<Map<String, Object>> getPaperTypeList();
	
	/**
	 * 获取知识点列表
	 * @return
	 */
	List<Map<String, Object>> getKnowledgePointList();
	
	/**
	 * 获取试题类型列表
	 * @return
	 */
	List<Map<String, Object>> getQuestionTypeList();
	
	/**
	 * 获取试题难度列表
	 * @return
	 */
	List<Map<String, Object>> getQuestionDifficultyList();

	/**
	 * 获取地区信息
	 * @return
	 */
	List<Map<String, Object>> getAreaList();

	/**
	 * 加载试题和知识点的关联信息
	 * @return
	 */
	List<Map<String, Object>> getQuestionKnowledgeList();

	/**
	 * 获取话题列表
	 * @return
	 */
	List<Map<String, Object>> getQuestionTopicList();
	
	/**
	 * 获取考点列表
	 * @return
	 */
	List<Map<String, Object>> getQuizPointList();
}
