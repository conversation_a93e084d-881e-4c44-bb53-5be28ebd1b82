package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.AdminBean;
import org.apache.ibatis.annotations.Param;


public interface IAdminDao {
	/**
	 * 登录：根据用户名和密码查询
	 * @param name
	 * @param password
	 * @return
	 */
	AdminBean getAdmin(@Param("name") String name,
						  @Param("password") String password);

	void insertOperationRecord(@Param("admin_name") String admin_name,@Param("ip") String ip,@Param("url") String url,
							   @Param("http_method") String http_method,@Param("params") String params);
}
