package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.AiMnemonicWord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * AI词汇助记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface AiMnemonicWordDao
{
    /**
     * 查询AI词汇助记
     * 
     * @param id AI词汇助记主键
     * @return AI词汇助记
     */
        AiMnemonicWord selectAiMnemonicWordById(Integer id);
        AiMnemonicWord selectByWordId(Integer wordId);

    /**
     * 查询AI词汇助记列表
     * 
     * @return AI词汇助记集合
     */
    List<AiMnemonicWord> selectAiMnemonicWordList(AiGenerateQuery aiGenerateQuery);
    int count(AiGenerateQuery aiGenerateQuery);

    /**
     * 新增AI词汇助记
     * 
     * @param aiMnemonicWord AI词汇助记
     * @return 结果
     */
    int insertAiMnemonicWord(AiMnemonicWord aiMnemonicWord);

    /**
     * 修改AI词汇助记
     * 
     * @param aiMnemonicWord AI词汇助记
     * @return 结果
     */
    int updateAiMnemonicWord(AiMnemonicWord aiMnemonicWord);

    /**
     * 删除AI词汇助记
     * 
     * @param id AI词汇助记主键
     * @return 结果
     */
    int deleteAiMnemonicWordById(Long id);

    /**
     * 批量删除AI词汇助记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAiMnemonicWordByIds(Long[] ids);
}
