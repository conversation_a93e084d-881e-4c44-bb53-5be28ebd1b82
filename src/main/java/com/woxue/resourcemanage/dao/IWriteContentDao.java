package com.woxue.resourcemanage.dao;


import com.woxue.common.model.redBook.WriteUnitContentBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IWriteContentDao {

    List<WriteUnitContentBean> getWriteContentList(Integer[] array);

    List<WriteUnitContentBean> getWriteContentListByUnitIdAndTitleId(@Param("unitId") Integer unitId, @Param("titleId") Integer titleId);

    List<WriteUnitContentBean> selectListByUnitId(@Param("unitId") Integer unitId);

    void deleteByUnitId(@Param("unitId") Integer unitId);

    void addUnitContent(WriteUnitContentBean unitContent);

    void updateUnitContent(WriteUnitContentBean unitContent);

}