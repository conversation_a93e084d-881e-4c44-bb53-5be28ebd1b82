package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.GrammarQuestionContentBean;
import com.woxue.resourcemanage.entity.grammar.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022 -02-23 10:53
 */
public interface INewGrammarManagerDao {

    List<SimulationQuestionBean> getCourseQuestionList(@Param("courseId")Integer courseId,
                                                       @Param("pageStart")Integer pageStart,
                                                       @Param("pageSize")Integer pageSize);


    List<Map<String, Object>> getCourseQuestionNum(Integer courseId);
    List<GrammarUnitBean> getUnitList(@Param("courseId") Integer courseId);
    int addGrammarUnit(@Param("unitId") Integer unitId);

    GrammarVersionBean getVersion(Integer versionId);


    GrammarCourseBean getCourse(Integer id);

    /**
     * 获取本章检测题questionid最小值
     * @return
     */
    Integer getCourseQuestionIdMinNum(Integer courseId);

    boolean addCourseQuestion(SimulationQuestionBean question);
    /*int updateCourseLevelQuestionNum(Integer courseId);
    int updateCourseModifyTime(Integer courseId);*/
    
    boolean updateCourseQuestion(SimulationQuestionBean question);

    boolean deleteCourseQuestion(Integer[] deleteIdArr);


    List<SimulationQuestionBean> getSystemQuestionList(@Param("gradePhase")Integer gradePhase,
                                                       @Param("gradeArr")Integer[] gradeArr,
                                                       @Param("knowledgePointList")List<Map<String, String>> knowledgePointList,
                                                       @Param("questionTypeArr")Integer[] questionTypeArr,
                                                       @Param("difficultyArr")Integer[] difficultyArr);

    List<SimulationQuestionBean> getShareQuestionList(@Param("noAid")String noAid,
                                                      @Param("noTeachId")String noTeachId,
                                                      @Param("gradePhase")Integer gradePhase,
                                                      @Param("gradeArr")Integer[] gradeArr,
                                                      @Param("knowledgePointList")List<Map<String, String>> knowledgePointList,
                                                      @Param("questionTypeArr")Integer[] questionTypeArr,
                                                      @Param("difficultyArr")Integer[] difficultyArr);
    List<SimulationQuestionBean> getAgentQuestionList(@Param("aid")String aid,
                                                      @Param("gradePhase")Integer gradePhase,
                                                      @Param("gradeArr")Integer[] gradeArr,
                                                      @Param("knowledgePointList")List<Map<String, String>> knowledgePointList,
                                                      @Param("questionTypeArr")Integer[] questionTypeArr,
                                                      @Param("difficultyArr")Integer[] difficultyArr);
    List<SimulationQuestionBean> getTeachQuestionList(@Param("teachId")String teachId,
                                                      @Param("gradePhase")Integer gradePhase,
                                                      @Param("gradeArr")Integer[] gradeArr,
                                                      @Param("knowledgePointList")List<Map<String, String>> knowledgePointList,
                                                      @Param("questionTypeArr")Integer[] questionTypeArr,
                                                      @Param("difficultyArr")Integer[] difficultyArr);
    Integer getQuestionByIdFromProgram(@Param("questionId")Integer questionId, @Param("courseId")Integer courseId);
    boolean updateQuestionDelete(Integer questionId);
    /**
     * 根据id查询题库
     * @param questionId
     * @return
     */
    SimulationQuestionBean getQuestionById(@Param("questionId")Integer questionId);
    /**
     * 克隆有题库题到语法通本章库
     * @param questionList
     * @return
     */
    boolean addNewCourseQuestion(
            @Param("courseId")Integer courseId,
            @Param("questionList")List<SimulationQuestionBean> questionList);

    GrammarNewUnitBean getGrammarCard(@Param("unitId")Integer unitId);

    List<GrammarUnitContentBean> getUnitContentList(@Param("unitId")Integer unitId, @Param("subtitleId")Integer subtitleId);

    List<GrammarUnitTitleBean> getUnitTitleListByParentId(@Param("unitId")Integer unitId,
                                                          @Param("parentId")Integer parentId);

    List<GrammarUnitSubtitleBean>getUnitSubtitleList(@Param("unitId")Integer unitId,
                                                     @Param("titleId")Integer titleId);

    List<GrammarUnitTitleBean> getUnitTitleListByIdList(
            @Param("titleIdList")List<Integer> titleIdList);

    List<GrammarUnitSubtitleBean> getUnitSubtitleListByIdList(
            @Param("subtitleIdList")List<Integer> subtitleIdList);

    List<GrammarUnitContentBean> getUnitContentListByIdList(
            @Param("contentIdList")List<Integer> contentIdList);
    CourseUpdateDetailBean getCourseUpdateDetail(@Param("courseId")Integer courseId,
                                                 @Param("unitId")Integer unitId, @Param("type")Integer type);
    boolean addCourseUpdateDetail(@Param("teachId")String teachId, @Param("courseId")Integer courseId,
                                  @Param("unitId")Integer unitId, @Param("type")Integer type);
    /**
     *根据id获取question
     * @return
     */
    SimulationQuestionBean getCourseQuestionById(Integer courseQuestionId);
    List<Map<String, Object>> getKnowledgeList(@Param("unitId")Integer unitId);
    int getKnowQuestionNum(@Param("unitId")Integer unitId, @Param("allKnow")boolean allKnow);
    List<GrammarQuestionBean> getQuestionList(@Param("unitId")Integer unitId,@Param("titleIds")String titleIds);

    boolean updateQuestion(GrammarQuestionBean question);
    int deleteTitleQuestion(@Param("questionIdArr")Integer[] questionIdArr);
    int addTitleQuestion(@Param("titleQuestionList")List<Map<String, Integer>> titleQuestionList);

    boolean addQuestion(GrammarQuestionBean question);

    boolean addQuestionList(List<GrammarQuestionBean> questionList);
    boolean addQuestionContentList(@Param("questionId") Integer questionId,@Param("list") List<GrammarQuestionContentBean> questionContentBeanList);
    boolean updateQuestionContent(GrammarQuestionContentBean grammarQuestionContentBean);
    boolean deleteQuestionContentById(Integer id);
    boolean deleteQuestionContentByQuestionId(Integer questionId);
    List<Map<String, Object>> getApplyQuestionList(@Param("unitId")Integer unitId, @Param("titleId")Integer titleId);

    boolean deleteQuestion(@Param("questionIdArr")Integer[] questionIdArr);
    /**
     * 获取地区信息
     * @return
     */
    List<Map<String, Object>> getAreaList();
    int updateCourseUpdateDetailComment(@Param("courseId")Integer courseId,
                                        @Param("unitId")Integer unitId,
                                        @Param("type")Integer type,
                                        @Param("comment")String comment);
    List<GrammarUnitContentBean> getGrammarContentList(Integer[] array);
    boolean addUnitContent(GrammarUnitContentBean unitContent);
    boolean replaceUnitContent(GrammarUnitContentBean unitContent);

    boolean updateUnitContent(GrammarUnitContentBean unitContent);
    int updateTitleIsBasic(@Param("titleId")Integer titleId);
    boolean addUnitTitle(GrammarUnitTitleBean unitTitle);

    boolean replaceUnitTitle(GrammarUnitTitleBean unitTitle);

    boolean updateUnitTitle(GrammarUnitTitleBean unitTitle);

    boolean addUnitSubtitle(GrammarUnitSubtitleBean subtitle);

    boolean updateUnitSubtitle(GrammarUnitSubtitleBean subtitle);

    boolean updateUnitInteNum(@Param("unitId")Integer unitId, @Param("inteNum")int inteNum);

    /**
     * 更新单元知识点
     * @param unitId
     * @return
     */
    boolean updateUnitKnowNum(@Param("unitId")Integer unitId);

    boolean deleteTitle(Integer id);
    boolean deleteSubTitle(Integer id);
    boolean deleteContent(Integer id);

    int updateUnitKnowledgeCount(@Param("unitId") Integer unitId, @Param("count") Integer count);
}
