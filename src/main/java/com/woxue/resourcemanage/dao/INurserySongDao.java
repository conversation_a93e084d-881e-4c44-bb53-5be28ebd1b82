package com.woxue.resourcemanage.dao;

import com.redbook.kid.common.model.NurserySongDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 儿歌资源数据访问接口
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
public interface INurserySongDao {

    /**
     * 插入儿歌
     *
     * @param song 儿歌信息
     * @return 影响行数
     */
    int insertSong(NurserySongDO song);

    /**
     * 根据ID查询儿歌
     *
     * @param id 儿歌ID
     * @return 儿歌信息
     */
    NurserySongDO getSongById(@Param("id") Integer id);

    /**
     * 更新儿歌信息
     *
     * @param song 儿歌信息
     * @return 影响行数
     */
    int updateSong(NurserySongDO song);

    /**
     * 删除儿歌
     *
     * @param id 儿歌ID
     * @return 影响行数
     */
    int deleteSong(@Param("id") Integer id);

    /**
     * 查询儿歌列表
     *
     * @param params 查询参数
     * @return 儿歌列表
     */
    List<NurserySongDO> getSongList(Map<String, Object> params);

    /**
     * 查询儿歌总数
     *
     * @param params 查询参数
     * @return 总数
     */
    int getSongCount(Map<String, Object> params);

    /**
     * 根据状态查询儿歌列表
     *
     * @param status 状态
     * @return 儿歌列表
     */
    List<NurserySongDO> getSongsByStatus(@Param("status") Integer status);

    /**
     * 根据难度等级查询儿歌列表
     *
     * @param difficultyLevel 难度等级
     * @return 儿歌列表
     */
    List<NurserySongDO> getSongsByDifficulty(@Param("difficultyLevel") Integer difficultyLevel);

    /**
     * 更新儿歌状态
     *
     * @param id 儿歌ID
     * @param status 新状态
     * @return 影响行数
     */
    int updateSongStatus(@Param("id") Integer id, @Param("status") Integer status);

    /**
     * 更新句子数量
     *
     * @param id 儿歌ID
     * @param sentenceCount 句子数量
     * @return 影响行数
     */
    int updateSentenceCount(@Param("id") Integer id, @Param("sentenceCount") Integer sentenceCount);

    /**
     * 增加播放次数
     *
     * @param id 儿歌ID
     * @return 影响行数
     */
    int incrementPlayCount(@Param("id") Integer id);

    /**
     * 增加录音次数
     *
     * @param id 儿歌ID
     * @return 影响行数
     */
    int incrementRecordingCount(@Param("id") Integer id);

    /**
     * 批量更新状态
     *
     * @param ids 儿歌ID列表
     * @param status 新状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Integer> ids, @Param("status") Integer status);
}
