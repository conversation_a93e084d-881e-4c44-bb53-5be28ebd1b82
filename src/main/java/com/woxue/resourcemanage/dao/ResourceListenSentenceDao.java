package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.listen.ResourceListenSentenceBean;

import java.util.List;

/**
 * 同步听力文章句子Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ResourceListenSentenceDao
{
    /**
     * 查询同步听力文章句子
     * 
     * @param id 同步听力文章句子主键
     * @return 同步听力文章句子
     */
    ResourceListenSentenceBean selectResourceListenSentenceById(Integer id);
    List<ResourceListenSentenceBean> listByUnitId(Integer unitId);

    /**
     * 查询同步听力文章句子列表
     * 
     * @param resourceListenSentence 同步听力文章句子
     * @return 同步听力文章句子集合
     */
    List<ResourceListenSentenceBean> selectResourceListenSentenceList(ResourceListenSentenceBean resourceListenSentence);

    /**
     * 新增同步听力文章句子
     * 
     * @param resourceListenSentence 同步听力文章句子
     * @return 结果
     */
    int insertResourceListenSentence(ResourceListenSentenceBean resourceListenSentence);

    int replaceInsert(ResourceListenSentenceBean resourceListenSentence);

    int batchInsert(List<ResourceListenSentenceBean> resourceListenSentence);

    /**
     * 修改同步听力文章句子
     * 
     * @param resourceListenSentence 同步听力文章句子
     * @return 结果
     */
    int updateResourceListenSentence(ResourceListenSentenceBean resourceListenSentence);

    /**
     * 删除同步听力文章句子
     * 
     * @param id 同步听力文章句子主键
     * @return 结果
     */
    int deleteResourceListenSentenceById(Integer id);

    /**
     * 批量删除同步听力文章句子
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteResourceListenSentenceByIds(String[] ids);
}
