package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.ResourceMnemonicWord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * 资源词汇助记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface ResourceMnemonicWordDao
{
    /**
     * 查询资源词汇助记
     * 
     * @param id 资源词汇助记主键
     * @return 资源词汇助记
     */
        ResourceMnemonicWord selectResourceMnemonicWordById(Integer id);
        ResourceMnemonicWord selectByWordId(Integer wordId);

    /**
     * 查询资源词汇助记列表
     * 
     * @return 资源词汇助记集合
     */
    List<ResourceMnemonicWord> selectResourceMnemonicWordList(AiGenerateQuery aiGenerateQuery);
    int count(AiGenerateQuery aiGenerateQuery);

    /**
     * 新增资源词汇助记
     * 
     * @param resourceMnemonicWord 资源词汇助记
     * @return 结果
     */
    int insertResourceMnemonicWord(ResourceMnemonicWord resourceMnemonicWord);

    /**
     * 修改资源词汇助记
     * 
     * @param resourceMnemonicWord 资源词汇助记
     * @return 结果
     */
    int updateResourceMnemonicWord(ResourceMnemonicWord resourceMnemonicWord);

    /**
     * 删除资源词汇助记
     * 
     * @param id 资源词汇助记主键
     * @return 结果
     */
    int deleteResourceMnemonicWordById(Long id);

    /**
     * 批量删除资源词汇助记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteResourceMnemonicWordByIds(Long[] ids);
}
