package com.woxue.resourcemanage.dao;


import com.woxue.common.model.redBook.readExpand.ReadExpandReadArticleBean;

import java.util.List;

/**
 * <p>
 * 扩展阅读-文章 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
public interface ReadExpandReadArticleDao {
    List<ReadExpandReadArticleBean> list(Integer unitId);

    ReadExpandReadArticleBean edit(Integer articleId);

    int save(ReadExpandReadArticleBean readexpandReadArticleBean);

    int update(ReadExpandReadArticleBean readexpandReadArticleBean);
}
