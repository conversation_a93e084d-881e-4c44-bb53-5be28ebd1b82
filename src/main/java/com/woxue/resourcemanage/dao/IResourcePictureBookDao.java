package com.woxue.resourcemanage.dao;

import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.PictureBook;
import com.woxue.common.model.redBook.PictureBookContent;
import com.woxue.common.model.redBook.PictureBookSentence;
import org.apache.ibatis.annotations.Param;

public interface IResourcePictureBookDao {

    boolean insertPictureBook(@Param("pictureBook") PictureBook pictureBook);

    boolean deletePictureBookById(@Param("pictureBookId") Integer pictureBookId);
    boolean deletePictureBookContentByPictureBookId(@Param("pictureBookId") Integer pictureBookId);
    boolean deletePictureBookSentenceByPictureBookId(@Param("pictureBookId") Integer pictureBookId);
    boolean deletePictureBookSentenceByPictureBookSentenceId( @Param("pictureBookSentenceId") Integer pictureBookSentenceId);
    boolean updatePictureBookById(@Param("pictureBook") PictureBook pictureBook);

    boolean insertPictureBookContent(@Param("pictureBookContent") PictureBookContent pictureBookContent);

    boolean updatePictureBookContentById(@Param("pictureBookContent") PictureBookContent pictureBookContent);

    boolean insertPictureBookSentence(@Param("pictureBookSentence") PictureBookSentence pictureBookSentence);

    boolean updatePictureBookSentenceById(@Param("pictureBookSentence") PictureBookSentence pictureBookSentence);

    boolean deletePictureBookWord(@Param("wordId") Integer wordId);

    boolean insertPictureBookWord(@Param("pictureBookId") Integer pictureBookId, @Param("spelling") String spelling, @Param("syllable") String syllable, @Param("meaningEnUs") String meaningEnUs, @Param("meaningZhCn") String meaningZhCn, @Param("exampleEnUs") String exampleEnUs, @Param("exampleZhCn") String exampleZhCn, @Param("imgUrl") String imgUrl);

    boolean updatePictureBookWord(@Param("wordId") Integer wordId, @Param("pictureBookId") Integer pictureBookId, @Param("spelling") String spelling, @Param("syllable") String syllable, @Param("meaningEnUs") String meaningEnUs, @Param("meaningZhCn") String meaningZhCn, @Param("exampleEnUs") String exampleEnUs, @Param("exampleZhCn") String exampleZhCn, @Param("imgUrl") String imgUrl);
}
