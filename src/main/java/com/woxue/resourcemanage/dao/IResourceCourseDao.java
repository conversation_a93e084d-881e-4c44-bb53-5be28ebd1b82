package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.resourcemanage.entity.ResourceCourse;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 12:20
 */
public interface IResourceCourseDao {

    List<RedBookCourse> getCourseList(@Param("versionId") Integer versionId, @Param("stage") Integer stage);

    /**
     * 获取课程数量，为版本提供数据
     * @param versionId
     * @return
     */
    Integer getCourseCount(@Param("versionId") Integer versionId, @Param("stage") Integer stage);

    Integer getMaxDisplayOrder();

    /**
     * 添加课程信息
     * @param resourceCourse
     * @return
     */
    boolean insertCourse(ResourceCourse resourceCourse);
    /**
     * 根据ID回显修改课程的信息
     * @param id
     * @return
     */
    Map<String,Object> getCourseById(Integer id);

    /**
     * 修改课程信息
     * @param nameEn
     * @param nameCn
     * @param grade
     * @param id
     * @return
     */
    boolean updateCourse(@Param("nameEn") String nameEn, @Param("nameCn") String nameCn, @Param("grade") Integer grade, @Param("id") Integer id);

    /**
     * 更新课程的单元数量
     * @param unitNum
     * @param id
     * @return
     */
    boolean updateCourseUnitNum(@Param("unitNum") Integer unitNum, @Param("id") Integer id);

    List<Map<String,Object>> getCourseWord(Integer courseId);
    Map<String,Object> getCourseSentence(Integer courseId);
    Map<String,Object>  getCourseQuestion(Integer courseId);
    Map<String,Object>  getCourseGrammar(Integer courseId);
    Map<String,Object>  getCourseArticle(Integer courseId);



    boolean insertCourseWord(@Param("resourceCourseId") Integer resourceCourseId, @Param("programName") String programName, @Param("showName") String showName);
    boolean insertCourseSentence(@Param("resourceCourseId") Integer resourceCourseId, @Param("programName") String programName, @Param("showName") String showName);
    boolean insertCourseArticle(@Param("resourceCourseId") Integer resourceCourseId, @Param("programName") String programName, @Param("showName") String showName);
    boolean insertCourseQuestion(@Param("resourceCourseId") Integer resourceCourseId, @Param("programId") Integer programId, @Param("showName") String showName);
    boolean insertCourseGrammar(@Param("resourceCourseId") Integer resourceCourseId, @Param("programId") Integer programId, @Param("showName") String showName);

    boolean delOldCourseWord(@Param("resourceCourseId") Integer resourceCourseId, @Param("oldProgramName") String oldProgramName);
    boolean delOldCourseSentence(Integer resourceCourseId);
    boolean delOldCourseQuestion(Integer resourceCourseId);
    boolean delOldCourseGrammar(Integer resourceCourseId);
    boolean delOldCourseArticle(Integer resourceCourseId);

    Map<String,Object>isHaveCourseWord(String programName);
    Map<String,Object>isHaveCourseSentence(String programName);
    Map<String,Object>isHaveCourseArticle(String programName);
    Map<String,Object>isHaveCourseQuestion(Integer programId);
    Map<String,Object>isHaveCourseGrammar(Integer programId);




    /**
     * 更新课程中专项是否包含
     * @param courseId
     * @param contentType
     * @param flag
     */
    void updateCourseContentContainStatus(@Param("courseId") Integer courseId, @Param("contentType") RedBookContentTypeEnum contentType, @Param("flag") boolean flag);
}

