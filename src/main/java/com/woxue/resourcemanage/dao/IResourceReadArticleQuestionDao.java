package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.read.ResourceReadArticleQuestionBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
public interface IResourceReadArticleQuestionDao {

    List<ResourceReadArticleQuestionBean> listByArticleId(Integer articleId);
    ResourceReadArticleQuestionBean edit(Integer questionId);

    int batchSave(List<ResourceReadArticleQuestionBean> list);

    int update(ResourceReadArticleQuestionBean resourceReadArticleQuestionBean);

    int deleteByArticleId(Integer articleId);

}
