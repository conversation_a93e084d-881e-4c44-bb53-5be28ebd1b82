package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.AiMnemonicSentence;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * AI句子学法助记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface AiMnemonicSentenceDao
{
    /**
     * 查询AI句子学法助记
     * 
     * @param id AI句子学法助记主键
     * @return AI句子学法助记
     */
        AiMnemonicSentence selectAiMnemonicSentenceById(Integer id);
        AiMnemonicSentence selectSentenceId(Integer sentenceId);

    /**
     * 查询AI句子学法助记列表
     * 
     * @return AI句子学法助记集合
     */
    List<AiMnemonicSentence> selectAiMnemonicSentenceList(AiGenerateQuery aiGenerateQuery);
    int count(AiGenerateQuery aiGenerateQuery);

    /**
     * 新增AI句子学法助记
     * 
     * @param aiMnemonicSentence AI句子学法助记
     * @return 结果
     */
    int insertAiMnemonicSentence(AiMnemonicSentence aiMnemonicSentence);

    /**
     * 修改AI句子学法助记
     * 
     * @param aiMnemonicSentence AI句子学法助记
     * @return 结果
     */
    int updateAiMnemonicSentence(AiMnemonicSentence aiMnemonicSentence);

    /**
     * 删除AI句子学法助记
     * 
     * @param id AI句子学法助记主键
     * @return 结果
     */
    int deleteAiMnemonicSentenceById(Long id);

    /**
     * 批量删除AI句子学法助记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAiMnemonicSentenceByIds(Long[] ids);
}
