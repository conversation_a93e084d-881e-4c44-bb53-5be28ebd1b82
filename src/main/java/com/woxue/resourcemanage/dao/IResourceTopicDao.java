package com.woxue.resourcemanage.dao;


import com.woxue.resourcemanage.entity.ResourceTopic;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IResourceTopicDao {

    ResourceTopic selectTopicByNameAndType(@Param("topicName") String topicName, @Param("type") String type);

    List<ResourceTopic> selectTopicListByType(@Param("type") String type);

    String getTopicNameById(@Param("topicId") Integer topicId);

    int addTopic(@Param("type") String type, @Param("topicName") String topicName);
}