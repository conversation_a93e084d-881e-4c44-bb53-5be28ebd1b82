package com.woxue.resourcemanage.dao;

import com.woxue.redbookresource.model.ResourcePublishRecords;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发布记录
 * <AUTHOR>
 * @date 2022/7/5 11:50
 */
public interface IResourcePublishRecordsDao {

    /**
     * 插入
     * @param records
     */
    void insertResourcePublishRecords(ResourcePublishRecords records);

    /**
     * 小红本资源发布记录（只返回最后发布id之后的记录）
     * @param lastPublishId 最后发布id
     * @return
     */
    List<ResourcePublishRecords> getResourcePublishRecordsList(@Param("lastPublishId") Integer lastPublishId);


}
