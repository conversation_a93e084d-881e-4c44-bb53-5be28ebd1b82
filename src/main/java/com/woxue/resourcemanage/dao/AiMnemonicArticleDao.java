package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.AiMnemonicArticle;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI课文背法助记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface AiMnemonicArticleDao
{
    /**
     * 查询AI课文背法助记
     * 
     * @param id AI课文背法助记主键
     * @return AI课文背法助记
     */
        AiMnemonicArticle selectAiMnemonicArticleById(Integer id);
        AiMnemonicArticle selectByArticleId(@Param("articleId") Integer articleId,@Param("gradeName") String gradeName);

    /**
     * 查询AI课文背法助记列表
     * 
     * @return AI课文背法助记集合
     */
    List<AiMnemonicArticle> selectAiMnemonicArticleList(AiGenerateQuery aiGenerateQuery);
    int count(AiGenerateQuery aiGenerateQuery);

    /**
     * 新增AI课文背法助记
     * 
     * @param aiMnemonicArticle AI课文背法助记
     * @return 结果
     */
    int insertAiMnemonicArticle(AiMnemonicArticle aiMnemonicArticle);

    /**
     * 修改AI课文背法助记
     * 
     * @param aiMnemonicArticle AI课文背法助记
     * @return 结果
     */
    int updateAiMnemonicArticle(AiMnemonicArticle aiMnemonicArticle);

    /**
     * 删除AI课文背法助记
     * 
     * @param id AI课文背法助记主键
     * @return 结果
     */
    int deleteAiMnemonicArticleById(Long id);

}
