package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.RedBookCourseStage;
import com.woxue.common.model.redBook.RedBookVersionStage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-26 14:20
 */
public interface IResourceVersionDao {

    /**
     * 获取版本信息列表
     * @param stage
     * @param pageStart
     * @param pageSize
     * @return
     */
    List<Map<String,Object>> getVersionList(@Param("versionType") Integer versionType,
                                            @Param("stage") Integer stage,
                                            @Param("search") String search,
                                            @Param("pageStart") Integer pageStart,
                                            @Param("pageSize") Integer pageSize);
    
    boolean updateVersionDisplayOrder(@Param("index") Integer index, @Param("Id") Integer Id);
    /**
     * 版本总条数
     * @param stage
     * @return
     */
    Integer getVersionListCount(@Param("versionType") Integer versionType, @Param("stage") Integer stage);
    /**
     * 添加版本时获取关联其他学段对应版本
     * @return
     */
    List<Map<String,Object>> getVersionTypeList();
    /**
     * 获取其他学段对应版本
     * @param type
     * @param id
     * @return
     */
    List<Map<String,Object>> getVersionByType(@Param("type") Integer type, @Param("id") Integer id);
    /**
     * 添加版本
     * @param nameEn
     * @param nameCn
     * @param type
     * @param stage
     * @return
     */
    boolean insertVersion(@Param("nameEn") String nameEn, @Param("nameCn") String nameCn, @Param("versionType") Integer versionType, @Param("type") Integer type, @Param("stage") Integer stage,
                          @Param("price") Integer price, @Param("briefIntroduction") String briefIntroduction, @Param("displayOrder") Integer displayOrder);

    /**
     * 根据ID回显版本信息
     * @param id
     * @return
     */
    Map<String,Object> getVersionById(@Param("id") Integer id);

    /**
     * 修改版本信息
     *
     * @param nameEn
     * @param nameCn
     * @param type
     * @param id
     * @return
     */
    boolean updateVersionById(@Param("nameEn") String nameEn, @Param("nameCn") String nameCn, @Param("versionType") Integer versionType, @Param("type") Integer type, @Param("price") Integer price, @Param("briefIntroduction") String briefIntroduction, @Param("displayOrder") Integer displayOrder, @Param("id") Integer id);

    boolean updateRelationType(@Param("type") Integer type, @Param("id") Integer id);

    /**
     * 最后的顺序
     *
     * @return
     */
    Integer getMaxDisplayOrder();

    boolean deleteVersionStage(@Param("versionId") Integer versionId);

    boolean insertVersionStage(@Param("versionId") Integer versionId, @Param("stage") Integer stage);


    boolean deleteCourseStage(@Param("courseId") Integer courseId);

    boolean insertCourseStage(@Param("courseId") Integer courseId, @Param("stage") Integer stage);

    int selectVersionCount(@Param("versionId") Integer versionId, @Param("stage") Integer stage);

}

