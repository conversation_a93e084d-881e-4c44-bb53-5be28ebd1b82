package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.model.redBook.WordAbbr;
import com.woxue.resourcemanage.entity.ResourceUnitSentence;
import com.woxue.resourcemanage.entity.ResourceUnitWord;
import com.woxue.resourcemanage.entity.WordDisturb;
import com.woxue.resourcemanage.entity.WordEntity;
import org.apache.ibatis.annotations.Param;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022 -05-12 19:54
 */
public interface IResourceWordDao {
    /**
     * 查询音标
     *
     * @param spelling
     * @return
     */
    String querySyllableBySpelling(@Param("spelling") String spelling);

    boolean insertUnitWord(ResourceUnitWord resourceUnitWord);

    int queryUnitMaxDisplayOrder(@Param("unitId") Integer unitId);

    boolean insertUnitSentence(ResourceUnitSentence resourceUnitSentence);

    boolean insertWordInfo(WordEntity wordEntity);

    /**
     * 根据句子拼写查找句子成分
     *
     * @param sentenceEn
     * @return
     */
    Map<String, String> getSentenceElementBySentence(@Param("sentenceEn") String sentenceEn);

    /**
     * 插入句子成分
     *
     * @param wordId
     * @param example_en_US
     * @param element_jz
     * @return
     */
    int insertSentenceElement(@Param("wordId") Integer wordId,
                              @Param("example_en_US") String example_en_US,
                              @Param("element_jz") String element_jz,
                              @Param("example_clause_en_US") String example_clause_en_US,
                              @Param("element_clause_jz") String element_clause_jz


    );

    int updateSentenceElement(@Param("wordId") Integer wordId,
                              @Param("example_en_US") String example_en_US,
                              @Param("element_jz") String element_jz,
                              @Param("example_clause_en_US") String example_clause_en_US,
                              @Param("element_clause_jz") String element_clause_jz,
                              @Param("confirmFlag") Integer confirmFlag

    );

    int updateByPrimaryKeySelective(WordEntity wordEntity);


    /**
     * 获取单词列表
     *
     * @param wordId    单词ID
     * @param spelling  拼写
     * @param courseId  课程ID
     * @param pageIndex 每页下标
     * @param pageSize  每页展示数
     * @return
     */
    List<WordEntity> getWordList(@Param("wordId") Integer wordId,
                                 @Param("spelling") String spelling,
                                 @Param("courseId") Integer courseId,
                                 @Param("pageIndex") Integer pageIndex,
                                 @Param("pageSize") Integer pageSize,
                                 @Param("type") RedBookContentTypeEnum type
    );

    /**
     * 获取单词数量
     *
     * @param wordId
     * @param spelling
     * @param courseId
     * @return
     */
    int getWordCount(@Param("wordId") Integer wordId,
                     @Param("spelling") String spelling,
                     @Param("courseId") Integer courseId,
                     @Param("type") RedBookContentTypeEnum type
    );

    /**
     * 模糊查询例句列表
     *
     * @param spelling  拼写
     * @param pageIndex 每页下标
     * @param pageSize  每页展示数
     * @return
     */
    List<WordEntity> queryExamplesList(@Param("spelling") String spelling,
                                       @Param("courseId") Integer courseId,
                                       @Param("pageIndex") Integer pageIndex,
                                       @Param("pageSize") Integer pageSize,
                                       @Param("searchFrom") String searchFrom);

    /**
     * 模糊查询例句的总数
     *
     * @param spelling
     * @return
     */
    int queryExampleCount(@Param("spelling") String spelling, @Param("courseId") Integer courseId, @Param("searchFrom") String searchFrom);

    /**
     * 获取无音标单词列表
     *
     * @param courseId  课程ID
     * @param pageIndex 每页下标
     * @param pageSize  每页展示数
     * @return
     */
    List<WordEntity> getNoSyllableWordList(
            @Param("courseId") Integer courseId,
            @Param("pageIndex") Integer pageIndex,
            @Param("pageSize") Integer pageSize);

    /**
     * 获取无音标单词数量
     *
     * @param courseId
     * @return
     */
    int getNoSyllableWordCount(@Param("courseId") Integer courseId);

    /**
     * 获取无例句单词列表
     *
     * @param courseId  课程ID
     * @param pageIndex 每页下标
     * @param pageSize  每页展示数
     * @return
     */
    List<WordEntity> getNoExampleWordList(
            @Param("courseId") Integer courseId,
            @Param("pageIndex") Integer pageIndex,
            @Param("pageSize") Integer pageSize);

    /**
     * 获取无例句单词数量
     *
     * @param courseId
     * @return
     */
    int getNoExampleWordCount(@Param("courseId") Integer courseId);

    /**
     * @param wordId    单词ID
     * @param spelling  拼写
     * @param courseId  课程ID
     * @param pageIndex 每页下标
     * @param pageSize  每页展示数
     * @return
     */
    List<WordEntity> getSentenceList(@Param("wordId") Integer wordId,
                                     @Param("spelling") String spelling,
                                     @Param("courseId") Integer courseId,
                                     @Param("divide") Integer divide,
                                     @Param("pageIndex") Integer pageIndex,
                                     @Param("pageSize") Integer pageSize);

    /**
     * 获取句子数量
     *
     * @param wordId
     * @param spelling
     * @param courseId
     * @return
     */
    int getSentenceCount(@Param("wordId") Integer wordId,
                         @Param("spelling") String spelling,
                         @Param("courseId") Integer courseId,
                         @Param("divide") Integer divide);

    int updateWordDetail(
            @Param("wordId") Integer wordId,
            @Param("spelling") String spelling,
            @Param("syllable") String syllable,
            @Param("meaning_en_US") String meaning_en_US,
            @Param("meaning_zh_CN") String meaning_zh_CN,
            @Param("example_en_US") String example_en_US,
            @Param("example_zh_CN") String example_zh_CN);

    int updateSentence(
            @Param("wordId") Integer wordId,
            @Param("example_en_US") String example_en_US,
            @Param("example_zh_CN") String example_zh_CN);

    /**
     * 获取单词助记信息
     *
     * @param spelling
     * @return
     * @throws SQLException
     */
    Map<String, String> getWordMnemonics(String spelling);

    List<Map<String, String>> getWordMnemonicsList(@Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    Integer getWordMnemonicsCount();

    void insertWordMnemonics(Map<String, String> wordMnemonics);

    void updateWordMnemonics(Map<String, String> wordMnemonics);

    /**
     * 获取分音节单词列表
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<Map<String, Object>> getSoundMarkList(@Param("spelling") String spelling, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 修改单词或音节
     *
     * @param oldSpelling
     * @param newSpelling
     * @param soundMark
     * @return
     */
    boolean updateSoundMark(@Param("oldSpelling") String oldSpelling, @Param("newSpelling") String newSpelling, @Param("soundMark") String soundMark);

    /**
     * 是否有这个音节
     *
     * @param spelling
     * @return
     */
    String getSoundMark(@Param("spelling") String spelling);

    /**
     * 添加音节
     *
     * @param spelling
     * @param soundMark
     * @return
     */
    boolean insertSoundMark(@Param("spelling") String spelling, @Param("soundMark") String soundMark);

    /**
     * 音节条目
     *
     * @return
     */
    Integer getSoundMarkCount();

    /**
     * 助记
     *
     * @param spelling
     * @return
     */
    int countWordDisturbSpelling(@Param("spelling") String spelling);

    List<WordDisturb> getWordDisturbList(@Param("spelling") String spelling, @Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    int updateWordDisturb(@Param("spelling") String spelling, @Param("disturbWordId") Integer disturbWordId, @Param("disturbSpelling") String disturbSpelling, @Param("disturbMeaning") String disturbMeaning);

    List<WordAbbr> queryWordAbbr();

    int updateWordAbbr(WordAbbr wordAbbr);

    int addWordAbbr(WordAbbr wordAbbr);

    Boolean deleteSoundMark(@Param("spelling") String spelling);

    Integer countSoundMark(@Param("spelling") String spelling);

    void insertWordUse(@Param("spelling") String spelling, @Param("sentence") String sentence, @Param("content") String content);

    void updateWordUseStatus(@Param("spell") String spell, @Param("sentence") String sentence, @Param("status") int status);

    List<Map<String, Object>> getWordUseList(@Param("spelling") String spelling, @Param("status") Integer status, @Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    Integer getWordUseCount(@Param("spelling") String spelling, @Param("status") Integer status);

    Integer updateWordUse(@Param("spelling") String spelling, @Param("sentence") String sentence, @Param("content") String content);
}
