package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.spoken.ResourceSpokenTopic;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 语音话题数据访问接口。
 * 提供了对语音话题及其内容的增、删、改、查等操作。
 */
public interface ISpokenDao {

    /**
     * 插入话题。
     *
     * @param name 话题名称
     * @param sense 话题场景
     * @param cover 话题封面图片链接
     * @param desc 话题描述
     * @return 插入成功返回受影响的行数，失败返回0
     */
    int insertTopic(@Param("name") String name, @Param("sense") String sense, @Param("cover") String cover, @Param("videoUrl") String videoUrl, @Param("desc") String desc,@Param("displayorder") Integer order);

    /**
     * 更新话题信息。
     *
     * @param id 话题ID
     * @param name 话题名称
     * @param sense 话题场景
     * @param cover 话题封面图片链接
     * @param desc 话题描述
     * @return 更新成功返回受影响的行数，失败返回0
     */
    int updateTopic(@Param("id") int id, @Param("name") String name, @Param("sense") String sense, @Param("cover") String cover,@Param("videoUrl") String videoUrl, @Param("desc") String desc,@Param("displayorder") Integer order);

    /**
     * 获取所有语音话题。
     *
     * @return 语音话题列表
     */
    List<ResourceSpokenTopic>getAllResourceSpokenTopic();

    /**
     * 根据话题ID获取话题内容。
     *
     * @param topicId 话题ID
     * @return 话题内容列表
     */
    List<ResourceSpokenTopicContent>getResourceSpokenTopicContentByTopicId(int topicId);

    /**
     * 插入话题内容。
     *
     * @param resourceSpokenTopicContent 话题内容对象
     * @return 插入成功返回受影响的行数，失败返回0
     */
    int insertTopicContent(ResourceSpokenTopicContent resourceSpokenTopicContent);

    /**
     * 更新话题内容。
     *
     * @param resourceSpokenTopicContent 话题内容对象
     * @return 更新成功返回受影响的行数，失败返回0
     */
    int updateTopicContent(ResourceSpokenTopicContent resourceSpokenTopicContent);

    /**
     * 删除话题内容。
     *
     * @param id 话题内容ID
     * @return 删除成功返回受影响的行数，失败返回0
     */
    int deleteTopicContent(int id);

    /**
     * 删除话题。
     *
     * @param topicId 话题ID
     * @return 删除成功返回受影响的行数，失败返回0
     */
    int deleteTopic(int topicId);

}
