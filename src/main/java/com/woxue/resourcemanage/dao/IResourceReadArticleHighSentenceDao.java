package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.read.ResourceReadArticleHighSentenceBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
public interface IResourceReadArticleHighSentenceDao {

    List<ResourceReadArticleHighSentenceBean> listByArticleId(Integer articleId);
    ResourceReadArticleHighSentenceBean edit(Integer sentenceId);
    int save(ResourceReadArticleHighSentenceBean resourceReadArticleHighSentenceBean);
    int batchSave(List<ResourceReadArticleHighSentenceBean> list);
    int update(ResourceReadArticleHighSentenceBean resourceReadArticleHighSentenceBean);

    int deleteByArticleId(Integer articleId);
}
