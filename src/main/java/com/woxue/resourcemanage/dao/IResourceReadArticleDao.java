package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.read.ResourceReadArticleBean;
import com.woxue.resourcemanage.entity.ResourceTopic;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
public interface IResourceReadArticleDao {

    List<ResourceReadArticleBean> listByUnitId(Integer unitId);
    ResourceReadArticleBean edit(Integer articleId);
    ResourceReadArticleBean editBySerialCode(@Param("unitId") Integer unitId,@Param("serialCode") String serialCode);
    int save(ResourceReadArticleBean resourceReadArticleBean);
    int update(ResourceReadArticleBean resourceReadArticleBean);
    int deleteByUnitId(Integer unitId);

    List<ResourceTopic> topicList();
    List<ResourceReadArticleBean> getTopicUnitArticleList(Integer topicId);
    int insertTopic(ResourceTopic resourceTopicBean);

}
