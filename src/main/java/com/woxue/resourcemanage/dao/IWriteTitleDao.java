package com.woxue.resourcemanage.dao;


import com.woxue.common.model.redBook.WriteUnitTitleBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface IWriteTitleDao {

    int deleteByPrimaryKey(Integer id);
    
    void updateTitleIsBasic(@Param("id") Integer id);

    WriteUnitTitleBean getWriteParientTitleByUnitId(@Param("unitId") Integer unitId);

    List<WriteUnitTitleBean> getTitleListByUnitIdAndParientId(@Param("unitId") Integer unitId, @Param("parientId") Integer parientId);

    void deleteByUnitId(@Param("unitId") Integer unitId);

    void addUnitTitle(WriteUnitTitleBean unitTitle);

    void updateUnitTitle(WriteUnitTitleBean unitTitle);
}