package com.woxue.resourcemanage.dao;


import com.woxue.common.model.redBook.read.ResourceReadArticleCorrelationBean;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:43
 */
public interface IResourceReadArticleCorrelationDao {
    ResourceReadArticleCorrelationBean edit(Integer correlationId);
    ResourceReadArticleCorrelationBean editByArticleId(Integer articleId);
    int save(ResourceReadArticleCorrelationBean resourceReadArticleCorrelationBean);
    int update(ResourceReadArticleCorrelationBean resourceReadArticleCorrelationBean);
    int deleteByArticleId(Integer articleId);
}
