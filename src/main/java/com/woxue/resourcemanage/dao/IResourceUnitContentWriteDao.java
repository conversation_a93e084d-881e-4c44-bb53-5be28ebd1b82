package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.RedBookUnitContentWrite;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IResourceUnitContentWriteDao {
    RedBookUnitContentWrite getRecordByUnitId(@Param("resourceUnitId") Integer resourceUnitId);

    List<Integer> getTopicRelatedUnitIdList(@Param("topicId") Integer topicId);

    int addUnitContent(RedBookUnitContentWrite redBookUnitContentWrite);

    int updateUnitContent(RedBookUnitContentWrite redBookUnitContentWrite);

}