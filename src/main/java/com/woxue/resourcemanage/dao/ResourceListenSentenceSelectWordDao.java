package com.woxue.resourcemanage.dao;

import com.woxue.common.model.redBook.listen.ResourceListenSentenceSelectWordBean;

import java.util.List;

/**
 * 同步听力句子选词Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ResourceListenSentenceSelectWordDao
{
    /**
     * 查询同步听力句子选词
     * 
     * @param id 同步听力句子选词主键
     * @return 同步听力句子选词
     */
    ResourceListenSentenceSelectWordBean selectResourceListenSentenceSelectWordById(Integer id);
    List<ResourceListenSentenceSelectWordBean> listByUnitId(Integer unitId);
    /**
     * 查询同步听力句子选词列表
     * 
     * @param resourceListenSentenceSelectWord 同步听力句子选词
     * @return 同步听力句子选词集合
     */
    List<ResourceListenSentenceSelectWordBean> selectResourceListenSentenceSelectWordList(ResourceListenSentenceSelectWordBean resourceListenSentenceSelectWord);

    /**
     * 新增同步听力句子选词
     * 
     * @param resourceListenSentenceSelectWord 同步听力句子选词
     * @return 结果
     */
    int insertResourceListenSentenceSelectWord(ResourceListenSentenceSelectWordBean resourceListenSentenceSelectWord);

    int batchInsert(List<ResourceListenSentenceSelectWordBean> resourceListenSentenceSelectWord);

    /**
     * 修改同步听力句子选词
     * 
     * @param resourceListenSentenceSelectWord 同步听力句子选词
     * @return 结果
     */
    int updateResourceListenSentenceSelectWord(ResourceListenSentenceSelectWordBean resourceListenSentenceSelectWord);

    /**
     * 删除同步听力句子选词
     * 
     * @param id 同步听力句子选词主键
     * @return 结果
     */
    int deleteResourceListenSentenceSelectWordById(Integer id);

    int deleteByUnitId(Integer unitId);

    /**
     * 批量删除同步听力句子选词
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteResourceListenSentenceSelectWordByIds(String[] ids);
}
