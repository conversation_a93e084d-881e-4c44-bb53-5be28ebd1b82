package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.common.model.redBook.listen.ResourceUnitContentListenBean;

import java.util.List;

/**
 * 单元同步听力内容Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ResourceUnitContentListenDao
{
    /**
     * 查询单元同步听力内容
     * 
     * @param id 单元同步听力内容主键
     * @return 单元同步听力内容
     */
    ResourceUnitContentListenBean selectResourceUnitContentListenById(Integer id);
    ResourceUnitContentListenBean editByUnitId(Integer unitId);
    List<ResourceTopic> topicList();

    /**
     * 查询单元同步听力内容列表
     * 
     * @param resourceUnitContentListen 单元同步听力内容
     * @return 单元同步听力内容集合
     */
    List<ResourceUnitContentListenBean> selectResourceUnitContentListenList(ResourceUnitContentListenBean resourceUnitContentListen);

    /**
     * 新增单元同步听力内容
     * 
     * @param resourceUnitContentListen 单元同步听力内容
     * @return 结果
     */
    int insertResourceUnitContentListen(ResourceUnitContentListenBean resourceUnitContentListen);

    /**
     * 修改单元同步听力内容
     * 
     * @param resourceUnitContentListen 单元同步听力内容
     * @return 结果
     */
    int updateResourceUnitContentListen(ResourceUnitContentListenBean resourceUnitContentListen);

    /**
     * 删除单元同步听力内容
     * 
     * @param id 单元同步听力内容主键
     * @return 结果
     */
    int deleteResourceUnitContentListenById(Integer id);

    /**
     * 批量删除单元同步听力内容
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteResourceUnitContentListenByIds(String[] ids);
}
