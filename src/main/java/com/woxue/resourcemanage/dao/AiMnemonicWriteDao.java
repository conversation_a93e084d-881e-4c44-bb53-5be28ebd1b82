package com.woxue.resourcemanage.dao;

import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.AiMnemonicWrite;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * AI写作辅导助记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface AiMnemonicWriteDao
{
    /**
     * 查询AI写作辅导助记
     * 
     * @param id AI写作辅导助记主键
     * @return AI写作辅导助记
     */
        AiMnemonicWrite selectAiMnemonicWriteById(Integer id);
        AiMnemonicWrite selectByUnitId(@Param("unitId") Integer unitId,@Param("gradeName") String gradeName);

    /**
     * 查询AI写作辅导助记列表
     * 
     * @return AI写作辅导助记集合
     */
    List<AiMnemonicWrite> selectAiMnemonicWriteList(AiGenerateQuery aiGenerateQuery);
    int count(AiGenerateQuery aiGenerateQuery);

    /**
     * 新增AI写作辅导助记
     * 
     * @param aiMnemonicWrite AI写作辅导助记
     * @return 结果
     */
    int insertAiMnemonicWrite(AiMnemonicWrite aiMnemonicWrite);

    /**
     * 修改AI写作辅导助记
     * 
     * @param aiMnemonicWrite AI写作辅导助记
     * @return 结果
     */
    int updateAiMnemonicWrite(AiMnemonicWrite aiMnemonicWrite);

    /**
     * 删除AI写作辅导助记
     * 
     * @param id AI写作辅导助记主键
     * @return 结果
     */
    int deleteAiMnemonicWriteById(Long id);

    /**
     * 批量删除AI写作辅导助记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAiMnemonicWriteByIds(Long[] ids);
}
