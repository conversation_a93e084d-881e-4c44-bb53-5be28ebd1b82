package com.woxue.resourcemanage.listener;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.redBook.RedBookVersion;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import com.woxue.resourceservice.util.PictureBookManager;
import com.woxue.resourceservice.util.RedBookCourseManager;
import com.woxue.resourceservice.util.RedBookRedisManager;
import com.woxue.resourceservice.util.ResourceSpokenManager;
import org.apache.dubbo.rpc.model.ApplicationModel;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;

/**
 * 监听器
 * 
 * <AUTHOR>
 * 
 */

public class InitializeListener implements ServletContextListener {


	@Override
	public void contextInitialized(ServletContextEvent sce) {
		System.out.println("----------------------------start server");
		SpringActionSupport.setContext(sce.getServletContext());
		Properties prop = new Properties();
		try {
			InputStream in = this.getClass().getResourceAsStream("/config.properties");
			prop.load(in);
			in.close();
		} catch (IOException e) {
			e.printStackTrace();
		}

		//检查配置是否需要启动时加载小红本课程资源
		if(prop.getProperty("STARTUP_LOAD_REDBOOK_COURSEDATA")!=null&&prop.getProperty("STARTUP_LOAD_REDBOOK_COURSEDATA").equals("on")){
			System.out.println("---start load redBook resource");
			IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
			//*************************清空redis课程内容   防止有重复数据
			JedisPool pool = null;
			Jedis jedis = null;
			try {
				pool = RedBookRedisManager.getPool();
				jedis = pool.getResource();
				jedis.flushDB();
			} catch (Exception e) {
				//释放redis对象
				pool.returnBrokenResource(jedis);
				e.printStackTrace();
			} finally {
				//返还到连接池
				if(jedis!=null){
					pool.returnResource(jedis);
				}
			}
			List<RedBookVersion> versionList = redBookCourseDao.getVersionList();
			RedBookVersion version;
			for (int i = 0; i < versionList.size(); i++) {
				version = versionList.get(i);
				RedBookCourseManager.reloadVersion(version);
				System.out.println(version.getNameCn()+" finish progress:"+(i+1)+"/"+versionList.size());
			}
			System.out.println("---end load redBook resource");
			//绘本
			PictureBookManager.reloadPictureBook();
			//AI口语
			ResourceSpokenManager.reloadResourceSpoken();
		}

		//检查配置是否需要启动时加载配置资源
		if(prop.getProperty("STARTUP_LOAD_CONFIGDATA")!=null&&prop.getProperty("STARTUP_LOAD_CONFIGDATA").equals("on")){

		}
	}
	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		ApplicationModel.defaultModel().destroy();
		System.out.println("----------------------------stop server");
	}
}





