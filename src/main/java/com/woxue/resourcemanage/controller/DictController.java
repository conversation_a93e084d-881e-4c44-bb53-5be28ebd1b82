package com.woxue.resourcemanage.controller;

import com.woxue.common.model.DictionaryBean;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourceservice.service.impl.DictionaryServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/dict")
@Api(tags = "词典管理")
public class DictController {

    @Autowired
    private DictionaryServiceImpl dictionaryService;

    //根据拼写获取列表
    @GetMapping("/getList")
    @ApiOperation(value = "根据拼写获取列表")
    public HSSJsonReulst<List<DictionaryBean>> getList(String spelling)
    {
        if (StringUtils.isBlank(spelling))return HSSJsonReulst.errorMsg("拼写不能为空");
        return HSSJsonReulst.ok(dictionaryService.getDictionaryListByLikeSpelling(spelling, 20));
    }

    //修改词
    @PostMapping("/update")
    @ApiOperation(value = "修改词")
    public HSSJsonReulst<String> update(@RequestBody DictionaryBean dictionaryBean)
    {
        return HSSJsonReulst.ok(dictionaryService.updateDictionary(dictionaryBean)>1);
    }

    @PostMapping("/insert")
    @ApiOperation(value = "插入词")
    public HSSJsonReulst<String> insert(@RequestBody DictionaryBean dictionaryBean)
    {
        return HSSJsonReulst.ok(dictionaryService.insertDictionary(dictionaryBean)>1);
    }


}
