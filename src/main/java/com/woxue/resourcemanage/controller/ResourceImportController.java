package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.IArticleService;
import com.woxue.resourcemanage.service.IWordService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@Api(tags = "资源导入接口")
@RestController
@RequestMapping("/import")
public class ResourceImportController {

    @Autowired
    private IWordService wordService;
    @Autowired
    IArticleService iArticleService;



    @ApiOperation("导入词汇资源")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "files",value = "文件",required = true),
        @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @PostMapping("/word")
    @ResponseBody
    public HSSJsonReulst wordImport(@RequestParam("files") MultipartFile[] files, @RequestParam("courseId") Integer courseId){
        return wordService.insertWordInfo(files , courseId, RedBookContentTypeEnum.WORD);
    }
    @ApiOperation("导入词组资源")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "files",value = "文件",required = true),
        @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @PostMapping("/wordPhrase")
    @ResponseBody
    public HSSJsonReulst wordPhraseImport(@RequestParam("files") MultipartFile[] files, @RequestParam("courseId") Integer courseId){
        return wordService.insertWordInfo(files , courseId, RedBookContentTypeEnum.WORD_PHRASE);
    }

    @ApiOperation("导入句子资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files",value = "文件",required = true),
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @PostMapping("/sentence")
    @ResponseBody
    public HSSJsonReulst sentenceImport(@RequestParam("files") MultipartFile[] files, @RequestParam("courseId") Integer courseId){
        return wordService.insertWordInfo(files , courseId, RedBookContentTypeEnum.SENTENCE);
    }


    @ApiOperation("导入课文资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files",value = "文件",required = true),
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @PostMapping("/article")
    @ResponseBody
    public HSSJsonReulst articleImport(@RequestParam("files") MultipartFile[] files, @RequestParam("courseId") Integer courseId){
        return iArticleService.insertArticleInfo(files , courseId);
    }



    /**
     * 更新操作
     * @param sheet
     * @return
     * @throws Exception
     */
    /*public boolean updateByExcel(Sheet sheet,String programName,int type,String username)throws Exception{
        boolean tf = false;

        int rows = sheet.getRows();
        for(int i=1;i<rows;i++){
            String wordid = sheet.getCell(0, i).getContents();
            String spelling = sheet.getCell(1, i).getContents();
            String syllable = sheet.getCell(2, i).getContents();
            String meaning_en_US = sheet.getCell(3, i).getContents();
            String meaning_zh_CN = sheet.getCell(4, i).getContents();
            String example_en_US = sheet.getCell(5, i).getContents();
            String example_zh_CN = sheet.getCell(6, i).getContents();
            String unit = sheet.getCell(7, i).getContents();

            WordBean wordEntity = new WordBean();

            //type=1单词如果拼写为空，continue   //type=2同步句子如果句子为空，continue
            if(type==1&&(spelling==null||spelling.equals(""))){
                continue;
            }else if(type==2&&(example_en_US==null||example_en_US.equals(""))){
                continue;
            }
            wordEntity.setWordId(Integer.parseInt(wordid));
            wordEntity.setSpelling(new String(spelling.getBytes("UTF-8"),"UTF-8"));
            wordEntity.setSyllable(new String(syllable.getBytes("UTF-8"),"UTF-8"));
            wordEntity.setMeaning_en_US(new String(meaning_en_US.getBytes("UTF-8"),"UTF-8"));
            wordEntity.setMeaning_zh_CN(new String(meaning_zh_CN.getBytes("UTF-8"),"UTF-8"));
            wordEntity.setExample_en_US(new String(example_en_US.getBytes("UTF-8"),"UTF-8"));
            wordEntity.setExample_zh_CN(new String(example_zh_CN.getBytes("UTF-8"),"UTF-8"));
            wordEntity.(new String(unit.getBytes("UTF-8"),"UTF-8"));

            //更新单词表
            try{
                wordService.updateWord(wordEntity);
                Map<String, Object> publishMap=new HashMap<>();
                publishMap.put("wordId", Integer.parseInt(wordid));
                publishMap.put("operate", 1);
                publishMap.put("username", username);
                publishMap.put("description", "批量导入修改单词详细信息");
                publishMap.put("deviceType", 1);
                publishMap.put("publishType", 1);
                publishMap.put("programName", programName);
                Integer publishWordStatus=publishService.getWordPublishStatusByWordId(Integer.parseInt(wordid));
                Integer publishProgramStatus = publishService.getProgramPublishStatusByProgramName(programName);
                if (publishProgramStatus == null || publishProgramStatus == 0){
                    if (publishWordStatus==null){
                        publishService.addWordPublishStatus(publishMap);
                    }else{
                        publishService.updateWordPublishOperaStatus(Integer.parseInt(wordid),(String) publishMap.get("description"), username, 1);
                    }
                    publishService.addWordPublishRecord(publishMap);
                }
            }catch(Exception ex){
                ex.printStackTrace();
                tf = false;
            }
            //更新programWord
            ProgramWord programWord = new ProgramWord();
            programWord.setWord_id(Integer.parseInt(wordid));
            programWord.setProgram_name(programName);
            programWord.setUnit_name(new String(unit.getBytes("UTF-8"),"UTF-8"));
            try{
                wordService.updateProgramWord(programWord);
            }catch(Exception ex){
                ex.printStackTrace();
                tf = false;
            }

        }
        return tf;
    }*/
}
