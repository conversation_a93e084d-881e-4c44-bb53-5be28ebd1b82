package com.woxue.resourcemanage.controller;

import com.redbook.kid.common.enums.KidPhonicsUnitTypeEnum;
import com.redbook.kid.common.model.phonics.*;
import com.redbook.kid.common.util.TencentCosUtil;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.KidPhonicsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.util.*;

/**
 * 少儿自然拼读资源管理控制器
 */
@RestController
@RequestMapping("/kid-phonics")
@Api(tags = "少儿自然拼读资源管理")
public class KidPhonicsController {

    @Autowired
    private KidPhonicsService kidPhonicsService;

    // 课程管理API
    @ApiOperation(value = "获取所有课程列表", notes = "获取所有少儿自然拼读课程")
    @PostMapping("/course/queryList")
    public HSSJsonReulst<List<KidPhonicsCourse>> getAllCourses() {
        try {
            List<KidPhonicsCourse> courses = kidPhonicsService.getAllCourses();
            return HSSJsonReulst.ok(courses);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取课程列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取课程详情", notes = "根据ID获取课程详情")
    @PostMapping("/course/queryDetail")
    public HSSJsonReulst<KidPhonicsCourse> getCourse(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("课程ID不能为空");
            }
            KidPhonicsCourse course = kidPhonicsService.getCourseById(id);
            if (course == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的课程");
            }
            return HSSJsonReulst.ok(course);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取课程详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建课程", notes = "创建新的少儿自然拼读课程")
    @PostMapping("/course/create")
    public HSSJsonReulst<KidPhonicsCourse> createCourse(@RequestBody KidPhonicsCourse course) {
        try {
            kidPhonicsService.insertCourse(course);
            return HSSJsonReulst.ok(course);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建课程失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新课程", notes = "更新现有少儿自然拼读课程")
    @PostMapping("/course/update")
    public HSSJsonReulst<KidPhonicsCourse> updateCourse(@RequestBody KidPhonicsCourse course) {
        try {
            if (course.getId() == null) {
                return HSSJsonReulst.errorMsg("课程ID不能为空");
            }
            kidPhonicsService.updateCourse(course);
            return HSSJsonReulst.ok(course);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新课程失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除课程", notes = "删除少儿自然拼读课程")
    @PostMapping("/course/delete")
    public HSSJsonReulst<Void> deleteCourse(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("课程ID不能为空");
            }
            kidPhonicsService.deleteCourse(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除课程失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "发布课程", notes = "发布少儿自然拼读课程，更新版本号")
    @PostMapping("/course/publish")
    public HSSJsonReulst<Void> publishCourse(@RequestBody Map<String, Object> request) {
        try {
            Integer id = (Integer) request.get("id");
            String publisherName = (String) request.get("publisherName");
            String remark = (String) request.get("remark");

            if (id == null) {
                return HSSJsonReulst.errorMsg("课程ID不能为空");
            }
            if (publisherName == null || publisherName.trim().isEmpty()) {
                return HSSJsonReulst.errorMsg("发布人姓名不能为空");
            }

            kidPhonicsService.publishCourse(id, publisherName, remark);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("发布课程失败: " + e.getMessage());
        }
    }

    // 单元管理API
    @ApiOperation(value = "获取课程下所有单元", notes = "根据课程ID获取所有单元")
    @PostMapping("/unit/queryByCourse")
    public HSSJsonReulst<List<KidPhonicsUnit>> getUnitsByCourse(@RequestBody Map<String, Integer> request) {
        try {
            Integer courseId = request.get("courseId");
            if (courseId == null) {
                return HSSJsonReulst.errorMsg("课程ID不能为空");
            }
            List<KidPhonicsUnit> units = kidPhonicsService.getUnitsByCourseId(courseId);
            return HSSJsonReulst.ok(units);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取单元详情", notes = "根据ID获取单元详情")
    @PostMapping("/unit/queryDetail")
    public HSSJsonReulst<KidPhonicsUnit> getUnit(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            KidPhonicsUnit unit = kidPhonicsService.getUnitById(id);
            if (unit == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的单元");
            }
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取单元详情(包含资源)", notes = "根据ID获取单元详情，包括音素、例词等资源")
    @PostMapping("/unit/queryWithResources")
    public HSSJsonReulst<KidPhonicsUnit> getUnitWithResources(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            KidPhonicsUnit unit = kidPhonicsService.getUnitById(id);
            if (unit == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的单元");
            }
            unit = kidPhonicsService.loadUnitWithResources(unit);
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建单元", notes = "创建新的单元")
    @PostMapping("/unit/create")
    public HSSJsonReulst<KidPhonicsUnit> createUnit(@RequestBody KidPhonicsUnit unit) {
        try {
            if (unit.getCourseId() == null) {
                return HSSJsonReulst.errorMsg("课程ID不能为空");
            }
            kidPhonicsService.insertUnit(unit);
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建单元失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新单元", notes = "更新现有单元")
    @PostMapping("/unit/update")
    public HSSJsonReulst<KidPhonicsUnit> updateUnit(@RequestBody KidPhonicsUnit unit) {
        try {
            if (unit.getId() == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            kidPhonicsService.updateUnit(unit);
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新单元失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除单元", notes = "删除单元")
    @PostMapping("/unit/delete")
    public HSSJsonReulst<Void> deleteUnit(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            kidPhonicsService.deleteUnit(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除单元失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新单元状态", notes = "启用或禁用单元")
    @PostMapping("/unit/updateStatus")
    public HSSJsonReulst<Void> updateUnitStatus(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            Integer status = request.get("status");
            if (id == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            if (status == null) {
                return HSSJsonReulst.errorMsg("状态不能为空");
            }
            kidPhonicsService.updateUnitStatus(id, status);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新单元状态失败: " + e.getMessage());
        }
    }

    // 音素管理API
    @ApiOperation(value = "获取单元下所有音素", notes = "根据单元ID获取所有音素")
    @PostMapping("/letter/queryByUnit")
    public HSSJsonReulst<List<KidPhonicsLetter>> getLettersByUnit(@RequestBody Map<String, Integer> request) {
        try {
            Integer unitId = request.get("unitId");
            if (unitId == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            List<KidPhonicsLetter> letters = kidPhonicsService.getLettersByUnitId(unitId);
            return HSSJsonReulst.ok(letters);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取音素列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取音素详情", notes = "根据ID获取音素详情")
    @PostMapping("/letter/queryDetail")
    public HSSJsonReulst<KidPhonicsLetter> getLetter(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            KidPhonicsLetter letter = kidPhonicsService.getLetterById(id);
            if (letter == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的音素");
            }
            return HSSJsonReulst.ok(letter);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取音素详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建音素", notes = "创建新的音素")
    @PostMapping("/letter/create")
    public HSSJsonReulst<KidPhonicsLetter> createLetter(@RequestBody KidPhonicsLetter letter) {
        try {
            if (letter.getUnitId() == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            kidPhonicsService.insertLetter(letter);
            return HSSJsonReulst.ok(letter);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建音素失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新音素", notes = "更新现有音素")
    @PostMapping("/letter/update")
    public HSSJsonReulst<KidPhonicsLetter> updateLetter(@RequestBody KidPhonicsLetter letter) {
        try {
            if (letter.getId() == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            kidPhonicsService.updateLetter(letter);
            return HSSJsonReulst.ok(letter);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新音素失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除音素", notes = "删除音素")
    @PostMapping("/letter/delete")
    public HSSJsonReulst<Void> deleteLetter(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            kidPhonicsService.deleteLetter(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除音素失败: " + e.getMessage());
        }
    }

    // 音素组件管理API
    @ApiOperation(value = "获取音素下所有组件", notes = "根据音素ID获取所有组件")
    @PostMapping("/component/queryByLetter")
    public HSSJsonReulst<List<KidPhonicsLetterComponent>> getComponentsByLetter(@RequestBody Map<String, Integer> request) {
        try {
            Integer letterId = request.get("letterId");
            if (letterId == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            List<KidPhonicsLetterComponent> components = kidPhonicsService.getComponentsByLetterId(letterId);
            return HSSJsonReulst.ok(components);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取音素组件列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取组件详情", notes = "根据ID获取组件详情")
    @PostMapping("/component/queryDetail")
    public HSSJsonReulst<KidPhonicsLetterComponent> getComponent(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("组件ID不能为空");
            }
            KidPhonicsLetterComponent component = kidPhonicsService.getLetterComponentById(id);
            if (component == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的组件");
            }
            return HSSJsonReulst.ok(component);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取组件详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建组件", notes = "创建新的音素组件")
    @PostMapping("/component/create")
    public HSSJsonReulst<KidPhonicsLetterComponent> createComponent(@RequestBody KidPhonicsLetterComponent component) {
        try {
            if (component.getLetterId() == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            kidPhonicsService.insertLetterComponent(component);
            return HSSJsonReulst.ok(component);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建组件失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新组件", notes = "更新现有组件")
    @PostMapping("/component/update")
    public HSSJsonReulst<KidPhonicsLetterComponent> updateComponent(@RequestBody KidPhonicsLetterComponent component) {
        try {
            if (component.getId() == null) {
                return HSSJsonReulst.errorMsg("组件ID不能为空");
            }
            kidPhonicsService.updateLetterComponent(component);
            return HSSJsonReulst.ok(component);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新组件失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除组件", notes = "删除组件")
    @PostMapping("/component/delete")
    public HSSJsonReulst<Void> deleteComponent(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("组件ID不能为空");
            }
            kidPhonicsService.deleteLetterComponent(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除组件失败: " + e.getMessage());
        }
    }

    // 例词管理API
    @ApiOperation(value = "获取音素下所有例词", notes = "根据音素ID获取所有例词")
    @PostMapping("/word/queryByLetter")
    public HSSJsonReulst<List<KidPhonicsWord>> getWordsByLetter(@RequestBody Map<String, Integer> request) {
        try {
            Integer letterId = request.get("letterId");
            if (letterId == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            List<KidPhonicsWord> words = kidPhonicsService.getWordsByLetterId(letterId);
            return HSSJsonReulst.ok(words);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取例词列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取例词详情", notes = "根据ID获取例词详情")
    @PostMapping("/word/queryDetail")
    public HSSJsonReulst<KidPhonicsWord> getWord(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("例词ID不能为空");
            }
            KidPhonicsWord word = kidPhonicsService.getWordById(id);
            if (word == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的例词");
            }
            return HSSJsonReulst.ok(word);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取例词详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建例词", notes = "创建新的例词")
    @PostMapping("/word/create")
    public HSSJsonReulst<KidPhonicsWord> createWord(@RequestBody KidPhonicsWord word) {
        try {
            if (word.getLetterId() == null) {
                return HSSJsonReulst.errorMsg("音素ID不能为空");
            }
            kidPhonicsService.insertWord(word);
            return HSSJsonReulst.ok(word);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建例词失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新例词", notes = "更新现有例词")
    @PostMapping("/word/update")
    public HSSJsonReulst<KidPhonicsWord> updateWord(@RequestBody KidPhonicsWord word) {
        try {
            if (word.getId() == null) {
                return HSSJsonReulst.errorMsg("例词ID不能为空");
            }
            kidPhonicsService.updateWord(word);
            return HSSJsonReulst.ok(word);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新例词失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除例词", notes = "删除例词")
    @PostMapping("/word/delete")
    public HSSJsonReulst<Void> deleteWord(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("例词ID不能为空");
            }
            kidPhonicsService.deleteWord(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除例词失败: " + e.getMessage());
        }
    }

    // 儿歌管理API
    @ApiOperation(value = "获取单元下所有儿歌", notes = "根据单元ID获取所有儿歌")
    @PostMapping("/rhyme/queryByUnit")
    public HSSJsonReulst<List<KidPhonicsRhyme>> getRhymesByUnit(@RequestBody Map<String, Integer> request) {
        try {
            Integer unitId = request.get("unitId");
            if (unitId == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            List<KidPhonicsRhyme> rhymes = kidPhonicsService.getRhymesByUnitId(unitId);
            return HSSJsonReulst.ok(rhymes);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取儿歌列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取儿歌详情", notes = "根据ID获取儿歌详情")
    @PostMapping("/rhyme/queryDetail")
    public HSSJsonReulst<KidPhonicsRhyme> getRhyme(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }
            KidPhonicsRhyme rhyme = kidPhonicsService.getRhymeById(id);
            if (rhyme == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的儿歌");
            }
            return HSSJsonReulst.ok(rhyme);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取儿歌详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建儿歌", notes = "创建新的儿歌")
    @PostMapping("/rhyme/create")
    public HSSJsonReulst<KidPhonicsRhyme> createRhyme(@RequestBody KidPhonicsRhyme rhyme) {
        try {
            if (rhyme.getUnitId() == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            kidPhonicsService.insertRhyme(rhyme);
            return HSSJsonReulst.ok(rhyme);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建儿歌失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新儿歌", notes = "更新现有儿歌")
    @PostMapping("/rhyme/update")
    public HSSJsonReulst<KidPhonicsRhyme> updateRhyme(@RequestBody KidPhonicsRhyme rhyme) {
        try {
            if (rhyme.getId() == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }
            kidPhonicsService.updateRhyme(rhyme);
            return HSSJsonReulst.ok(rhyme);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新儿歌失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除儿歌", notes = "删除儿歌")
    @PostMapping("/rhyme/delete")
    public HSSJsonReulst<Void> deleteRhyme(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("儿歌ID不能为空");
            }
            kidPhonicsService.deleteRhyme(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除儿歌失败: " + e.getMessage());
        }
    }

    // 绘本管理API
    @ApiOperation(value = "获取单元下所有绘本", notes = "根据单元ID获取所有绘本")
    @PostMapping("/pictureBook/queryByUnit")
    public HSSJsonReulst<List<KidPhonicsPictureBook>> getPictureBooksByUnit(@RequestBody Map<String, Integer> request) {
        try {
            Integer unitId = request.get("unitId");
            if (unitId == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            List<KidPhonicsPictureBook> pictureBooks = kidPhonicsService.getPictureBooksByUnitId(unitId);
            return HSSJsonReulst.ok(pictureBooks);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取绘本详情", notes = "根据ID获取绘本详情")
    @PostMapping("/pictureBook/queryDetail")
    public HSSJsonReulst<KidPhonicsPictureBook> getPictureBook(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("绘本ID不能为空");
            }
            KidPhonicsPictureBook pictureBook = kidPhonicsService.getPictureBookById(id);
            if (pictureBook == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的绘本");
            }
            return HSSJsonReulst.ok(pictureBook);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建绘本", notes = "创建新的绘本")
    @PostMapping("/pictureBook/create")
    public HSSJsonReulst<KidPhonicsPictureBook> createPictureBook(@RequestBody KidPhonicsPictureBook pictureBook) {
        try {
            if (pictureBook.getUnitId() == null) {
                return HSSJsonReulst.errorMsg("单元ID不能为空");
            }
            kidPhonicsService.insertPictureBook(pictureBook);
            return HSSJsonReulst.ok(pictureBook);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建绘本失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新绘本", notes = "更新现有绘本")
    @PostMapping("/pictureBook/update")
    public HSSJsonReulst<KidPhonicsPictureBook> updatePictureBook(@RequestBody KidPhonicsPictureBook pictureBook) {
        try {
            if (pictureBook.getId() == null) {
                return HSSJsonReulst.errorMsg("绘本ID不能为空");
            }
            kidPhonicsService.updatePictureBook(pictureBook);
            return HSSJsonReulst.ok(pictureBook);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新绘本失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除绘本", notes = "删除绘本")
    @PostMapping("/pictureBook/delete")
    public HSSJsonReulst<Void> deletePictureBook(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("绘本ID不能为空");
            }
            kidPhonicsService.deletePictureBook(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除绘本失败: " + e.getMessage());
        }
    }

    // 绘本内容管理API
    @ApiOperation(value = "获取绘本下所有内容页", notes = "根据绘本ID获取所有内容页")
    @PostMapping("/pictureBookContent/queryByBook")
    public HSSJsonReulst<List<KidPhonicsPictureBookContent>> getContentsByPictureBook(@RequestBody Map<String, Integer> request) {
        try {
            Integer bookId = request.get("bookId");
            if (bookId == null) {
                return HSSJsonReulst.errorMsg("绘本ID不能为空");
            }
            List<KidPhonicsPictureBookContent> contents = kidPhonicsService.getPictureBookContentsByPictureBookId(bookId);
            return HSSJsonReulst.ok(contents);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本内容列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取绘本内容详情", notes = "根据ID获取绘本内容详情")
    @PostMapping("/pictureBookContent/queryDetail")
    public HSSJsonReulst<KidPhonicsPictureBookContent> getPictureBookContent(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("内容ID不能为空");
            }
            KidPhonicsPictureBookContent content = kidPhonicsService.getPictureBookContentById(id);
            if (content == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的绘本内容");
            }
            return HSSJsonReulst.ok(content);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本内容详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建绘本内容", notes = "创建新的绘本内容")
    @PostMapping("/pictureBookContent/create")
    public HSSJsonReulst<KidPhonicsPictureBookContent> createPictureBookContent(@RequestBody KidPhonicsPictureBookContent content) {
        try {
            if (content.getPictureBookId() == null) {
                return HSSJsonReulst.errorMsg("绘本ID不能为空");
            }
            kidPhonicsService.insertPictureBookContent(content);
            return HSSJsonReulst.ok(content);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建绘本内容失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新绘本内容", notes = "更新现有绘本内容")
    @PostMapping("/pictureBookContent/update")
    public HSSJsonReulst<KidPhonicsPictureBookContent> updatePictureBookContent(@RequestBody KidPhonicsPictureBookContent content) {
        try {
            if (content.getId() == null) {
                return HSSJsonReulst.errorMsg("内容ID不能为空");
            }
            kidPhonicsService.updatePictureBookContent(content);
            return HSSJsonReulst.ok(content);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新绘本内容失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除绘本内容", notes = "删除绘本内容")
    @PostMapping("/pictureBookContent/delete")
    public HSSJsonReulst<Void> deletePictureBookContent(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("内容ID不能为空");
            }
            kidPhonicsService.deletePictureBookContent(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除绘本内容失败: " + e.getMessage());
        }
    }

    // 绘本句子管理API
    @ApiOperation(value = "获取内容页下所有句子", notes = "根据内容页ID获取所有句子")
    @PostMapping("/pictureBookSentence/queryByContent")
    public HSSJsonReulst<List<KidPhonicsPictureBookSentence>> getSentencesByContent(@RequestBody Map<String, Integer> request) {
        try {
            Integer contentId = request.get("contentId");
            if (contentId == null) {
                return HSSJsonReulst.errorMsg("内容页ID不能为空");
            }
            List<KidPhonicsPictureBookSentence> sentences = kidPhonicsService.getPictureBookSentencesByContentId(contentId);
            return HSSJsonReulst.ok(sentences);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本句子列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取绘本句子详情", notes = "根据ID获取绘本句子详情")
    @PostMapping("/pictureBookSentence/queryDetail")
    public HSSJsonReulst<KidPhonicsPictureBookSentence> getPictureBookSentence(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("句子ID不能为空");
            }
            KidPhonicsPictureBookSentence sentence = kidPhonicsService.getPictureBookSentenceById(id);
            if (sentence == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的绘本句子");
            }
            return HSSJsonReulst.ok(sentence);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本句子详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建绘本句子", notes = "创建新的绘本句子")
    @PostMapping("/pictureBookSentence/create")
    public HSSJsonReulst<KidPhonicsPictureBookSentence> createPictureBookSentence(@RequestBody KidPhonicsPictureBookSentence sentence) {
        try {
            if (sentence.getContentId() == null) {
                return HSSJsonReulst.errorMsg("内容页ID不能为空");
            }
            kidPhonicsService.insertPictureBookSentence(sentence);
            return HSSJsonReulst.ok(sentence);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建绘本句子失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新绘本句子", notes = "更新现有绘本句子")
    @PostMapping("/pictureBookSentence/update")
    public HSSJsonReulst<KidPhonicsPictureBookSentence> updatePictureBookSentence(@RequestBody KidPhonicsPictureBookSentence sentence) {
        try {
            if (sentence.getId() == null) {
                return HSSJsonReulst.errorMsg("句子ID不能为空");
            }
            kidPhonicsService.updatePictureBookSentence(sentence);
            return HSSJsonReulst.ok(sentence);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新绘本句子失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除绘本句子", notes = "删除绘本句子")
    @PostMapping("/pictureBookSentence/delete")
    public HSSJsonReulst<Void> deletePictureBookSentence(@RequestBody Map<String, Integer> request) {
        try {
            Integer id = request.get("id");
            if (id == null) {
                return HSSJsonReulst.errorMsg("句子ID不能为空");
            }
            kidPhonicsService.deletePictureBookSentence(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除绘本句子失败: " + e.getMessage());
        }
    }

    public static final String PATH_PREFIX = "/kid-phonics";

    // 文件上传API
    @ApiOperation(value = "上传音频文件", notes = "上传音频文件并返回URL")
    @PostMapping("/upload/audio")
    public HSSJsonReulst<Map<String, String>> uploadAudio(@RequestParam("file") MultipartFile file) {
        return upload(file, 2);
    }

    @ApiOperation(value = "上传图片文件", notes = "上传图片文件并返回URL")
    @PostMapping("/upload/image")
    public HSSJsonReulst<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        return upload(file, 1);
    }

    @ApiOperation(value = "上传视频文件", notes = "上传视频文件并返回URL")
    @PostMapping("/upload/video")
    public HSSJsonReulst<Map<String, String>> uploadVideo(@RequestParam("file") MultipartFile file) {
        return upload(file, 3);
    }

    /**
     * 文件上传通用方法
     * 
     * @param file 文件
     * @param type 文件类型：1-图片，2-音频，3-视频，其他-其他文件
     * @return 文件URL
     */
    private HSSJsonReulst<Map<String, String>> upload(MultipartFile file, int type) {
        if (file.isEmpty()) {
            return HSSJsonReulst.errorMsg("文件为空");
        }

        try {
            String fileName = file.getOriginalFilename();
            String filePath;
            
            // 根据文件类型确定路径
            switch (type) {
                case 1:
                    filePath = PATH_PREFIX + "/picture/" + fileName;
                    break;
                case 2:
                    filePath = PATH_PREFIX + "/sound/" + fileName;
                    break;
                case 3:
                    filePath = PATH_PREFIX + "/video/" + fileName;
                    break;
                default:
                    filePath = PATH_PREFIX + "/other/" + fileName;
                    break;
            }
            
            // 使用TencentCosUtil上传文件
            URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
            
            if (url != null) {
                Map<String, String> resultMap = new HashMap<>(2);
                resultMap.put("url", url.toString());
                resultMap.put("fileName", fileName);
                return HSSJsonReulst.ok(resultMap);
            } else {
                return HSSJsonReulst.errorMsg("文件上传失败");
            }
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     * 
     * @param files 文件列表
     * @param type 文件类型：1-图片，2-音频，3-视频，其他-其他文件
     * @return 文件URL列表
     */
    @ApiOperation(value = "批量上传文件", notes = "批量上传文件并返回URL列表")
    @PostMapping("/upload/batch")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "上传的文件列表", required = true, dataType = "MultipartFile"),
            @ApiImplicitParam(name = "type", value = "文件类型（1-图片，2-音频，3-视频）", required = true, dataType = "Integer")
    })
    public HSSJsonReulst<List<Map<String, String>>> uploadBatch(@RequestParam("files") MultipartFile[] files, @RequestParam int type) {
        List<String> filePathList = new ArrayList<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                try {
                    String fileName = file.getOriginalFilename();
                    String filePath;
                    
                    // 根据文件类型确定路径
                    switch (type) {
                        case 1:
                            filePath = PATH_PREFIX + "/picture/" + fileName;
                            break;
                        case 2:
                            filePath = PATH_PREFIX + "/sound/" + fileName;
                            break;
                        case 3:
                            filePath = PATH_PREFIX + "/video/" + fileName;
                            break;
                        default:
                            filePath = PATH_PREFIX + "/other/" + fileName;
                            break;
                    }
                    
                    // 使用TencentCosUtil上传文件
                    URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
                    
                    if (url != null) {
                        Map<String, String> fileMap = new HashMap<>(2);
                        fileMap.put("url", url.toString());
                        fileMap.put("fileName", fileName);
                        resultList.add(fileMap);
                    }
                } catch (Exception e) {
                    return HSSJsonReulst.errorMsg("批量上传文件失败: " + e.getMessage());
                }
            }
        }
        
        return HSSJsonReulst.ok(resultList);
    }

    @ApiOperation(value = "获取单元类型枚举", notes = "获取所有单元类型枚举值")
    @PostMapping("/enum/queryUnitTypes")
    public HSSJsonReulst<Map<String, Object>> getUnitTypeEnums() {
        try {
            Map<String, Object> result = new HashMap<>();
            KidPhonicsUnitTypeEnum[] enums = KidPhonicsUnitTypeEnum.values();
            List<Map<String, String>> enumList = new ArrayList<>();

            for (KidPhonicsUnitTypeEnum e : enums) {
                Map<String, String> enumMap = new HashMap<>();
                enumMap.put("value", e.name());
                enumMap.put("label", e.getDesc());
                enumList.add(enumMap);
            }

            result.put("unitTypes", enumList);
            return HSSJsonReulst.ok(result);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元类型枚举失败: " + e.getMessage());
        }
    }
} 