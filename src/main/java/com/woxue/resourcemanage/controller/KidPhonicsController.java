package com.woxue.resourcemanage.controller;

import com.redbook.kid.common.enums.KidPhonicsUnitTypeEnum;
import com.redbook.kid.common.model.phonics.*;
import com.redbook.kid.common.util.TencentCosUtil;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.KidPhonicsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.util.*;

/**
 * 少儿自然拼读资源管理控制器
 */
@RestController
@RequestMapping("/kid-phonics")
@Api(tags = "少儿自然拼读资源管理")
public class KidPhonicsController {

    @Autowired
    private KidPhonicsService kidPhonicsService;

    // 课程管理API
    @ApiOperation(value = "获取所有课程列表", notes = "获取所有少儿自然拼读课程")
    @GetMapping("/courses")
    public HSSJsonReulst<List<KidPhonicsCourse>> getAllCourses() {
        try {
            List<KidPhonicsCourse> courses = kidPhonicsService.getAllCourses();
            return HSSJsonReulst.ok(courses);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取课程列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取课程详情", notes = "根据ID获取课程详情")
    @ApiImplicitParam(name = "id", value = "课程ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/courses/{id}")
    public HSSJsonReulst<KidPhonicsCourse> getCourse(@PathVariable Integer id) {
        try {
            KidPhonicsCourse course = kidPhonicsService.getCourseById(id);
            if (course == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的课程");
            }
            return HSSJsonReulst.ok(course);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取课程详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建课程", notes = "创建新的少儿自然拼读课程")
    @PostMapping("/courses")
    public HSSJsonReulst<KidPhonicsCourse> createCourse(@RequestBody KidPhonicsCourse course) {
        try {
            kidPhonicsService.insertCourse(course);
            return HSSJsonReulst.ok(course);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建课程失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新课程", notes = "更新现有少儿自然拼读课程")
    @PutMapping("/courses/{id}")
    public HSSJsonReulst<KidPhonicsCourse> updateCourse(@PathVariable Integer id, @RequestBody KidPhonicsCourse course) {
        try {
            course.setId(id);
            kidPhonicsService.updateCourse(course);
            return HSSJsonReulst.ok(course);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新课程失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除课程", notes = "删除少儿自然拼读课程")
    @ApiImplicitParam(name = "id", value = "课程ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/courses/{id}")
    public HSSJsonReulst<Void> deleteCourse(@PathVariable Integer id) {
        try {
            kidPhonicsService.deleteCourse(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除课程失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "发布课程", notes = "发布少儿自然拼读课程，更新版本号")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "课程ID", required = true, dataType = "int", paramType = "path"),
        @ApiImplicitParam(name = "publisherName", value = "发布人姓名", required = true, dataType = "string"),
        @ApiImplicitParam(name = "remark", value = "发布备注", required = false, dataType = "string")
    })
    @PostMapping("/courses/{id}/publish")
    public HSSJsonReulst<Void> publishCourse(@PathVariable Integer id, 
                                    @RequestParam String publisherName, 
                                    @RequestParam(required = false) String remark) {
        try {
            kidPhonicsService.publishCourse(id, publisherName, remark);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("发布课程失败: " + e.getMessage());
        }
    }

    // 单元管理API
    @ApiOperation(value = "获取课程下所有单元", notes = "根据课程ID获取所有单元")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/courses/{courseId}/units")
    public HSSJsonReulst<List<KidPhonicsUnit>> getUnitsByCourse(@PathVariable Integer courseId) {
        try {
            List<KidPhonicsUnit> units = kidPhonicsService.getUnitsByCourseId(courseId);
            return HSSJsonReulst.ok(units);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取单元详情", notes = "根据ID获取单元详情")
    @ApiImplicitParam(name = "id", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/units/{id}")
    public HSSJsonReulst<KidPhonicsUnit> getUnit(@PathVariable Integer id) {
        try {
            KidPhonicsUnit unit = kidPhonicsService.getUnitById(id);
            if (unit == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的单元");
            }
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取单元详情(包含资源)", notes = "根据ID获取单元详情，包括音素、例词等资源")
    @ApiImplicitParam(name = "id", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/units/{id}/with-resources")
    public HSSJsonReulst<KidPhonicsUnit> getUnitWithResources(@PathVariable Integer id) {
        try {
            KidPhonicsUnit unit = kidPhonicsService.getUnitById(id);
            if (unit == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的单元");
            }
            unit = kidPhonicsService.loadUnitWithResources(unit);
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建单元", notes = "创建新的单元")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/courses/{courseId}/units")
    public HSSJsonReulst<KidPhonicsUnit> createUnit(@PathVariable Integer courseId, @RequestBody KidPhonicsUnit unit) {
        try {
            unit.setCourseId(courseId);
            kidPhonicsService.insertUnit(unit);
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建单元失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新单元", notes = "更新现有单元")
    @PutMapping("/units/{id}")
    public HSSJsonReulst<KidPhonicsUnit> updateUnit(@PathVariable Integer id, @RequestBody KidPhonicsUnit unit) {
        try {
            unit.setId(id);
            kidPhonicsService.updateUnit(unit);
            return HSSJsonReulst.ok(unit);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新单元失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除单元", notes = "删除单元")
    @ApiImplicitParam(name = "id", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/units/{id}")
    public HSSJsonReulst<Void> deleteUnit(@PathVariable Integer id) {
        try {
            kidPhonicsService.deleteUnit(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除单元失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新单元状态", notes = "启用或禁用单元")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "单元ID", required = true, dataType = "int", paramType = "path"),
        @ApiImplicitParam(name = "status", value = "状态(0-禁用，1-启用)", required = true, dataType = "int")
    })
    @PutMapping("/units/{id}/status")
    public HSSJsonReulst<Void> updateUnitStatus(@PathVariable Integer id, @RequestParam Integer status) {
        try {
            kidPhonicsService.updateUnitStatus(id, status);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新单元状态失败: " + e.getMessage());
        }
    }

    // 音素管理API
    @ApiOperation(value = "获取单元下所有音素", notes = "根据单元ID获取所有音素")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/units/{unitId}/letters")
    public HSSJsonReulst<List<KidPhonicsLetter>> getLettersByUnit(@PathVariable Integer unitId) {
        try {
            List<KidPhonicsLetter> letters = kidPhonicsService.getLettersByUnitId(unitId);
            return HSSJsonReulst.ok(letters);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取音素列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取音素详情", notes = "根据ID获取音素详情")
    @ApiImplicitParam(name = "id", value = "音素ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/letters/{id}")
    public HSSJsonReulst<KidPhonicsLetter> getLetter(@PathVariable Integer id) {
        try {
            KidPhonicsLetter letter = kidPhonicsService.getLetterById(id);
            if (letter == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的音素");
            }
            return HSSJsonReulst.ok(letter);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取音素详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建音素", notes = "创建新的音素")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/units/{unitId}/letters")
    public HSSJsonReulst<KidPhonicsLetter> createLetter(@PathVariable Integer unitId, @RequestBody KidPhonicsLetter letter) {
        try {
            letter.setUnitId(unitId);
            kidPhonicsService.insertLetter(letter);
            return HSSJsonReulst.ok(letter);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建音素失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新音素", notes = "更新现有音素")
    @PutMapping("/letters/{id}")
    public HSSJsonReulst<KidPhonicsLetter> updateLetter(@PathVariable Integer id, @RequestBody KidPhonicsLetter letter) {
        try {
            letter.setId(id);
            kidPhonicsService.updateLetter(letter);
            return HSSJsonReulst.ok(letter);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新音素失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除音素", notes = "删除音素")
    @ApiImplicitParam(name = "id", value = "音素ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/letters/{id}")
    public HSSJsonReulst<Void> deleteLetter(@PathVariable Integer id) {
        try {
            kidPhonicsService.deleteLetter(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除音素失败: " + e.getMessage());
        }
    }

    // 音素组件管理API
    @ApiOperation(value = "获取音素下所有组件", notes = "根据音素ID获取所有组件")
    @ApiImplicitParam(name = "letterId", value = "音素ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/letters/{letterId}/components")
    public HSSJsonReulst<List<KidPhonicsLetterComponent>> getComponentsByLetter(@PathVariable Integer letterId) {
        try {
            List<KidPhonicsLetterComponent> components = kidPhonicsService.getComponentsByLetterId(letterId);
            return HSSJsonReulst.ok(components);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取音素组件列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取组件详情", notes = "根据ID获取组件详情")
    @ApiImplicitParam(name = "id", value = "组件ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/components/{id}")
    public HSSJsonReulst<KidPhonicsLetterComponent> getComponent(@PathVariable Integer id) {
        try {
            KidPhonicsLetterComponent component = kidPhonicsService.getLetterComponentById(id);
            if (component == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的组件");
            }
            return HSSJsonReulst.ok(component);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取组件详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建组件", notes = "创建新的音素组件")
    @ApiImplicitParam(name = "letterId", value = "音素ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/letters/{letterId}/components")
    public HSSJsonReulst<KidPhonicsLetterComponent> createComponent(@PathVariable Integer letterId, @RequestBody KidPhonicsLetterComponent component) {
        try {
            component.setLetterId(letterId);
            kidPhonicsService.insertLetterComponent(component);
            return HSSJsonReulst.ok(component);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建组件失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新组件", notes = "更新现有组件")
    @PutMapping("/components/{id}")
    public HSSJsonReulst<KidPhonicsLetterComponent> updateComponent(@PathVariable Integer id, @RequestBody KidPhonicsLetterComponent component) {
        try {
            component.setId(id);
            kidPhonicsService.updateLetterComponent(component);
            return HSSJsonReulst.ok(component);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新组件失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除组件", notes = "删除组件")
    @ApiImplicitParam(name = "id", value = "组件ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/components/{id}")
    public HSSJsonReulst<Void> deleteComponent(@PathVariable Integer id) {
        try {
            kidPhonicsService.deleteLetterComponent(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除组件失败: " + e.getMessage());
        }
    }

    // 例词管理API
    @ApiOperation(value = "获取音素下所有例词", notes = "根据音素ID获取所有例词")
    @ApiImplicitParam(name = "letterId", value = "音素ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/letters/{letterId}/words")
    public HSSJsonReulst<List<KidPhonicsWord>> getWordsByLetter(@PathVariable Integer letterId) {
        try {
            List<KidPhonicsWord> words = kidPhonicsService.getWordsByLetterId(letterId);
            return HSSJsonReulst.ok(words);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取例词列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取例词详情", notes = "根据ID获取例词详情")
    @ApiImplicitParam(name = "id", value = "例词ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/words/{id}")
    public HSSJsonReulst<KidPhonicsWord> getWord(@PathVariable Integer id) {
        try {
            KidPhonicsWord word = kidPhonicsService.getWordById(id);
            if (word == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的例词");
            }
            return HSSJsonReulst.ok(word);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取例词详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建例词", notes = "创建新的例词")
    @ApiImplicitParam(name = "letterId", value = "音素ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/letters/{letterId}/words")
    public HSSJsonReulst<KidPhonicsWord> createWord(@PathVariable Integer letterId, @RequestBody KidPhonicsWord word) {
        try {
            word.setLetterId(letterId);
            kidPhonicsService.insertWord(word);
            return HSSJsonReulst.ok(word);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建例词失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新例词", notes = "更新现有例词")
    @PutMapping("/words/{id}")
    public HSSJsonReulst<KidPhonicsWord> updateWord(@PathVariable Integer id, @RequestBody KidPhonicsWord word) {
        try {
            word.setId(id);
            kidPhonicsService.updateWord(word);
            return HSSJsonReulst.ok(word);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新例词失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除例词", notes = "删除例词")
    @ApiImplicitParam(name = "id", value = "例词ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/words/{id}")
    public HSSJsonReulst<Void> deleteWord(@PathVariable Integer id) {
        try {
            kidPhonicsService.deleteWord(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除例词失败: " + e.getMessage());
        }
    }

    // 儿歌管理API
    @ApiOperation(value = "获取单元下所有儿歌", notes = "根据单元ID获取所有儿歌")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/units/{unitId}/rhymes")
    public HSSJsonReulst<List<KidPhonicsRhyme>> getRhymesByUnit(@PathVariable Integer unitId) {
        try {
            List<KidPhonicsRhyme> rhymes = kidPhonicsService.getRhymesByUnitId(unitId);
            return HSSJsonReulst.ok(rhymes);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取儿歌列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取儿歌详情", notes = "根据ID获取儿歌详情")
    @ApiImplicitParam(name = "id", value = "儿歌ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/rhymes/{id}")
    public HSSJsonReulst<KidPhonicsRhyme> getRhyme(@PathVariable Integer id) {
        try {
            KidPhonicsRhyme rhyme = kidPhonicsService.getRhymeById(id);
            if (rhyme == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的儿歌");
            }
            return HSSJsonReulst.ok(rhyme);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取儿歌详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建儿歌", notes = "创建新的儿歌")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/units/{unitId}/rhymes")
    public HSSJsonReulst<KidPhonicsRhyme> createRhyme(@PathVariable Integer unitId, @RequestBody KidPhonicsRhyme rhyme) {
        try {
            rhyme.setUnitId(unitId);
            kidPhonicsService.insertRhyme(rhyme);
            return HSSJsonReulst.ok(rhyme);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建儿歌失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新儿歌", notes = "更新现有儿歌")
    @PutMapping("/rhymes/{id}")
    public HSSJsonReulst<KidPhonicsRhyme> updateRhyme(@PathVariable Integer id, @RequestBody KidPhonicsRhyme rhyme) {
        try {
            rhyme.setId(id);
            kidPhonicsService.updateRhyme(rhyme);
            return HSSJsonReulst.ok(rhyme);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新儿歌失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除儿歌", notes = "删除儿歌")
    @ApiImplicitParam(name = "id", value = "儿歌ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/rhymes/{id}")
    public HSSJsonReulst<Void> deleteRhyme(@PathVariable Integer id) {
        try {
            kidPhonicsService.deleteRhyme(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除儿歌失败: " + e.getMessage());
        }
    }

    // 绘本管理API
    @ApiOperation(value = "获取单元下所有绘本", notes = "根据单元ID获取所有绘本")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/units/{unitId}/picture-books")
    public HSSJsonReulst<List<KidPhonicsPictureBook>> getPictureBooksByUnit(@PathVariable Integer unitId) {
        try {
            List<KidPhonicsPictureBook> pictureBooks = kidPhonicsService.getPictureBooksByUnitId(unitId);
            return HSSJsonReulst.ok(pictureBooks);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取绘本详情", notes = "根据ID获取绘本详情")
    @ApiImplicitParam(name = "id", value = "绘本ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/picture-books/{id}")
    public HSSJsonReulst<KidPhonicsPictureBook> getPictureBook(@PathVariable Integer id) {
        try {
            KidPhonicsPictureBook pictureBook = kidPhonicsService.getPictureBookById(id);
            if (pictureBook == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的绘本");
            }
            return HSSJsonReulst.ok(pictureBook);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建绘本", notes = "创建新的绘本")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/units/{unitId}/picture-books")
    public HSSJsonReulst<KidPhonicsPictureBook> createPictureBook(@PathVariable Integer unitId, @RequestBody KidPhonicsPictureBook pictureBook) {
        try {
            pictureBook.setUnitId(unitId);
            kidPhonicsService.insertPictureBook(pictureBook);
            return HSSJsonReulst.ok(pictureBook);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建绘本失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新绘本", notes = "更新现有绘本")
    @PutMapping("/picture-books/{id}")
    public HSSJsonReulst<KidPhonicsPictureBook> updatePictureBook(@PathVariable Integer id, @RequestBody KidPhonicsPictureBook pictureBook) {
        try {
            pictureBook.setId(id);
            kidPhonicsService.updatePictureBook(pictureBook);
            return HSSJsonReulst.ok(pictureBook);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新绘本失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除绘本", notes = "删除绘本")
    @ApiImplicitParam(name = "id", value = "绘本ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/picture-books/{id}")
    public HSSJsonReulst<Void> deletePictureBook(@PathVariable Integer id) {
        try {
            kidPhonicsService.deletePictureBook(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除绘本失败: " + e.getMessage());
        }
    }

    // 绘本内容管理API
    @ApiOperation(value = "获取绘本下所有内容页", notes = "根据绘本ID获取所有内容页")
    @ApiImplicitParam(name = "bookId", value = "绘本ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/picture-books/{bookId}/contents")
    public HSSJsonReulst<List<KidPhonicsPictureBookContent>> getContentsByPictureBook(@PathVariable Integer bookId) {
        try {
            List<KidPhonicsPictureBookContent> contents = kidPhonicsService.getPictureBookContentsByPictureBookId(bookId);
            return HSSJsonReulst.ok(contents);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本内容列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取绘本内容详情", notes = "根据ID获取绘本内容详情")
    @ApiImplicitParam(name = "id", value = "内容ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/picture-book-contents/{id}")
    public HSSJsonReulst<KidPhonicsPictureBookContent> getPictureBookContent(@PathVariable Integer id) {
        try {
            KidPhonicsPictureBookContent content = kidPhonicsService.getPictureBookContentById(id);
            if (content == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的绘本内容");
            }
            return HSSJsonReulst.ok(content);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本内容详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建绘本内容", notes = "创建新的绘本内容")
    @ApiImplicitParam(name = "bookId", value = "绘本ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/picture-books/{bookId}/contents")
    public HSSJsonReulst<KidPhonicsPictureBookContent> createPictureBookContent(@PathVariable Integer bookId, @RequestBody KidPhonicsPictureBookContent content) {
        try {
            content.setPictureBookId(bookId);
            kidPhonicsService.insertPictureBookContent(content);
            return HSSJsonReulst.ok(content);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建绘本内容失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新绘本内容", notes = "更新现有绘本内容")
    @PutMapping("/picture-book-contents/{id}")
    public HSSJsonReulst<KidPhonicsPictureBookContent> updatePictureBookContent(@PathVariable Integer id, @RequestBody KidPhonicsPictureBookContent content) {
        try {
            content.setId(id);
            kidPhonicsService.updatePictureBookContent(content);
            return HSSJsonReulst.ok(content);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新绘本内容失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除绘本内容", notes = "删除绘本内容")
    @ApiImplicitParam(name = "id", value = "内容ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/picture-book-contents/{id}")
    public HSSJsonReulst<Void> deletePictureBookContent(@PathVariable Integer id) {
        try {
            kidPhonicsService.deletePictureBookContent(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除绘本内容失败: " + e.getMessage());
        }
    }

    // 绘本句子管理API
    @ApiOperation(value = "获取内容页下所有句子", notes = "根据内容页ID获取所有句子")
    @ApiImplicitParam(name = "contentId", value = "内容页ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/picture-book-contents/{contentId}/sentences")
    public HSSJsonReulst<List<KidPhonicsPictureBookSentence>> getSentencesByContent(@PathVariable Integer contentId) {
        try {
            List<KidPhonicsPictureBookSentence> sentences = kidPhonicsService.getPictureBookSentencesByContentId(contentId);
            return HSSJsonReulst.ok(sentences);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本句子列表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取绘本句子详情", notes = "根据ID获取绘本句子详情")
    @ApiImplicitParam(name = "id", value = "句子ID", required = true, dataType = "int", paramType = "path")
    @GetMapping("/picture-book-sentences/{id}")
    public HSSJsonReulst<KidPhonicsPictureBookSentence> getPictureBookSentence(@PathVariable Integer id) {
        try {
            KidPhonicsPictureBookSentence sentence = kidPhonicsService.getPictureBookSentenceById(id);
            if (sentence == null) {
                return HSSJsonReulst.errorMsg("未找到ID为" + id + "的绘本句子");
            }
            return HSSJsonReulst.ok(sentence);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取绘本句子详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "创建绘本句子", notes = "创建新的绘本句子")
    @ApiImplicitParam(name = "contentId", value = "内容页ID", required = true, dataType = "int", paramType = "path")
    @PostMapping("/picture-book-contents/{contentId}/sentences")
    public HSSJsonReulst<KidPhonicsPictureBookSentence> createPictureBookSentence(@PathVariable Integer contentId, @RequestBody KidPhonicsPictureBookSentence sentence) {
        try {
            sentence.setContentId(contentId);
            kidPhonicsService.insertPictureBookSentence(sentence);
            return HSSJsonReulst.ok(sentence);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("创建绘本句子失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新绘本句子", notes = "更新现有绘本句子")
    @PutMapping("/picture-book-sentences/{id}")
    public HSSJsonReulst<KidPhonicsPictureBookSentence> updatePictureBookSentence(@PathVariable Integer id, @RequestBody KidPhonicsPictureBookSentence sentence) {
        try {
            sentence.setId(id);
            kidPhonicsService.updatePictureBookSentence(sentence);
            return HSSJsonReulst.ok(sentence);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("更新绘本句子失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "删除绘本句子", notes = "删除绘本句子")
    @ApiImplicitParam(name = "id", value = "句子ID", required = true, dataType = "int", paramType = "path")
    @DeleteMapping("/picture-book-sentences/{id}")
    public HSSJsonReulst<Void> deletePictureBookSentence(@PathVariable Integer id) {
        try {
            kidPhonicsService.deletePictureBookSentence(id);
            return HSSJsonReulst.ok();
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("删除绘本句子失败: " + e.getMessage());
        }
    }

    public static final String PATH_PREFIX = "/kid-phonics";

    // 文件上传API
    @ApiOperation(value = "上传音频文件", notes = "上传音频文件并返回URL")
    @PostMapping("/upload/audio")
    public HSSJsonReulst<Map<String, String>> uploadAudio(@RequestParam("file") MultipartFile file) {
        return upload(file, 2);
    }

    @ApiOperation(value = "上传图片文件", notes = "上传图片文件并返回URL")
    @PostMapping("/upload/image")
    public HSSJsonReulst<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        return upload(file, 1);
    }

    @ApiOperation(value = "上传视频文件", notes = "上传视频文件并返回URL")
    @PostMapping("/upload/video")
    public HSSJsonReulst<Map<String, String>> uploadVideo(@RequestParam("file") MultipartFile file) {
        return upload(file, 3);
    }

    /**
     * 文件上传通用方法
     * 
     * @param file 文件
     * @param type 文件类型：1-图片，2-音频，3-视频，其他-其他文件
     * @return 文件URL
     */
    private HSSJsonReulst<Map<String, String>> upload(MultipartFile file, int type) {
        if (file.isEmpty()) {
            return HSSJsonReulst.errorMsg("文件为空");
        }

        try {
            String fileName = file.getOriginalFilename();
            String filePath;
            
            // 根据文件类型确定路径
            switch (type) {
                case 1:
                    filePath = PATH_PREFIX + "/picture/" + fileName;
                    break;
                case 2:
                    filePath = PATH_PREFIX + "/sound/" + fileName;
                    break;
                case 3:
                    filePath = PATH_PREFIX + "/video/" + fileName;
                    break;
                default:
                    filePath = PATH_PREFIX + "/other/" + fileName;
                    break;
            }
            
            // 使用TencentCosUtil上传文件
            URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
            
            if (url != null) {
                Map<String, String> resultMap = new HashMap<>(2);
                resultMap.put("url", url.toString());
                resultMap.put("fileName", fileName);
                return HSSJsonReulst.ok(resultMap);
            } else {
                return HSSJsonReulst.errorMsg("文件上传失败");
            }
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     * 
     * @param files 文件列表
     * @param type 文件类型：1-图片，2-音频，3-视频，其他-其他文件
     * @return 文件URL列表
     */
    @ApiOperation(value = "批量上传文件", notes = "批量上传文件并返回URL列表")
    @PostMapping("/upload/batch")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "上传的文件列表", required = true, dataType = "MultipartFile"),
            @ApiImplicitParam(name = "type", value = "文件类型（1-图片，2-音频，3-视频）", required = true, dataType = "Integer")
    })
    public HSSJsonReulst<List<Map<String, String>>> uploadBatch(@RequestParam("files") MultipartFile[] files, @RequestParam int type) {
        List<String> filePathList = new ArrayList<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                try {
                    String fileName = file.getOriginalFilename();
                    String filePath;
                    
                    // 根据文件类型确定路径
                    switch (type) {
                        case 1:
                            filePath = PATH_PREFIX + "/picture/" + fileName;
                            break;
                        case 2:
                            filePath = PATH_PREFIX + "/sound/" + fileName;
                            break;
                        case 3:
                            filePath = PATH_PREFIX + "/video/" + fileName;
                            break;
                        default:
                            filePath = PATH_PREFIX + "/other/" + fileName;
                            break;
                    }
                    
                    // 使用TencentCosUtil上传文件
                    URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
                    
                    if (url != null) {
                        Map<String, String> fileMap = new HashMap<>(2);
                        fileMap.put("url", url.toString());
                        fileMap.put("fileName", fileName);
                        resultList.add(fileMap);
                    }
                } catch (Exception e) {
                    return HSSJsonReulst.errorMsg("批量上传文件失败: " + e.getMessage());
                }
            }
        }
        
        return HSSJsonReulst.ok(resultList);
    }

    @ApiOperation(value = "获取单元类型枚举", notes = "获取所有单元类型枚举值")
    @GetMapping("/enums/unit-types")
    public HSSJsonReulst<Map<String, Object>> getUnitTypeEnums() {
        try {
            Map<String, Object> result = new HashMap<>();
            KidPhonicsUnitTypeEnum[] enums = KidPhonicsUnitTypeEnum.values();
            List<Map<String, String>> enumList = new ArrayList<>();
            
            for (KidPhonicsUnitTypeEnum e : enums) {
                Map<String, String> enumMap = new HashMap<>();
                enumMap.put("value", e.name());
                enumMap.put("label", e.getDesc());
                enumList.add(enumMap);
            }
            
            result.put("unitTypes", enumList);
            return HSSJsonReulst.ok(result);
        } catch (Exception e) {
            return HSSJsonReulst.errorMsg("获取单元类型枚举失败: " + e.getMessage());
        }
    }
} 