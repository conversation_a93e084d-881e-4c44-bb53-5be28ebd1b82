package com.woxue.resourcemanage.controller;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.common.model.redBook.RedBookVersion;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class ReloadSentenceCourseController {

    @GetMapping("/reloadSentenceCourse")
    public void reloadSentenceCourse(String key){
        if ("oooooo".equals(key)){
            IRedBookCourseDao redBookCourseDao = SpringActionSupport.getSpringBean("redBookCourseDao", null, IRedBookCourseDao.class);
            //获取全部版本

            List<RedBookVersion> versionList = redBookCourseDao.getVersionList();
            //遍历版本
            for (RedBookVersion version : versionList) {
                List<RedBookCourse> list = redBookCourseDao.getCourseList(version.getId());
                for (RedBookCourse course : list) {
                    RedBookCourse bookCourse = RedBookCourseManager.getRedBookCourse(course.getId());
                    //判断是已经上线的版本，并且包含句子的课程，就重新加载
                    if (bookCourse != null) {
                        RedBookCourse course1 = redBookCourseDao.getCourse(bookCourse.getId());
                        if (course1.getBranchId()<1){
                            RedBookCourseManager.reloadCourse(bookCourse.getId());
                        }
                    }
                }
            }
        }
    }
}
