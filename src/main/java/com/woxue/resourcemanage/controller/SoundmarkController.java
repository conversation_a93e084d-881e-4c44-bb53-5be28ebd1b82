package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.ISoundMarkService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/soundmark")
@ApiOperation("分音节管理")
public class SoundmarkController {

    @Autowired
    ISoundMarkService iSoundMarkService;

    /**
     * CREATE TABLE `soundmark` (
     * `spelling` varchar(30) NOT NULL,
     * `soundmark` varchar(30) DEFAULT NULL,
     * PRIMARY KEY (`spelling`) USING BTREE
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
     */
    @ApiOperation("分音节列表")
    @RequestMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true)
    })
    public HSSJsonReulst list(String spelling, Integer pageNum, Integer pageSize) {
        List<Map<String, Object>> list = iSoundMarkService.list(spelling, (pageNum - 1) * pageSize, pageSize);
        Integer count = iSoundMarkService.count(spelling);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("list", list);
        hashMap.put("count", count);
        return HSSJsonReulst.ok(hashMap);
    }

    @ApiOperation("添加分音节")
    @RequestMapping("/add")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "soundmark", value = "音标", required = true)
    })
    public HSSJsonReulst add(String spelling, String soundmark) {
        return HSSJsonReulst.ok(iSoundMarkService.add(spelling, soundmark));
    }

    @ApiOperation("更新分音节")
    @RequestMapping("/update")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "oldSpelling", value = "旧拼写", required = true),
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "soundmark", value = "音标", required = true)
    })
    public HSSJsonReulst<Boolean> update(String oldSpelling, String spelling, String soundmark) {
        return HSSJsonReulst.ok(iSoundMarkService.update(oldSpelling, spelling, soundmark));
    }

    @ApiOperation("删除分音节")
    @RequestMapping("/delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true)
    })
    public HSSJsonReulst<Boolean> delete(String spelling) {
        return HSSJsonReulst.ok(iSoundMarkService.delete(spelling));
    }
}
