package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.RedBookConstant;
import com.woxue.common.model.redBook.RedBookVersion;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.redbookresource.model.ResourcePublishRecords;
import com.woxue.resourcemanage.util.SysUserUtils;
import com.woxue.resourceservice.util.RedBookCourseManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


@Api(tags = "发布上线")
@RestController
@RequestMapping("/publish")
public class PublishController {

    @ApiOperation("版本发布上线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "versionId", value = "版本ID", required = true)
    })
    @PostMapping("/version")
    @ResponseBody
    public HSSJsonReulst version(@RequestParam("versionId") Integer versionId, HttpServletRequest request) {
        if (SysUserUtils.checkEdit(request)) {
            if (RedBookCourseManager.reloadVersion(versionId)) {
                RedBookVersion redBookVersion = RedBookCourseManager.getRedBookVersion(versionId);
                if (redBookVersion.getVersionType()== RedBookConstant.VersionType.PHONICS_NEW_V2.value){
                    RedBookCourseManager.addPublishRecord(ResourcePublishRecords.PublishType.PHONIC_NEW_V2_PUBLISH, "versionId", versionId);
                }else {
                    RedBookCourseManager.addPublishRecord(ResourcePublishRecords.PublishType.VERSION_PUBLISH, "versionId", versionId);
                }
                return HSSJsonReulst.ok("发布成功！");
            }
            return HSSJsonReulst.errorMsg("发布失败！");
        } else {
            return HSSJsonReulst.errorMsg("没有权限！");
        }
    }

    @ApiOperation("课程发布上线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = true)
    })
    @PostMapping("/course")
    @ResponseBody
    public HSSJsonReulst course(@RequestParam("courseId") Integer courseId, HttpServletRequest request) {
        if (SysUserUtils.checkEdit(request)) {
            if (RedBookCourseManager.reloadCourse(courseId)) {
                RedBookCourseManager.addPublishRecord(ResourcePublishRecords.PublishType.COURSE_PUBLISH, "courseId", courseId);
                return HSSJsonReulst.ok("发布成功！");
            }
            return HSSJsonReulst.errorMsg("发布失败！");
        } else {
            return HSSJsonReulst.errorMsg("没有权限！");
        }

    }


    @ApiOperation("同步阅读课程发布上线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = true)
    })
    @PostMapping("/readCourse")
    @ResponseBody
    public HSSJsonReulst readCourse(@RequestParam("courseId") Integer courseId, HttpServletRequest request) {
        if (SysUserUtils.checkEdit(request)) {
            RedBookCourseManager.reloadReadCourse(courseId);
            return HSSJsonReulst.ok("同步阅读课程发布成功！");
        } else {
            return HSSJsonReulst.errorMsg("没有权限！");
        }
    }
    @ApiOperation("同步阅读单元发布上线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unitId", value = "单元ID", required = true)
    })
    @PostMapping("/readUnit")
    @ResponseBody
    public HSSJsonReulst readUnit(@RequestParam("unitId") Integer unitId, HttpServletRequest request) {
        if (SysUserUtils.checkEdit(request)) {
            RedBookCourseManager.reloadReadUnit(unitId);
            return HSSJsonReulst.ok("同步阅读单元发布成功！");
        } else {
            return HSSJsonReulst.errorMsg("没有权限！");
        }
    }

}
