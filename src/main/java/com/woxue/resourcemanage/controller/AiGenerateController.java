package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.AiGenerateQuery;
import com.woxue.resourcemanage.entity.AiMnemonicParent;
import com.woxue.resourcemanage.service.AiGenerateService;
import com.woxue.resourcemanage.service.JobService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/aiGenerate")
@Api(tags = "AI生成内容管理")
public class AiGenerateController {

    @Autowired
    private AiGenerateService aiGenerateService;
    @Autowired
    private JobService jobService;

    @ApiOperation("ai内容列表")
    @GetMapping("/list")
    public HSSJsonReulst<Map<String, Object>> list(@ApiParam("模块类型：resourceWord、word、sentence、write、article") String moduleType,
                                                   @ApiParam("处理状态：0未处理，1已处理，2忽略") Integer status,
                                                   @ApiParam("关联内容") String relationContent,
                                                   @ApiParam("课程信息") String courseDetails,
                                                   @ApiParam("正序倒序:asc desc") String sort,
                                                   @ApiParam("排序字段") String order,
                                                   @ApiParam("页码") Integer pageNum, @ApiParam("每页显示数") Integer pageSize, HttpServletResponse response) {
        AiGenerateQuery aiGenerateQuery = new AiGenerateQuery();
        aiGenerateQuery.setModuleType(moduleType);
        aiGenerateQuery.setStatus(status);
        aiGenerateQuery.setRelationContent(relationContent);
        aiGenerateQuery.setCourseDetails(courseDetails);
        aiGenerateQuery.setSort(sort);
        aiGenerateQuery.setOrder(order);
        aiGenerateQuery.setPageNum(pageNum);
        aiGenerateQuery.setPageSize(pageSize);
        return HSSJsonReulst.ok(aiGenerateService.list(aiGenerateQuery,response));
    }

    @ApiOperation("ai重新生成")
    @PostMapping("/execute")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "助记主键id",required = true),
            @ApiImplicitParam(name = "moduleType",value = "模块类型：sentence、write、article",required = true)
    })
    public HSSJsonReulst execute(Integer id,String moduleType,HttpServletResponse response) {
        aiGenerateService.execute(id, moduleType,response);
        return HSSJsonReulst.ok();
    }
    @ApiOperation("ai重新生成词汇")
    @PostMapping("/executeWord")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "助记主键id",required = true)
    })
    public HSSJsonReulst<String> executeWord(Integer id,HttpServletResponse response) {
        return HSSJsonReulst.ok(aiGenerateService.executeWord(id,response));
    }

    /**
     * 只需要传id、type、反馈内容
     * @param aiMnemonicParent
     * @return
     */
    @ApiOperation("修改")
    @PostMapping("/update")
    public HSSJsonReulst update(@RequestBody AiMnemonicParent aiMnemonicParent) {
        return HSSJsonReulst.ok(aiGenerateService.update(aiMnemonicParent));
    }

    @ApiOperation("忽略处理")
    @PostMapping("/handleIgnore")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "助记主键id",required = true),
            @ApiImplicitParam(name = "moduleType",value = "模块类型：resourceWord、word、sentence、write、article",required = true)
    })
    public HSSJsonReulst handleIgnore(Integer id,String moduleType) {
        return HSSJsonReulst.ok(aiGenerateService.handleIgnore(id,moduleType));
    }

    @ApiOperation("加载助记反馈缓存")
    @PostMapping("/reload")
    public HSSJsonReulst reload() {
        jobService.aiMnemonicJob();
        return HSSJsonReulst.ok();
    }

}
