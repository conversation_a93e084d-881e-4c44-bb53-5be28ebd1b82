package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.RedBookUnit;
import com.woxue.common.model.redBook.RedBookUnitContentWrite;
import com.woxue.common.model.redBook.ResourceUnitWriteSentence;
import com.woxue.common.model.redBook.WriteUnitTitleBean;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.write.EditSampleDto;
import com.woxue.resourcemanage.entity.dto.write.EditWordDto;
import com.woxue.resourcemanage.entity.dto.write.SaveUnitTopicDto;
import com.woxue.resourcemanage.entity.write.TopicRelatedUnitBean;
import com.woxue.resourcemanage.entity.write.WriteNewUnitBean;
import com.woxue.resourcemanage.service.IResourceWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/write")
@Api(tags = "同步写作相关")
public class ResourceWriteController {
    @Autowired
    IResourceWriteService resourceWriteService;
    @Autowired
    private HttpSession session;

    @PostMapping("/saveWriteMethodCard")
    @ResponseBody
    @ApiOperation(value = "保存写作方法卡片")
    public Map<String, Object> saveWriteMethodCard(@RequestBody WriteNewUnitBean writeNewUnitBean) {
        // 文件保存路径
        String contextPath = session.getServletContext().getRealPath("/");
        if (contextPath.endsWith(File.separator)) {
            contextPath = contextPath.substring(0, contextPath.length() - 1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator) + 1);
        Map<String, Object> map = resourceWriteService.saveWriteMethodCard(contextPath, writeNewUnitBean);
        return map;
    }

    @RequestMapping("/textUploadByPaste")
    @ResponseBody
    @ApiOperation(value = "上传")
    public Map<String, Object> textUploadByPaste(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        return resourceWriteService.textUploadByPaste(unitId, upload, request);
    }

    @PostMapping("/addTopic")
    @ResponseBody
    @ApiOperation(value = "保存主题")
    public HSSJsonReulst<Integer> addTopic(@ApiParam(value = "主题内容", name = "topicName") String topicName, @ApiParam(value = "类型 1：阅读 2：写作", name = "type") String type) {
        return resourceWriteService.addTopic(topicName, type);
    }

    @PostMapping("/addSentenceTrain")
    @ResponseBody
    @ApiOperation(value = "添加句型训练")
    public HSSJsonReulst<Integer> addSentenceTrain(@RequestBody ResourceUnitWriteSentence resourceUnitWriteSentence) {
        return resourceWriteService.addSentenceTrain(resourceUnitWriteSentence);
    }

    @GetMapping("/getSentenceTrainListByUnitId/{unitId}")
    @ResponseBody
    @ApiOperation(value = "获取句型训练")
    public HSSJsonReulst<List<ResourceUnitWriteSentence>> getSentenceTrainListByUnitId(@PathVariable("unitId") Integer unitId) {
        return resourceWriteService.getSentenceTrainListByUnitId(unitId);
    }

    @PostMapping("/editSentenceTrain")
    @ResponseBody
    @ApiOperation(value = "编辑句型训练")
    public HSSJsonReulst<Boolean> editSentenceTrain(@RequestBody ResourceUnitWriteSentence resourceUnitWriteSentence) {
        return resourceWriteService.editSentenceTrain(resourceUnitWriteSentence);
    }

    @GetMapping("/getTopicListByType/{type}")
    @ResponseBody
    @ApiOperation(value = "获取主题列表")
    public HSSJsonReulst<List<ResourceTopic>> getTopicListByType(@PathVariable("type") String type) {
        return resourceWriteService.getTopicListByType(type);
    }

    @GetMapping("/getWriteMethodByUnitId/{unitId}")
    @ResponseBody
    @ApiOperation(value = "获取写作方法")
    public HSSJsonReulst<List<WriteUnitTitleBean>> getWriteMethodByUnitId(@PathVariable("unitId") Integer unitId) {
        return resourceWriteService.getWriteMethodByUnitId(unitId);
    }

    @GetMapping("/getSampleByUnitId/{unitId}")
    @ResponseBody
    @ApiOperation(value = "获取范文")
    public HSSJsonReulst<RedBookUnitContentWrite> getSampleByUnitId(@PathVariable("unitId") Integer unitId) {
        return resourceWriteService.getSampleByUnitId(unitId);
    }

    @PostMapping("/editUnitWords")
    @ResponseBody
    @ApiOperation(value = "编辑关键词汇")
    public HSSJsonReulst<Boolean> editUnitWords(@RequestBody EditWordDto editWordDto) {
        return resourceWriteService.editUnitWords(editWordDto);
    }

    @PostMapping("/editSample")
    @ResponseBody
    @ApiOperation(value = "编辑范文数据")
    public HSSJsonReulst<Boolean> editSample(@RequestBody EditSampleDto editSampleDto) {
        return resourceWriteService.editSample(editSampleDto);
    }

    @GetMapping("/getUnitListByCourseId/{courseId}")
    @ResponseBody
    @ApiOperation(value = "获取单元列表")
    public HSSJsonReulst<List<RedBookUnit>> getUnitListByCourseId(@PathVariable("courseId") Integer courseId) {
        return resourceWriteService.getUnitListByCourseId(courseId);
    }

    @GetMapping("/getTopicRelatedUnitList/{topicId}")
    @ResponseBody
    @ApiOperation(value = "获取主题关联单元列表")
    public HSSJsonReulst<List<TopicRelatedUnitBean>> getTopicRelatedUnitList(@PathVariable("topicId") Integer topicId) {
        return resourceWriteService.getTopicRelatedUnitList(topicId);
    }

    @PostMapping("/saveUnitTopic")
    @ResponseBody
    @ApiOperation(value = "保存单元主题")
    public HSSJsonReulst<Boolean> saveUnitTopic(@RequestBody SaveUnitTopicDto saveUnitTopicDto) {
        return resourceWriteService.saveUnitTopic(saveUnitTopicDto);
    }

    @PostMapping("/importSentence")
    @ResponseBody
    @ApiOperation(value = "导入句型训练")
    public HSSJsonReulst<Boolean> importSentence(@RequestParam("file") MultipartFile file,
                                                 @RequestParam("courseId") Integer courseId) throws IOException {
        return resourceWriteService.importSentence(file, courseId);
    }

    @PostMapping("/importSample")
    @ResponseBody
    @ApiOperation(value = "导入范文")
    public HSSJsonReulst<Boolean> importSample(@RequestParam("file") MultipartFile file,
                                               @RequestParam("courseId") Integer courseId) throws IOException {
        return resourceWriteService.importSample(file, courseId);
    }

}
