package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.RedBookConstant;
import com.woxue.common.model.redBook.RedBookContentTypeEnum;
import com.woxue.common.util.FormatUtils;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.OSSManager;
import com.woxue.common.util.UtilControl;
import com.woxue.common.util.wordUse.WordUseBean;
import com.woxue.resourcemanage.entity.WordEntity;
import com.woxue.resourcemanage.entity.WordUseDto;
import com.woxue.resourcemanage.entity.WordUseUpdate;
import com.woxue.resourcemanage.entity.vo.MatchResultVO;
import com.woxue.resourcemanage.service.IWordService;
import com.woxue.resourcemanage.util.CourseManager;
import com.woxue.resourceservice.dao.IRedBookCourseDao;
import com.woxue.resourceservice.util.RedBookCourseManager;
import com.woxue.resourceservice.util.SoundMarkManager;
import com.woxue.resourceservice.util.WordUseManager;
import io.swagger.annotations.*;
import jxl.Sheet;
import jxl.Workbook;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 词汇、句子校对相关
 * <AUTHOR>
 * @date 2022/5/24 16:40
 */
@Api(tags = "词汇、句子校对相关")
@RestController
@RequestMapping("/wordEdit")
public class WordEditController {
    @Autowired
    IWordService wordService;

    @Autowired
    IRedBookCourseDao redBookCourseDao;


    /**
     * 获取课程树
     * @return
     * @throws Exception
     */
    @PostMapping("/getCourseTree")
    @ResponseBody
    @ApiOperation("获取课程树")
    public Map<String, Object> getDCWCourseTree() throws Exception {
        Map<String, Object> map=new HashMap<>();
        if(CourseManager.getInstanse().isNull()){
            CourseManager.getInstanse().treeGenerate(redBookCourseDao);
        }
        map.put("level1List", CourseManager.getInstanse().getLevel1List());
        map.put("level2List", CourseManager.getInstanse().getLevel2List());
        map.put("level3List", CourseManager.getInstanse().getLevel3List());
        return map;
    }
    /**
     * 查询单词列表
     * @return
     * @throws Exception
     */
    @ApiOperation("获取单词列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "operationType",value = "0：所有单词，1：不发音单词，2：不发音例句，3：无音标单词，4：无例句单词"),
            @ApiImplicitParam(name = "courseId",value = "课程ID"),
            @ApiImplicitParam(name = "wordId",value = "单词ID"),
            @ApiImplicitParam(name = "spelling",value = "拼写"),
            @ApiImplicitParam(name = "pageNum",value = "页码，从1开始",required = true),
            @ApiImplicitParam(name = "pageSize",value = "每页显示多少条",required = true)
    })
    @PostMapping("/showWordList")
    public Map<String, Object> showWordList(Integer operationType, Integer courseId, Integer wordId, String spelling, Integer pageNum, Integer pageSize, String orderBy, RedBookContentTypeEnum type)throws Exception{
        Map<String, Object> map=new HashMap<>();
        if ((courseId == null || courseId <= 0) && (spelling == null || spelling.isEmpty()) && wordId == null){
            map.put("count",0);
            return map;
        }
        if(operationType==null){
            operationType = 0;
        }
        if (type==null ){
            type = RedBookContentTypeEnum.WORD;
        }
        Integer count = 0;
        List<WordEntity> wordList = new ArrayList<>();
        //0所有单词
        if(operationType==0){
            count = wordService.queryWordsCount(courseId, wordId, spelling,type);
            wordList = wordService.queryWords(courseId, wordId, spelling, (pageNum-1)*pageSize, pageSize, orderBy,type);
        }else if(operationType==1){//1：不发音单词
            List<WordEntity> list = wordService.queryWords(courseId, wordId, spelling, null, null, orderBy,type);
            for (WordEntity wordTemp : list) {
                String soundFile = UtilControl.getWordSoundUrlNoDomainName(wordTemp.getSpelling(), wordTemp.getSoundFile());
                if(!this.hasFile(soundFile,wordTemp.getSpelling())){
                    wordList.add(wordTemp);
                }
            }
            if(wordList.size()==0){
                count=0;
            }else{
                count = wordList.size();
                if(pageNum!=null&&pageSize!=null){
                    if(pageNum==1){
                        pageNum=(pageNum-1)*pageSize;
                    } else{
                        pageNum=pageNum*pageSize-pageSize;
                        pageSize=pageSize+pageNum;
                    }
                }
                if(pageSize>wordList.size()){
                    pageSize=wordList.size();
                }
                wordList.subList(pageNum,pageSize);
            }
            //2：不发音例句
        }else if(operationType==2){
            List<WordEntity> list = wordService.queryWords(courseId, wordId, spelling, null,null, orderBy,type);
            for (WordEntity wordTemp : list) {
                String example = wordTemp.getExample_en_US();
                if(example!=null && example.length()>0) {
                    String exampleSoundFile = UtilControl.getSentenceSoundUrlNoDomainName(wordTemp.getExample_en_US());
                    if(!this.hasFile(exampleSoundFile, wordTemp.getExample_en_US())){
                        wordList.add(wordTemp);
                    }
                }
            }
            if(wordList.size()==0){
                count=0;
            }else{
                count = wordList.size();
                if(pageNum!=null&&pageSize!=null){
                    if(pageNum==1){
                        pageNum=(pageNum-1)*pageSize;
                    } else{
                        pageNum=pageNum*pageSize-pageSize;
                        pageSize=pageSize+pageNum;
                    }
                }
                if(pageSize>wordList.size()){
                    pageSize=wordList.size();
                }
                wordList.subList(pageNum,pageSize);
            }
        }else if(operationType==3){//3：无音标单词
            count = wordService.queryWordNoSyllableCount(courseId);
            wordList = wordService.queryWordNoSyllable(courseId,(pageNum-1)*pageSize, pageSize);
        }else if(operationType==4) {//4：无例句单词
            count = wordService.queryWordNoExampleCount(courseId);
            wordList = wordService.queryWordNoExample(courseId,(pageNum-1)*pageSize, pageSize);
        }

        if(wordList.size()>0){
            Integer word_id;
            Map<String,String> wordMnemonics;//单词助记信息
            String soundServer = RedBookConstant.MEDIA_DOMAIN_NAME;
            for(int i=0;i<wordList.size();i++){
                WordEntity wordTemp = wordList.get(i);
                String soundFile = UtilControl.getWordSoundUrlNoDomainName(wordTemp.getSpelling(), wordTemp.getSoundFile());
                wordTemp.setHasSpellingSound(this.hasFile(soundFile,wordTemp.getSpelling()));
                wordTemp.setSoundFile(soundServer+soundFile);

                String example = wordTemp.getExample_en_US();
                if(example!=null && example.length()>0){
                    String exampleSoundFile = UtilControl.getSentenceSoundUrlNoDomainName(example);
                    wordTemp.setHasExampleSound(this.hasFile(exampleSoundFile,example));
                    wordTemp.setExampleSoundFile(soundServer+exampleSoundFile);
                }
                wordTemp.setSyllable(FormatUtils.formatSyllAble(wordTemp.getSyllable()));
                word_id = wordTemp.getWordId();
                wordMnemonics = wordService.getWordMnemonics(wordTemp.getSpelling());
                if(wordMnemonics!=null&&wordMnemonics.size()>0){
                    if(wordMnemonics.get("no_mnemonics_word_ids").contains("|"+word_id+"|")){
                    }else if(wordMnemonics.get("other_word_ids2").contains("|"+word_id+"|")){
                        wordTemp.setMnemonics(wordMnemonics.get("other_mnemonics2"));
                    }else if(wordMnemonics.get("other_word_ids1").contains("|"+word_id+"|")){
                        wordTemp.setMnemonics(wordMnemonics.get("other_mnemonics1"));
                    }else{
                        wordTemp.setMnemonics(wordMnemonics.get("mnemonics"));
                    }
                    wordTemp.setOther_mnemonics1((wordMnemonics.get("other_mnemonics1")==null?"":wordMnemonics.get("other_mnemonics1")));
                    wordTemp.setOther_mnemonics2((wordMnemonics.get("other_mnemonics2")==null?"":wordMnemonics.get("other_mnemonics2")));
                }
                wordList.set(i, wordTemp);
            }
        }

        String[] aReplace = { "θ", "ɑ", "ʌ", "ə", "ε", "æ", "ɔ", "ʧ", "ʃ",
                "ð", "ŋ", "ʤ", "ʒ", "ֽ" };
        map.put("syllableList", aReplace);
        map.put("wordList", wordList);
        map.put("count",count);
        return map;
    }


    private Boolean hasFile(String soundFile,String spelling){
        //是否有音频
        boolean flag = false;//TODO 记录到redis中，每次判断性能不好
        try {
            //doesObjectExist判断文件是否存在
            flag = OSSManager.doesObjectExist(OSSManager.SOUND_SOURCE_BUCKETNAME,soundFile.substring(1));
            if (flag){
                //TODO 记录到redis中，每次判断性能不好
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        /*}else {
            return true;
        }*/
        return flag;
    }



    @ApiOperation("得到详细单词信息")
    @PostMapping("/getWordDetail")
    public HSSJsonReulst<List<WordEntity>> getWordDetail(String wordIds){
        List<WordEntity> list=new ArrayList<>();
        wordIds=wordIds.substring(1);
        String[] wordIdArr=wordIds.split("\\|");
        for (String wordId : wordIdArr) {
            list.addAll(wordService.queryWords(null,Integer.valueOf(wordId),null,null,null,null,null));
        }
        return HSSJsonReulst.ok(list);
    }

    @ApiOperation("修改单词信息")
    @PostMapping("/updateWordDetail")
    public String updateWordDetail(HttpServletRequest request, Integer wordId, String spelling, String syllable, String meaning_en_US , String meaning_zh_CN, String example_en_US, String example_zh_CN)throws Exception{
        Map map=new HashMap();
        if(syllable!=null){
            syllable = FormatUtils.formatSyllAble(syllable);
        }
        if(spelling.indexOf("\t")>-1){
            spelling=spelling.replaceAll("\t"," ");
        }
        map.put("wordId", wordId);
        map.put("spelling", spelling);
        map.put("syllable", syllable);
        map.put("meaning_en_US", meaning_en_US);
        map.put("meaning_zh_CN", meaning_zh_CN);
        map.put("example_en_US", example_en_US);
        map.put("example_zh_CN", example_zh_CN);
        wordService.updateWordDetail(wordId, spelling, syllable, meaning_en_US, meaning_zh_CN, example_en_US, example_zh_CN);
        RedBookCourseManager.deleteWord(wordId);
        return "success";
    }
    @RequestMapping("/updateExample")
    @ResponseBody
    @ApiOperation("updateExample")
    public String updateExample(Integer wordId, String fromWordId)throws Exception{
        WordEntity wordTemp = wordService.queryWord(wordId);
        WordEntity wordFromTemp = wordService.queryWord(Integer.parseInt(fromWordId));
        //转码 UTF-8  UTF-8
        HashMap map = new HashMap();
        if(wordTemp!=null) {
            String spelling = wordTemp.getSpelling();
            String syllable = "";
            if (wordTemp.getSyllable() != null) {
                syllable = wordTemp.getSyllable();
                syllable = FormatUtils.formatSyllAble(syllable);
            }
            String meaning_en_US = wordTemp.getMeaning_en_US();
            String meaning_zh_CN = "";
            if (wordTemp.getMeaning_zh_CN() != null) {
                meaning_zh_CN = new String(wordTemp.getMeaning_zh_CN().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            }
            //更换例句
            String example_en_US = wordFromTemp.getExample_en_US();
            String example_zh_CN = "";
            if (wordFromTemp.getExample_zh_CN() != null) {
                example_zh_CN = new String(wordFromTemp.getExample_zh_CN().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            }
            //封装map
            map.put("wordId", wordId);
            map.put("spelling", spelling);
            map.put("syllable", syllable);
            map.put("meaning_en_US", meaning_en_US);
            map.put("meaning_zh_CN", meaning_zh_CN);
            map.put("example_en_US", example_en_US);
            map.put("example_zh_CN", example_zh_CN);
            wordService.updateWordDetail(wordId, spelling, syllable, meaning_en_US, meaning_zh_CN, example_en_US, example_zh_CN);
            RedBookCourseManager.deleteWord(wordId);
        }
        return "success";
    }

    @ApiOperation("查询更多例句列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling",value = "拼写",required = true),
            @ApiImplicitParam(name = "pageNum",value = "页码，从1开始",required = true),
            @ApiImplicitParam(name = "pageSize",value = "每页显示多少条",required = true)
    })
    @PostMapping("/queryExampleBySpelling")
    public Map<String, Object> queryExampleBySpelling(@RequestParam String spelling,
                                                      @RequestParam Integer pageNum,
                                                      @RequestParam Integer pageSize,
                                                      @RequestParam String searchFrom) throws IOException {
        Map<String, Object>map=new HashMap<>();
        int count = wordService.queryExampleCount( spelling,null,searchFrom);
        List<WordEntity> wordList = wordService.queryExamplesList( spelling, null,(pageNum-1)*pageSize, pageSize,searchFrom);
        String soundServer = RedBookConstant.MEDIA_DOMAIN_NAME;
        for(int i=0;i<wordList.size();i++){
            WordEntity wordTemp = wordList.get(i);
            String example = wordTemp.getExample_en_US();
            if(example!=null && example.length()>0){
                String exampleSoundFile = UtilControl.getSentenceSoundUrlNoDomainName(example);
                wordTemp.setHasExampleSound(this.hasFile(exampleSoundFile,example));
                wordTemp.setExampleSoundFile(soundServer+exampleSoundFile);
            }
            wordList.set(i, wordTemp);
        }
        map.put("count", count);
        map.put("exampleList", wordList);
        return map;
    }

    @ApiOperation("获取句子列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId",value = "课程ID"),
            @ApiImplicitParam(name = "wordId",value = "句子ID"),
            @ApiImplicitParam(name = "sentence",value = "句子英文拼写"),
            @ApiImplicitParam(name = "divide",value = "是否划分成分 0不限制 1已划分 2未划分"),
            @ApiImplicitParam(name = "pageNum",value = "页码，从1开始",required = true),
            @ApiImplicitParam(name = "pageSize",value = "每页显示多少条",required = true),
            @ApiImplicitParam(name = "operationType",value = "0：所有单词，2：不发音例句")
    })
    @PostMapping("/showSentenceList")
    public Map<String, Object> showSentenceList(Integer courseId, Integer wordId, String sentence, Integer divide, Integer pageNum, Integer pageSize, String orderBy,Integer operationType)throws Exception{
        Map<String, Object> map=new HashMap<>();
        operationType=operationType==null?0:operationType;
        if ((courseId==null || courseId<=0)
                && (sentence == null || sentence.equals(""))
                && (wordId ==null || wordId.equals(""))){
            map.put("count",0);
            return map;
        }
        if(divide==null){
            divide=0;
        }
        if (2==operationType){
            List<WordEntity> list = wordService.querySentences(courseId, wordId, sentence, divide,null, null, null);
            List<WordEntity> wordList = new ArrayList<>();
            int count=0;
            for (WordEntity wordTemp : list) {
                String example = wordTemp.getExample_en_US();
                if(example!=null && example.length()>0) {
                    String exampleSoundFile = UtilControl.getSentenceSoundUrlNoDomainName(wordTemp.getExample_en_US());
                    if(!this.hasFile(exampleSoundFile, wordTemp.getExample_en_US())){
                        wordList.add(wordTemp);
                    }
                }
            }
            if(wordList.size()==0){
                count=0;
            }else{
                count = wordList.size();
                if(pageNum!=null&&pageSize!=null){
                    if(pageNum==1){
                        pageNum=(pageNum-1)*pageSize;
                    } else{
                        pageNum=pageNum*pageSize-pageSize;
                        pageSize=pageSize+pageNum;
                    }
                }
                if(pageSize>wordList.size()){
                    pageSize=wordList.size();
                }
                wordList=wordList.subList(pageNum,pageSize);
            }
            map.put("wordList", wordList);
            map.put("count",count);
        }else {
            Integer count = wordService.querySentencesCount(courseId, wordId, sentence,divide);
            List<WordEntity> wordList = wordService.querySentences(courseId, wordId, sentence, divide,(pageNum-1)*pageSize, pageSize, orderBy);
            String soundServer = RedBookConstant.MEDIA_DOMAIN_NAME;
            for(int i=0;i<wordList.size();i++){
                WordEntity wordTemp = wordList.get(i);
                String example = wordTemp.getExample_en_US();
                if(example!=null && example.length()>0){
                    String exampleSoundFile = UtilControl.getSentenceSoundUrlNoDomainName(example);
                    wordTemp.setHasExampleSound(this.hasFile(exampleSoundFile,example));
                    wordTemp.setExampleSoundFile(soundServer+exampleSoundFile);
                }
                wordList.set(i, wordTemp);
            }
            map.put("wordList", wordList);
            map.put("count",count);
        }

        String[] aReplace = { "θ", "ɑ", "ʌ", "ə", "ε", "æ", "ɔ", "ʧ", "ʃ",
                "ð", "ŋ", "ʤ", "ʒ", "ֽ" };
        map.put("syllableList", aReplace);
        return map;
    }

    @ApiOperation("修改句子信息")
    @PostMapping("/updateSentenceDetails")
    public String updateSentenceDetails(WordEntity sentence)throws Exception{
        wordService.updateSentenceDetail(sentence);
        RedBookCourseManager.deleteWord(sentence.getWordId());
        return "success";
    }

    @ApiOperation("匹配句子成分")
    @PostMapping("/matchSentenceElement")
    public HSSJsonReulst<MatchResultVO> matchSentenceElement(@ApiParam("课程ID") @RequestParam Integer courseId)throws Exception{
        return HSSJsonReulst.ok(wordService.matchSentenceElement(courseId));
    }

    @ApiOperation("匹配例句")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId",value = "需要匹配例句的课程ID",required = true),
            @ApiImplicitParam(name = "sourceCourseIdList",value = "例句来源课程id列表",required = true)
    })
    @PostMapping("/matchExample")
    public Map<String, Object> matchExample(@RequestParam Integer courseId,@RequestParam List<Integer> sourceCourseIdList) {
        int successCount=0;
        List<WordEntity> exampleList;
        List<WordEntity> wordEntityList = wordService.queryWordNoExample(courseId, null, null);
        for (WordEntity wordEntity : wordEntityList) {
            for (Integer sourceCourseId : sourceCourseIdList) {
                exampleList = wordService.queryExamplesList(wordEntity.getSpelling(),sourceCourseId,null,null,"spelling");
                if (exampleList==null)exampleList=wordService.queryExamplesList(wordEntity.getSpelling(),sourceCourseId,null,null,"example");
                if(exampleList!=null&&exampleList.size()>0){
                    wordEntity.setExample_en_US(exampleList.get(0).getExample_en_US());
                    wordEntity.setExample_zh_CN(exampleList.get(0).getExample_zh_CN());
                    break;
                }
            }
            if(wordEntity.getExample_en_US()!=null&&wordEntity.getExample_en_US().length()>0){
                successCount++;
                wordService.updateWordDetail(wordEntity.getWordId(),wordEntity.getSpelling(),wordEntity.getSyllable(),
                        wordEntity.getMeaning_en_US(),wordEntity.getMeaning_zh_CN(),wordEntity.getExample_en_US(),wordEntity.getExample_zh_CN());
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("totalCount", wordEntityList.size());
        map.put("successCount", successCount);
        map.put("surplusCount", wordEntityList.size()-successCount);
        return map;
    }


    @ApiOperation("导入助记内容")
    @PostMapping("/importMnemonics")
    public Object importMnemonics(HttpServletRequest request, @RequestParam("files") MultipartFile[] files){
        List wordIdList = null;
        List programWordList = null;
        List unitIdList = null;
        String msg="";
        InputStream fileStream = null;
        for (MultipartFile mf : files) {
            if(!mf.isEmpty()){
                try {
                    fileStream=mf.getInputStream();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //获得文件
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");

//		List<FileItem> items =null;
//		try {
//			items = upload.parseRequest(request);
//		} catch (FileUploadException e1) {
//			e1.printStackTrace();
//		}
//		Iterator<FileItem> it = items.iterator();
//		while(it.hasNext()){
//			FileItem fi = (FileItem) it.next();
//			if(!fi.isFormField()){
//				try {
//					fileStream = fi.getInputStream();
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}
//		}

        InputStream is = fileStream;
        try{
            Workbook wb = Workbook.getWorkbook(is);
            int sheets = wb.getNumberOfSheets();
            //用于存放wordId,异常时回滚
            wordIdList = new ArrayList();
            //用于存放program_word,异常时回滚
            programWordList = new ArrayList();
            //用于存放单元名称list
            Set unitList = new LinkedHashSet();
            //用于存放单元id list,异常时回滚
            unitIdList = new ArrayList();
            if(sheets==1){
                List<String> existSpellingList = new ArrayList<String>();
                int totalNum = 0;
                Sheet sheet = wb.getSheet(0);
                int cols = sheet.getColumns();
                int rows = sheet.getRows();
                //检查行数、列数,工作表名
                if(cols!=4){
                    msg = "资源导入，工作表列数不符合要求!";
                }else if(rows<=1){
                    msg = "资源导入，工作表没有数据!";
                }else{
                    int order = 0;
                    Map<String, String> wordMnemonics;
                    for(int i=1;i<rows;i++){
                        String spelling = sheet.getCell(0, i).getContents().trim();
                        String mnemonics = sheet.getCell(1, i).getContents().trim();
                        String otherMnemonics1 = sheet.getCell(2, i).getContents().trim();
                        String otherMnemonics2 = sheet.getCell(3, i).getContents().trim();

                        //如果拼写为空，continue
                        if(spelling==null&&spelling.equals("")){
                            continue;
                        }
                        totalNum++;
                        spelling = new String(spelling.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                        mnemonics = new String(mnemonics.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                        otherMnemonics1 = new String(otherMnemonics1.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                        otherMnemonics2 = new String(otherMnemonics2.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                        wordMnemonics = wordService.getWordMnemonics(spelling);
                        try {
                            if(wordMnemonics==null||wordMnemonics.size()<=0||wordMnemonics.get("spelling")==null){
                                wordMnemonics = new HashMap<String, String>();
                                wordMnemonics.put("spelling", spelling.trim());
                                wordMnemonics.put("mnemonics", mnemonics.trim());
                                wordMnemonics.put("other_mnemonics1", otherMnemonics1.trim());
                                wordMnemonics.put("other_word_ids1", "");
                                wordMnemonics.put("other_mnemonics2", otherMnemonics2.trim());
                                wordMnemonics.put("other_word_ids2", "");
                                wordMnemonics.put("no_mnemonics_word_ids", "");
                                wordService.insertWordMnemonics(wordMnemonics);
                            }else{//已经存在
                                existSpellingList.add(spelling.trim());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    msg = "导入成功，共成功导入"+(totalNum-existSpellingList.size())+"条数据，重复数据"+existSpellingList.size()+"条（重复内容不会导入）。";
                }
            }
        }catch(Exception ex){
            msg="资源导入异常";
            ex.printStackTrace();
        }

        try {
            if(is!=null){
                is.close();
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return HSSJsonReulst.ok(msg);
    }


    @ApiOperation("保存助记内容")
    @PostMapping("/saveWordMnemonics")
    public Object saveWordMnemonics(HttpServletRequest request, String spelling, String mnemonics, String otherMnemonics1, String otherMnemonics2, String mnemonicsSelect, Integer wordId, String programName)throws Exception{
        Map<String, String> wordMnemonics = wordService.getWordMnemonics(spelling);
        boolean result = true;
        mnemonics = URLDecoder.decode(mnemonics,"UTF-8");
        otherMnemonics1 = URLDecoder.decode(otherMnemonics1,"UTF-8");
        otherMnemonics2 = URLDecoder.decode(otherMnemonics2,"UTF-8");
        try {
            if(wordMnemonics==null||wordMnemonics.size()<=0||wordMnemonics.get("spelling")==null){
                wordMnemonics = new HashMap<String, String>();
                wordMnemonics.put("spelling", spelling);
                wordMnemonics.put("mnemonics", mnemonics);
                wordMnemonics.put("other_mnemonics1", otherMnemonics1);
                wordMnemonics.put("other_word_ids1", "");
                wordMnemonics.put("other_mnemonics2", otherMnemonics2);
                wordMnemonics.put("other_word_ids2", "");
                wordMnemonics.put("no_mnemonics_word_ids", "");
                //String mnemonicsSelect;//选择显示的助记  default  other1  other2  off
                if(mnemonicsSelect.equals("other1")){
                    wordMnemonics.put("other_word_ids1", "|"+wordId+"|");
                }else if(mnemonicsSelect.equals("other2")){
                    wordMnemonics.put("other_word_ids2", "|"+wordId+"|");
                }else if(mnemonicsSelect.equals("off")){
                    wordMnemonics.put("no_mnemonics_word_ids", "|"+wordId+"|");
                }
                wordService.insertWordMnemonics(wordMnemonics);
            }else{
                wordMnemonics.put("mnemonics", mnemonics);
                wordMnemonics.put("other_mnemonics1", otherMnemonics1);
                wordMnemonics.put("other_mnemonics2", otherMnemonics2);
                wordMnemonics.put("other_word_ids1", wordMnemonics.get("other_word_ids1").replace("|"+wordId+"|", ""));
                wordMnemonics.put("other_word_ids2", wordMnemonics.get("other_word_ids2").replace("|"+wordId+"|", ""));
                wordMnemonics.put("no_mnemonics_word_ids", wordMnemonics.get("no_mnemonics_word_ids").replace("|"+wordId+"|", ""));
                //String mnemonicsSelect;//选择显示的助记  default  other1  other2  off
                if(mnemonicsSelect.equals("other1")){
                    wordMnemonics.put("other_word_ids1", wordMnemonics.get("other_word_ids1")+"|"+wordId+"|");
                }else if(mnemonicsSelect.equals("other2")){
                    wordMnemonics.put("other_word_ids2", wordMnemonics.get("other_word_ids2")+"|"+wordId+"|");
                }else if(mnemonicsSelect.equals("off")){
                    wordMnemonics.put("no_mnemonics_word_ids", wordMnemonics.get("no_mnemonics_word_ids")+"|"+wordId+"|");
                }
                wordService.updateWordMnemonics(wordMnemonics);
            }
            //删除redis中助记信息
            RedBookCourseManager.deleteWordMnemonics(spelling);
        } catch (Exception e) {
            result = false;
            e.printStackTrace();
        }

        Map<String,Object> jsonData = new HashMap<>();
        jsonData.put("result", result);
        return jsonData;
    }



    @ApiOperation("查询助记列表")
    @PostMapping("/showMnemonicsList")
    public Object showMnemonicsList(String spelling, Integer pageNum, Integer pageSize)throws Exception{
        Map<String, Object> map=new HashMap<>();
        List<Map<String, String>> mnemonicsList = new ArrayList<>();
        int count = 0;
        //根据单词拼写查询
        if(spelling!=null&&!spelling.equals("")){
            Map<String, String> wordMnemonics = wordService.getWordMnemonics(spelling);
            if(wordMnemonics==null){
                count = 0;
            }else{
                count = 1;
                mnemonicsList.add(wordMnemonics);
            }
        }else{
            count = wordService.getWordMnemonicsCount();
            mnemonicsList = wordService.getWordMnemonicsList((pageNum-1)*pageSize, pageSize);
        }

        int length=0;
        int index = 0;
        for(Map<String, String> wordMnemonics:mnemonicsList){
            length=0;
            if(wordMnemonics.get("other_word_ids1").length()>0){
                while(true){
                    index = wordMnemonics.get("other_word_ids1").indexOf("|", index)+1;
                    if(index!=0){
                        length++;
                    }else{
                        break;
                    }
                }
            }
            wordMnemonics.put("other1_length",""+(length/2));
            length=0;
            index=0;
            if(wordMnemonics.get("other_word_ids2").length()>0){
                while(true){
                    index = wordMnemonics.get("other_word_ids2").indexOf("|", index)+1;
                    if(index!=0){
                        length++;
                    }else{
                        break;
                    }
                }
            }
            wordMnemonics.put("other2_length",""+(length/2));
            length=0;
            index=0;
            if(wordMnemonics.get("no_mnemonics_word_ids").length()>0){
                while(true){
                    index = wordMnemonics.get("no_mnemonics_word_ids").indexOf("|", index)+1;
                    if(index!=0){
                        length++;
                    }else{
                        break;
                    }
                }
            }
            wordMnemonics.put("off_length",""+(length/2));
        }
        map.put("mnemonicsList", mnemonicsList);
        map.put("count", count);
        return HSSJsonReulst.ok(map);
    }


    @ApiOperation("编辑助记")
    @PostMapping("/editWordMnemonics")
    public Object editWordMnemonics(String mnemonics, String otherMnemonics1, String otherMnemonics2, String spelling, String otherWordIds1, String otherWordIds2, String noMnemonicsWordIds)throws Exception{
        boolean result = true;
        try {
            Map<String, String> wordMnemonics = wordService.getWordMnemonics(spelling);
            if(wordMnemonics==null||wordMnemonics.size()<=0||wordMnemonics.get("spelling")==null){
                result = false;
            }else{
                wordMnemonics.put("mnemonics", mnemonics);
                wordMnemonics.put("other_mnemonics1", otherMnemonics1);
                wordMnemonics.put("other_mnemonics2", otherMnemonics2);
                wordMnemonics.put("other_word_ids1", checkWordIds(otherWordIds1));
                wordMnemonics.put("other_word_ids2", checkWordIds(otherWordIds2));
                wordMnemonics.put("no_mnemonics_word_ids", checkWordIds(noMnemonicsWordIds));
                wordService.updateWordMnemonics(wordMnemonics);
                result = true;
                //删除redis中助记信息
                RedBookCourseManager.deleteWordMnemonics(spelling);
            }
        } catch (Exception e) {
            result = false;
            e.printStackTrace();
        }
        return HSSJsonReulst.ok(result);
    }
    private String checkWordIds(String wordIdStrs){
        String result = "";
        if(wordIdStrs.length()>0){
            for(String wid:wordIdStrs.split("\\|")){
                if(wid.trim().length()>0){
                    if(wordService.queryWord(Integer.parseInt(wid.trim()))!=null){
                        result+="|"+wid.trim()+"|";
                    }
                }
            }
        }
        return result;
    }


    @ApiOperation("分音节管理 获取分音节单词列表")
    @PostMapping("/getSoundMarkList")
    public HSSJsonReulst getSoundMarkList(String spelling,Integer pageNum,Integer pageSize){
        List<Map<String,Object>> soundMarkList = wordService.getSoundMarkList(spelling,pageNum,pageSize);
        Integer count = wordService.getSoundMarkCount();
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("soundMarkList",soundMarkList);
        resultMap.put("count",count);
        return HSSJsonReulst.ok(resultMap);
    }

    @ApiOperation("用词管理 获取列表")
    @PostMapping("/getWordUseList")
    public HSSJsonReulst getWordUseList(String spelling,Integer status,Integer pageNum,Integer pageSize){
        List<Map<String,Object>> wordUseList = wordService.getWordUseList(spelling,status,pageNum,pageSize);
        Integer count = wordService.getWordUseCount(spelling,status);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("wordUseList",wordUseList);
        resultMap.put("count",count);
        return HSSJsonReulst.ok(resultMap);
    }

    @ApiOperation("用词修改")
    @PostMapping("/updateWordUse")
    public HSSJsonReulst updateWordUse(@RequestBody WordUseUpdate update){
        for(WordUseDto wordUseDto:update.getUpdate()){
            wordService.updateWordUse(wordUseDto.getSpelling(),wordUseDto.getSentence(),wordUseDto.getWordUseBean());
        }
        return HSSJsonReulst.ok();
    }

    @ApiModelProperty("用词审核通过上线")
    @PostMapping("/updateWordUseOnline")
    public HSSJsonReulst updateWordUseOnline(@RequestBody WordUseUpdate update){
        Integer count=0;
        for(WordUseDto wordUseDto:update.getUpdate()){
            WordUseManager.addWordUseBean(wordUseDto.getWordUseBean());
            count++;
        }
        return HSSJsonReulst.ok(count);
    }

    @ApiOperation("用词数据生成")
    @PostMapping("/generateWordUse")
    public HSSJsonReulst generateWordUse(Integer courseId){
        boolean flag = wordService.generateWordUse(courseId);
        return HSSJsonReulst.ok(flag);
    }



    @ApiOperation("修改单词或音节")
    @PostMapping("/updateSoundMark")
    public HSSJsonReulst updateSoundMark(String oldSpelling,String newSpelling,String soundMark){
        boolean flag = wordService.updateSoundMark(oldSpelling,newSpelling,soundMark);
        if(flag){
            SoundMarkManager.deleteSoundmark(oldSpelling);
            SoundMarkManager.deleteSoundmark(newSpelling);
        }
        return HSSJsonReulst.ok(flag);
    }

    @ApiOperation("添加单词和音节")
    @PostMapping("/insertSoundMark")
    public HSSJsonReulst insertSoundMark(String spelling ,String soundmark){
        boolean flag = wordService.insertSoundMark(spelling,soundmark);
        if(flag){
            SoundMarkManager.deleteSoundmark(spelling);
        }
        return HSSJsonReulst.ok(flag);
    }
}
