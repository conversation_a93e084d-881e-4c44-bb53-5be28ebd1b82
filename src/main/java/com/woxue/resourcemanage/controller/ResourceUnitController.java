package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.IResourceUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * <AUTHOR>
 * @date 2021 -05-27 15:22
 */
@Controller
@RequestMapping("/resourceUnit")
public class ResourceUnitController {

    @Autowired
    IResourceUnitService resourceUnitService;

    /**
     * 根据课程id获取单元列表   
     * @param courseId
     * @param pageStart
     * @param pageSize
     * @return
     */
    @PostMapping("/getUnitList")
    @ResponseBody
    public HSSJsonReulst getUnitList(Integer courseId, Integer pageStart, Integer pageSize) {
        return HSSJsonReulst.ok(resourceUnitService.getUnitList(courseId,pageStart*pageSize,pageSize));
    }

    /**
     * 添加单元信息并更新课程单元数量
     * @param nameEn
     * @param nameCn
     * @param courseId
     * @return
     */
    @PostMapping("/insertUnit")
    @ResponseBody
    public HSSJsonReulst insertUnit(String nameEn, String nameCn, Integer courseId) {
        return HSSJsonReulst.ok(resourceUnitService.insertUnit(nameEn,nameCn,courseId));
    }

    /**
     * 批量添加单元信息并更新课程单元数量
     * @param nameEns
     * @param courseId
     * @return
     */
    @PostMapping("/insertUnits")
    @ResponseBody
    public HSSJsonReulst insertUnits(@RequestParam("nameEns") String [] nameEns, @RequestParam("courseId") Integer courseId) {
        return HSSJsonReulst.ok(resourceUnitService.insertUnits(nameEns,courseId));
    }

    /**
     * 修改回显获取单元信息
     * @param id
     * @return
     */
    @PostMapping("/getUnitById")
    @ResponseBody
    public HSSJsonReulst getUnitById(Integer id) {
        return HSSJsonReulst.ok(resourceUnitService.getUnitById(id));
    }

    @PostMapping("/updateUnit")
    @ResponseBody
    public HSSJsonReulst updateUnit(String nameEn, String nameCn, Integer id) {
        return HSSJsonReulst.ok(resourceUnitService.updateUnit(nameEn,nameCn,id));
    }
}
