package com.woxue.resourcemanage.controller;


import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.dto.ReadExpandReadArticleWordDTO;
import com.woxue.common.model.redBook.readExpand.*;
import com.woxue.resourcemanage.entity.vo.ReadExpandReadArticleVO;
import com.woxue.resourcemanage.enums.ReadExpandArticleQuestionEnum;
import com.woxue.resourcemanage.enums.ReadExpandReadDifficultyEnum;
import com.woxue.resourcemanage.service.*;
import com.woxue.resourceservice.util.ReadExpandReadResourceManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <p>
 * 扩展阅读-文章
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Api(tags = "扩展阅读-文章")
@RestController
@RequestMapping("/readExpand/article")
public class ReadExpandReadArticleController {

    @Autowired
    IReadExpandReadArticleService readexpandReadArticleService;

    @Autowired
    IReadExpandReadArticleQuestionService questionService;

    @Autowired
    IReadExpandReadArticleWordService wordService;

    @Autowired
    IReadExpandReadArticleCorrelationService correlationService;

    @Autowired
    IReadExpandKnowledgeQuestionService knowledgeQuestionService;

    @ApiOperation("列表（每个单元包含文章列表）")
    @PostMapping("/listByCourseId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true),
            @ApiImplicitParam(name = "pageNum",value = "当前页，从1开始",required = true),
            @ApiImplicitParam(name = "pageSize",value = "一页显示多少行",required = true)
    })
    public HSSJsonReulst listByCourseId(@RequestParam("courseId") Integer courseId,Integer pageNum, Integer pageSize) {
        return HSSJsonReulst.ok(readexpandReadArticleService.listByCourseId(courseId,(pageNum-1)*pageSize,pageSize));
    }

//    @ApiOperation("文章列表")
//    @PostMapping("/list")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "unitId", value = "单元id", required = true)
//    })
//    public HSSJsonReulst<ReadExpandReadArticleBean> list(@RequestParam("unitId") Integer unitId) {
//        return HSSJsonReulst.ok(readexpandReadArticleService.list(unitId));
//    }

    @ApiOperation("文章详情")
    @PostMapping("/edit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "articleId", value = "文章id", required = true)
    })
    public HSSJsonReulst<ReadExpandReadArticleVO> edit(@RequestParam("articleId") Integer articleId) {
        return HSSJsonReulst.ok(readexpandReadArticleService.edit(articleId));
    }
    @ApiOperation("新增文章")
    @PostMapping("/save")
    public HSSJsonReulst save(@RequestBody ReadExpandReadArticleBean readexpandReadArticleBean) {
        readexpandReadArticleService.save(readexpandReadArticleBean);
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("编辑文章")
    @PostMapping("/update")
    public HSSJsonReulst update(@RequestBody ReadExpandReadArticleBean readexpandReadArticleBean) {
        readexpandReadArticleService.update(readexpandReadArticleBean);
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("导入文章")
    @PostMapping("/importExcel")
    public HSSJsonReulst importExcel(@RequestParam("file") MultipartFile file,
                                     @RequestParam("versionId") Integer versionId,
                                     @RequestParam("courseId") Integer courseId) {

        return HSSJsonReulst.ok(readexpandReadArticleService.importExcel(file,versionId,courseId,1));
    }

    /**
     * 支持基础训练和拔高训练
     * @param articleQuestionBean
     * @return
     */
    @ApiOperation("新增或编辑-基础训练、拔高训练")
    @PostMapping("/saveOrUpdateQuestion")
    public HSSJsonReulst saveOrUpdateQuestion(@RequestBody ReadExpandReadArticleQuestionBean articleQuestionBean) {
        //更新
        if(articleQuestionBean != null && articleQuestionBean.getQuestionId() != null){
            questionService.update(articleQuestionBean);
        }else {
            //新增
            questionService.save(articleQuestionBean);
        }
        return HSSJsonReulst.ok("操作成功！");
    }


    @ApiOperation("新增或编辑-句句对应")
    @PostMapping("/saveOrUpdateCorrelation")
    public HSSJsonReulst saveOrUpdateCorrelation(@RequestBody ReadExpandReadArticleCorrelationBean readexpandReadArticleCorrelationBean) {
        //更新
        if(readexpandReadArticleCorrelationBean != null && readexpandReadArticleCorrelationBean.getCorrelationId() != null){
            correlationService.update(readexpandReadArticleCorrelationBean);
        }else {
            //新增
            correlationService.save(readexpandReadArticleCorrelationBean);
        }
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("新增或编辑-重点单词")
    @PostMapping("/saveOrUpdateWord")
    public HSSJsonReulst saveOrUpdateWord(@RequestBody ReadExpandReadArticleWordBean readexpandReadArticleWordBean) {
        //更新
        if(readexpandReadArticleWordBean != null && readexpandReadArticleWordBean.getWordId() != null){
            wordService.update(readexpandReadArticleWordBean);
        }else {
            //新增
            wordService.save(readexpandReadArticleWordBean);
        }
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("新增或编辑-知识重点试题")
    @PostMapping("/saveOrUpdateKnowledgeQuestion")
    public HSSJsonReulst saveOrUpdateKnowledgeQuestion(@RequestBody ReadExpandKnowledgeQuestionBean knowledgeQuestionBean) {
        //更新
        if(knowledgeQuestionBean != null && knowledgeQuestionBean.getId() != null){
            knowledgeQuestionService.updateReadExpandKnowledgeQuestion(knowledgeQuestionBean);
        }else {
            //新增
            knowledgeQuestionService.insertReadExpandKnowledgeQuestion(knowledgeQuestionBean);
        }
        return HSSJsonReulst.ok("操作成功！");
    }
    @ApiOperation("批量新增或编辑-重点单词")
    @PostMapping("/batchSaveOrUpdateWord")
    public HSSJsonReulst batchSaveOrUpdateWord(@RequestBody ReadExpandReadArticleWordDTO readexpandReadArticleWordDTO) {
        wordService.batchSaveOrUpdateWord(readexpandReadArticleWordDTO.getWordBeanList());
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("拖拽排序-重点单词")
    @PostMapping("/sortWord")
    public HSSJsonReulst sortWord(@RequestBody ReadExpandReadArticleWordDTO readexpandReadArticleWordDTO) {
        wordService.batchUpdate(readexpandReadArticleWordDTO.getWordBeanList());
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("删除-重点单词")
    @PostMapping("/deleteWord")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wordId", value = "单词id", required = true)
    })
    public HSSJsonReulst deleteWord(@RequestParam("wordId") Integer wordId) {
        wordService.delete(wordId);
        return HSSJsonReulst.ok("操作成功！");
    }


    @ApiOperation("文章体裁-下拉列表")
    @GetMapping("/articleGenreList")
    public HSSJsonReulst<List<ReadExpandArticleQuestionEnum.QuestionDO>> articleGenreList() {
        return HSSJsonReulst.ok(ReadExpandArticleQuestionEnum.articleGenreList());
    }

    @ApiOperation("基础训练-下拉列表")
    @GetMapping("/basicTrainList")
    public HSSJsonReulst<List<ReadExpandArticleQuestionEnum.QuestionDO>> basicTrainList() {
        return HSSJsonReulst.ok(ReadExpandArticleQuestionEnum.basicTrainList());
    }

    @ApiOperation("拔高训练-下拉列表")
    @GetMapping("/raiseTrainList")
    public HSSJsonReulst<List<ReadExpandArticleQuestionEnum.QuestionDO>> raiseTrainList() {
        return HSSJsonReulst.ok(ReadExpandArticleQuestionEnum.raiseTrainList());
    }

    @ApiOperation("困难程度-下拉列表")
    @GetMapping("/difficultyList")
    public HSSJsonReulst<List<ReadExpandReadDifficultyEnum.DifficultyDO>> difficultyList() {
        return HSSJsonReulst.ok(ReadExpandReadDifficultyEnum.list());
    }

//    @ApiOperation("发布全部文章内容")
//    @PostMapping("/publishAll")
//    public HSSJsonReulst publish() {
//        ReadExpandReadResourceManager.reloadReadExpandReadResource();
//        return HSSJsonReulst.ok("发布成功！");
//    }
//
//    @ApiOperation("发布某单元-文章内容")
//    @PostMapping("/publishUnitId")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "unitId", value = "单元id", required = true)
//    })
//    public HSSJsonReulst publishUnitId(@RequestParam("unitId") Integer unitId) {
//        ReadExpandReadResourceManager.reloadReadExpandReadResource(unitId);
//        return HSSJsonReulst.ok("发布成功！");
//    }


}
