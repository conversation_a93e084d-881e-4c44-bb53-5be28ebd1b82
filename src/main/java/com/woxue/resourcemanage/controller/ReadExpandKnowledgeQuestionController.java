package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.dto.ReadExpandKnowledgeQuestionParamsDTO;
import com.woxue.resourcemanage.service.IReadExpandKnowledgeQuestionService;
import com.woxue.resourcemanage.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.woxue.common.model.redBook.readExpand.ReadExpandKnowledgeQuestionBean;

import java.util.List;

/**
 * 扩展阅读知识重点-试题Controller
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/readExpand/knowledgeQuestion")
@Api(tags = "扩展阅读知识重点-试题")
public class ReadExpandKnowledgeQuestionController{
    @Autowired
    private IReadExpandKnowledgeQuestionService readexpandKnowledgeQuestionService;

    /**
     * 查询扩展阅读知识重点-试题列表
     */
    @GetMapping("/list")
    @ApiOperation("查询扩展阅读知识重点-试题列表")
    public HSSJsonReulst<PageUtil> list(ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO)
    {
        int count = readexpandKnowledgeQuestionService.count(questionParamsDTO);
        PageUtil pageUtil = new PageUtil(questionParamsDTO.getPageIndex(), questionParamsDTO.getPageSize(), count, null);
        questionParamsDTO.setPageIndex(pageUtil.getPageIndex());
        questionParamsDTO.setPageSize(pageUtil.getPageSize());
        List<ReadExpandKnowledgeQuestionBean> knowledgeQuestionBeanList = readexpandKnowledgeQuestionService.selectReadExpandKnowledgeQuestionList(questionParamsDTO);
        pageUtil.setPageIndex(questionParamsDTO.getPageIndex());
        pageUtil.setData(knowledgeQuestionBeanList);
        pageUtil.setTotal(count);
        return HSSJsonReulst.ok(pageUtil);
    }

    /**
     * 获取扩展阅读知识重点-试题详细信息
     */
    @ApiOperation("获取扩展阅读知识重点-试题详细信息")
    @GetMapping(value = "/info/{id}")
    public HSSJsonReulst getInfo(@PathVariable("id") Long id)
    {
        return HSSJsonReulst.ok(readexpandKnowledgeQuestionService.selectReadExpandKnowledgeQuestionById(id));
    }

    /**
     * 新增扩展阅读知识重点-试题
     */
    @ApiOperation("新增扩展阅读知识重点-试题")
    @PostMapping("/save")
    public HSSJsonReulst add(@RequestBody ReadExpandKnowledgeQuestionBean readexpandKnowledgeQuestion)
    {
        return HSSJsonReulst.ok(readexpandKnowledgeQuestionService.insertReadExpandKnowledgeQuestion(readexpandKnowledgeQuestion));
    }

    /**
     * 修改扩展阅读知识重点-试题
     */
    @ApiOperation("修改扩展阅读知识重点-试题")
    @PostMapping("/update")
    public HSSJsonReulst edit(@RequestBody ReadExpandKnowledgeQuestionBean readexpandKnowledgeQuestion)
    {
        return HSSJsonReulst.ok(readexpandKnowledgeQuestionService.updateReadExpandKnowledgeQuestion(readexpandKnowledgeQuestion));
    }

    /**
     * 删除扩展阅读知识重点-试题
     */
    @ApiOperation("删除扩展阅读知识重点-试题")
	@PostMapping("/batchDelete")
    public HSSJsonReulst remove(@RequestBody ReadExpandKnowledgeQuestionParamsDTO questionParamsDTO)
    {
        return HSSJsonReulst.ok(readexpandKnowledgeQuestionService.deleteReadExpandKnowledgeQuestionByIds(questionParamsDTO.getIds()));
    }


}
