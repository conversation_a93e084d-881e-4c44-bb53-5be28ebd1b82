package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.redbookquestionBank.model.SuitBean;
import com.woxue.resourcemanage.entity.grammar.GrammarUnitBean;
import com.woxue.resourcemanage.service.IQuestionService;
import com.woxue.resourcemanage.service.IResourceCourseService;
import com.woxue.resourcemanage.service.IResourceUnitService;
import com.woxue.resourceservice.dao.IQuestionSuitDao;
import com.woxue.resourceservice.util.QuestionBankCourseManager;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/question")
public class QuestionController {

    @Autowired
    IQuestionService questionService;
    @Autowired
    IResourceUnitService resourceUnitService;
    @Autowired
    IResourceCourseService resourceCourseService;
    @Autowired
    IQuestionSuitDao questionSuitDao;

    /**
     * 获取课程下的习题单元列表
     *
     * @param courseId
     * @return
     */
    @PostMapping("/getUnitList")
    @ApiOperation("获取课程下的习题单元列表")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = true)
    })
    public HSSJsonReulst< List<Map<String, Object>> > getUnitList(@RequestParam("courseId") Integer courseId) {
        return HSSJsonReulst.ok(questionService.getUnitList(courseId));
    }


    /**
     * 获取所有系列
     *
     * @return
     */
    @PostMapping("course/allParentList")
    @ApiOperation("获取所有习题系列")
    @ResponseBody
    public HSSJsonReulst getAllParentList() {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        Map<String, Object> map30 = new HashMap<>();
        Map<String, Object> map40 = new HashMap<>();
        Map<String, Object> map50 = new HashMap<>();
        map30.put("id", 30);
        map30.put("name", "小学");
        map40.put("id", 40);
        map40.put("name", "初中");
        map50.put("id", 50);
        map50.put("name", "高中");
        list.add(map30);
        list.add(map40);
        list.add(map50);
        map.put("parentSeriesList", list);
        return HSSJsonReulst.ok(map);
    }

    /**
     * 获取指定年级的版本列表
     *
     * @param gradePhase
     * @return
     */
    @PostMapping("course/getVersionList")
    @ApiOperation("获取获取指定年级的版本列表")
    @ResponseBody
    public List<Map<String, Object>> getVersionList(Integer gradePhase) {
        return new QuestionBankCourseManager().getVersionList(gradePhase);
    }

    /**
     * 获取指定版本的课程列表
     *
     * @param versionId
     * @return
     */
    @PostMapping("course/getCourseList")
    @ApiOperation("获取指定版本的课程列表")
    @ResponseBody
    public List<Map<String, Object>> getCourseList(Integer versionId) {
        return  new QuestionBankCourseManager().getAllCourseList(versionId);
    }

    /**
     * 获取指定课程的单元列表
     *
     * @param courseId
     * @return
     */
    @PostMapping("course/getUnitList")
    @ApiOperation("获取指定课程的单元列表")
    @ResponseBody
    public List<Map<String, Object>> getCourseUnitList(Integer courseId,Integer syncCourseId) {
        List<Map<String, Object>> unitList = new QuestionBankCourseManager().getUnitList(courseId);
        for (Map<String, Object> stringObjectMap : unitList) {
            List<SuitBean> suitLst = questionSuitDao.getUnitPaper((Integer) stringObjectMap.get("courseId"),(Integer) stringObjectMap.get("id"));
            stringObjectMap.put("suitList", suitLst);
        }
        return unitList;
    }

}
