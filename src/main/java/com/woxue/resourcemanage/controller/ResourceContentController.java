package com.woxue.resourcemanage.controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.woxue.common.model.redBook.RedBookConstant;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.SentenceBean;
import com.woxue.resourcemanage.service.IResourceContentService;
import com.woxue.resourcemanage.service.IResourceCourseService;
import com.woxue.resourcemanage.service.IResourceUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021 -05-28 15:14
 */
@RestController
@RequestMapping("/contentWord")
public class ResourceContentController {

    @Autowired
    IResourceContentService resourceContentService;

    @Autowired
    IResourceUnitService resourceUnitService;
    @Autowired
    IResourceCourseService resourceCourseService;

    /**
     * 根据课文Id获取课文分段句子
     * @param articleId
     * @return
     */
    @PostMapping("/getArticleContentList")
    @ResponseBody
    public HSSJsonReulst getArticleContentList(Integer articleId){
        List<SentenceBean> mapList = resourceContentService.getArticleContentList(articleId);
        String context = RedBookConstant.MEDIA_DOMAIN_NAME;
        for (SentenceBean sentenceBean : mapList) {
            sentenceBean.setUrl(context);
        }
        return  HSSJsonReulst.ok(mapList);
    }

    /**
     * 批量修改课文句子
     */
    @PostMapping("/updateArticleContentList")
    public HSSJsonReulst updateArticleContentList(String contentList){

        List<SentenceBean> list = new ArrayList<>();
        try {
            Gson gson = new Gson();
            JsonArray arry = new JsonParser().parse(contentList).getAsJsonArray();
            for (JsonElement jsonElement : arry) {
                list.add(gson.fromJson(jsonElement, SentenceBean.class));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean result = resourceContentService.updateArticleContentList(list);
        return  HSSJsonReulst.ok(result);
    }

    /**
     * 获取所有系列
     * @return
     */
    @PostMapping("/allParentList")
    @ResponseBody
    public HSSJsonReulst getAllParentList(Integer type) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
        Map<String,Object> map30=new HashMap<>();
        Map<String,Object> map40=new HashMap<>();
        Map<String,Object> map50=new HashMap<>();
        switch (type){
            case 1: //词汇
                //map.put("parentSeriesList", programService.getSeriesBeanList(0, 1));
                break;
            case 2: //句子
                //map.put("parentSeriesList", programService.getSeriesBeanList(0, 17));
                break;
            case 3://语法
                map30.put("id",30);
                map30.put("name","初级语法");
                map40.put("id",40);
                map40.put("name","中级语法");
                map50.put("id",50);
                map50.put("name","高级语法");
                list.add(map30);
                list.add(map40);
                list.add(map50);
                map.put("parentSeriesList",list);
                break;
            case 4://习题
                map30.put("id",30);
                map30.put("name","小学");
                map40.put("id",40);
                map40.put("name","初中");
                map50.put("id",50);
                map50.put("name","高中");
                list.add(map30);
                list.add(map40);
                list.add(map50);
                map.put("parentSeriesList",list);
                break;
            case 5://课文
               // map.put("parentSeriesList", syncKWLearningDataService.getGradePhaseList());
                break;
        }
        return HSSJsonReulst.ok(map);
    }

    /**
     * 获取所有的版本
     * @param parentId
     * @return
     */
    /*@PostMapping("/allSeriesList")
    @ResponseBody
    public HSSJsonReulst getAllSeriesList(int parentId, Integer type) {
        Map<String, Object> map = new HashMap<String, Object>();
        switch (type){
            case 1: //词汇
                List<SeriesBean> seriesBeanList = programService.getSeriesBeanList(parentId, 1);
                seriesBeanList.sort(Comparator.comparing(SeriesBean::getDisporder));
                map.put("seriesList",seriesBeanList);
                break;
            case 2: //句子
                map.put("seriesList", programService.getSeriesBeanList(parentId, 17));
                break;
            case 3://语法
                map.put("seriesList",grammarLearningDataService.getVersionList(parentId));
                break;
            case 4://习题
                map.put("seriesList",syncQuestionLearningDataService.getVersionList(parentId));
                break;
            case 5://课文
                map.put("seriesList",syncKWLearningDataService.getVersionList(parentId));
                break;
        }
        return HSSJsonReulst.ok(map);
    }*/

    /**
     * 获取所有的课程
     * @param seriesId
     * @return
     */
    /*@PostMapping("/allProgramList")
    @ResponseBody
    public HSSJsonReulst getAllProgramList(int seriesId, Integer type) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ProgramBean>  newProgramBeanList=new ArrayList<>();
        List<Map<String, Object>>  newCourseList=new ArrayList<>();
        switch (type){
            case 1: //词汇
                List<ProgramBean> programBeanList = programService.getProgramBeanList(seriesId);
                programBeanList.sort(Comparator.comparing(ProgramBean::getDisporder));
                for (ProgramBean programBean : programBeanList) {
                    Map<String, Object> haveCourseWord = resourceCourseService.isHaveCourseWord(programBean.getProgramName());
                    if(haveCourseWord==null){
                        newProgramBeanList.add(programBean);
                    }
                }
                map.put("programList",newProgramBeanList);
                break;
            case 2: //句子
                List<ProgramBean> programSentenceList = programService.getProgramBeanList(seriesId);
                programSentenceList.sort(Comparator.comparing(ProgramBean::getDisporder));
                for (ProgramBean programBean : programSentenceList) {
                    Map<String, Object> haveCourseSentence = resourceCourseService.isHaveCourseSentence(programBean.getProgramName());
                    if(haveCourseSentence==null){
                        newProgramBeanList.add(programBean);
                    }
                }
                map.put("programList",newProgramBeanList);
                break;
            case 3://语法
                List<Map<String, Object>> courseList = grammarLearningDataService.getCourseList(seriesId);
                for (Map<String, Object> stringObjectMap : courseList) {
                    Map<String, Object> haveCourseGrammar = resourceCourseService.isHaveCourseGrammar((Integer) stringObjectMap.get("courseId"));
                    if (haveCourseGrammar==null){
                        newCourseList.add(stringObjectMap);
                    }
                }
                map.put("programList",newCourseList);
                break;
            case 4://习题
                List<Map<String, Object>> courseList1 = syncQuestionLearningDataService.getCourseList(seriesId);
                for (Map<String, Object> stringObjectMap : courseList1) {
                    Map<String, Object> haveCourseQuestionr = resourceCourseService.isHaveCourseQuestion((Integer) stringObjectMap.get("id"));
                    if(haveCourseQuestionr==null){
                        newCourseList.add(stringObjectMap);
                    }
                }
                map.put("programList",newCourseList);
                break;
            case 5://课文
                List<Map<String, Object>> courseList2 = syncKWLearningDataService.getCourseList(seriesId);
                for (Map<String, Object> stringObjectMap : courseList2) {
                    Map<String, Object> programName = resourceCourseService.isHaveCourseArticle(stringObjectMap.get("programName").toString());
                    if (programName==null){
                        newCourseList.add(stringObjectMap);
                    }
                }
                map.put("programList",newCourseList);
                break;
        }
        return  HSSJsonReulst.ok(map);
    }*/

    /**
     * 单元列表
     *
     * @param programName
     * @return
     */
    /*@PostMapping("/allUnitList")
    @ResponseBody
    public HSSJsonReulst getAllUnitList(String programName, Integer programId, Integer type, Integer resourceUnitId) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Map<String, Object>> unitList = new ArrayList<Map<String, Object>>();
        Map<String, Object> unitInfo = null;
        WordBean wBean=null;
        switch (type){
            case 1: //词汇   //把所有课程的单元合并成一列返回
                String[] programNameList = programName.split(",");
                for (String s : programNameList) {
                    ProgramBean programBean = programService.getProgramBean(s);
                    List<UnitBean> unitBeanList = programService.getUnitBeanList(s);
                    for (UnitBean unit : unitBeanList) {
                        Map<String, Object> unitWord = resourceUnitService.getUnitWord(s, unit.getUnitName());
                        if(unitWord==null){
                            unitInfo = new HashMap<String, Object>();
                            unitInfo.put("unitId", unit.getId());
                            unitInfo.put("unitName", unit.getUnitName());
                            unitInfo.put("unitCNName", unit.getUnitCNName());
                            unitInfo.put("wordsCount", unit.getWordsCount());
                            unitInfo.put("programName", programBean.getProgramName());
                            unitInfo.put("programCNName", programBean.getProgramCNName());
                            List<String> unitWordIdList = programService.getUnitWordIdList(programBean.getProgramName(), unit.getUnitName());
                            if(unitWordIdList!=null){
                                wBean = programService.getWordBean(unitWordIdList.get(0));
                            }
                            if (wBean!=null&&wBean.getExample_en_US()!=null&&wBean.getExample_en_US().length()>0){
                                unitInfo.put("hasExampleSentence", 1);
                            }else{
                                unitInfo.put("hasExampleSentence", 0);
                            }
                            unitList.add(unitInfo);
                        }
                    }
                }
                    break;
            case 2: //句子
                List<UnitBean> unitBeanList = programService.getUnitBeanList(programName);
                unitBeanList.sort(Comparator.comparing(UnitBean::getDisporder));
                for (UnitBean unit : unitBeanList) {
                    Map<String, Object> unitWord = resourceUnitService.getUnitSentence(programName, unit.getUnitName());
                    if(unitWord==null){
                        unitInfo = new HashMap<String, Object>();
                        unitInfo.put("unitId", unit.getId());
                        unitInfo.put("unitName", unit.getUnitName());
                        unitInfo.put("unitCNName", unit.getUnitCNName());
                        unitInfo.put("wordsCount", unit.getWordsCount());
                        unitList.add(unitInfo);
                    }
                }
                break;
            case 3://语法
                if(programId==null){
                    unitList=new ArrayList<>();
                }else{
                    List<Map<String, Object>> unitListGrammar = grammarLearningDataService.getUnitList(programId);
                    for (Map<String, Object> stringObjectMap : unitListGrammar) {
                        Map<String, Object> unitGrammar = resourceUnitService.getUnitGrammar((Integer) stringObjectMap.get("unitId"));
                        if(unitGrammar==null){
                            unitList.add(stringObjectMap);
                        }
                    }
                }
                break;
            case 4://习题
                if(programId==null){
                    unitList=new ArrayList<>();
                }else{
                    unitList=syncQuestionLearningDataService.getUnitList(programId);
                    for (Map<String, Object> stringObjectMap : unitList) {
                        List<SuitBean> newSuitLst=new ArrayList<>();
                        List<SuitBean> suitLst = (List<SuitBean>) stringObjectMap.get("suitLst");
                        for (SuitBean suitBean : suitLst) {
                            Map<String, Object> unitQuestion = resourceUnitService.getUnitQuestion(suitBean.getId());
                            if(unitQuestion==null){
                                newSuitLst.add(suitBean);
                            }
                        }
                        stringObjectMap.put("suitLst",newSuitLst);
                    }
                }
                break;
            case 5://课文 暂时不需要修改
                List<Map<String, Object>> unitList1 = syncKWLearningDataService.getUnitList(programName);
                for (Map<String, Object> stringObjectMap : unitList1) {
                    String unitName = stringObjectMap.get("unitName").toString();
                    Map<String, Object> unitArticle = resourceUnitService.getUnitArticle(programName, unitName);
                    if(unitArticle==null||unitArticle.get("resource_unit_id").equals(resourceUnitId)){
                        unitList.add(stringObjectMap);
                    }
                }
                break;
        }
        map.put("unitList", unitList);
        return HSSJsonReulst.ok(map);
    }*/


    /**
     * 保存词汇或句子的内容关联信息
     * @param resourceUnitId
     * @param programName
     * @param unitName
     * @param showName
     * @param wordCount
     * @param hasExampleSentence
     * @return
     */
    @PostMapping("/insertContentWord")
    @ResponseBody
    public HSSJsonReulst insertContentWord(Integer resourceUnitId, Integer level, String programName, String unitName, String showName, Integer wordCount, Integer hasExampleSentence, Integer type, Integer id) {
        if(type==1){
            if(id==null){
                return HSSJsonReulst.ok(resourceContentService.insertContentWord(resourceUnitId,level,programName,unitName,showName,wordCount,hasExampleSentence));
            }else{
                return HSSJsonReulst.ok(resourceContentService.updateContentWord(programName,unitName,showName,wordCount,hasExampleSentence,id));
            }
        }else{
            if(id==null){
                return HSSJsonReulst.ok(resourceContentService.insertContentSentence(resourceUnitId,programName,unitName,showName,wordCount));
            }else{
                return HSSJsonReulst.ok(resourceContentService.updateContentSentence(programName,unitName,showName,wordCount,id));
            }
        }
    }

    /**
     * 保存语法的内容关联信息
     * @param resourceUnitId
     * @param grammarCourseId
     * @param grammarUnitId
     * @param showName
     * @param knowledgeCount
     * @return
     */
    @PostMapping("/insertContentGrammar")
    @ResponseBody
    public HSSJsonReulst insertContentGrammar(Integer resourceUnitId, Integer grammarCourseId, Integer grammarUnitId, String showName, Integer knowledgeCount, Integer id){
        if(id==null){
            return HSSJsonReulst.ok( resourceContentService.insertContentGrammar(resourceUnitId,grammarCourseId,grammarUnitId,showName,knowledgeCount));
        }else{
            return HSSJsonReulst.ok(resourceContentService.updateContentGrammar(grammarCourseId,grammarUnitId,showName,knowledgeCount,id));
        }
    }

    /**
     * 保存优题的内容关联信息
     * @param resourceUnitId
     * @param level
     * @param syncQuestionCourseId
     * @param syncQuestionUnitId
     * @param showName
     * @param paperId
     * @return
     */
    @PostMapping("/insertContentQuestion")
    @ResponseBody
    public HSSJsonReulst insertContentQuestion(Integer resourceUnitId, Integer level, Integer syncQuestionCourseId, Integer syncQuestionUnitId, String showName, Integer paperId, Integer id){
        if(id==null){
            return HSSJsonReulst.ok( resourceContentService.insertContentQuestion(resourceUnitId,level,syncQuestionCourseId,syncQuestionUnitId,showName,paperId));
        }else{
            return HSSJsonReulst.ok(resourceContentService.updateContentQuestion(syncQuestionCourseId,syncQuestionUnitId,showName,paperId,id));
        }
    }



    /**
     * 取消关联
     * @param id
     * @param type
     * @return
     */
    @PostMapping("/delContentWord")
    @ResponseBody
    public HSSJsonReulst delContentWord(Integer id, Integer type, Integer resourceUnitId){
        boolean result=false;
        switch (type) {
            case 1: //词汇
                result = resourceContentService.delContentWord(id);
                break;
            case 2: //句子
                result = resourceContentService.delContentSentence(id);
                break;
            case 3: //语法
                result = resourceContentService.delContentGrammar(id);
                break;
            case 4://习题
                result = resourceContentService.delContentQuestion(id);
                break;
        }
        if(result){
            resourceUnitService.updateUnitContentNum(-1,resourceUnitId);
        }
        return  HSSJsonReulst.ok(result);
    }


}
