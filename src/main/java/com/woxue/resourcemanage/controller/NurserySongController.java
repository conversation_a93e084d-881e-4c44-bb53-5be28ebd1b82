package com.woxue.resourcemanage.controller;

import com.redbook.kid.common.model.NurserySongDO;
import com.redbook.kid.common.model.NurserySongSentenceDO;
import com.redbook.kid.common.model.NurserySongWithSentencesDTO;
import com.redbook.kid.common.util.TencentCosUtil;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.INurserySongManageService;
import com.woxue.resourcemanage.util.SubtitleParserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 儿歌资源管理控制器
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
@Slf4j
@Api(tags = "儿歌资源管理")
@RestController
@RequestMapping("/nursery-song")
public class NurserySongController {

    @Autowired
    private INurserySongManageService nurserySongManageService;

    /**
     * 儿歌资源文件存储路径前缀
     */
    public static final String NURSERY_SONG_PATH_PREFIX = "/nursery-song";

    @ApiOperation("儿歌文件上传")
    @PostMapping("/upload")
    public HSSJsonReulst<String> uploadFile(
            @ApiParam("文件") @RequestParam("file") MultipartFile file,
            @ApiParam("文件类型(1:视频 2:原唱音频 3:伴奏音频 4:字幕文件)") @RequestParam Integer type,
            @ApiParam("儿歌ID(可选)") @RequestParam(required = false) Integer songId) {

        if (file.isEmpty()) {
            return HSSJsonReulst.errorMsg("文件不能为空");
        }

        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.trim().isEmpty()) {
                return HSSJsonReulst.errorMsg("文件名不能为空");
            }

            // 验证文件类型
            String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            if (!isValidFileType(type, fileExtension)) {
                return HSSJsonReulst.errorMsg("文件类型不匹配");
            }

            // 构建文件路径
            String filePath = buildFilePath(type, songId, fileName);

            // 上传到腾讯云COS
            URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
            if (url != null) {
                log.info("文件上传成功，路径: {}", url.toString());
                return HSSJsonReulst.ok(url.toString());
            } else {
                return HSSJsonReulst.errorMsg("文件上传失败");
            }
        } catch (Exception e) {
            log.error("文件上传异常", e);
            return HSSJsonReulst.errorMsg("文件上传异常: " + e.getMessage());
        }
    }

    @ApiOperation("批量上传儿歌文件")
    @PostMapping("/upload/batch")
    public HSSJsonReulst<Map<String, String>> uploadBatchFiles(
            @ApiParam("视频文件") @RequestParam(value = "videoFile", required = false) MultipartFile videoFile,
            @ApiParam("原唱音频文件") @RequestParam(value = "originalAudioFile", required = false) MultipartFile originalAudioFile,
            @ApiParam("伴奏音频文件") @RequestParam(value = "backgroundAudioFile", required = false) MultipartFile backgroundAudioFile,
            @ApiParam("字幕文件") @RequestParam(value = "subtitleFile", required = false) MultipartFile subtitleFile,
            @ApiParam("儿歌ID(可选)") @RequestParam(required = false) Integer songId) {

        Map<String, String> uploadResults = new HashMap<>();

        try {
            // 上传视频文件
            if (videoFile != null && !videoFile.isEmpty()) {
                HSSJsonReulst<String> result = uploadFile(videoFile, 1, songId);
                if (result.getCode()==200) {
                    uploadResults.put("videoUrl", result.getData());
                } else {
                    return HSSJsonReulst.errorMsg("视频文件上传失败: " + result.getMsg());
                }
            }

            // 上传原唱音频文件
            if (originalAudioFile != null && !originalAudioFile.isEmpty()) {
                HSSJsonReulst<String> result = uploadFile(originalAudioFile, 2, songId);
                if (result.getCode()==200) {
                    uploadResults.put("originalAudioUrl", result.getData());
                } else {
                    return HSSJsonReulst.errorMsg("原唱音频文件上传失败: " + result.getMsg());
                }
            }

            // 上传伴奏音频文件
            if (backgroundAudioFile != null && !backgroundAudioFile.isEmpty()) {
                HSSJsonReulst<String> result = uploadFile(backgroundAudioFile, 3, songId);
                if (result.getCode()==200) {
                    uploadResults.put("backgroundAudioUrl", result.getData());
                } else {
                    return HSSJsonReulst.errorMsg("伴奏音频文件上传失败: " + result.getMsg());
                }
            }

            // 上传字幕文件
            if (subtitleFile != null && !subtitleFile.isEmpty()) {
                HSSJsonReulst<String> result = uploadFile(subtitleFile, 4, songId);
                if (result.getCode()==200) {
                    uploadResults.put("subtitleFileUrl", result.getData());
                } else {
                    return HSSJsonReulst.errorMsg("字幕文件上传失败: " + result.getMsg());
                }
            }

            return HSSJsonReulst.ok(uploadResults);
        } catch (Exception e) {
            log.error("批量文件上传异常", e);
            return HSSJsonReulst.errorMsg("批量文件上传异常: " + e.getMessage());
        }
    }

    /**
     * 验证文件类型是否匹配
     */
    private boolean isValidFileType(Integer type, String fileExtension) {
        List<String> videoTypes = new ArrayList<>();
        videoTypes.add("mp4");
        videoTypes.add("avi");
        videoTypes.add("mov");
        videoTypes.add("wmv");
        videoTypes.add("flv");
        videoTypes.add("mkv");
        List<String> audioTypes = new ArrayList<>();
        audioTypes.add("mp3");
        audioTypes.add("wav");
        audioTypes.add("aac");
        audioTypes.add("flac");
        audioTypes.add("ogg");
        audioTypes.add("m4a");
        List<String> subtitleTypes = new ArrayList<>();
        subtitleTypes.add("srt");
        subtitleTypes.add("vtt");
        subtitleTypes.add("json");
        subtitleTypes.add("ass");
        subtitleTypes.add("ssa");
        List<String> imageTypes = new ArrayList<>();
        imageTypes.add("jpg");
        imageTypes.add("jpeg");
        imageTypes.add("png");
        imageTypes.add("gif");
        imageTypes.add("bmp");
        switch (type) {
            case 1: // 视频文件
                return videoTypes.contains(fileExtension);
            case 2: // 原唱音频
            case 3: // 伴奏音频
                return audioTypes.contains(fileExtension);
            case 4: // 字幕文件
                return subtitleTypes.contains(fileExtension);
            case 5: // 缩略图
                return imageTypes.contains(fileExtension);
            default:
                return false;
        }
    }

    /**
     * 构建文件存储路径
     */
    private String buildFilePath(Integer type, Integer songId, String fileName) {
        StringBuilder pathBuilder = new StringBuilder(NURSERY_SONG_PATH_PREFIX);

        if (songId != null) {
            pathBuilder.append("/").append(songId);
        }

        switch (type) {
            case 1:
                pathBuilder.append("/video/");
                break;
            case 2:
                pathBuilder.append("/original-audio/");
                break;
            case 3:
                pathBuilder.append("/background-audio/");
                break;
            case 4:
                pathBuilder.append("/subtitle/");
                break;
            case 5:
                pathBuilder.append("/thumbnail/");
                break;
            default:
                pathBuilder.append("/other/");
                break;
        }

        pathBuilder.append(fileName);
        return pathBuilder.toString();
    }

    @ApiOperation("添加儿歌")
    @PostMapping("/add")
    public HSSJsonReulst<Integer> addSong(@RequestBody NurserySongDO song) {
        return nurserySongManageService.addSong(song);
    }

    @ApiOperation("更新儿歌信息")
    @PostMapping("/update")
    public HSSJsonReulst<Boolean> updateSong(@RequestBody NurserySongDO song) {
        return nurserySongManageService.updateSong(song);
    }

    @ApiOperation("删除儿歌")
    @PostMapping("/delete")
    public HSSJsonReulst<Boolean> deleteSong(@ApiParam("儿歌ID") @RequestParam Integer id) {
        return nurserySongManageService.deleteSong(id);
    }

    @ApiOperation("获取儿歌详情")
    @GetMapping("/detail")
    public HSSJsonReulst<NurserySongWithSentencesDTO> getSongDetail(@ApiParam("儿歌ID") @RequestParam Integer id) {
        return nurserySongManageService.getSongDetail(id);
    }

    @ApiOperation("分页查询儿歌列表")
    @PostMapping("/list")
    public HSSJsonReulst<Map<String, Object>> getSongList(@RequestBody Map<String, Object> params) {
        // 设置分页参数
        Integer pageIndex = 1;
        Integer pageSize = 50;
        
        if (pageIndex != null && pageSize != null) {
            int offset = (pageIndex - 1) * pageSize;
            params.put("offset", offset);
        }
        
        return nurserySongManageService.getSongList(params);
    }

    @ApiOperation("上传并解析字幕文件")
    @PostMapping("/upload-subtitle")
    public HSSJsonReulst<List<SubtitleParserUtil.SentenceTimeline>> uploadSubtitle(
            @ApiParam("儿歌ID") @RequestParam Integer songId,
            @ApiParam("字幕文件") @RequestParam("file") MultipartFile subtitleFile) {
        return nurserySongManageService.uploadAndParseSubtitle(songId, subtitleFile);
    }

    @ApiOperation("处理儿歌资源")
    @PostMapping("/process")
    public HSSJsonReulst<INurserySongManageService.ProcessingResult> processSong(
            @ApiParam("儿歌ID") @RequestParam Integer songId) {
        return nurserySongManageService.processSongResources(songId);
    }

    @ApiOperation("更新儿歌状态")
    @PostMapping("/update-status")
    public HSSJsonReulst<Boolean> updateSongStatus(
            @ApiParam("儿歌ID") @RequestParam Integer id,
            @ApiParam("状态(0:下架 1:上架 2:待审核)") @RequestParam Integer status) {
        return nurserySongManageService.updateSongStatus(id, status);
    }

    @ApiOperation("批量更新状态")
    @PostMapping("/batch-update-status")
    public HSSJsonReulst<Boolean> batchUpdateStatus(
            @ApiParam("儿歌ID列表") @RequestParam List<Integer> ids,
            @ApiParam("状态") @RequestParam Integer status) {
        return nurserySongManageService.batchUpdateStatus(ids, status);
    }

    @ApiOperation("获取句子列表")
    @GetMapping("/sentences")
    public HSSJsonReulst<List<NurserySongSentenceDO>> getSentences(
            @ApiParam("儿歌ID") @RequestParam Integer songId) {
        return nurserySongManageService.getSentencesBySongId(songId);
    }

    @ApiOperation("更新句子信息")
    @PostMapping("/sentence/update")
    public HSSJsonReulst<Boolean> updateSentence(@RequestBody NurserySongSentenceDO sentence) {
        return nurserySongManageService.updateSentence(sentence);
    }

    @ApiOperation("删除句子")
    @PostMapping("/sentence/delete")
    public HSSJsonReulst<Boolean> deleteSentence(@ApiParam("句子ID") @RequestParam Integer sentenceId) {
        return nurserySongManageService.deleteSentence(sentenceId);
    }

    @ApiOperation("验证儿歌数据")
    @PostMapping("/validate")
    public HSSJsonReulst validateSongData(@RequestBody Map<String, Object> params) {
        try {
            NurserySongDO song = (NurserySongDO) params.get("song");
            @SuppressWarnings("unchecked")
            List<NurserySongSentenceDO> sentences = (List<NurserySongSentenceDO>) params.get("sentences");
            
            return nurserySongManageService.validateSongData(song, sentences);
        } catch (Exception e) {
            log.error("验证数据异常", e);
            return HSSJsonReulst.errorMsg("验证数据异常: " + e.getMessage());
        }
    }

    @ApiOperation("重新处理儿歌资源")
    @PostMapping("/reprocess")
    public HSSJsonReulst<INurserySongManageService.ProcessingResult> reprocessSong(
            @ApiParam("儿歌ID") @RequestParam Integer songId) {
        return nurserySongManageService.reprocessSongResources(songId);
    }

    @ApiOperation("批量处理儿歌资源")
    @PostMapping("/batch-process")
    public HSSJsonReulst<INurserySongManageService.BatchProcessingResult> batchProcessSongs(
            @ApiParam("儿歌ID列表") @RequestParam List<Integer> songIds) {
        return nurserySongManageService.batchProcessSongs(songIds);
    }

    @ApiOperation("获取状态选项")
    @GetMapping("/status-options")
    public HSSJsonReulst<Map<String, Object>> getStatusOptions() {
        Map<String, Object> options = new HashMap<>();
        options.put("OFFLINE", 0);
        options.put("ONLINE", 1);
        options.put("PENDING", 2);
        return HSSJsonReulst.ok(options);
    }

    @ApiOperation("获取难度等级选项")
    @GetMapping("/difficulty-options")
    public HSSJsonReulst<List<Map<String, Object>>> getDifficultyOptions() {
        List<Map<String, Object>> options = new ArrayList<>();
        Map<String, Object> opt1 = new HashMap<>();
        opt1.put("value", 1);
        opt1.put("label", "简单");
        options.add(opt1);
        Map<String, Object> opt2 = new HashMap<>();
        opt2.put("value", 2);
        opt2.put("label", "较简单");
        options.add(opt2);
        Map<String, Object> opt3 = new HashMap<>();
        opt3.put("value", 3);
        opt3.put("label", "中等");
        options.add(opt3);
        Map<String, Object> opt4 = new HashMap<>();
        opt4.put("value", 4);
        opt4.put("label", "较难");
        options.add(opt4);
        Map<String, Object> opt5 = new HashMap<>();
        opt5.put("value", 5);
        opt5.put("label", "困难");
        options.add(opt5);
        return HSSJsonReulst.ok(options);
    }
}
