package com.woxue.resourcemanage.controller;

import com.woxue.ai.model.AnalysisResponse;
import com.woxue.common.model.redBook.read.ResourceReadArticleCorrelationBean;
import com.woxue.common.model.redBook.read.ResourceReadArticleDTO;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.resourcemanage.entity.dto.read.AIGenerateArticleDTO;
import com.woxue.resourcemanage.entity.dto.read.ReadCorrelationDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceReadArticleNewDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceUnitTopicIdPostDTO;
import com.woxue.resourcemanage.entity.vo.read.TopicUnitArticleListVO;
import com.woxue.resourcemanage.service.IResourceReadArticleService;
import com.woxue.resourcemanage.service.IResourceUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-07-20 13:41
 */
@Api(tags = "同步阅读控制层")
@RestController
@RequestMapping("/resourceReadArticle")
public class ResourceReadArticleController {

    @Autowired
    IResourceReadArticleService iResourceReadArticleService;

    @Autowired
    IResourceUnitService unitService;
    @Autowired
    IResourceCourseDao courseDao;

    @ApiOperation("单元列表（包含文章）")
    @PostMapping("/listByCourseId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true),
            @ApiImplicitParam(name = "pageNum",value = "当前页，从1开始",required = true),
            @ApiImplicitParam(name = "pageSize",value = "一页显示多少行",required = true)
    })
    public HSSJsonReulst listByCourseId(@RequestParam("courseId") Integer courseId, Integer pageNum, Integer pageSize) {

        return HSSJsonReulst.ok(iResourceReadArticleService.listByCourseId(courseId,(pageNum-1)*pageSize,pageSize));
    }

    @ApiOperation("主题列表")
    @PostMapping("/topicList")
    public HSSJsonReulst<ResourceTopic> topicList() {
        return HSSJsonReulst.ok(iResourceReadArticleService.topicList());
    }
    @ApiOperation("添加主题")
    @PostMapping("/insertTopic")
    public HSSJsonReulst insertTopic(@RequestBody ResourceTopic resourceTopicBean) {
        resourceTopicBean.setType("1");
        return HSSJsonReulst.ok(iResourceReadArticleService.insertTopic(resourceTopicBean));
    }
    @ApiOperation("查询主题关联的阅读文章")
    @PostMapping("/getTopicUnitArticleList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "topicId", value = "主题id", required = true)
    })
    public HSSJsonReulst<List<TopicUnitArticleListVO>> getTopicUnitArticleList(@RequestParam("topicId") Integer topicId) {
        return HSSJsonReulst.ok(iResourceReadArticleService.getTopicUnitArticleList(topicId));
    }

    @ApiOperation("保存主题关联的阅读文章")
    @PostMapping("/saveTopicUnitArticleList")
    public HSSJsonReulst saveTopicUnitArticleList(@RequestBody ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO) {
        return HSSJsonReulst.ok(iResourceReadArticleService.saveTopicUnitArticleList(resourceUnitTopicIdPostDTO));
    }

    @ApiOperation("AI生成文章")
    @PostMapping("/aiGenerateArticle")
    public HSSJsonReulst<ResourceReadArticleDTO> aiGenerateArticle(@RequestBody AIGenerateArticleDTO aiGenerateArticleDTO) {
        return HSSJsonReulst.ok(iResourceReadArticleService.aiGenerateArticle(aiGenerateArticleDTO));
    }

    @ApiOperation("AI全文解析")
    @PostMapping("/aiArticleAnalysis")
    public HSSJsonReulst<AnalysisResponse> aiArticleAnalysis(@RequestBody AIGenerateArticleDTO aiGenerateArticleDTO) {
        return HSSJsonReulst.ok(iResourceReadArticleService.aiArticleAnalysis(aiGenerateArticleDTO));
    }
    @ApiOperation("AI生成图片")
    @PostMapping("/aiGenerateImages")
    public HSSJsonReulst<String> aiGenerateImages(@RequestBody AIGenerateArticleDTO aiGenerateArticleDTO) {
        return HSSJsonReulst.ok(iResourceReadArticleService.aiGenerateImages(aiGenerateArticleDTO));
    }

    @ApiOperation("文章详情")
    @PostMapping("/edit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "articleId", value = "文章id", required = true)
    })
    public HSSJsonReulst<ResourceReadArticleNewDTO> edit(@RequestParam("articleId") Integer articleId) {
        return HSSJsonReulst.ok(iResourceReadArticleService.edit(articleId));
    }

    @ApiOperation("新增或编辑文章")
    @PostMapping("/saveOrUpdate")
    public HSSJsonReulst saveOrUpdate(@RequestBody ResourceReadArticleNewDTO resourceReadArticleDTO) {
        Map<String,Object> unit = unitService.getUnitById(resourceReadArticleDTO.getUnitId());
        Integer courseId = (Integer) unit.get("course_id");
        Map<String,Object> course = courseDao.getCourseById(courseId);
        Integer stage = (Integer) course.get("stage");
        if(stage == 2 || (stage == 3 && resourceReadArticleDTO.getSerialCode().equals("E"))){
            String sentenceList = resourceReadArticleDTO.getSentenceList();
            ResourceReadArticleCorrelationBean correlationBean = resourceReadArticleDTO.getCorrelationBean();
            if(sentenceList != null && correlationBean != null){
                String[] sentenceListSplit = sentenceList.split("\\|");
                String content = correlationBean.getContent();
                if(content != null){
                    List<String> correlationContentList = resourceReadArticleDTO.getCorrelationContentList();
                    if(correlationContentList != null){
                        List<String> collect = correlationContentList.stream().filter(item -> !item.equals("\n")).collect(Collectors.toList());
                        for(String s : sentenceListSplit){
                            if(!collect.contains(s+"\n") && !collect.contains(s)){
                                return HSSJsonReulst.errorMsg("选句填空的句子与句句对应中的句子不匹配，请修改："+s);
                            }
                        }
                    }
                    if(resourceReadArticleDTO.getCorrelationContentList() != null && resourceReadArticleDTO.getCorrelationTranslateList() !=null){
                        if(resourceReadArticleDTO.getCorrelationContentList().size() != resourceReadArticleDTO.getCorrelationTranslateList().size()){
                            return HSSJsonReulst.errorMsg("句句对应中英文和翻译句子数量不匹配，请修改");
                        }
                    }
                }
            }
        }
        iResourceReadArticleService.saveOrUpdate(resourceReadArticleDTO);
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("上传图片")
    @PostMapping(value = "/uploadPhoto")
    public HSSJsonReulst<Map<String, Object>> uploadPhoto(@RequestParam("unitId") Integer unitId,@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        return HSSJsonReulst.ok(iResourceReadArticleService.uploadPhoto(unitId, file, request));
    }

    @ApiOperation("句句对应-拆句")
    @PostMapping("/patternSplitContent")
    public HSSJsonReulst<ReadCorrelationDTO> patternSplitContent(@RequestBody ReadCorrelationDTO readCorrelationDTO) {
        return HSSJsonReulst.ok(iResourceReadArticleService.patternSplitContent(readCorrelationDTO));
    }

    @ApiOperation("句句对应-拆句V2")
    @PostMapping("/patternSplitContentV2")
    public HSSJsonReulst<ReadCorrelationDTO> patternSplitContentV2(@RequestBody ReadCorrelationDTO readCorrelationDTO) {
        return HSSJsonReulst.ok(iResourceReadArticleService.patternSplitContentV2(readCorrelationDTO));
    }

    @ApiOperation("导入文章")
    @PostMapping("/importExcel")
    public HSSJsonReulst importExcel(@RequestParam("file") MultipartFile file,
                                     @RequestParam("versionId") Integer versionId,
                                     @RequestParam("courseId") Integer courseId) {

        return HSSJsonReulst.ok(iResourceReadArticleService.importExcel(file,versionId,courseId,1));
    }


}
