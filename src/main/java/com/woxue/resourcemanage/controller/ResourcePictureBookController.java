package com.woxue.resourcemanage.controller;

import com.woxue.common.model.WordBean;
import com.woxue.common.model.redBook.PictureBook;
import com.woxue.common.model.redBook.PictureBookContent;
import com.woxue.common.model.redBook.PictureBookSentence;
import com.woxue.common.model.redBook.PictureBookWord;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.OSSManager;
import com.woxue.common.util.UtilControl;
import com.woxue.resourcemanage.service.ResourcePictureBookService;
import com.woxue.resourcemanage.util.wangsu.WangSuManager;
import com.woxue.resourcemanage.util.wangsu.model.WangSuResponse;
import com.woxue.resourceservice.util.PictureBookManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

@Api(tags = "绘本资源接口")
@RestController
@RequestMapping("/pictureBook")
public class ResourcePictureBookController {
    @Autowired
    private ResourcePictureBookService resourcePictureBookService;

    @ApiOperation("查询绘本资源")
    @PostMapping("/list")
    public HSSJsonReulst<List<PictureBook>> queryPictureBook() {
        return HSSJsonReulst.ok(resourcePictureBookService.getPictureBookList());
    }

    @ApiOperation("添加绘本")
    @PostMapping("/add")
    public HSSJsonReulst<Boolean> addPictureBook(PictureBook pictureBook) {
        return HSSJsonReulst.ok(resourcePictureBookService.insertPictureBook(pictureBook));
    }

    @ApiOperation("更新绘本")
    @PostMapping("/update")
    public HSSJsonReulst<Boolean> updatePictureBook(PictureBook pictureBook) {
        return HSSJsonReulst.ok(resourcePictureBookService.updatePictureBookById(pictureBook));
    }

    @ApiOperation("删除绘本")
    @PostMapping("/delete")
    public HSSJsonReulst<Boolean> deletePictureBook(@RequestParam("pictureBookId") Integer pictureBookId) {
        return HSSJsonReulst.ok(resourcePictureBookService.deletePictureBookById(pictureBookId));
    }

    @ApiOperation("查询绘本内容列表")
    @PostMapping("/contentList")
    public HSSJsonReulst<List<PictureBookContent>> queryPictureBookContentList(Integer pictureBookId) {
        return HSSJsonReulst.ok(resourcePictureBookService.getPictureBookContentList(pictureBookId));
    }

    @ApiOperation("添加绘本内容")
    @PostMapping("/addContent")
    public HSSJsonReulst<Boolean> addPictureBookContent(PictureBookContent pictureBookContent) {
        return HSSJsonReulst.ok(resourcePictureBookService.insertPictureBookContent(pictureBookContent));
    }

    @ApiOperation("更新绘本内容")
    @PostMapping("/updateContent")
    public HSSJsonReulst<Boolean> updatePictureBookContent(PictureBookContent pictureBookContent) {
        return HSSJsonReulst.ok(resourcePictureBookService.updatePictureBookContentById(pictureBookContent));
    }

    @ApiOperation("查询绘本句子列表")
    @PostMapping("/sentenceList")
    public HSSJsonReulst<List<PictureBookSentence>> queryPictureBookSentenceList(Integer pictureBookContentId) {
        return HSSJsonReulst.ok(resourcePictureBookService.getPictureBookSentenceList(pictureBookContentId));
    }

    @ApiOperation("添加绘本句子")
    @PostMapping("/addSentence")
    public HSSJsonReulst<Boolean> addPictureBookSentence(PictureBookSentence pictureBookSentence) {
        return HSSJsonReulst.ok(resourcePictureBookService.insertPictureBookSentence(pictureBookSentence));
    }

    @ApiOperation("删除绘本句子")
    @PostMapping("/deleteSentence")
    public HSSJsonReulst<Boolean> deleteSentence(Integer pictureBookSentenceId) {
        return HSSJsonReulst.ok(resourcePictureBookService.deletePictureBookSentence(pictureBookSentenceId));
    }

    @ApiOperation("更新绘本句子")
    @PostMapping("/updateSentence")
    public HSSJsonReulst<Boolean> updatePictureBookSentence(PictureBookSentence pictureBookSentence) {
        return HSSJsonReulst.ok(resourcePictureBookService.updatePictureBookSentenceById(pictureBookSentence));
    }

    @ApiOperation("查询绘本单词列表")
    @PostMapping("/wordList")
    @ApiImplicitParam(name = "pictureBookId", value = "绘本id", required = true)
    public HSSJsonReulst<List<PictureBookWord>> queryPictureBookWordList(Integer pictureBookId) {
        return HSSJsonReulst.ok(resourcePictureBookService.getWordListByPictureBookId(pictureBookId));
    }

    @ApiOperation("添加绘本单词")
    @PostMapping("/addWord")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pictureBookId", value = "绘本id", required = true),
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "syllable", value = "音节", required = true),
            @ApiImplicitParam(name = "meaning_en_US", value = "英文意思", required = true),
            @ApiImplicitParam(name = "meaning_zh_CN", value = "中文意思", required = true),
            @ApiImplicitParam(name = "example_en_US", value = "英文例句", required = true),
            @ApiImplicitParam(name = "example_zh_CN", value = "中文例句", required = true),
            @ApiImplicitParam(name = "imgUrl", value = "图片地址", required = true)
    })
    public HSSJsonReulst<Boolean> addPictureBookWord(Integer pictureBookId, String spelling, String syllable, String meaning_en_US, String meaning_zh_CN, String example_en_US, String example_zh_CN,String imgUrl) {
        return HSSJsonReulst.ok(resourcePictureBookService.insertPictureBookWord(pictureBookId,spelling,syllable,meaning_en_US,meaning_zh_CN,example_en_US,example_zh_CN,imgUrl));
    }

    @ApiOperation("更新绘本单词")
    @PostMapping("/updateWord")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wordId", value = "单词id", required = true),
            @ApiImplicitParam(name = "pictureBookId", value = "绘本id", required = true),
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "syllable", value = "音节", required = true),
            @ApiImplicitParam(name = "meaning_en_US", value = "英文意思", required = true),
            @ApiImplicitParam(name = "meaning_zh_CN", value = "中文意思", required = true),
            @ApiImplicitParam(name = "example_en_US", value = "英文例句", required = true),
            @ApiImplicitParam(name = "example_zh_CN", value = "中文例句", required = true),
            @ApiImplicitParam(name = "imgUrl", value = "图片地址", required = true)
    })
    public HSSJsonReulst<Boolean> updatePictureBookWord(Integer wordId, Integer pictureBookId, String spelling, String syllable, String meaning_en_US, String meaning_zh_CN, String example_en_US, String example_zh_CN,String imgUrl) {
        return HSSJsonReulst.ok(resourcePictureBookService.updatePictureBookWord(wordId, pictureBookId,spelling,syllable,meaning_en_US,meaning_zh_CN,example_en_US,example_zh_CN,imgUrl));
    }

    @ApiOperation("删除绘本单词")
    @PostMapping("/deleteWord")
    @ApiImplicitParam(name = "wordId", value = "单词id", required = true)
    public HSSJsonReulst<Boolean> deletePictureBookWord(Integer wordId) {
        return HSSJsonReulst.ok(resourcePictureBookService.deletePictureBookWord(wordId));
    }

    @ApiOperation("最后发布时间")
    @PostMapping("/getPublishTime")
    public HSSJsonReulst<Date> getPublishTime() {
        return HSSJsonReulst.ok(PictureBookManager.getUpdateDate());
    }

    @ApiOperation("发布绘本全部")
    @PostMapping("/publish/all")
    public HSSJsonReulst<Boolean> publishPictureBook() {
        return HSSJsonReulst.ok(resourcePictureBookService.publishPictureBook());
    }

    @ApiOperation("发布绘本单篇")
    @PostMapping("/publish")
    public HSSJsonReulst<Boolean> publishPictureBook(Integer bookId) {
        return HSSJsonReulst.ok(PictureBookManager.loadPictureBook(bookId));
    }

    @ApiOperation("文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", required = true),
            @ApiImplicitParam(name = "id", value = "id", required = true),
            @ApiImplicitParam(name = "type", value = "类型", required = true, example = "1:音频 2:图片")
    })
    @PostMapping("/pictureBookFileUpload")
    @ResponseBody
    public HSSJsonReulst<String> pictureBook(@RequestParam("file") MultipartFile file, @RequestParam("id") Integer pictureBookId, @RequestParam("type") Integer type) {
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");
        if (!file.isEmpty()) {
            String fileName = file.getOriginalFilename();
            String url = "";
            if (type == 1) {
                url = getSoundFilePath(pictureBookId, fileName);
            } else {
                url = getImgFilePath(pictureBookId, fileName);
            }
            try {
                fileStream = file.getInputStream();
                OSSManager.upload(OSSManager.SOUND_SOURCE_BUCKETNAME, url, fileStream);
                fileStream.close();
                return HSSJsonReulst.ok(url);
            } catch (IOException e) {
                e.printStackTrace();
                return HSSJsonReulst.errorMsg(e + "：导入失败！");
            } finally {
                WangSuManager.clearUrl(url);
            }
        }
        return HSSJsonReulst.ok("导入成功！");
    }

    public static String getSoundFilePath(Integer sentenceId, String fileName) {
        return "picture_book/" + sentenceId + "/mp3/" + fileName;
    }

    public static String getImgFilePath(Integer pictureBookId, String fileName) {
        return "picture_book/" + pictureBookId + "/img/" + fileName;
    }
}
