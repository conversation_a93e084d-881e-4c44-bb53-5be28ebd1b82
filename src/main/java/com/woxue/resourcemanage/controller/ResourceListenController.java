package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.dao.IResourceCourseDao;
import com.woxue.resourcemanage.entity.ResourceTopic;
import com.woxue.common.model.redBook.listen.ResourceUnitListenDTO;
import com.woxue.resourcemanage.entity.dto.read.ResourceUnitTopicIdPostDTO;
import com.woxue.resourcemanage.service.IResourceListenService;
import com.woxue.resourcemanage.service.IResourceUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 */
@Api(tags = "同步听力控制层")
@RestController
@RequestMapping("/resourceListen")
public class ResourceListenController {

    @Autowired
    IResourceListenService resourceListenService;
    @Autowired
    IResourceUnitService unitService;
    @Autowired
    IResourceCourseDao courseDao;

    @ApiOperation("单元列表")
    @PostMapping("/listByCourseId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true),
            @ApiImplicitParam(name = "pageNum",value = "当前页，从1开始",required = true),
            @ApiImplicitParam(name = "pageSize",value = "一页显示多少行",required = true)
    })
    public HSSJsonReulst listByCourseId(@RequestParam("courseId") Integer courseId, Integer pageNum, Integer pageSize) {

        return HSSJsonReulst.ok(resourceListenService.listByCourseId(courseId,(pageNum-1)*pageSize,pageSize));
    }

    @ApiOperation("主题列表（听力）")
    @PostMapping("/topicList")
    public HSSJsonReulst<ResourceTopic> topicList() {
        return HSSJsonReulst.ok(resourceListenService.topicList());
    }
    @ApiOperation("添加主题")
    @PostMapping("/insertTopic")
    public HSSJsonReulst insertTopic(@RequestBody ResourceTopic resourceTopicBean) {
        resourceTopicBean.setType("3");
        return HSSJsonReulst.ok(resourceListenService.insertTopic(resourceTopicBean));
    }
//    @ApiOperation("查询主题关联的听力")
//    @PostMapping("/getTopicUnitListenList")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "topicId", value = "主题id", required = true)
//    })
//    public HSSJsonReulst<List<TopicUnitArticleListVO>> getTopicUnitArticleList(@RequestParam("topicId") Integer topicId) {
//        return HSSJsonReulst.ok(iResourceReadArticleService.getTopicUnitArticleList(topicId));
//    }
//
    @ApiOperation("保存主题关联的单元听力")
    @PostMapping("/saveTopicUnitListenList")
    public HSSJsonReulst saveTopicUnitListenList(@RequestBody ResourceUnitTopicIdPostDTO resourceUnitTopicIdPostDTO) {
        return HSSJsonReulst.ok(resourceListenService.saveTopicUnitListenList(resourceUnitTopicIdPostDTO));
    }


    @ApiOperation("单元听力详情")
    @PostMapping("/edit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unitId", value = "单元id", required = true)
    })
    public HSSJsonReulst<ResourceUnitListenDTO> edit(@RequestParam("unitId") Integer unitId) {
        return HSSJsonReulst.ok(resourceListenService.edit(unitId));
    }

    @ApiOperation("新增或编辑")
    @PostMapping("/saveOrUpdate")
    public HSSJsonReulst saveOrUpdate(@RequestBody ResourceUnitListenDTO resourceUnitListenDTO) {
        resourceListenService.saveOrUpdate(resourceUnitListenDTO);
        return HSSJsonReulst.ok("操作成功！");
    }

    @ApiOperation("删除听力句子")
    @PostMapping("/deleteSentence")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "句子id", required = true)
    })
    public HSSJsonReulst<ResourceUnitListenDTO> deleteSentence(@RequestParam("id") Integer id) {
        return HSSJsonReulst.ok(resourceListenService.deleteSentence(id));
    }
    @ApiOperation("删除听力题目")
    @PostMapping("/deleteQuestion")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "题目id", required = true)
    })
    public HSSJsonReulst<ResourceUnitListenDTO> deleteQuestion(@RequestParam("questionId") Integer questionId) {
        return HSSJsonReulst.ok(resourceListenService.deleteQuestion(questionId));
    }

    @ApiOperation("上传图片")
    @PostMapping(value = "/uploadPhoto")
    public HSSJsonReulst<Map<String, Object>> uploadPhoto(@RequestParam("unitId") Integer unitId,@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        return HSSJsonReulst.ok(resourceListenService.uploadPhoto(unitId, file, request));
    }

    @ApiOperation("导入文章")
    @PostMapping("/importSentenceExcel")
    public HSSJsonReulst importExcel(@RequestParam("file") MultipartFile file,
                                     @RequestParam("courseId") Integer courseId) {

        return HSSJsonReulst.ok(resourceListenService.importExcel(file,courseId,1));
    }
    @ApiOperation("导入题目")
    @PostMapping("/importQuestionExcel")
    public HSSJsonReulst importQuestionExcel(@RequestParam("file") MultipartFile file,
                                     @RequestParam("courseId") Integer courseId) {

        return HSSJsonReulst.ok(resourceListenService.importQuestionExcel(file,courseId,1));
    }


}
