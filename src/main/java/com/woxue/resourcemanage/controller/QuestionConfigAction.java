package com.woxue.resourcemanage.controller;

import com.woxue.resourcemanage.util.QuestionConfigManager;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Controller
@RequestMapping("/questionConfig")
public class QuestionConfigAction {
	/**
	 * 获取区域列表
	 * @param parentId
	 * @return
	 */
	@PostMapping("/getAreaList")
	@ResponseBody
	public List<Map<String, Object>> getAreaList(Integer parentId){
		return QuestionConfigManager.getInstance().getAreaList(parentId);
	}
	
	/**
	 * 获取学段列表
	 * @return
	 */
	@PostMapping("/getGradePhaseList")
	@ResponseBody
	public List<Map<String, Object>> getGradePhaseList(){
		return QuestionConfigManager.getInstance().getGradePhaseList();
	}
	
	/**
	 * 获取年级列表
	 * @return
	 */
	@PostMapping("/getGradeList")
	@ResponseBody
	public List<Map<String, Object>> getGradeList(){
		return QuestionConfigManager.getInstance().getGradeList();
	}
	
	/**
	 * 获取试卷类型列表
	 * @return
	 */
	@PostMapping("/getPaperTypeList")
	@ResponseBody
	public List<Map<String, Object>> getPaperTypeList(){
		return QuestionConfigManager.getInstance().getPaperTypeList();
	}
	/**
	 * 获取知识点列表
	 * @return
	 */
	@PostMapping("/getKnowledgePointMap")
	@ResponseBody
	public Map<String, List<Map<String, Object>>> getKnowledgePointMap(){
		return QuestionConfigManager.getInstance().getKnowledgePointMap();
	}
	
	/**
	 * 获取试题类型列表
	 * @return
	 */
	@PostMapping("/getQuestionTypeList")
	@ResponseBody
	public List<Map<String, Object>> getQuestionTypeList(){
		return QuestionConfigManager.getInstance().getQuestionTypeList();
	}
	
	/**
	 * 获取试题难度列表
	 * @return
	 */
	@PostMapping("/getQuestionDifficultyList")
	@ResponseBody
	public List<Map<String, Object>> getQuestionDifficultyList(){
		return QuestionConfigManager.getInstance().getQuestionDifficultyList();
	}
	
	/**
	 * 获取题型与知识点的关联信息
	 * @return
	 */
	@PostMapping("/getQuestionKnowledgeMap")
	@ResponseBody
	public  Map<String, Object> getQuestionKnowledgeMap(){
		return QuestionConfigManager.getInstance().getQuestionKnowledgeMap();
	}
	
	/**
	 * 获取话题配置信息
	 * @return
	 */
	@PostMapping("/getQuestionTopicMap")
	@ResponseBody
	public  Map<String, Object> getQuestionTopicMap(){
		return QuestionConfigManager.getInstance().getQuestionTopicMap();
	}
	
	/**
	 * 获取考点配置信息
	 * @return
	 */
	@PostMapping("/getQuizPointMap")
	@ResponseBody
	public  Map<String, Object> getQuizPointMap(){
		return QuestionConfigManager.getInstance().getQuizPointMap();
	}
	
	/**
	 * 获取系统当前时间
	 * @return
	 */
	@PostMapping("/getSystemTime")
	@ResponseBody
	public Date getSystemTime(){
		return new Date();
	}
}
