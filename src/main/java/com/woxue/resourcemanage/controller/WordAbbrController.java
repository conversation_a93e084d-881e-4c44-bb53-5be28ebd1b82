package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.WordAbbr;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.vo.WordDisturbListVO;
import com.woxue.resourcemanage.service.IWordAbbrService;
import com.woxue.resourcemanage.util.wangsu.util.HttpUtils;
import com.woxue.resourceservice.util.RedBookCourseManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Api(tags = "单词缩写管理")
@RestController
@RequestMapping("/wordAbbr")
public class WordAbbrController {
    @Autowired
    IWordAbbrService iWordAbbrService;

    @ApiOperation("缩写列表")
    @PostMapping("/list")
    @ResponseBody
    public HSSJsonReulst<List> list() {
        return HSSJsonReulst.ok(iWordAbbrService.list());
    }

    @ApiOperation("添加缩写")
    @PostMapping("/add")
    @ResponseBody
    public HSSJsonReulst<Boolean> add(WordAbbr wordAbbr) {
        boolean b = iWordAbbrService.addWordAbbr(wordAbbr);
        if (b) {
            RedBookCourseManager.addWordAbbrList(iWordAbbrService.list());
        }
        return HSSJsonReulst.ok(b);
    }

    @ApiOperation("更新缩写")
    @PostMapping("/update")
    @ResponseBody
    public HSSJsonReulst<Boolean> update(WordAbbr wordAbbr) {
        boolean b = iWordAbbrService.updateWordAbbr(wordAbbr);
        if (b) {
            RedBookCourseManager.addWordAbbrList(iWordAbbrService.list());
        }
        return HSSJsonReulst.ok(b);
    }

}
