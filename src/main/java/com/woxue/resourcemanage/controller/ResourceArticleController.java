package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.IArticleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@Api(tags = "课文相关")
@RestController
@RequestMapping("/article")
public class ResourceArticleController {

    @Autowired
    IArticleService iArticleService;

    @ApiOperation("列表（每个单元包含文章列表）")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "courseId",value = "课程ID",required = true),
        @ApiImplicitParam(name = "pageNum",value = "当前页，从1开始",required = true),
        @ApiImplicitParam(name = "pageSize",value = "一页显示多少行",required = true)
    })
    @PostMapping("/list")
    @ResponseBody
    public HSSJsonReulst list(@RequestParam("courseId") Integer courseId,Integer pageNum, Integer pageSize){
        return HSSJsonReulst.ok(iArticleService.getUnitList(courseId , (pageNum-1)*pageSize,pageSize));
    }

    //导出课程的所有文章
    @ApiOperation("导出课程的所有文章")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @GetMapping("/export")
    public void export(@RequestParam("courseId") Integer courseId, HttpServletResponse response){
         iArticleService.export(courseId,response);
    }



}
