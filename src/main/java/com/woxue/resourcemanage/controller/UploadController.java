package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.OSSManager;
import com.woxue.common.util.UtilControl;
import com.woxue.resourcemanage.util.PropertiesUtils;
import com.woxue.resourcemanage.util.wangsu.WangSuManager;
import com.woxue.resourcemanage.util.wangsu.model.WangSuResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;


@Api(tags = "文件上传接口")
@RestController
@RequestMapping("/upload")
public class UploadController {

    @ApiOperation("课文音频文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file",value = "音频文件",required = true),
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @PostMapping("/articleSoundFile")
    @ResponseBody
    public HSSJsonReulst<Boolean> articleSoundFile(@RequestParam("file") MultipartFile file, @RequestParam("courseId") Integer courseId){
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");
        if(!file.isEmpty()){
            String url = PropertiesUtils.getProperty("redBook_article_sound")+"/"+courseId+"/"+file.getOriginalFilename();
            try {
                fileStream=file.getInputStream();
                OSSManager.upload(OSSManager.SOUND_SOURCE_BUCKETNAME, url,fileStream);
                fileStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                return HSSJsonReulst.errorMsg(e +"：导入失败！");
            }finally {
                WangSuResponse wangSuResponse = WangSuManager.clearUrl(url);
                if(wangSuResponse.getCode().intValue()!=1){
                    return HSSJsonReulst.ok("导入成功！缓存清除失败："+wangSuResponse.getMessage());
                }
            }
        }
        return HSSJsonReulst.ok("导入成功！");
    }


    @ApiOperation("同步听力音频文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file",value = "音频文件",required = true),
            @ApiImplicitParam(name = "courseId",value = "课程ID",required = true)
    })
    @PostMapping("/listenSoundFile")
    @ResponseBody
    public HSSJsonReulst<Boolean> listenSoundFile(@RequestParam("file") MultipartFile file, @RequestParam("courseId") Integer courseId){
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");
        if(!file.isEmpty()){
            String url = PropertiesUtils.getProperty("redBook_listen_sound")+"/"+courseId+"/"+file.getOriginalFilename();
            try {
                fileStream=file.getInputStream();
                OSSManager.upload(OSSManager.SOUND_SOURCE_BUCKETNAME, url,fileStream);
                fileStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                return HSSJsonReulst.errorMsg(e +"：导入失败！");
            }finally {
                WangSuResponse wangSuResponse = WangSuManager.clearUrl(url);
                if(wangSuResponse.getCode().intValue()!=1){
                    return HSSJsonReulst.ok("导入成功！缓存清除失败："+wangSuResponse.getMessage());
                }
            }
        }
        return HSSJsonReulst.ok("导入成功！");
    }

    @ApiOperation("单词音频文件上传")
    @ApiImplicitParams({
       @ApiImplicitParam(name = "file",value = "音频文件",required = true)
    })
    @PostMapping("/wordSoundFile")
    @ResponseBody
    public HSSJsonReulst<Boolean> wordSoundFile(@RequestParam("file") MultipartFile file){
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");
        if(!file.isEmpty()){
            String fileName = file.getOriginalFilename();
            fileName = fileName.substring(fileName.lastIndexOf("\\/")+1,fileName.lastIndexOf("\\.")).trim();
            String url = UtilControl.getWordSoundUrlNoDomainName(fileName,null);
            url = url.substring(1);
            try {
                fileStream=file.getInputStream();
                OSSManager.upload(OSSManager.SOUND_SOURCE_BUCKETNAME, url,fileStream);
                fileStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                return HSSJsonReulst.errorMsg(e +"：导入失败！");
            }finally {
                WangSuResponse wangSuResponse = WangSuManager.clearUrl(url);
                if(wangSuResponse.getCode().intValue()!=1){
                    return HSSJsonReulst.ok("导入成功！缓存清除失败："+wangSuResponse.getMessage());
                }
            }
        }
        return HSSJsonReulst.ok("导入成功！");
    }

    @ApiOperation("句子/例句音频文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file",value = "音频文件",required = true)
    })
    @PostMapping("/sentenceSoundFile")
    @ResponseBody
    public HSSJsonReulst<Boolean> sentenceSoundFile(@RequestParam("file") MultipartFile file){
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        dfi.setSizeThreshold(4194304);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(4194304);
        upload.setHeaderEncoding("UTF-8");
        if(!file.isEmpty()){
            String fileName = file.getOriginalFilename();
            fileName = fileName.substring(fileName.lastIndexOf("\\/")+1,fileName.lastIndexOf("\\.")).trim();
            String url = UtilControl.getSentenceSoundUrlNoDomainName(fileName);
            url = url.substring(1);
            try {
                fileStream=file.getInputStream();
                OSSManager.upload(OSSManager.SOUND_SOURCE_BUCKETNAME, url,fileStream);
                fileStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                return HSSJsonReulst.errorMsg(e +"：导入失败！");
            }finally {
                WangSuResponse wangSuResponse = WangSuManager.clearUrl(url);
                if(wangSuResponse.getCode().intValue()!=1){
                    return HSSJsonReulst.ok("导入成功！缓存清除失败："+wangSuResponse.getMessage());
                }
            }
        }
        return HSSJsonReulst.ok("导入成功！");
    }
}
