package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.spoken.ResourceSpokenTopic;
import com.woxue.common.model.redBook.spoken.ResourceSpokenTopicContent;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.common.util.OSSManager;
import com.woxue.resourcemanage.service.ISpokenService;
import com.woxue.resourcemanage.util.wangsu.WangSuManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Api(tags = "口语控制层")
@RestController
@RequestMapping("/resourceSpoken")
public class ResourceSpokenController {
    @Autowired
    ISpokenService iSpokenService;

    @PostMapping("/deleteTopic")
    @ApiOperation("删除话题")
    @ApiImplicitParam(name = "topicId", value = "topicId", required = true)
    public HSSJsonReulst<Boolean> deleteTopic(int topicId)
    {
        return HSSJsonReulst.ok(iSpokenService.deleteTopic(topicId)>0);
    }

    @PostMapping("/deleteTopicContent")
    @ApiOperation("删除话题内容")
    @ApiImplicitParam(name = "id", value = "id", required = true)
    public HSSJsonReulst<Boolean> deleteTopicContent(int id)
    {
        return HSSJsonReulst.ok(iSpokenService.deleteTopicContent(id)>0);
    }

    @GetMapping("/getAllResourceSpokenTopic")
    @ApiOperation("获取所有话题")
    public HSSJsonReulst<List<ResourceSpokenTopic>> getAllResourceSpokenTopic()
    {
        return HSSJsonReulst.ok(iSpokenService.getAllResourceSpokenTopic());
    }

    @GetMapping("/getResourceSpokenTopicContentByTopicId")
    @ApiOperation("获取话题内容")
    @ApiImplicitParam(name = "topicId", value = "topicId", required = true)
    public HSSJsonReulst<List<ResourceSpokenTopicContent>> getResourceSpokenTopicContentByTopicId(int topicId)
    {
        return HSSJsonReulst.ok(iSpokenService.getResourceSpokenTopicContentByTopicId(topicId));
    }

    @PostMapping("/insertTopic")
    @ApiOperation("插入话题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "话题名称", required = true),
            @ApiImplicitParam(name = "sense", value = "话题场景", required = true),
            @ApiImplicitParam(name = "cover", value = "话题封面", required = true),
            @ApiImplicitParam(name = "videoUrl", value = "视频地址", required = true),
            @ApiImplicitParam(name = "desc", value = "话题描述", required = true),
            @ApiImplicitParam(name = "order", value = "话题顺序", required = true)
    })
    public HSSJsonReulst<Boolean> insertTopic(String name,String sense,String cover,String videoUrl,String desc,Integer order)
    {
        return HSSJsonReulst.ok(iSpokenService.insertTopic(name,sense,cover,videoUrl,desc,order)>0);
    }

    @PostMapping("/updateTopic")
    @ApiOperation("更新话题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "话题id", required = true),
            @ApiImplicitParam(name = "name", value = "话题名称", required = true),
            @ApiImplicitParam(name = "sense", value = "话题场景", required = true),
            @ApiImplicitParam(name = "cover", value = "话题封面", required = true),
            @ApiImplicitParam(name = "videoUrl", value = "视频地址", required = true),
            @ApiImplicitParam(name = "desc", value = "话题描述", required = true),
            @ApiImplicitParam(name = "order", value = "话题顺序", required = true)
    })
    public HSSJsonReulst<Boolean> updateTopic(int id,String name,String sense,String cover,String videoUrl,String desc,Integer order)
    {
        return HSSJsonReulst.ok(iSpokenService.updateTopic(id,name,sense,cover,videoUrl,desc,order)>0);
    }

    @PostMapping("/insertTopicContent")
    @ApiOperation("插入话题内容")
    public HSSJsonReulst<Boolean> insertTopicContent(@RequestBody ResourceSpokenTopicContent resourceSpokenTopicContent)
    {
        return HSSJsonReulst.ok(iSpokenService.insertTopicContent(resourceSpokenTopicContent)>0);
    }


    @PostMapping("/updateTopicContent")
    @ApiOperation("更新话题内容")
    public HSSJsonReulst<Boolean> updateTopicContent(@RequestBody ResourceSpokenTopicContent resourceSpokenTopicContent)
    {
        return HSSJsonReulst.ok(iSpokenService.updateTopicContent(resourceSpokenTopicContent)>0);
    }

    @PostMapping("/publishTopic")
    @ApiOperation("发布话题")
    public HSSJsonReulst<Boolean> publishTopic()
    {
        return HSSJsonReulst.ok(iSpokenService.publishTopic());
    }


    @ApiOperation("文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", required = true),
            @ApiImplicitParam(name = "type", value = "文件类型 1：封面背景图 2：视频", required = true),
            @ApiImplicitParam(name = "topicId", value = "topicId", required = true),
    })
    @PostMapping("/FileUpload")
    @ResponseBody
    public HSSJsonReulst<String> pictureBook(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer type,@RequestParam("topicId") Integer topicId) {
        InputStream fileStream = null;
        DiskFileItemFactory dfi = new DiskFileItemFactory();
        int size=type==1?4194304:20971520;
        dfi.setSizeThreshold(size);
        ServletFileUpload upload = new ServletFileUpload(dfi);
        upload.setSizeMax(size);
        upload.setHeaderEncoding("UTF-8");
        if (!file.isEmpty()) {
            String fileName = file.getOriginalFilename();
            String url = "spoken/"+topicId+(type==1?"/cover/" : "/video/")+fileName;
            try {
                fileStream = file.getInputStream();
                OSSManager.upload(OSSManager.SOUND_SOURCE_BUCKETNAME, url, fileStream);
                fileStream.close();
                return HSSJsonReulst.ok("https://sound.hssenglish.com/"+url);
            } catch (IOException e) {
                e.printStackTrace();
                return HSSJsonReulst.errorMsg(e + "：导入失败！");
            } finally {
                WangSuManager.clearUrl(url);
            }
        }
        return HSSJsonReulst.ok("导入成功！");
    }

}
