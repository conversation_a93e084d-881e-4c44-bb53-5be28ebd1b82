package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.RedBookVersionStage;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.IResourceVersionService;
import com.woxue.resourceservice.util.RedBookCourseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021 -05-26 14:28
 */
@Controller
@RequestMapping("/resourceVersion")
public class ResourceVersionController {

    @Autowired
    IResourceVersionService resourceVersionService;

    /**
     * 根据学段获取版本列表
     * @param versionType     版本内容类型：1：同步  2：词汇  3：句子 4：语法  10：综合（包含两种及以上内容）11：拼读  12：音标 13：乐学  14：抢分王
     * @param stage    所属学段（1小学/2初中/3高中）
     * @param search
     * @param pageStart
     * @param pageSize
     * @return
     */
    @PostMapping("/getVersionList")
    @ResponseBody
    public HSSJsonReulst getVersionList(Integer versionType, Integer stage, String search, Integer pageStart, Integer pageSize) {
      return HSSJsonReulst.ok(resourceVersionService.getVersionList(versionType,stage,search,pageStart*pageSize,pageSize));
    }

    

    /**
     * 更换版本的排列顺序
     * @return
     */
    @PostMapping("/updateVersionDisplayOrder")
    @ResponseBody
    public HSSJsonReulst updateVersionDisplayOrder(@RequestParam("pageStart")Integer pageStart,
                                                   @RequestParam("pageSize")Integer pageSize,
                                                   @RequestParam("newIndex") Integer newIndex,
                                                   @RequestParam("newId") Integer newId,
                                                   @RequestParam("oldIndex")Integer oldIndex,
                                                   @RequestParam("oldId")Integer oldId) {
        boolean result = resourceVersionService.updateVersionDisplayOrder(pageStart, pageSize, newIndex + 1, newId, oldIndex + 1, oldId);
        if(result){
            //已发布,更新redis中版本信息
            if(RedBookCourseManager.getRedBookVersion(newId)!=null){
                RedBookCourseManager.updateVersionInfo(newId);
            }
            if(RedBookCourseManager.getRedBookVersion(oldId)!=null){
                RedBookCourseManager.updateVersionInfo(oldId);
            }
        }
        return  HSSJsonReulst.ok(result);
    }

    /**
     * 获取添加时 出现的关联其他学段对应版本 的内容
     * @return
     */
    @PostMapping("/getVersionTypeList")
    @ResponseBody
    public HSSJsonReulst getVersionTypeList(){
        return HSSJsonReulst.ok(resourceVersionService.getVersionTypeList());
    }

    /**
     * 添加版本信息
     * @param nameEn
     * @param nameCn
     * @param type
     * @param stage
     * @return
     */
    @PostMapping("/insertVersion")
    @ResponseBody
    public HSSJsonReulst insertVersion(String nameEn, String nameCn, Integer versionType, Integer type, Integer stage, Integer price, String briefIntroduction) {
        return  HSSJsonReulst.ok(resourceVersionService.insertVersion(nameEn,nameCn,versionType,type,stage,price,briefIntroduction));
    }

    /**
     * 修改时回显版本信息
     * @param id
     * @return
     */
    @PostMapping("/getVersionById")
    @ResponseBody
    public HSSJsonReulst getVersionById(Integer id) {
        return HSSJsonReulst.ok(resourceVersionService.getVersionById(id));
    }

    /**
     * 根据版本id修改版本信息
     * @param nameEn
     * @param nameCn
     * @param type
     * @param id
     * @return
     */
    @PostMapping("/updateVersionById")
    @ResponseBody
    public HSSJsonReulst updateVersionById(String nameEn, String nameCn, Integer versionType, Integer type, Integer price, String briefIntroduction, Integer id,Integer displayOrder) {
        boolean result = resourceVersionService.updateVersionById(nameEn, nameCn, versionType, type, price, briefIntroduction,displayOrder, id);
        if(result){
            //已发布,更新redis中版本信息
            if(RedBookCourseManager.getRedBookVersion(id)!=null){
                RedBookCourseManager.updateVersionInfo(id);
            }
        }
        return HSSJsonReulst.ok(result);
    }


    /**
     * 配置版本和其它学段的关联
     * @param
     * @return
     */
    @PostMapping("/insertVersionStage")
    @ResponseBody
    public HSSJsonReulst insertVersionStage(Integer versionId,Integer... stageList) {
        return HSSJsonReulst.ok(resourceVersionService.insertVersionStage(versionId,stageList));
    }

}
