package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.IHomonymService;
import com.woxue.resourceservice.util.HomonymManager;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/homonym")
@ApiOperation("同音词管理")
public class HomonymController {

    /**
     * CREATE TABLE `homonym` (
     * `id` int(4) NOT NULL AUTO_INCREMENT COMMENT '自增id',
     * `spelling` varchar(50) NOT NULL COMMENT '拼写',
     * `meaning` varchar(255) DEFAULT NULL COMMENT '解释',
     * PRIMARY KEY (`id`) USING BTREE,
     * UNIQUE KEY `spelling` (`spelling`) USING BTREE
     * ) ENGINE=InnoDB AUTO_INCREMENT=509 DEFAULT CHARSET=utf8;
     */

    @Autowired
    IHomonymService iHomonymService;

    @ApiOperation("同音词列表")
    @PostMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "pageNum", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = true)
    })
    public HSSJsonReulst list(String spelling, Integer pageNum, Integer pageSize) {
        List<Map> list = iHomonymService.list(spelling, (pageNum - 1) * pageSize, pageSize);
        Integer count = iHomonymService.count(spelling);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("list", list);
        hashMap.put("count", count);
        return HSSJsonReulst.ok(hashMap);
    }

    @ApiOperation("添加同音词")
    @PostMapping("/add")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "meaning", value = "解释", required = true)
    })
    public Boolean add(String spelling, String meaning) {
        Boolean add = iHomonymService.add(spelling, meaning);
        if (add){
            HomonymManager.clearHomonymList();
        }
        return add;
    }


    @ApiOperation("更新同音词")
    @PostMapping("/update")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true),
            @ApiImplicitParam(name = "meaning", value = "解释", required = true)
    })
    public Boolean update(String spelling, String meaning) {
        Boolean update = iHomonymService.update(spelling, meaning);
        if (update){
            HomonymManager.clearHomonymList();
        }
        return update;
    }

    @ApiOperation("删除同音词")
    @PostMapping("/delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "spelling", value = "拼写", required = true)
    })
    public Boolean delete(String spelling) {
        Boolean delete = iHomonymService.delete(spelling);
        if (delete){
            HomonymManager.clearHomonymList();
        }
        return delete;
    }


}
