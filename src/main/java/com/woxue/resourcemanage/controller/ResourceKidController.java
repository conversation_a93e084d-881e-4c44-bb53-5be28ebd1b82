package com.woxue.resourcemanage.controller;


import com.redbook.kid.common.model.*;
import com.redbook.kid.common.util.TencentCosUtil;
import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.service.ResourceKidService;
import com.woxue.resourceservice.util.KidResourceManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/resourceKid")
@Api(tags = "儿童资源相关接口")
public class ResourceKidController {


    @Autowired
    private ResourceKidService resourceKidService;

    // resource_kid_course相关接口
    @ApiOperation("获取课程列表")
    @GetMapping("/course/list")
    public HSSJsonReulst<List<ResourceKidCourse>> courseList() {
        List<ResourceKidCourse> list = resourceKidService.getAllCourses();
        return HSSJsonReulst.ok(list);
    }

    @ApiOperation("插入课程")
    @PostMapping("/course/add")
    @ApiImplicitParam(name = "course", value = "课程对象", required = true, dataType = "ResourceKidCourse")
    public HSSJsonReulst<Void> addCourse(@RequestBody ResourceKidCourse course) {
        resourceKidService.insertCourse(course);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新课程")
    @PostMapping("/course/update")
    @ApiImplicitParam(name = "course", value = "课程对象", required = true, dataType = "ResourceKidCourse")
    public HSSJsonReulst<Void> updateCourse(@RequestBody ResourceKidCourse course) {
        resourceKidService.updateCourse(course);
        return HSSJsonReulst.ok();
    }

//    @ApiOperation("删除课程")
//    @PostMapping("/course/delete")
//    @ApiImplicitParam(name = "id", value = "课程ID", required = true)
//    public HSSJsonReulst<Void> deleteCourse(@RequestParam int id) {
//        resourceKidService.deleteCourse(id);
//        return HSSJsonReulst.ok();
//    }

    // resource_kid_picture_book相关接口
    @ApiOperation("获取绘本列表")
    @GetMapping("/pictureBook")
    public HSSJsonReulst<ResourceKidPictureBook> pictureBookList(Integer unitId) {
        return HSSJsonReulst.ok(resourceKidService.getPictureBookByUnitId(unitId));
    }

    @ApiOperation("插入绘本")
    @PostMapping("/pictureBook/add")
    @ApiImplicitParam(name = "pictureBook", value = "绘本对象", required = true, dataType = "ResourceKidPictureBook")
    public HSSJsonReulst<Void> addPictureBook(@RequestBody ResourceKidPictureBook pictureBook) {
        resourceKidService.insertPictureBook(pictureBook);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新绘本")
    @PostMapping("/pictureBook/update")
    @ApiImplicitParam(name = "pictureBook", value = "绘本对象", required = true, dataType = "ResourceKidPictureBook")
    public HSSJsonReulst<Void> updatePictureBook(@RequestBody ResourceKidPictureBook pictureBook) {
        resourceKidService.updatePictureBook(pictureBook);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除绘本")
    @PostMapping("/pictureBook/delete")
    @ApiImplicitParam(name = "id", value = "绘本ID", required = true)
    public HSSJsonReulst<Void> deletePictureBook(@RequestParam int id) {
        resourceKidService.deletePictureBook(id);
        return HSSJsonReulst.ok();
    }


    @ApiOperation("插入绘本内容")
    @PostMapping("/pictureBookContent/add")
    @ApiImplicitParam(name = "content", value = "绘本内容对象", required = true, dataType = "ResourceKidPictureBookContent")
    public HSSJsonReulst<Void> addPictureBookContent(@RequestBody ResourceKidPictureBookContent content) {
        resourceKidService.insertPictureBookContent(content);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新绘本内容")
    @PostMapping("/pictureBookContent/update")
    @ApiImplicitParam(name = "content", value = "绘本内容对象", required = true, dataType = "ResourceKidPictureBookContent")
    public HSSJsonReulst<Void> updatePictureBookContent(@RequestBody ResourceKidPictureBookContent content) {
        resourceKidService.updatePictureBookContent(content);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除绘本内容")
    @PostMapping("/pictureBookContent/delete")
    @ApiImplicitParam(name = "id", value = "绘本内容ID", required = true)
    public HSSJsonReulst<Void> deletePictureBookContent(@RequestParam int id) {
        resourceKidService.deletePictureBookContent(id);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("插入绘本句子")
    @PostMapping("/pictureBookSentence/add")
    @ApiImplicitParam(name = "sentence", value = "绘本句子对象", required = true, dataType = "ResourceKidPictureBookSentence")
    public HSSJsonReulst<Void> addPictureBookSentence(@RequestBody ResourceKidPictureBookSentence sentence) {
        resourceKidService.insertPictureBookSentence(sentence);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新绘本句子")
    @PostMapping("/pictureBookSentence/update")
    @ApiImplicitParam(name = "sentence", value = "绘本句子对象", required = true, dataType = "ResourceKidPictureBookSentence")
    public HSSJsonReulst<Void> updatePictureBookSentence(@RequestBody ResourceKidPictureBookSentence sentence) {
        resourceKidService.updatePictureBookSentence(sentence);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除绘本句子")
    @PostMapping("/pictureBookSentence/delete")
    @ApiImplicitParam(name = "id", value = "绘本句子ID", required = true)
    public HSSJsonReulst<Void> deletePictureBookSentence(@RequestParam int id) {
        resourceKidService.deletePictureBookSentence(id);
        return HSSJsonReulst.ok();
    }

    // resource_kid_scene相关接口
    @ApiOperation("获取场景列表")
    @GetMapping("/scene/list")
    public HSSJsonReulst<List<ResourceKidScene>> sceneList(@RequestParam Integer courseId) {
        List<ResourceKidScene> list = resourceKidService.getSceneByCourseId(courseId);
        return HSSJsonReulst.ok(list);
    }

    @ApiOperation("插入场景")
    @PostMapping("/scene/add")
    @ApiImplicitParam(name = "scene", value = "场景对象", required = true, dataType = "ResourceKidScene")
    public HSSJsonReulst<Void> addScene(@RequestBody ResourceKidScene scene) {
        resourceKidService.insertScene(scene);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新场景")
    @PostMapping("/scene/update")
    @ApiImplicitParam(name = "scene", value = "场景对象", required = true, dataType = "ResourceKidScene")
    public HSSJsonReulst<Void> updateScene(@RequestBody ResourceKidScene scene) {
        resourceKidService.updateScene(scene);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除场景")
    @PostMapping("/scene/delete")
    @ApiImplicitParam(name = "id", value = "场景ID", required = true)
    public HSSJsonReulst<Void> deleteScene(@RequestParam int id) {
        resourceKidService.deleteScene(id);
        return HSSJsonReulst.ok();
    }

    // resource_kid_unit相关接口
    @ApiOperation("获取单元列表")
    @GetMapping("/unit/list")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true)
    public HSSJsonReulst<List<ResourceKidUnit>> unitList(Integer courseId) {
        List<ResourceKidUnit> list = resourceKidService.getUnitByCourseId(courseId);
        return HSSJsonReulst.ok(list);
    }

    @ApiOperation("插入单元")
    @PostMapping("/unit/add")
    @ApiImplicitParam(name = "unit", value = "单元对象", required = true, dataType = "ResourceKidUnit")
    public HSSJsonReulst<Void> addUnit(@RequestBody ResourceKidUnit unit) {
        resourceKidService.insertUnit(unit);
        return HSSJsonReulst.ok();
    }

    //批量创建单元
    @ApiOperation("批量创建单元")
    @PostMapping("/unit/add/batch")
    public HSSJsonReulst<Void> addUnitBatch(@RequestBody List<ResourceKidUnit> units) {
        units.forEach(unit -> resourceKidService.insertUnit(unit));
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新单元")
    @PostMapping("/unit/update")
    @ApiImplicitParam(name = "unit", value = "单元对象", required = true, dataType = "ResourceKidUnit")
    public HSSJsonReulst<Void> updateUnit(@RequestBody ResourceKidUnit unit) {
        resourceKidService.updateUnit(unit);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除单元")
    @PostMapping("/unit/delete")
    @ApiImplicitParam(name = "id", value = "单元ID", required = true)
    public HSSJsonReulst<Void> deleteUnit(@RequestParam int id) {
        resourceKidService.deleteUnit(id);
        return HSSJsonReulst.ok();
    }


    @ApiOperation("插入视频")
    @PostMapping("/video/add")
    @ApiImplicitParam(name = "video", value = "视频对象", required = true, dataType = "ResourceKidVideo")
    public HSSJsonReulst<Void> addVideo(@RequestBody ResourceKidVideo video) {
        resourceKidService.insertVideo(video);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新视频")
    @PostMapping("/video/update")
    @ApiImplicitParam(name = "video", value = "视频对象", required = true, dataType = "ResourceKidVideo")
    public HSSJsonReulst<Void> updateVideo(@RequestBody ResourceKidVideo video) {
        resourceKidService.updateVideo(video);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除视频")
    @PostMapping("/video/delete")
    @ApiImplicitParam(name = "id", value = "视频ID", required = true)
    public HSSJsonReulst<Void> deleteVideo(@RequestParam int id) {
        resourceKidService.deleteVideo(id);
        return HSSJsonReulst.ok();
    }

    // resource_kid_word相关接口
    @ApiOperation("获取单词列表")
    @GetMapping("/word/list")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true)
    public HSSJsonReulst<List<ResourceKidWord>> wordList(@RequestParam Integer unitId) {
        List<ResourceKidWord> list = resourceKidService.getWordByUnitId(unitId);
        return HSSJsonReulst.ok(list);
    }

    @ApiOperation("插入单词")
    @PostMapping("/word/add")
    @ApiImplicitParam(name = "word", value = "单词对象", required = true, dataType = "ResourceKidWord")
    public HSSJsonReulst<Void> addWord(@RequestBody ResourceKidWord word) {
        resourceKidService.insertWord(word);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("更新单词")
    @PostMapping("/word/update")
    @ApiImplicitParam(name = "word", value = "单词对象", required = true, dataType = "ResourceKidWord")
    public HSSJsonReulst<Void> updateWord(@RequestBody ResourceKidWord word) {
        resourceKidService.updateWord(word);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除单词")
    @PostMapping("/word/delete")
    @ApiImplicitParam(name = "id", value = "单词ID", required = true)
    public HSSJsonReulst<Void> deleteWord(@RequestParam int id) {
        resourceKidService.deleteWord(id);
        return HSSJsonReulst.ok();
    }

    // 根据单元ID获取单词
    @ApiOperation("根据单元ID获取单词")
    @GetMapping("/unit/{unitId}/word")
    @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, paramType = "path")
    public HSSJsonReulst<List<ResourceKidWord>> getWordByUnitId(@PathVariable int unitId) {
        List<ResourceKidWord> words = resourceKidService.getWordByUnitId(unitId);
        return HSSJsonReulst.ok(words);
    }

    public static final String PATH_SUFFIX = "/resource";

    //文件批量上传
    @ApiOperation("同步课程文件批量上传")
    @PostMapping("/upload/batch")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "上传的文件列表", required = true, dataType = "MultipartFile"),
            @ApiImplicitParam(name = "type", value = "文件类型（例如：1 表示图片，2 表示音频 3视频）", required = true, dataType = "Integer")
    })
    public HSSJsonReulst<List<String>> uploadBatch(@RequestParam("files") MultipartFile[] files, @RequestParam int type) {
        List<String> filePathList = new ArrayList<>();
        for (MultipartFile mf : files) {
            if (!mf.isEmpty()) {
                try {
                    String fileName = mf.getOriginalFilename();
                    String filePath;
                    switch (type) {
                        case 1:
                            filePath = ResourceKidController.PATH_SUFFIX + "/picture/" + fileName;
                            break;
                        case 2:
                            filePath = ResourceKidController.PATH_SUFFIX + "/sound/" + fileName;
                            break;
                        case 3:
                            filePath = ResourceKidController.PATH_SUFFIX + "/video/" + fileName;
                            break;
                        default:
                            filePath = ResourceKidController.PATH_SUFFIX + "/other/" + fileName;
                            break;
                    }
                    URL url = TencentCosUtil.uploadStream(filePath, mf.getInputStream());
                    filePathList.add(url.toString());
                    System.out.println(url);
                    //获取文件名
                } catch (Exception e) {
                    return HSSJsonReulst.errorMsg("上传失败");
                }
            }
        }
        return HSSJsonReulst.ok(filePathList);
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "上传的文件", required = true, dataType = "MultipartFile"),
            @ApiImplicitParam(name = "type", value = "文件类型（例如：1 表示图片，2 表示音频 3视频）", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "unitId", value = "单元ID", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "wordId", value = "单词ID", required = false, dataType = "Integer")
    })
    public HSSJsonReulst<URL> upload(@RequestParam("file") MultipartFile file, @RequestParam int type, @RequestParam(required = false) Integer unitId, @RequestParam(required = false) Integer wordId) {
        if (!file.isEmpty()) {
            try {
                String fileName = file.getOriginalFilename();
                String filePath;
                switch (type) {
                    case 1:
                        filePath = ResourceKidController.PATH_SUFFIX + "/picture/";
                        break;
                    case 2:
                        filePath = ResourceKidController.PATH_SUFFIX + "/sound/";
                        break;
                    case 3:
                        filePath = ResourceKidController.PATH_SUFFIX + "/video/";
                        break;
                    default:
                        filePath = ResourceKidController.PATH_SUFFIX + "/other/";
                        break;
                }
                if (wordId != null) {
                    filePath += "word" + wordId + "/";
                }
                filePath += fileName;
                URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
                return HSSJsonReulst.ok(url);
            } catch (Exception e) {
                return HSSJsonReulst.errorMsg("上传失败");
            }
        }
        return HSSJsonReulst.ok(null);
    }

    @ApiOperation("文件上传字母")
    @PostMapping("/upload/letter")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "上传的文件", required = true, dataType = "MultipartFile")
    })
    public HSSJsonReulst<URL> upload(@RequestParam("file") MultipartFile file) {
        if (!file.isEmpty()) {
            try {
                String fileName = file.getOriginalFilename();
                String filePath="/resource/letter/";
                filePath += fileName;
                URL url = TencentCosUtil.uploadStream(filePath, file.getInputStream());
                return HSSJsonReulst.ok(url);
            } catch (Exception e) {
                return HSSJsonReulst.errorMsg("上传失败");
            }
        }
        return HSSJsonReulst.ok(null);
    }


    @ApiOperation("获取字母单元列表")
    @GetMapping("/letterUnit/list")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, dataType = "Integer")
    public HSSJsonReulst<List<KidLetterUnit>> letterUnitList(@RequestParam int courseId) {
        List<KidLetterUnit> kidLetterUnits = resourceKidService.getLetterUnitVOByCourseId(courseId);
        return HSSJsonReulst.ok(kidLetterUnits);
    }

    @ApiOperation("添加字母")
    @PostMapping("/letterUnit/add")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "letter", value = "字母", required = true, dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "单元ID", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = false, dataType = "Integer")
    })
    public HSSJsonReulst<Void> addLetterUnit(@RequestParam String letter, @RequestParam(required = false) Integer unitId, @RequestParam(required = false) Integer courseId) {
        resourceKidService.addLetterUnit(letter, unitId, courseId);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("修改字母")
    @PostMapping("/letterUnit/update")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "letter", value = "字母", required = true, dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "单元ID", required = false, dataType = "Integer")
    })
    public HSSJsonReulst<Void> updateLetterUnit(@RequestParam String letter, @RequestParam Integer unitId) {
        resourceKidService.updateLetterUnit(letter, unitId);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("添加字母例句")
    @PostMapping("/letterExample/add")
    public HSSJsonReulst<Void> addLetterExample(@RequestBody ResourceKidLetterExample kidLetterExample) {
        resourceKidService.addLetterExample(kidLetterExample);
        return HSSJsonReulst.ok();
    }


    @ApiOperation("修改字母例句")
    @PostMapping("/letterExample/update")
    public HSSJsonReulst<Void> updateLetterExample(@RequestBody ResourceKidLetterExample kidLetterExample) {
        resourceKidService.updateLetterExample(kidLetterExample);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("删除字母例句")
    @PostMapping("/letterExample/delete")
    public HSSJsonReulst<Void> deleteLetterExample(@RequestParam Integer id) {
        resourceKidService.deleteLetterExample(id);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("单元设置为上/下线")
    @PostMapping("/unit/setStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unitId", value = "单元ID", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "onLine", value = "是否上/下线", required = true, dataType = "Boolean")
    })
    public HSSJsonReulst<Void> setUnitStatus(@RequestParam Integer unitId, @RequestParam Boolean onLine) {
        resourceKidService.setUnitOnLine(unitId, onLine);
        return HSSJsonReulst.ok();
    }

    @ApiOperation("课程发布")
    @PostMapping("/course/publish")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, dataType = "Integer")
    })
    public HSSJsonReulst<Void> publishCourse(@RequestParam Integer courseId) {
        KidResourceManager.publishKidResource(courseId);
        return HSSJsonReulst.ok();
    }
}