package com.woxue.resourcemanage.controller;

import com.woxue.common.model.redBook.RedBookCourseStage;import com.woxue.common.model.redBook.RedBookVersionStage;import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.vo.RedBookCourseVO;
import com.woxue.resourcemanage.service.IResourceCourseService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -05-27 13:49
 */
@Controller
@RequestMapping("/resourceCourse")
public class ResourceCourseController {
    @Autowired
    IResourceCourseService resourceCourseService;

    @ApiOperation("获取课程列表")
    @PostMapping("/getCourseList")
    @ResponseBody
    public HSSJsonReulst<List<RedBookCourseVO>> getCourseList(Integer versionId,Integer stage) {
        return HSSJsonReulst.ok(resourceCourseService.getCourseList(versionId,stage));
    }


    /**insertVersion
     * 添加课程信息
     * @param versionId
     * @param nameEn
     * @param nameCn
     * @param stage
     * @param grade
     * @return
     */
    @PostMapping("/insertCourse")
    @ResponseBody
    public HSSJsonReulst insertCourse(Integer versionId, String nameEn, String nameCn, Integer stage, Integer grade, String paramString, Integer resourceCourseId) {
        return HSSJsonReulst.ok(resourceCourseService.insertCourse(versionId,nameEn,nameCn,stage,grade,paramString,resourceCourseId));
    }

    /**
     * 修改回显获取课程信息
     * @param id
     * @return
     */
    @PostMapping("/getCourseById")
    @ResponseBody
    public HSSJsonReulst getCourseById(Integer id) {
        Map<String, Object> courseById = resourceCourseService.getCourseById(id);
        return HSSJsonReulst.ok(courseById);
    }

    /**
     * 修改课程信息
     * @param nameEn
     * @param nameCn
     * @param grade
     * @param id
     * @return
     */
    @PostMapping("/updateCourse")
    @ResponseBody
    public HSSJsonReulst updateCourse(String nameEn, String nameCn, Integer grade, Integer id) {
        return HSSJsonReulst.ok(resourceCourseService.updateCourse(nameEn,nameCn,grade,id));
    }


    /**
     * 跳转到单元页面后获取课程关联的信息
     * @param courseId
     * @return
     */
    @PostMapping("/getCourseRelevant")
    @ResponseBody
    public HSSJsonReulst getCourseRelevant(Integer courseId){
        return HSSJsonReulst.ok(resourceCourseService.getCourseRelevant(courseId));
    }



    /**
     * 配置课程和其它学段的关联
     * @param
     * @return
     */
    @PostMapping("/insertCourseStage")
    @ResponseBody
    public HSSJsonReulst insertCourseStage(Integer courseId,Integer... stageList) {
        return HSSJsonReulst.ok(resourceCourseService.insertCourseStage(courseId,stageList));
    }
}
