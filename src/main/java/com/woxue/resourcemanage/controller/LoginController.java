package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.AdminBean;
import com.woxue.resourcemanage.service.IAdminService;
import com.woxue.resourcemanage.util.AdminManager;
import com.woxue.resourcemanage.util.ProfileUtils;
import com.woxue.resourcemanage.util.ResourceManageJwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022 -05-09 16:29
 */
@Api(tags = "登录模块")
@RestController
@RequestMapping
public class LoginController {
    @Autowired
    HttpServletRequest request;
    @Autowired
    private IAdminService adminService;

    /**
     * 登录
     * @param name    账号
     * @param password   密码
     * @param response   响应
     * @return
     */
    @ApiOperation(value = "用户登录管理")
    @PostMapping("/login")
    public HSSJsonReulst login(@RequestParam("loginId") String name,@RequestParam("password") String password, HttpServletResponse response) {

        AdminBean sessionAdmin = adminService.getUser(name, password);
        if (sessionAdmin != null) {
            //生成token
            String token = ResourceManageJwtUtil.createToken(sessionAdmin.getName(), sessionAdmin.getRole().toString());
            //修改登录
            sessionAdmin.setToken(token);
            AdminManager.saveToken(sessionAdmin);
            //写入token到相应头信息
            response.addHeader("Authorization",token);
            return HSSJsonReulst.ok(sessionAdmin);
        } else {
            return HSSJsonReulst.errorMsg("账号/密码错误，登录失败");
        }
    }


    /**
     * 退出登录
     * @return
     */
    @PostMapping("/clearLogin")
    @ApiOperation(value = "用户退出登录")
    @ResponseBody
    public HSSJsonReulst clearLogin(){
        Object userName = request.getAttribute("userName");
        if(userName==null){
            return HSSJsonReulst.ok(false);
        }
        AdminManager.delToken(userName.toString());
        return HSSJsonReulst.ok(true);
    }

}
