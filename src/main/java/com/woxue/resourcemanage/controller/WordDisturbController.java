package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.vo.WordDisturbListVO;
import com.woxue.resourcemanage.service.IWordDisturbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "干扰项管理")
@RestController
@RequestMapping("/wordDisturb")
public class WordDisturbController {
    @Autowired
    IWordDisturbService iWordDisturbService;

    @ApiOperation("干扰项列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "spelling",value = "单词拼写",required = false),
        @ApiImplicitParam(name = "pageNum",value = "当前页，从1开始",required = true),
        @ApiImplicitParam(name = "pageSize",value = "一页显示多少行",required = true)
    })
    @PostMapping("/list")
    @ResponseBody
    public HSSJsonReulst<WordDisturbListVO> list(String spelling, Integer pageNum, Integer pageSize){
        return HSSJsonReulst.ok(iWordDisturbService.list(spelling,(pageNum-1)*pageSize,pageSize));
    }

    @ApiOperation("更换干扰项")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "spelling",value = "单词拼写",required = true),
        @ApiImplicitParam(name = "wordId",value = "要更换的单词id",required = true)
    })
    @PostMapping("/change")
    @ResponseBody
    public HSSJsonReulst<Boolean> change(@RequestParam String spelling, @RequestParam Integer wordId){
        return HSSJsonReulst.ok(iWordDisturbService.change(spelling,wordId));
    }
}
