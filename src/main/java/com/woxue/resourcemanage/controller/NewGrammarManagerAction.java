package com.woxue.resourcemanage.controller;

import com.woxue.common.util.HSSJsonReulst;
import com.woxue.resourcemanage.entity.AdminBean;
import com.woxue.resourcemanage.entity.grammar.*;
import com.woxue.resourcemanage.service.INewGrammarManagerService;
import com.woxue.resourcemanage.util.SysUserUtils;
import com.woxue.resourceservice.util.RedisManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022 -02-23 10:28
 */
@Controller
@RequestMapping("/newGrammarManager")
@Api(tags = "语法")
public class NewGrammarManagerAction {
    @Autowired
    INewGrammarManagerService newGrammarManagerService;
    @Autowired
    private HttpSession session;
    private final String COPIED_ID_PREFIX = "newGrammar:copiedIdArr:";
    private final String COPIED_UNITID_PREFIX = "newGrammar:copiedUnitId:";
    @Autowired
    HttpServletRequest request;


    /**
     * 获取课程下的试题列表
     *
     * @param courseId
     * @return
     */
    @PostMapping("/getCourseQuestionList")
    @ResponseBody
    public Map<String, Object> getCourseQuestionList(Integer courseId, Integer pageNum, Integer pageSize) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<SimulationQuestionBean> courseQuestionList = newGrammarManagerService.getCourseQuestionList(courseId, pageNum, pageSize);
        map.put("courseQuestionList", courseQuestionList);
        if (pageNum == 1) {
            List<Map<String, Object>> questionTypeList = newGrammarManagerService.getCourseQuestionNum(courseId);
            map.put("questionTypeList", questionTypeList);
        }
        return map;
    }

    /**
     * 获取课程下的语法单元列表
     *
     * @param courseId
     * @return
     */
    @PostMapping("/getUnitList")
    @ApiOperation("获取课程下的语法单元列表")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程ID", required = true)
    })
    public HSSJsonReulst<List<GrammarUnitBean>> getUnitList(@RequestParam("courseId") Integer courseId) {
        return HSSJsonReulst.ok(newGrammarManagerService.getUnitList(courseId));
    }

    /**
     * 添加语法单元
     *
     * @param unitId
     * @return
     */
    @PostMapping("/addGrammarUnit")
    @ApiOperation("添加语法单元")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unitId", value = "单元Id", required = true)
    })
    public HSSJsonReulst<Boolean> addGrammarUnit(@RequestParam("unitId") Integer unitId) {
        return HSSJsonReulst.ok(newGrammarManagerService.addGrammarUnit(unitId));
    }


    /**
     * 获取课程全名
     *
     * @param courseId
     * @return
     */
    @PostMapping("/getCourseFullNameById")
    @ResponseBody
    public Map<String, Object> getCourseFullNameById(Integer courseId) {
        return newGrammarManagerService.getCourseFullNameById(courseId);
    }


    /**
     * 添加试题
     *
     * @param question
     * @return
     */
    @PostMapping("/addCourseQuestionList")
    @ResponseBody
    public boolean addCourseQuestionList(@RequestBody SimulationQuestionBean question) {
        Integer minQuestionId = newGrammarManagerService.getCourseQuestionIdMinNum(question.getCourseId());
        question.setQuestionId(--minQuestionId);
        List<SimulationQuestionBean> list = new ArrayList<SimulationQuestionBean>();
        list.add(question);
        return newGrammarManagerService.addCourseQuestionList(list);
    }

    /**
     * 修改试题
     *
     * @param question
     * @return
     */
    @PostMapping("/updateCourseQuestion")
    @ResponseBody
    public boolean updateCourseQuestion(@RequestBody SimulationQuestionBean question) {
        return newGrammarManagerService.updateCourseQuestion(question);
    }

    /**
     * 批量删除课程下面的试题
     *
     * @param deleteIdArr
     * @return
     */
    @PostMapping("/deleteCourseQuestion")
    @ResponseBody
    public boolean deleteCourseQuestion(@RequestParam("courseId") Integer courseId,
                                        @RequestParam("deleteIdArr[]") Integer[] deleteIdArr) {
        return newGrammarManagerService.deleteCourseQuestion(/*user.getUserName()*/"", courseId, deleteIdArr);
    }


    /**
     * 获取满足条件的语法题库试题
     *
     * @return
     */
    @PostMapping("/getSimulationQuestionList")
    @ResponseBody
    public Map<String, Object> getSimulationQuestionList(@RequestParam(value = "sourceTypeArr[]", required = false) Integer[] sourceTypeArr, Integer gradePhase,
                                                         @RequestParam(value = "gradeArr[]", required = false) Integer[] gradeArr, @RequestParam(value = "knowledgePointArr[]", required = false) String[] knowledgePointArr,
                                                         @RequestParam(value = "questionTypeArr[]", required = false) Integer[] questionTypeArr, @RequestParam(value = "difficultyArr[]", required = false) Integer[] difficultyArr,
                                                         String orderBy, Integer pageNum, Integer pageSize) {
        Map<String, Object> map = newGrammarManagerService.getSimulationQuestionList("system",/*user.getUserName()*/"", sourceTypeArr, gradePhase,
                gradeArr, knowledgePointArr, questionTypeArr, difficultyArr, orderBy, pageNum, pageSize);
        return map;
    }

    /**
     * 添加所有本章检测试题
     *
     * @return
     */
    @PostMapping("/addAllCourseQuestion")
    @ResponseBody
    public boolean addAllCourseQuestion(Integer courseId, @RequestParam(value = "sourceTypeArr[]", required = false) Integer[] sourceTypeArr, Integer gradePhase,
                                        @RequestParam(value = "gradeArr[]", required = false) Integer[] gradeArr, @RequestParam(value = "knowledgePointArr[]", required = false) String[] knowledgePointArr,
                                        @RequestParam(value = "questionTypeArr[]", required = false) Integer[] questionTypeArr, @RequestParam(value = "difficultyArr[]", required = false) Integer[] difficultyArr) {
        return newGrammarManagerService.addAllCourseQuestion("system",/*user.getUserName()*/"", courseId, sourceTypeArr, gradePhase,
                gradeArr, knowledgePointArr, questionTypeArr, difficultyArr);
    }

    /**
     * 添加选中的本章检测试题
     *
     * @return
     */
    @PostMapping("/addSelectCourseQuestion")
    @ResponseBody
    public boolean addSelectCourseQuestion(Integer courseId, @RequestParam("questionIdArr[]") Integer[] questionIdArr) {
        return newGrammarManagerService.addSelectCourseQuestion(/*user.getUserName()*/"", courseId, questionIdArr);
    }


    /**
     * 获取语法卡片的详细信息
     *
     * @param unitId
     * @return
     */
    @PostMapping("/getGrammarCardDetail")
    @ResponseBody
    public GrammarNewUnitBean getGrammarCardDetail(Integer unitId) {
        return newGrammarManagerService.getGrammarCardDetail(unitId);
    }

    @PostMapping("/deleteContent")
    @ResponseBody
    public boolean deleteContent(Integer unitId,Integer questionType,Integer contentId) {
        return newGrammarManagerService.deleteContent(unitId,questionType,contentId);
    }

    /**
     * 保存复制内容的id列表
     *
     * @param copiedIdArr
     * @return
     */
    @PostMapping("/saveCopiedIdArr")
    @ResponseBody
    public Integer saveCopiedIdArr(@RequestParam("copiedIdArr[]") String[] copiedIdArr) {
        if (copiedIdArr == null || copiedIdArr.length == 0) {
            return 0;
        }
        AdminBean adminBean = SysUserUtils.getAdminBean(request);
        RedisManager.setExBean(COPIED_ID_PREFIX + adminBean.getName(), 24*60*60, copiedIdArr);
        return copiedIdArr.length;
    }

    /**
     * 保存复制内容的单元 id列表
     *
     * @param unitId
     * @return
     */
    @PostMapping("/saveCopiedUnitIdArr")
    @ResponseBody
    public boolean saveCopiedUnitIdArr(@RequestParam("unitId") Integer unitId) {
        if (unitId == null) {
            return false;
        }
        AdminBean adminBean = SysUserUtils.getAdminBean(request);
        RedisManager.setExString(COPIED_UNITID_PREFIX + adminBean.getName(), 24 * 60 * 60, unitId.toString());
        return true;
    }

    @PostMapping("/pasteUnit")
    @ResponseBody
    public boolean pasteUnit(@RequestParam("courseId") Integer courseId, @RequestParam("unitId") Integer unitId) {
        AdminBean adminBean = SysUserUtils.getAdminBean(request);
        String unit = RedisManager.getString(COPIED_UNITID_PREFIX + adminBean.getName());
        if (unit == null) {
            return false;
        }
        GrammarNewUnitBean cardDetail = newGrammarManagerService.getGrammarCardDetail(Integer.parseInt(unit));
        if (cardDetail == null) {
            return false;
        }
        GrammarNewUnitBean tmp = new GrammarNewUnitBean();
        BeanUtils.copyProperties(cardDetail, tmp);
        try {
            newGrammarManagerService.addGrammarUnit(unitId);
        } catch (Exception e) {
        }
        cardDetail.setId(unitId);
        cardDetail.setCourseId(courseId);
        List<GrammarUnitTitleBean> titleList = cardDetail.getTitleList();
        ArrayList<GrammarUnitContentBean> grammarUnitContentBeans = new ArrayList<>();
        if (cardDetail.getTargetContentList() != null) {
            GrammarUnitContentBean grammarContentBean = new GrammarUnitContentBean();
            grammarContentBean.setQuestion("学习目标");
            grammarContentBean.setQuestionType(101);
            grammarContentBean.setId(-1);
            grammarUnitContentBeans.add(grammarContentBean);
            grammarUnitContentBeans.addAll(cardDetail.getTargetContentList());
        }
        convert(grammarUnitContentBeans, titleList);
        if (cardDetail.getSummaryContentList() != null) {
            GrammarUnitContentBean grammarContentBean = new GrammarUnitContentBean();
            grammarContentBean.setQuestion("知识点小结");
            grammarContentBean.setQuestionType(101);
            grammarContentBean.setId(-2);
            grammarUnitContentBeans.add(grammarContentBean);
            grammarUnitContentBeans.addAll(cardDetail.getSummaryContentList());
        }
        cardDetail.setGrammarContentList(grammarUnitContentBeans);
        saveGrammarCard(cardDetail);
        cardDetail = getGrammarCardDetail(unitId);
//        List<GrammarUnitTitleBean> tmpTitleList = tmp.getTitleList();
//        List<GrammarUnitTitleBean> cardDetailTitleList = cardDetail.getTitleList();
        Map<String, Object> questionList = newGrammarManagerService.getQuestionList(Integer.valueOf(unit), 1, 100, "allKnow");
        copyQuestionList2(questionList, unitId);
        return true;
    }

    private void copyQuestionList2(Map<String, Object> questionList, Integer unitId) {
        List<GrammarQuestionBean> questionList1 = (List<GrammarQuestionBean>) questionList.get("questionList");
        questionList1.forEach(grammarQuestionBean -> {
                grammarQuestionBean.setId(null);
                grammarQuestionBean.setUnitId(unitId);
                newGrammarManagerService.addQuestionList(Collections.singletonList(grammarQuestionBean));
        });
    }

    private GrammarUnitTitleBean getByTitleId(List<GrammarUnitTitleBean> list, int titleId) {
        if (list.stream().anyMatch(grammarUnitTitleBean -> grammarUnitTitleBean.getId().equals(titleId))) {
            return list.stream().filter(grammarUnitTitleBean -> grammarUnitTitleBean.getId().equals(titleId)).collect(Collectors.toList()).get(0);
        }
        for (GrammarUnitTitleBean unitBean : list) {
            if (unitBean.getTitleList() != null) {
                GrammarUnitTitleBean byTitleId = getByTitleId(unitBean.getTitleList(), titleId);
                if (byTitleId != null) return byTitleId;
            }
        }
        return null;
    }

    private GrammarUnitTitleBean getByTitleName(List<GrammarUnitTitleBean> list, String titleName) {
        if (list.stream().anyMatch(grammarUnitTitleBean -> grammarUnitTitleBean.getName().equals(titleName))) {
            return list.stream().filter(grammarUnitTitleBean -> grammarUnitTitleBean.getName().equals(titleName)).collect(Collectors.toList()).get(0);
        }
        for (GrammarUnitTitleBean unitBean : list) {
            if (unitBean.getTitleList() != null) {
                GrammarUnitTitleBean byTitleName = getByTitleName(unitBean.getTitleList(), titleName);
                if (byTitleName != null) return byTitleName;
            }
        }
        return null;
    }

    //递归处理
    private void copyQuestionList(List<GrammarUnitTitleBean> sourceList, List<GrammarUnitTitleBean> targetList) {

        sourceList.forEach(grammarUnitTitleBean -> {
            List<GrammarUnitTitleBean> collect = targetList.stream().filter(title -> title.getName().equals(grammarUnitTitleBean.getName())).collect(Collectors.toList());
            GrammarUnitTitleBean titleBean = collect.get(0);
            if (grammarUnitTitleBean.getTitleList() != null) {
                copyQuestionList(grammarUnitTitleBean.getTitleList(), titleBean.getTitleList());
            }
            Map<String, Object> questionList = newGrammarManagerService.getQuestionList(grammarUnitTitleBean.getUnitId(), 1, 100, grammarUnitTitleBean.getId().toString());
            Integer questionNum = (Integer) questionList.get("questionNum");
            if (questionNum > 0) {
                List<GrammarQuestionBean> questionList1 = (List<GrammarQuestionBean>) questionList.get("questionList");
                questionList1.forEach(grammarQuestionBean -> {
                    grammarQuestionBean.setTitleIdList(Collections.singletonList(titleBean.getId()));
                    grammarQuestionBean.setUnitId(titleBean.getUnitId());
                    newGrammarManagerService.addQuestionList(Collections.singletonList(grammarQuestionBean));
                });
            }
        });
    }

    private void convert(List<GrammarUnitContentBean> grammarUnitContentBeans, List<GrammarUnitTitleBean> titleList) {
        for (GrammarUnitTitleBean grammarUnitTitleBean : titleList) {
            GrammarUnitContentBean grammarUnitContentBean = new GrammarUnitContentBean();
            grammarUnitContentBean.setQuestion(grammarUnitTitleBean.getName());
            grammarUnitContentBean.setQuestionType(100 + grammarUnitTitleBean.getLevel());
            grammarUnitContentBeans.add(grammarUnitContentBean);
            List<GrammarUnitSubtitleBean> subtitleList = grammarUnitTitleBean.getSubtitleList();
            if (!CollectionUtils.isEmpty(subtitleList)) {
                subtitleList.forEach(subtitle -> {
                    GrammarUnitContentBean grammarUnitContentBeansub = new GrammarUnitContentBean();
                    grammarUnitContentBeansub.setQuestion(subtitle.getName());
                    grammarUnitContentBeansub.setQuestionType(100 + subtitle.getType());
                    if (subtitle.getName().contains("观察") ||
                            subtitle.getName().contains("提炼") || subtitle.getName().contains("强化") || subtitle.getName().contains("应用")
                    ) grammarUnitContentBeansub.setQuestionType(104);
                    grammarUnitContentBeans.add(grammarUnitContentBeansub);
                    if (subtitle.getContentList() != null) subtitle.getContentList().forEach(content -> {
                        content.setId(null);
                        content.setSubtitleId(null);
                        grammarUnitContentBeans.add(content);
                    });
                });
            }
            if (grammarUnitTitleBean.getTitleList() != null) {
                convert(grammarUnitContentBeans, grammarUnitTitleBean.getTitleList());
            }
        }
    }

    /**
     * 获取已复制数量
     *
     * @return
     */
    @PostMapping("/getCopiedNum")
    @ResponseBody
    public Integer getCopiedNum() {
        AdminBean adminBean = SysUserUtils.getAdminBean(request);
        String[] idArr = (String[]) RedisManager.getBean(COPIED_ID_PREFIX+adminBean.getName());
        if(idArr==null){
            return 0;
        }else{
            return idArr.length;
        }
    }

    /**
     * 获取剪切板的内容
     *
     * @return
     */
    @PostMapping("/getCopiedContentList")
    @ResponseBody
    public List<GrammarUnitContentBean> getCopiedContentList() {
        AdminBean adminBean = SysUserUtils.getAdminBean(request);
        String[] idArr = (String[]) RedisManager.getBean(COPIED_ID_PREFIX+adminBean.getName());
        if(idArr==null || idArr.length==0){
            return null;
        }
        RedisManager.delBean(COPIED_ID_PREFIX+adminBean.getName());
        return newGrammarManagerService.getCopiedContentList(idArr);
    }

    /**
     * 获取课程单元全名
     *
     * @param unitId
     * @return
     */
    @PostMapping("/getUnitName")
    @ResponseBody
    public Map<String, Object> getUnitName(Integer unitId) {
        return newGrammarManagerService.getUnitName(unitId);
    }

    /**
     * 获取知识点列表
     *
     * @param unitId
     * @return
     */
    @PostMapping("/getKnowledgeListWithNum")
    @ResponseBody
    public Map<String, Object> getKnowledgeListWithNum(Integer unitId) {
        return newGrammarManagerService.getKnowledgeListWithNum(unitId);
    }

    /**
     * 获取试题列表
     *
     * @param unitId
     * @return
     */
    @PostMapping("/getQuestionList")
    @ResponseBody
    public Map<String, Object> getQuestionList(Integer unitId, Integer pageNum, Integer pageSize, String titleIds) {
        return newGrammarManagerService.getQuestionList(unitId, pageNum, pageSize, titleIds);
    }

    /**
     * 修改试题
     *
     * @param question
     * @return
     */
    @PostMapping("/updateQuestion")
    @ResponseBody
    public boolean updateQuestion(@RequestBody GrammarQuestionBean question) {
//         User user= UserLoginManager.getSession(request.getAttribute("sessionId").toString());
//        question.setTeachId(user.getUserName());
        return newGrammarManagerService.updateQuestion(question);
    }

    /**
     * 添加试题
     *
     * @param question
     * @return
     */
    @PostMapping("/addQuestionList")
    @ResponseBody
    public boolean addQuestionList(@RequestBody GrammarQuestionBean question) {
//         User user= UserLoginManager.getSession(request.getAttribute("sessionId").toString());
//        question.setTeachId(user.getUserName());
        List<GrammarQuestionBean> list = new ArrayList<GrammarQuestionBean>();
        question.setTeachId("system");
        list.add(question);
        return newGrammarManagerService.addQuestionList(list);
    }

    /**
     * 本章检测，上传测试试题
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/uploadExcel")
    public void uploadExcel(MultipartFile file, Integer unitId, HttpServletResponse response) throws Exception {
        response.setContentType("text/html;charset=utf-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        //判断文件是否符合条件
        if (file == null || file.isEmpty()) {
            response.getWriter().write("error");
            return;
        }
        if (file.getSize() > 2 * 1024 * 1024) {
            response.getWriter().write("size is too big");
            return;
        }
        String filename = file.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf("."));
        if (!".xlsx".equals(suffix) && !".xls".equals(suffix)) {
            response.getWriter().write("format is wrong");
            return;
        }
        List<Map<String, Object>> areaList = newGrammarManagerService.getAreaList();
        //读取文件内容
        List<GrammarQuestionBean> questionList = new ArrayList<GrammarQuestionBean>();
//         User user= UserLoginManager.getSession(request.getAttribute("sessionId").toString());
        InputStream is = file.getInputStream();
        Workbook workbook = null;
        if (".xls".equals(suffix)) {
            workbook = new HSSFWorkbook(is);
        } else {
            workbook = new XSSFWorkbook(is);
        }
        Sheet sheet = workbook.getSheetAt(0);
        int rowNum = sheet.getLastRowNum();
        int colNum = sheet.getRow(0).getPhysicalNumberOfCells();
        if (rowNum < 1 || colNum < 9) {
            response.getWriter().write("error");
            return;
        }
        String reg1 = "^[ABCDE]$";
        String reg2 = "^[ABCDE](;[ABCDE]){0,4}$";
        String correctOption = null;
        String[] answerArr = null;
        Row row;
        for (int i = 1; i <= rowNum; i++) {
            GrammarQuestionBean question = new GrammarQuestionBean();
            row = sheet.getRow(i);
            if (row.getCell(0) == null || "".equals(row.getCell(0).getStringCellValue())) {
                continue;
            }
            if ("".equals(row.getCell(1).getStringCellValue()) || row.getCell(1) == null ||
                    "".equals(row.getCell(7).getStringCellValue()) || row.getCell(7) == null ||
                    "".equals(row.getCell(9).getStringCellValue()) || row.getCell(9) == null) {
                response.getWriter().write("error");
                return;
            }
            String questionTypeStr = row.getCell(0).getStringCellValue();
            int questionType = 1;
            if ("单选题".equals(questionTypeStr)) {
                questionType = 1;
            } else if ("多选题".equals(questionTypeStr)) {
                questionType = 2;
            } else if ("填空题".equals(questionTypeStr)) {
                questionType = 3;
            }
            question.setQuestionType(questionType);
            question.setQuestion(row.getCell(1).getStringCellValue().trim());
            //获取选项及答案
            switch (questionType) {
                case 1:
                case 2:
                    if (row.getCell(2) != null) {
                        question.setOptionA(row.getCell(2).getStringCellValue().trim());
                    }
                    if (row.getCell(3) != null) {
                        question.setOptionB(row.getCell(3).getStringCellValue().trim());
                    }
                    if (row.getCell(4) != null) {
                        question.setOptionC(row.getCell(4).getStringCellValue().trim());
                    }
                    if (row.getCell(5) != null) {
                        question.setOptionD(row.getCell(5).getStringCellValue().trim());
                    }
                    if (row.getCell(6) != null) {
                        question.setOptionE(row.getCell(6).getStringCellValue().trim());
                    }
                    correctOption = row.getCell(7).getStringCellValue().trim();
                    if (questionType == 1 && correctOption.matches(reg1)) {
                        //do nothing
                    } else if (questionType == 2 && correctOption.matches(reg2)) {
                        correctOption = correctOption.replaceAll(";", ",");
                    } else {
                        response.getWriter().write("error");
                        return;
                    }
                    question.setCorrectOption(correctOption);
                    break;
                case 3:
                    correctOption = row.getCell(7).getStringCellValue().trim();
                    answerArr = correctOption.split(";");
                    for (int k = 0; k < answerArr.length; k++) {
                        if (answerArr[k] == "") {
                            continue;
                        }
                        switch (k) {
                            case 0:
                                question.setOptionA(answerArr[k]);
                                break;
                            case 1:
                                question.setOptionB(answerArr[k]);
                                break;
                            case 2:
                                question.setOptionC(answerArr[k]);
                                break;
                            case 3:
                                question.setOptionD(answerArr[k]);
                                break;
                            case 4:
                                question.setOptionE(answerArr[k]);
                                break;
                        }
                    }
                    break;
            }

            if (row.getCell(8) != null) {
                question.setParse(row.getCell(8).getStringCellValue().trim());
            }
            //难度
            String difficultyStr = row.getCell(9).getStringCellValue().trim();
            Integer difficulty = 1;
            if ("易".equals(difficultyStr)) {
                difficulty = 1;
            } else if ("中".equals(difficultyStr)) {
                difficulty = 2;
            } else if ("难".equals(difficultyStr)) {
                difficulty = 3;
            }
            question.setDifficulty(difficulty);
            //来源
            String sourceStr = row.getCell(10) == null ? "" : row.getCell(10).getStringCellValue().trim();
            Integer source = 1;
            if ("自编".equals(sourceStr)) {
                source = 1;
            } else if ("模拟".equals(sourceStr)) {
                source = 2;
            } else if ("高考".equals(sourceStr)) {
                source = 3;
            }
            question.setSource(source);
            //年份
            String yearStr = row.getCell(11) == null ? "" : row.getCell(11).getStringCellValue().trim();
            Integer year = Calendar.getInstance().get(Calendar.YEAR);
            if (yearStr.matches("^\\d{4}$")) {
                Integer yearTemp = Integer.valueOf(yearStr);
                if (yearTemp >= 2015 && yearTemp <= year) {
                    year = yearTemp;
                }
            }
            question.setYear(year);
            //地区
            String area = row.getCell(12) == null ? "" : row.getCell(12).getStringCellValue().trim();
            Integer areaId = 0;
            if (!"".equals(area)) {
                for (Map<String, Object> map : areaList) {
                    if (area.equals(map.get("name").toString())) {
                        areaId = Integer.valueOf(map.get("id").toString());
                        break;
                    }
                }
            }
            question.setAreaId(areaId);
            //关联知识点
            String knowIds = row.getCell(13) == null ? "" : row.getCell(13).getStringCellValue().trim();
            if (knowIds.matches("^[\\d;]+$")) {
                List<Integer> titleIdList = new ArrayList<Integer>();
                String[] knowIdArr = knowIds.split(";");
                for (String knowIdStr : knowIdArr) {
                    if (!"".equals(knowIdStr)) {
                        titleIdList.add(Integer.valueOf(knowIdStr));
                    }
                }
                if (titleIdList.size() > 0) {
                    question.setTitleIdList(titleIdList);
                }
            }

            question.setTeachId(/*user.getUserName()*/"");
            question.setUnitId(unitId);

            questionList.add(question);
        }
        if (questionList.size() > 0) {//批量添加试题
            if (!newGrammarManagerService.addQuestionList(questionList)) {
                response.getWriter().write("failed");
                return;
            }
        }
        response.getWriter().write("success");
    }


    /**
     * 获取应用试题列表
     *
     * @param unitId
     * @return
     */
    @PostMapping("/getApplyQuestionList")
    @ResponseBody
    public List<Map<String, Object>> getApplyQuestionList(Integer unitId, Integer titleId) {
        return newGrammarManagerService.getApplyQuestionList(unitId, titleId);
    }

    /**
     * 根据试题id删除试题
     *
     * @return
     */
    @PostMapping("/deleteQuestion")
    @ResponseBody
    public boolean deleteQuestion(Integer courseId, Integer unitId, @RequestParam("questionIdArr[]") Integer[] questionIdArr) {
//         User user= UserLoginManager.getSession(request.getAttribute("sessionId").toString());
        return newGrammarManagerService.deleteQuestion(/*user.getUserName()*/"", courseId, unitId, questionIdArr);
    }

    /**
     * 添加语法卡片
     *
     * @param grammarCard
     * @return
     */
    @PostMapping("/saveGrammarCard")
    @ResponseBody
    public Map<String, Object> saveGrammarCard(@RequestBody GrammarNewUnitBean grammarCard) {
//        User user= UserLoginManager.getSession(request.getAttribute("sessionId").toString());
//        grammarCard.setTeachId(user.getUserName());

        // 文件保存路径
        String contextPath = session.getServletContext().getRealPath("/");
        if (contextPath.endsWith(File.separator)) {
            contextPath = contextPath.substring(0, contextPath.length() - 1);
        }
        //取与项目同级的本地路径
        contextPath = contextPath.substring(0, contextPath.lastIndexOf(File.separator) + 1);
        Map<String, Object> map = newGrammarManagerService.saveGrammarCard(contextPath, grammarCard);
        return map;
    }

    /**
     * 正在编辑
     *
     * @return
     */
    @PostMapping("/editing")
    @ResponseBody
    public boolean editing() {
        return true;
    }

    /**
     * 上传图片
     *
     * @param upload
     */
    @RequestMapping("/uploadImage")
    @ResponseBody
    public Map<String, String> uploadImage(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        return newGrammarManagerService.uploadImage(unitId, upload, request);
    }

    /**
     * 通过抓取的方式上传文本域中的图片
     *
     * @param upload
     */
    @RequestMapping("/textUploadByPaste")
    @ResponseBody
    public Map<String, Object> textUploadByPaste(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        return newGrammarManagerService.textUploadByPaste(unitId, upload, request);
    }
    @RequestMapping("/audioUploadByPaste")
    @ResponseBody
    public Map<String, Object> audioUploadByPaste(Integer unitId, MultipartFile upload, HttpServletRequest request) {
        return newGrammarManagerService.audioUploadByPaste(unitId, upload, request);
    }
    /**
     * 通过图片上传按钮方式上传文本域中的图片
     *
     * @param upload
     */
    @RequestMapping("/textUpload")
    @ResponseBody
    public void textUpload(Integer unitId, MultipartFile upload, HttpServletResponse response, HttpServletRequest request) {
        newGrammarManagerService.textUpload(unitId, upload, response, request);
    }


    /**
     * 添加所有本章检测试题
     *
     * @return
     */
    @RequestMapping("/addAllUnitQuestion")
    @ResponseBody
    public boolean addAllUnitQuestion(Integer unitId, @RequestParam(value = "sourceTypeArr[]", required = false) Integer[] sourceTypeArr, Integer gradePhase,
                                      @RequestParam(value = "gradeArr[]", required = false) Integer[] gradeArr, @RequestParam(value = "knowledgePointArr[]", required = false) String[] knowledgePointArr,
                                      @RequestParam(value = "questionTypeArr[]", required = false) Integer[] questionTypeArr, @RequestParam(value = "difficultyArr[]", required = false) Integer[] difficultyArr) {
        return newGrammarManagerService.addAllUnitQuestion(null, null, unitId, sourceTypeArr, gradePhase,
                gradeArr, knowledgePointArr, questionTypeArr, difficultyArr);
    }

    /**
     * 添加选中的本章检测试题
     *
     * @return
     */
    @RequestMapping("/addSelectUnitQuestion")
    @ResponseBody
    public boolean addSelectUnitQuestion(Integer unitId, @RequestParam("questionIdArr[]") Integer[] questionIdArr) {
        return newGrammarManagerService.addSelectUnitQuestion(null, unitId, questionIdArr);
    }

    @PostMapping("/updateUnitKnowledgeCount")
    @ResponseBody
    public boolean updateUnitKnowledgeCount(Integer unitId, Integer count) {
        return newGrammarManagerService.updateUnitKnowledgeCount(unitId, count);
    }
}
