package com.woxue.resourcemanage.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 困难程度枚举类
 */
public enum ReadExpandReadDifficultyEnum {

    EASY(1,"易",5),
    CENTRE_UP(2,"中上",7),
    CENTRE(3,"中",9),
    CENTRE_DOWN(4,"中下",10),
    DIFFICULT(5,"难",12),

    ;

    private final Integer number;
    private final String name;
    private final Integer time;

    private static final List<ReadExpandReadDifficultyEnum> list = new ArrayList<>();
    private static final Map<Integer, ReadExpandReadDifficultyEnum> map = new HashMap();

    static {
        for (ReadExpandReadDifficultyEnum anEnum : values()) {
            list.add(anEnum);
            map.put(anEnum.getNumber(),anEnum);
        }
    }

    /**
     * 通过name获取枚举对象
     * @return
     */
    public static ReadExpandReadDifficultyEnum getByNumber(Integer number) {
        return number == null ? null : map.get(number);
    }


    /**
     * 困难程度list
     * @return
     */
    public static List<ReadExpandReadDifficultyEnum.DifficultyDO> list() {
        List<ReadExpandReadDifficultyEnum.DifficultyDO> difficultyDOList = list.stream().map(item -> {
            ReadExpandReadDifficultyEnum.DifficultyDO difficultyDO = new ReadExpandReadDifficultyEnum.DifficultyDO();
            difficultyDO.setNumber(item.getNumber());
            difficultyDO.setName(item.getName());
            difficultyDO.setTime(item.getTime());
            return difficultyDO;
        }).collect(Collectors.toList());
        return difficultyDOList;
    }


    @Data
    public static class DifficultyDO {
        private Integer number;
        private String name;
        private Integer time;
    }

    ReadExpandReadDifficultyEnum(Integer number, String name, Integer time) {
        this.name = name;
        this.number = number;
        this.time = time;
    }

    public Integer getNumber() {
        return number;
    }

    public String getName() {
        return name;
    }

    public Integer getTime() {
        return time;
    }
    public static void main(String[] args) {
        ReadExpandReadDifficultyEnum difficultyEnum = getByNumber(1);
        System.out.println(difficultyEnum);
        System.out.println(list());
    }

}
