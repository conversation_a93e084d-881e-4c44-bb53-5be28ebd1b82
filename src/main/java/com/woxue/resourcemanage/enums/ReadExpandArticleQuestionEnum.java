package com.woxue.resourcemanage.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文章体裁、题型的枚举类
 */
public enum ReadExpandArticleQuestionEnum {


    //训练阶段
    BASIC_TRAIN("基础训练","BASIC_TRAIN",""),

    RAISE_TRAIN("拔高训练","RAISE_TRAIN",""),

    //文章体裁总结
    PRACTICAL_ESSAY("应用文","PRACTICAL_ESSAY","文章体裁"),
    NARRATIVE_ESSAY("记叙文","NARRATIVE_ESSAY","文章体裁"),
    EXPOSITORY_ESSAY("说明文","EXPOSITORY_ESSAY","文章体裁"),
    DISCUSSION_ESSAY("议论文","DISCUSSION_ESSAY","文章体裁"),

    //新题型总结：基础训练，拔高训练共有，parent默认填基础训练，便于提供查询
    DETAIL_COMPREHEND("细节理解","DETAIL_COMPREHEND","基础训练"),
    WORD_SENTENCE_CONJECTURE("词句猜测","WORD_SENTENCE_CONJECTURE","基础训练"),
    INFERENCE_JUDGMENT("推理判断","INFERENCE_JUDGMENT","基础训练"),
    ARTICLE_GENRE("文章体裁","ARTICLE_GENRE","基础训练"),
    ARTICLE_STRUCTURE("文章结构","ARTICLE_STRUCTURE","基础训练"),
    GENERAL_IDEA("主旨大意","GENERAL_IDEA","基础训练"),

    //原题型总结:区分基础训练，拔高训练
//
//    DETAIL_COMPREHEND("细节理解题","DETAIL_COMPREHEND","基础训练"),
//    INFERENCE_JUDGMENT("推理判断题","INFERENCE_JUDGMENT","基础训练"),
//    WORD_CONJECTURE("词义猜测题","WORD_CONJECTURE","基础训练"),
//    SENTENCE_CONJECTURE("句意猜测题","SENTENCE_CONJECTURE","基础训练"),
//    PRONOUN_REFERENCE("代词指代题","PRONOUN_REFERENCE","基础训练"),
//    ACROSS_SUBJECT_KNOWLEDGE("跨学科知识题","ACROSS_SUBJECT_KNOWLEDGE","基础训练"),
//    ARTICLE_GENRE("文章体裁题","ARTICLE_GENRE","拔高训练"),
//    PARAGRAPH_MAIN("段落大意题","PARAGRAPH_MAIN","拔高训练"),
//    SENTENCE_SORT("句子排序题","SENTENCE_SORT","拔高训练"),
//    ARTICLE_STRUCTURE("文章结构题","ARTICLE_STRUCTURE","拔高训练"),
//    ARTICLE_SOURCE("文章出处题","ARTICLE_SOURCE","拔高训练"),
//    GENERAL_IDEA("主旨大意题","GENERAL_IDEA","拔高训练"),
//    TITLE_INDUCTION("标题归纳题","TITLE_INDUCTION","拔高训练"),
//    WRITING_INTENTION("写作意图题","WRITING_INTENTION","拔高训练"),
//    AUTHOR_ATTITUDE("作者态度题","AUTHOR_ATTITUDE","拔高训练"),
//    WRITING_TECHNIQUE("写作手法题","WRITING_TECHNIQUE","拔高训练"),
    ;

    private final String name;
    private final String code;
    private final String parent;

    private static final List<ReadExpandArticleQuestionEnum> list = new ArrayList<>();
    private static final Map<String,ReadExpandArticleQuestionEnum> map = new HashMap();

    static {
        for (ReadExpandArticleQuestionEnum anEnum : values()) {
            list.add(anEnum);
            map.put(anEnum.getName(),anEnum);
        }
    }

    /**
     * 通过name获取枚举对象
     * @return
     */
    public static ReadExpandArticleQuestionEnum getByName(String name) {
        return name == null ? null : map.get(name);
    }


    /**
     * 文章体裁list
     * @return
     */
    public static List<QuestionDO> articleGenreList() {
        return turnList(PRACTICAL_ESSAY.parent);
    }

    /**
     * 基础训练list
     * @return
     */
    public static List<QuestionDO> basicTrainList() {
        return turnList(BASIC_TRAIN.name);
    }

    /**
     * 拔高训练list
     * @return
     */
    public static List<QuestionDO> raiseTrainList() {
        return turnList(RAISE_TRAIN.name);
    }

    public static List<QuestionDO> turnList(String name){
        List<QuestionDO> articleGenreList = list.stream().filter(item -> item.getParent().equals(name)).map(item -> {
            QuestionDO questionDO = new QuestionDO();
            questionDO.setCode(item.getCode());
            questionDO.setName(item.getName());
            return questionDO;
        }).collect(Collectors.toList());
        return articleGenreList;
    }


    ReadExpandArticleQuestionEnum(String name, String code,String parent) {
        this.name = name;
        this.parent = parent;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getParent() {
        return parent;
    }


    public static void main(String[] args) {
        ReadExpandArticleQuestionEnum questionEnum = getByName("应用文");
        System.out.println(questionEnum);

        System.out.println(articleGenreList());
    }

    @Data
    public static class QuestionDO {
        private String name;
        private String code;
    }
}
