package com.woxue.resourcemanage.util;


import com.woxue.common.model.redBook.WriteUnitContentBean;

import java.util.ArrayList;
import java.util.List;

public class WriteContentStack {
    private final List<WriteUnitContentBean> list = new ArrayList<WriteUnitContentBean>();

    public int size() {
        return list.size();
    }

    public boolean push(WriteUnitContentBean content) {
        list.add(content);
        return true;
    }

    public WriteUnitContentBean pop() {
        if (list.size() == 0) {
            return null;
        }
        return list.remove(list.size() - 1);
    }

    public WriteUnitContentBean getTop() {
        if (list.size() == 0) {
            return null;
        }
        return list.get(list.size() - 1);
    }

    public boolean clear() {
        list.clear();
        return true;
    }

    private int index = 1;

    public int counter() {
        return index++;
    }
}
