package com.woxue.resourcemanage.util;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;


/**
 * 给图片加水印
 * <AUTHOR>
 * @create 2020/3/19
 */
public class WaterMarkUtils {
	private static Font font = null; // 水印字体
    private static final String waterMarkContent = "XIAOHONGBEN"; // 水印内容
    private static final Color markContentColor = new Color(100, 100, 100);//水印颜色
    private static final Float alpha = 0.2f;//水印透明度
    private static final Integer degree = -40;//水印旋转角度
    private static final int xMove = 75;// 水印之间的间隔
    private static final int yMove = 75;// 水印之间的间隔
    
    private static Font getFont(){
    	if(font==null){
    		try{
    			File file = new File(WaterMarkUtils.class.getClassLoader().getResource("simsun.ttc").getPath());
    			FileInputStream in = new FileInputStream(file);
    			Font dynamicFont = Font.createFont(Font.TRUETYPE_FONT, in);
    			Font dynamicFontPt =  dynamicFont.deriveFont(Font.ITALIC, 16);
    			in.close();
    			font = dynamicFontPt;
    		}catch(Exception e) {//异常处理
    			e.printStackTrace();
    			font = new Font("宋体", Font.ITALIC, 16);
    		}
    	}
    	return font;
    }

    /**
     * 添加水印
     * @param imgPath 源图片路径
     * @param savePath 保存图片路径
     *
     */
    public static Boolean addWaterMark(String imgPath,String savePath) {
        try {
            // 读取原图片信息
            File srcImgFile = new File(imgPath);// 得到文件
            Image srcImg = ImageIO.read(srcImgFile);// 文件转化为图片
            int srcImgWidth = srcImg.getWidth(null);// 获取图片的宽
            int srcImgHeight = srcImg.getHeight(null);// 获取图片的高
            // 加水印
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight,
                    BufferedImage.TYPE_INT_RGB);
            Graphics2D g = bufImg.createGraphics();
            // 设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                    RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            // g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
            g.drawImage(
                    srcImg.getScaledInstance(srcImg.getWidth(null),
                            srcImg.getHeight(null), Image.SCALE_SMOOTH), 0, 0,
                    null);
            // 设置水印旋转
            if (null != degree) {
                g.rotate(Math.toRadians(degree),
                        (double) bufImg.getWidth() / 2,
                        (double) bufImg.getHeight() / 2);
            }
            g.setColor(markContentColor); // 根据图片的背景设置水印颜色
            Font f = getFont();
            g.setFont(f); // 设置字体
//			g.setFont(new Font("宋体", Font.PLAIN, 20));
//			g.setFont(new Font("宋体", Font.PLAIN, srcImg.getWidth(null)/300*15));
            // 设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP,
                    alpha));

            // 设置水印的坐标
            int x = 0;//-srcImgWidth / 2;
            int y = 0;//-srcImgHeight / 2;
            int markWidth = f.getSize() * getTextLength(waterMarkContent);// 字体长度
            int markHeight = f.getSize();// 字体高度
//			BigDecimal number= new BigDecimal(srcImg.getWidth(null));
//			BigDecimal olDnumber= new BigDecimal(800);

//			Integer MOVE = number.divide(olDnumber).intValue()*150;
            // 循环添加水印
            while (x < srcImgWidth * 1.5) {
                y = -srcImgHeight;// / 2;
                while (y < srcImgHeight * 2) {
                    g.drawString(waterMarkContent, x, y);
                    y += markHeight + yMove;
                }
                x += markWidth + xMove;
            }
            g.dispose();
            // 输出图片
            File saveFile = new File(savePath.substring(0,savePath.lastIndexOf(File.separator)));
            if(!saveFile.exists()){
                saveFile.mkdirs();
            }
            FileOutputStream outImgStream = new FileOutputStream(savePath);
            ImageIO.write(bufImg, savePath.substring(savePath.lastIndexOf(".") + 1), outImgStream);
            outImgStream.flush();
            outImgStream.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取文本长度。汉字为1:1，英文和数字为2:1
     */
    private static int getTextLength(String text) {
        int length = text.length();
        for (int i = 0; i < text.length(); i++) {
            String s = String.valueOf(text.charAt(i));
            if (s.getBytes().length > 1) {
                length++;
            }
        }
        length = length % 2 == 0 ? length / 2 : length / 2 + 1;
        return length;
    }

}