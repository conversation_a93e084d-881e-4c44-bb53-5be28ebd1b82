package com.woxue.resourcemanage.util;

import io.jsonwebtoken.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022 -05-10 17:39
 */
public class ResourceManageJwtUtil {

    public static final String TOKEN_HEADER = "authorization";
    private static final long EXPIRITION = 1000 * 24 * 60 * 60 * 7;//7天
    private static final String secretKey = "drgqaksdkd@$fwbnbfabqk$$$lqsskk)_ssfqwdasmvz___saq";
    public static final String ADMIN_NAME = "ADMIN_NAME";
    public static final String ADMIN_ROLES = "ADMIN_ROLES";

    /**
     * 生成 jwt token
     */
    public static String createToken(String adminName, String roles) {
        Date now = new Date();
        Date date = new Date(now.getTime() + EXPIRITION);//根据当前时间 + tokenExpireTime（token有效期，以毫秒为单位 例：7200000为2小时）
        return Jwts.builder()
                .claim(ADMIN_NAME, adminName)//自定义对象，存放自定义参数（数据存放在Payload）
                .claim(ADMIN_ROLES, roles)//自定义对象，存放自定义参数（数据存放在Payload）
                .setIssuedAt(now)//生成时间
                .setExpiration(date)//token到期时间
                .signWith(SignatureAlgorithm.HS256, secretKey)//加密方式，secretKey自定义密钥
                .compact();
    }

    /**
     * 解析 jwt
     */
    public static Claims parseJwt(String token) throws Exception{
        try {
            return Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody();
        } catch (MalformedJwtException e) {
            throw new Exception("token格式不对！");
        } catch (ExpiredJwtException e) {
            throw new Exception("校验的token过期！");
        } catch (SignatureException e) {
            throw new Exception("签名错误！");
        } catch (UnsupportedJwtException e) {
            throw new Exception("不支持！");
        }catch (Exception e) {
            throw new Exception("其他未知异常！");
        }
    }

    public static void main(String[] args) throws Exception {
        String token = createToken("666","123123123");
        System.out.println(token);
        Claims claims = parseJwt(token);
        System.out.println(claims.get(ADMIN_NAME));
    }
}
