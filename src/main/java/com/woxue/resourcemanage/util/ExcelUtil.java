package com.woxue.resourcemanage.util;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.util.List;

/**
 * 描述 导出表到Excel
 *
 * <AUTHOR>
 * @date ***
 */
public class ExcelUtil {

    private static final String SHEET_NAME = "test合并";

    /**
     * 描述 导出表到Excel
     *
     * @param sheetName
     * @param title
     * @param values
     * @param wb
     * @return HSSFWorkbook
     * @return
     * <AUTHOR>
     * @date ***
     */
    public static HSSFWorkbook getHSSFWorkbook(String sheetName, String[] title, List<List<String>> values, HSSFWorkbook wb) {

        if (wb == null) {
            wb = new HSSFWorkbook();
        }

        HSSFSheet sheet = wb.createSheet(sheetName);

        HSSFRow row = sheet.createRow(0);

        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);

        HSSFCell cell;

        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }

        //遍历集合数据，产生数据行
        for (int i = 0; i < values.size(); i++) {
            row = sheet.createRow(i + 1);
            List<String> list = values.get(i);
            for (int j = 0; j < list.size(); j++) {
                //将内容按顺序赋给对应的列对象
                row.createCell(j).setCellValue(list.get(j));
            }
        }

        /*//当当前的表格是预定义的表格时进行如下合并操作。
        if (SHEET_NAME.equals(sheetName)) {
            //下面是针对某些列需要合并的做处理
            //从第1行开始（第0行是标题行），此处设定每3行进行一次针对某些列的合并处理
            for (int i = 1; i < values.length; i += 3) {
                //以下是每隔3行，针对第1，2，3，6，9，10，11，13等列进行相同合并处理（列的下标从0开始）
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 2, 2));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 3, 3));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 6, 6));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 9, 9));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 10, 10));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 11, 11));
                sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 13, 13));
            }
        }*/

        return wb;
    }
}

