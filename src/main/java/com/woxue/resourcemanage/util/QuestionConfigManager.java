package com.woxue.resourcemanage.util;

import com.woxue.common.action.SpringActionSupport;
import com.woxue.resourceservice.util.RedBookRedisManager;
import com.woxue.resourcemanage.dao.IQuestionBankConfigDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QuestionConfigManager {
	private static final QuestionConfigManager INSTANCE = new QuestionConfigManager();

	public QuestionConfigManager() {
		/**
		 * 加载所有的配置资源
		 */
		loadGradePhaseConfig();
		loadGradeConfig();
		loadPaperTypeConfig();
		loadKnowledgePointConfig();
		loadQuestionTypeConfig();
		loadQuestionDifficultyConfig();
		loadAreaConfig();
		loadQuestionKnowledgeConfig();
		loadQuestionTopicConfig();
		loadQuizPointConfig();
	}

	public static QuestionConfigManager getInstance(){
		return INSTANCE;
	}
	private final String GRADE_PHASE_LIST = "config:gradePhase:list";
	private final String GRADE_LIST = "config:grade:list";
	private final String PAPER_TYPE_LIST = "config:paperType:list";
	private final String KNOWLEDGE_POINT_MAP = "config:knowledgePoint:map";
	private final String QUESTION_TYPE_LIST = "config:questionType:list";
	private final String QUESTION_DIFFICULTY_LIST = "config:questionDifficulty:list";
	private final String AREA_MAP = "config:area:map";
	private final String QUESTION_KNOWLEDGE_MAP = "config:questionKnow:map";
	private final String QUESTION_TOPIC_MAP = "config:questionTopic:map";
	private final String QUIZ_POINT_MAP = "config:quiz:map";


	/**
	 * 加载学段配置
	 */
	public void loadGradePhaseConfig(){
		List<Map<String, Object>> gradePhaseList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getGradePhaseList();
		RedBookRedisManager.setResourceBean(GRADE_PHASE_LIST, gradePhaseList);
	}
	
	/**
	 * 加载年级配置
	 */
	public void loadGradeConfig(){
		List<Map<String, Object>> gradeList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getGradeList();
		RedBookRedisManager.setResourceBean(GRADE_LIST, gradeList);
	}
	
	/**
	 * 加载试卷类型配置
	 */
	public void loadPaperTypeConfig(){
		List<Map<String, Object>> paperTypeList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getPaperTypeList();
		RedBookRedisManager.setResourceBean(PAPER_TYPE_LIST, paperTypeList);
	}
	
	/**
	 * 加载语法点配置
	 */
	public void loadKnowledgePointConfig(){
		List<Map<String, Object>> knowledgePointAllList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getKnowledgePointList();
		Map<String, List<Map<String, Object>>> knowledgePointListMap= new HashMap<String, List<Map<String, Object>>>();
		List<Map<String, Object>> knowledgePointList = null;
		String parentId = null;
		for (Map<String, Object> knowledgePoint : knowledgePointAllList) {
			parentId = knowledgePoint.get("parentId").toString();
			knowledgePointList = knowledgePointListMap.get(parentId);
			if(knowledgePointList==null){
				knowledgePointList = new ArrayList<Map<String, Object>>();
				knowledgePointList.add(knowledgePoint);
				knowledgePointListMap.put(parentId, knowledgePointList);
			}else{
				knowledgePointList.add(knowledgePoint);
			}
		}
		RedBookRedisManager.setResourceBean(KNOWLEDGE_POINT_MAP, knowledgePointListMap);
	}
	
	/**
	 * 加载试题类型配置
	 */
	public void loadQuestionTypeConfig(){
		List<Map<String, Object>> questionTypeList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getQuestionTypeList();
		RedBookRedisManager.setResourceBean(QUESTION_TYPE_LIST, questionTypeList);
	}
	
	/**
	 * 加载试题难度配置
	 */
	public void loadQuestionDifficultyConfig(){
		List<Map<String, Object>> questionDifficultyList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getQuestionDifficultyList();
		RedBookRedisManager.setResourceBean(QUESTION_DIFFICULTY_LIST, questionDifficultyList);
	}
	
	/**
	 * 加载地区信息
	 */
	public void loadAreaConfig(){
		List<Map<String, Object>> areaAllList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getAreaList();
		Map<Integer, List<Map<String, Object>>> areaListMap= new HashMap<Integer, List<Map<String, Object>>>();
		List<Map<String, Object>> areaList = null;
		Integer parentId = null;
		for (Map<String, Object> area : areaAllList) {
			parentId = Integer.parseInt(area.get("parentId").toString());
			areaList = areaListMap.get(parentId);
			if(areaList==null){
				areaList = new ArrayList<Map<String, Object>>();
				areaList.add(area);
				areaListMap.put(parentId, areaList);
			}else{
				areaList.add(area);
			}
		}
		RedBookRedisManager.setResourceBean(AREA_MAP, areaListMap);
		
	}
	
	/**
	 * 加载题型与知识点的关联信息
	 */
	public void loadQuestionKnowledgeConfig(){
		List<Map<String, Object>> questionKnowledgeList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getQuestionKnowledgeList();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<Integer, List<Map<String, String>>> gradePhaseMap = null;
		List<Map<String, String>> knowledgePointList = null;
		Map<String, String> knowledgeMap = null;
		String parentId, knowledgePoint, name;
		Integer questionType = null;
		for (Map<String, Object> map : questionKnowledgeList) {
			parentId = map.get("parentId").toString();
			questionType = Integer.valueOf(map.get("questionType").toString());
			knowledgePoint = map.get("knowledgePoint").toString();
			name = map.get("name").toString();
			
			gradePhaseMap = (Map<Integer, List<Map<String, String>>>) resultMap.get(parentId);
			if(gradePhaseMap==null){
				gradePhaseMap = new HashMap<Integer, List<Map<String, String>>>();
			}
			knowledgePointList = gradePhaseMap.get(questionType);
			if(knowledgePointList==null){
				knowledgePointList = new ArrayList<Map<String, String>>();
			}
			knowledgeMap = new HashMap<String, String>();
			knowledgeMap.put("id", knowledgePoint);
			knowledgeMap.put("name", name);
			knowledgePointList.add(knowledgeMap);
			gradePhaseMap.put(questionType, knowledgePointList);
			resultMap.put(parentId, gradePhaseMap);
		}
		RedBookRedisManager.setResourceBean(QUESTION_KNOWLEDGE_MAP, resultMap);
	}
	
	/**
	 * 加载话题配置信息
	 */
	public void loadQuestionTopicConfig(){
		List<Map<String, Object>> questionTopicAllList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getQuestionTopicList();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<Integer, List<Map<String, Object>>> gradePhaseMap = null;
		List<Map<String, Object>> questionTopicList = null;
		Map<String, Object> questionTopicMap = null;
		String gradePhase, name;
		Integer id, questionType;
		for (Map<String, Object> map : questionTopicAllList) {
			id = Integer.valueOf(map.get("id").toString());
			gradePhase = map.get("gradePhase").toString();
			questionType = Integer.valueOf(map.get("questionType").toString());
			name = map.get("name").toString();
			
			gradePhaseMap = (Map<Integer, List<Map<String, Object>>>) resultMap.get(gradePhase);
			if(gradePhaseMap==null){
				gradePhaseMap = new HashMap<Integer, List<Map<String, Object>>>();
			}
			questionTopicList = gradePhaseMap.get(questionType);
			if(questionTopicList==null){
				questionTopicList = new ArrayList<Map<String, Object>>();
			}
			questionTopicMap = new HashMap<String, Object>();
			questionTopicMap.put("id", id);
			questionTopicMap.put("name", name);
			questionTopicList.add(questionTopicMap);
			gradePhaseMap.put(questionType, questionTopicList);
			resultMap.put(gradePhase, gradePhaseMap);
		}
		RedBookRedisManager.setResourceBean(QUESTION_TOPIC_MAP, resultMap);
	}
	
	/**
	 * 加载考点配置信息
	 */
	public void loadQuizPointConfig(){
		List<Map<String, Object>> quizPointAllList = SpringActionSupport.getSpringBean("configDao", null, IQuestionBankConfigDao.class).getQuizPointList();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<Integer, List<Map<String, Object>>> gradePhaseMap = null;
		List<Map<String, Object>> quizPointList = null;
		Map<String, Object> quizPointMap = null;
		String gradePhase, name;
		Integer id, questionType;
		for (Map<String, Object> map : quizPointAllList) {
			id = Integer.valueOf(map.get("id").toString());
			gradePhase = map.get("gradePhase").toString();
			questionType = Integer.valueOf(map.get("questionType").toString());
			name = map.get("name").toString();
			
			gradePhaseMap = (Map<Integer, List<Map<String, Object>>>) resultMap.get(gradePhase);
			if(gradePhaseMap==null){
				gradePhaseMap = new HashMap<Integer, List<Map<String, Object>>>();
			}
			quizPointList = gradePhaseMap.get(questionType);
			if(quizPointList==null){
				quizPointList = new ArrayList<Map<String, Object>>();
			}
			quizPointMap = new HashMap<String, Object>();
			quizPointMap.put("id", id);
			quizPointMap.put("name", name);
			quizPointList.add(quizPointMap);
			gradePhaseMap.put(questionType, quizPointList);
			resultMap.put(gradePhase, gradePhaseMap);
		}
		RedBookRedisManager.setResourceBean(QUIZ_POINT_MAP, resultMap);
	}
	
	/**
	 * 获取学段列表
	 * @return
	 */
	public List<Map<String, Object>> getGradePhaseList(){
		return (List<Map<String, Object>>) RedBookRedisManager.getResourceBean(GRADE_PHASE_LIST);
	}
	
	/**
	 * 获取年级列表
	 * @return
	 */
	public List<Map<String, Object>> getGradeList(){
		return (List<Map<String, Object>>) RedBookRedisManager.getResourceBean(GRADE_LIST);
	}
	
	/**
	 * 获取试卷类型列表
	 * @return
	 */
	public List<Map<String, Object>> getPaperTypeList(){
		return (List<Map<String, Object>>) RedBookRedisManager.getResourceBean(PAPER_TYPE_LIST);
	}
	
	/**
	 * 获取知识点列表
	 * @return
	 */
	public Map<String, List<Map<String, Object>>> getKnowledgePointMap(){
		return (Map<String, List<Map<String, Object>>>) RedBookRedisManager.getResourceBean(KNOWLEDGE_POINT_MAP);
	}
	
	/**
	 * 获取试题类型列表
	 * @return
	 */
	public List<Map<String, Object>> getQuestionTypeList(){
		return (List<Map<String, Object>>) RedBookRedisManager.getResourceBean(QUESTION_TYPE_LIST);
	}
	
	/**
	 * 获取试题难度列表
	 * @return
	 */
	public List<Map<String, Object>> getQuestionDifficultyList(){
		return (List<Map<String, Object>>) RedBookRedisManager.getResourceBean(QUESTION_DIFFICULTY_LIST);
	}
	
	/**
	 * 根据parentId获取区域列表
	 * @param parentId 0：代表省级列表
	 * @return
	 */
	public  List<Map<String, Object>> getAreaList(Integer parentId){
		Map<Integer, List<Map<String, Object>>> areaListMap = (Map<Integer, List<Map<String, Object>>>) RedBookRedisManager.getResourceBean(AREA_MAP);
		return areaListMap.get(parentId);
	}
	
	/**
	 * 获取题型与知识点的关联信息
	 * @return
	 */
	public  Map<String, Object> getQuestionKnowledgeMap(){
		return (Map<String, Object>) RedBookRedisManager.getResourceBean(QUESTION_KNOWLEDGE_MAP);
	}
	
	/**
	 * 获取话题配置信息
	 * @return
	 */
	public  Map<String, Object> getQuestionTopicMap(){
		return (Map<String, Object>) RedBookRedisManager.getResourceBean(QUESTION_TOPIC_MAP);
	}
	
	/**
	 * 获取考点配置信息
	 * @return
	 */
	public  Map<String, Object> getQuizPointMap(){
		return (Map<String, Object>) RedBookRedisManager.getResourceBean(QUIZ_POINT_MAP);
	}
}
