package com.woxue.resourcemanage.util;

import com.woxue.resourceservice.util.RedBookRedisManager;
import com.woxue.resourcemanage.entity.AdminBean;

/**
 * 管理员相关管理类
 */
public class AdminManager {
	private static final String ADMIN_LOGIN_PREFIX = "adminLoginToken:userName:";

	public static AdminBean getAdmin(String userName){
		Object bean = RedBookRedisManager.getResourceBean(ADMIN_LOGIN_PREFIX + userName);
		if(bean==null){
			return null;
		}
		return ((AdminBean)bean);
	}

	public static void saveToken(AdminBean sessionAdmin){
		RedBookRedisManager.setResourceBean(ADMIN_LOGIN_PREFIX+sessionAdmin.getName(),sessionAdmin);
	}

	public static void delToken(String userName){
		RedBookRedisManager.delResourceBean(ADMIN_LOGIN_PREFIX+userName);
	}

}
