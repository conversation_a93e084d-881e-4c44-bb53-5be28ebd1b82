package com.woxue.resourcemanage.util;

import com.woxue.resourcemanage.entity.AdminBean;
import io.jsonwebtoken.Claims;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

public class SysUserUtils {

    public static AdminBean getAdminBean(HttpServletRequest request) {
        String token = request.getHeader(ResourceManageJwtUtil.TOKEN_HEADER);
        //判断authorization是否存在
        if(null != token){
            try {
                //token认证成功
                Claims claimsJws = ResourceManageJwtUtil.parseJwt(token);
                //判断用户是不是退出登录
                if(claimsJws!=null && claimsJws.get(ResourceManageJwtUtil.ADMIN_NAME)!=null){
                    String userName = claimsJws.get(ResourceManageJwtUtil.ADMIN_NAME).toString();
                    return AdminManager.getAdmin(userName);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return null;
    }

    public static Boolean checkEdit(HttpServletRequest request){
        return getAdminBean(request)!=null&& Objects.requireNonNull(getAdminBean(request)).getRole()==1;
    }
}
