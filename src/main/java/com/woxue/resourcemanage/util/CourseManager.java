package com.woxue.resourcemanage.util;


import com.woxue.common.model.redBook.RedBookCourse;
import com.woxue.common.model.redBook.RedBookVersion;
import com.woxue.resourceservice.dao.IRedBookCourseDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程列表类（用于保存课程列表，以防每次都访问数据库）
 * <AUTHOR>
 *
 */
public class CourseManager {

	private boolean update=true;//是否需要更新

	private Map<String, Object> level1List = new HashMap<>();
	private Map<String, Object> level2List = new HashMap<>();
	private Map<String, Object> level3List = new HashMap<>();

	private static final CourseManager cm = new CourseManager();

	private CourseManager(){
	}

	public static CourseManager getInstanse(){
		return cm;
	}
	
	public Map<String, Object> getLevel1List() {
		return level1List;
	}

	public void setLevel1List(Map<String, Object> level1List) {
		this.level1List = level1List;
	}

	public Map<String, Object> getLevel2List() {
		return level2List;
	}

	public void setLevel2List(Map<String, Object> level2List) {
		this.level2List = level2List;
	}

	public Map<String, Object> getLevel3List() {
		return level3List;
	}

	public void setLevel3List(Map<String, Object> level3List) {
		this.level3List = level3List;
	}

	public boolean isUpdate() {
		return update;
	}

	public void setUpdate(boolean update) {
		this.update = update;
	}

	public boolean isNull(){
		if(level1List.size()<1||level2List.size()<1||level3List.size()<1){
			return true;
		}
        return update;
    }

	/**
	 * 课程从数据库查询
	 * @return
	 * @throws Exception
	 */
	public void treeGenerate(IRedBookCourseDao courseDao)throws Exception {
		ArrayList<HashMap<String, Object>> redBookCourses = new ArrayList<>();
		HashMap<String, Object> tmpMap = new HashMap<>();
		tmpMap.put("nameCn", "小学");
		tmpMap.put("id", 1);
		redBookCourses.add(tmpMap);
		HashMap<String, Object> tmpMap1 = new HashMap<>();
		tmpMap1.put("nameCn", "初中");
		tmpMap1.put("id", 2);
		redBookCourses.add(tmpMap1);
		HashMap<String, Object> tmpMap2 = new HashMap<>();
		tmpMap2.put("nameCn", "高中");
		tmpMap2.put("id", 3);
		redBookCourses.add(tmpMap2);
		HashMap<String, Object> tmpMap3 = new HashMap<>();
		tmpMap3.put("seriesList",redBookCourses);
		tmpMap3.put("count",redBookCourses.size());
		CourseManager.getInstanse().setLevel1List(tmpMap3);
		List<RedBookVersion> versionList = courseDao.getVersionListByType(1);
		HashMap<String, Object> tmpMap4 = new HashMap<>();
		tmpMap4.put("versionList",versionList);
		tmpMap4.put("count",versionList.size());
		CourseManager.getInstanse().setLevel2List(tmpMap4);
		ArrayList<RedBookCourse> bookCourses = new ArrayList<>();
		versionList.stream().forEach(redBookVersion -> {
			bookCourses.addAll(courseDao.getCourseList(redBookVersion.getId()));
		});
		HashMap<String, Object> tmpMap5 = new HashMap<>();
		tmpMap5.put("courseList",bookCourses);
		tmpMap5.put("count",bookCourses.size());
		CourseManager.getInstanse().setLevel3List(tmpMap5);
	}



 
}
