package com.woxue.resourcemanage.util;

import java.io.Serializable;
import java.util.List;

/**
 * 分页
 */
public class PageUtil implements Serializable {
    
    private static final long serialVersionUID = 1L;
    //当前页码
    private Integer pageIndex;
    //每页显示条数
    private Integer pageSize;
    //总条数
    private int total;
    //总页数
    private Integer pages;
    //接收全部数据的集合 data
    private List<?> data;

    public Integer getPages() {
        //     计算总页数
        this.pages = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
        return pages;
    }

    public PageUtil(Integer pageIndex , Integer pageSize, int total, List<?> list) {

        if (pageSize == null){
            this.pageSize = 10;
        } else{
            this.pageSize = pageSize;
        }
        if (pageIndex == null){
            this.pageIndex = 0;
        } else{
            this.pageIndex = (pageIndex == 1 ? 0 : pageIndex-1) * this.pageSize;
        }
        this.data = list;
        this.total = total;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }


    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public List<?> getData() {
        return data;
    }

    public void setData(List<?> data) {
        this.data = data;
    }
}
