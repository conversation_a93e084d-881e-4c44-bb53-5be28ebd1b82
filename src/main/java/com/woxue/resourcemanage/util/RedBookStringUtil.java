package com.woxue.resourcemanage.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import java.io.StringReader;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RedBookStringUtil {
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final String[] CHINESE_NUMBERS = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    /**
     * 是否包含中文
     *
     * @param str
     * @return
     */
    public static boolean isContainChinese(String str) {
        String regex = "[\\u4E00-\\u9FA5]";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(str);
        return m.find();
    }
    // 判断字符串是否包含英文字符
    public static boolean containsEnglish(String text) {
        String regex = "[a-zA-Z]";
        return text != null && Pattern.compile(regex).matcher(text).find();
    }

    /**
     * 校验是否是手机号
     */
    public static boolean isPhone(String phone) {
        /*
        1、判断是否是十一位
        2、判断是否是全数字
        3、判断是否是1开头
         */
        String regex = "^1[0-9]{10}$";
        if (phone.length() != 11) {
            return false;
        } else {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(phone);
            return m.matches();
        }
    }
    /**
     * 判断两个字符串匹配度
     *
     * @param str
     * @param target
     * @return
     */
    public static float getSimilarityRatio(String str, String target) {
        int d[][]; // 矩阵
        int n = str.length();
        int m = target.length();
        int i; // 遍历str的
        int j; // 遍历target的
        char ch1; // str的
        char ch2; // target的
        int temp; // 记录相同字符,在某个矩阵位置值的增量,不是0就是1
        if (n == 0 || m == 0) {
            return 0;
        }
        d = new int[n + 1][m + 1];
        for (i = 0; i <= n; i++) { // 初始化第一列
            d[i][0] = i;
        }

        for (j = 0; j <= m; j++) { // 初始化第一行
            d[0][j] = j;
        }

        for (i = 1; i <= n; i++) { // 遍历str
            ch1 = str.charAt(i - 1);
            // 去匹配target
            for (j = 1; j <= m; j++) {
                ch2 = target.charAt(j - 1);
                if (ch1 == ch2 || ch1 == ch2 + 32 || ch1 + 32 == ch2) {
                    temp = 0;
                } else {
                    temp = 1;
                }
                // 左边+1,上边+1, 左上角+temp取最小
                d[i][j] = Math.min(Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1), d[i - 1][j - 1] + temp);
            }
        }

        return (1 - (float) d[n][m] / Math.max(str.length(), target.length())) * 100F;
    }

    public static String transAphaNum(String aphaNum) {
        String result = "";
        switch (aphaNum) {
            case "1":
                result = "一";
                break;
            case "2":
                result = "二";
                break;
            case "3":
                result = "三";
                break;
            case "4":
                result = "四";
                break;
            case "5":
                result = "五";
                break;
            case "6":
                result = "六";
                break;
            case "7":
                result = "日";
                break;
        }
        return result;
    }


    public static String removeDuplicateSubstrings(String s, char delimiter) {
        if (s == null || s.isEmpty()) {
            return s;
        }

        // 使用 LinkedHashSet 保持插入顺序
        Set<String> uniqueSubstrings = new LinkedHashSet<>();

        // 分割字符串
        String[] substrings = s.split(String.valueOf(delimiter));

        // 将分割后的子字符串添加到 Set 中
        for (String substring : substrings) {
            uniqueSubstrings.add(substring);
        }

        // 构建新的字符串
        StringBuilder result = new StringBuilder();
        for (String substring : uniqueSubstrings) {
            // 去掉“退盟”及其括号
            substring = substring.replaceAll("\\（退盟\\）", "");
            result.append(substring).append(delimiter);
        }

        // 去掉最后一个多余的分隔符
        if (!result.toString().isEmpty()) {
            result.setLength(result.length() - 1);
        }

        return result.toString();
    }
    /**
     * 获取年级
     * @param nowGrade
     * @return
     */
    public static String getGradeName(Integer nowGrade){
        String gradeName = "其他";
        try {
            if (nowGrade == -1) {
                gradeName = "其他";
            } else if (nowGrade < 100) {//小学/初中
                String[] strs = {"一", "二", "三", "四", "五", "六", "七", "八", "九"};
                gradeName = strs[nowGrade / 10 - 1] + "年级";
            }else if(nowGrade==120){
                gradeName = "大学";
            }else if(nowGrade==130){
                gradeName = "出国";
            } else {//高中
                gradeName = "高中";//TODO 高中必修不同版本不好对应年级，这里暂时不显示具体是高几，课程优化了这里再做修改。
            }
        }catch (Exception e){

        }

        return gradeName;
    }
    /**
     * 验证手机号是否正确
     * @param phone
     * @return
     */
    public static Boolean verifyPhone(String phone) {
        Pattern pattern = Pattern.compile("^1[3-9]\\d{9}$");
        Matcher m = pattern.matcher(phone);
        return m.find();
    }

    /**
     * 验证密码是否正确
     * @param password
     * @return
     */
    public static Boolean verifyPassword(String password) {
        Pattern pattern = Pattern.compile("^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$");
        Matcher m = pattern.matcher(password);
        return m.find();
    }

    /**
     * 验证支付密码
     * @param password
     * @return
     */
    public static Boolean verifyPayPassword(String password) {
        Pattern pattern = Pattern.compile("^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,11}$");
        Matcher m = pattern.matcher(password);
        return m.find();
    }

    /**
     * 格式化手机号，中间四位****
     * @param phone
     * @return
     */
    public static String formatPhone(String phone) {
        StringBuilder stringBuilder = new StringBuilder(phone);
        StringBuilder replace = stringBuilder.replace(3, 7, "****");
        return replace.toString();
    }
    /*
     * <AUTHOR>
     * @Description  格式化数字，强制保留scale位数
     * @Date 2019/5/14  14:41
     * @Param [scale, data]
     * @return double
     **/
    public static double formatDoubleToDouble(double data, int scale) {
        return new BigDecimal(data).setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
    /**
     * 小数优化
     * @param value
     * @param scale
     * @return
     */
    public static String formatDoubleToStr(double value, int scale) {
        String format = String.format("%." + scale + "f", value);
        String[] split = format.split("\\.");
        String result=format;
        if(split.length==2){
            String s0 = split[0];
            String s1 = split[1];
            if("0".equals(s1)||"00".equals(s1)){
                result=s0;
            }
        }
        return result;
    }
    public static int formatDoubleToInt(double value, int scale) {
        String format = String.format("%." + scale + "f", value);
        return Integer.parseInt(format);
    }

    /**
     * 去除括号及括号之间的内容
     * @param str
     * @return
     */
    public static String removeParentheses(String str){
        if(StringUtils.isEmpty(str)){
            return "";
        }
        String regex = "\\(.*?\\)";
        String regex2 = "\\（.*?\\）";
        str = str.replaceAll(regex, "");
        str = str.replaceAll(regex2, "");
        return str;
    }
    /**
     * 判断是否是小写
     * @param c
     * @return
     */
    public static boolean isLowerCase(char c) {
        return c >=97 && c <= 122;
    }
    /**
     * 替换“认字母”为“字母”
     * @param name
     * @return
     */
    public static String replaceLetterName(String name){
        String result=name;
        if(StringUtils.isNotEmpty(name)&&name.contains("认字母")){
            result=name.replaceAll("认字母","字母");
        }
        return result;
    }
    //将数字学段转化为汉字字符串
    public static String transIntegerStageToCnStr(Integer stage){
        //1小学/2初中/3高中/4大学/5 出国/11小升初/21 中考
        String result="";
        switch (stage){
            case 1:
                result="小学";
                break;
            case 2:
                result="初中";
                break;
            case 3:
                result="高中";
                break;
            case 4:
                result="大学";
                break;
            case 5:
                result="出国";
                break;
            case 11:
                result="小升初";
                break;
            case 21:
                result="中考";
                break;
        }
        return result;
    }
    /**
     * 判断是否是json
     * @param input
     * @return
     */
    public static boolean isJson(String input) {
        try {
            // 尝试将字符串解析为Json对象
            ObjectMapper mapper = new ObjectMapper();
            mapper.readTree(input);
            return true;
        } catch (Exception e) {
            // 解析失败，说明字符串不是Json格式
            return false;
        }
    }



    /**
     * 去除html标签
     * @param input
     * @return
     */
    public static String removeHtmlTags(String input) {
        if(StringUtils.isEmpty(input)){
            return "";
        }
        // 正则表达式匹配HTML标签
        String regex = "<[^<>]*?>";
//        String regex = "<[^>]*>";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);
        // 替换所有匹配到的HTML标签为空字符串
        return matcher.replaceAll("");
    }

    public static String removeTags(String input) {
        // 定义正则表达式，匹配 <center> 和 </center> 标签
        String regexCenterStart = "<center>";
        String regexCenterEnd = "</center>";
        // 定义正则表达式，匹配 <right> 和 </right> 标签
        String regexRightStart = "<right>";
        String regexRightEnd = "</right>";
        // 使用 replaceAll 方法去除标签
        input = input.replaceAll(regexCenterStart, "");
        input = input.replaceAll(regexCenterEnd, "");
        input = input.replaceAll(regexRightStart, "");
        input = input.replaceAll(regexRightEnd, "");
        return input;
    }

    public static String generateSecureRandomString(int length) {
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder stringBuilder = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int index = secureRandom.nextInt(CHARACTERS.length());
            stringBuilder.append(CHARACTERS.charAt(index));
        }

        return stringBuilder.toString();
    }
    public static String numberToChinese(String numberStr) {
        StringBuilder result = new StringBuilder();
        for (char digitChar : numberStr.toCharArray()) {
            int digit = Character.getNumericValue(digitChar);
            result.append(CHINESE_NUMBERS[digit]);
        }
        return result.toString();
    }

    /**
     * 去除html标签
     * @param richText
     * @return
     */
    public static String convertRichTextToPlainText(String richText) {
        // 替换段落标签 <p> 和 </p> 为换行符 \n
        String plainText = richText.replaceAll("<p[^>]*>", "\n").replaceAll("</p>", "\n");
        // &nbsp;替换成空格
        plainText = plainText.replaceAll("&nbsp;", " ");
        // 去除所有其他HTML标签
        plainText = plainText.replaceAll("<[^>]*>", "");
        // 去除多余的换行符
        plainText = plainText.replaceAll("\\n+", "\n").trim();

        return plainText;
    }
    public static String extractChineseNew(String cn) {
        // 使用正则表达式匹配所有汉字
        String regex = "[\\u4e00-\\u9fa5]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(cn);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            result.append(matcher.group());
        }
        return result.toString();
    }
    //替换词汇中文音频文件用
    public static String extractNumberFromPath(String path) {
        // 使用正则表达式匹配数字部分
        String regex = "word-video/(\\d+)\\.wav";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(path);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null; // 或者抛出异常，根据需求决定
    }
}
