package com.woxue.resourcemanage.util;

import com.redbook.kid.common.model.NurserySongDO;
import com.redbook.kid.common.model.NurserySongSentenceDO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 儿歌数据验证工具类
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
@Slf4j
public class NurserySongValidatorUtil {

    /**
     * 验证儿歌基础信息
     */
    public static ValidationResult validateSong(NurserySongDO song) {
        ValidationResult result = new ValidationResult();
        
        if (song == null) {
            result.addError("儿歌对象不能为空");
            return result;
        }

        // 验证标题
        if (song.getTitle() == null || song.getTitle().trim().isEmpty()) {
            result.addError("儿歌标题不能为空");
        } else if (song.getTitle().length() > 100) {
            result.addError("儿歌标题长度不能超过100个字符");
        }

        // 验证视频URL
        if (song.getVideoUrl() == null || song.getVideoUrl().trim().isEmpty()) {
            result.addError("视频URL不能为空");
        } else if (!isValidUrl(song.getVideoUrl())) {
            result.addWarning("视频URL格式可能不正确");
        }

        // 验证原唱音频URL
        if (song.getOriginalAudioUrl() == null || song.getOriginalAudioUrl().trim().isEmpty()) {
            result.addError("原唱音频URL不能为空");
        } else if (!isValidUrl(song.getOriginalAudioUrl())) {
            result.addWarning("原唱音频URL格式可能不正确");
        }

        // 验证伴奏音频URL
        if (song.getBackgroundAudioUrl() == null || song.getBackgroundAudioUrl().trim().isEmpty()) {
            result.addError("伴奏音频URL不能为空");
        } else if (!isValidUrl(song.getBackgroundAudioUrl())) {
            result.addWarning("伴奏音频URL格式可能不正确");
        }

        // 验证字幕文件URL
        if (song.getSubtitleFileUrl() == null || song.getSubtitleFileUrl().trim().isEmpty()) {
            result.addError("字幕文件URL不能为空");
        } else if (!isValidUrl(song.getSubtitleFileUrl())) {
            result.addWarning("字幕文件URL格式可能不正确");
        }

        // 验证时长
        if (song.getDuration() == null || song.getDuration() <= 0) {
            result.addError("儿歌时长必须大于0");
        } else if (song.getDuration() > 600000) { // 10分钟
            result.addWarning("儿歌时长超过10分钟，请确认是否正确");
        }

        // 验证难度等级
        if (song.getDifficultyLevel() == null) {
            result.addError("难度等级不能为空");
        } else if (song.getDifficultyLevel() < 1 || song.getDifficultyLevel() > 5) {
            result.addError("难度等级必须在1-5之间");
        }

        // 验证描述长度
        if (song.getDescription() != null && song.getDescription().length() > 500) {
            result.addWarning("描述长度超过500个字符");
        }

        // 验证标签长度
        if (song.getTags() != null && song.getTags().length() > 200) {
            result.addWarning("标签长度超过200个字符");
        }

        return result;
    }

    /**
     * 验证句子列表
     */
    public static ValidationResult validateSentences(List<NurserySongSentenceDO> sentences, Integer songDuration) {
        ValidationResult result = new ValidationResult();
        
        if (sentences == null || sentences.isEmpty()) {
            result.addError("句子列表不能为空");
            return result;
        }

        // 验证句子数量
        if (sentences.size() > 100) {
            result.addWarning("句子数量超过100个，可能影响性能");
        }

        // 验证每个句子
        for (int i = 0; i < sentences.size(); i++) {
            NurserySongSentenceDO sentence = sentences.get(i);
            ValidationResult sentenceResult = validateSentence(sentence, i, songDuration);
            result.merge(sentenceResult);
        }

        // 验证时间轴连续性
        validateTimelineContinuity(sentences, result);

        return result;
    }

    /**
     * 验证单个句子
     */
    public static ValidationResult validateSentence(NurserySongSentenceDO sentence, int index, Integer songDuration) {
        ValidationResult result = new ValidationResult();
        String prefix = "句子[" + index + "] ";

        if (sentence == null) {
            result.addError(prefix + "句子对象不能为空");
            return result;
        }

        // 验证时间
        if (sentence.getStartTime() == null || sentence.getStartTime() < 0) {
            result.addError(prefix + "开始时间不能为空且必须大于等于0");
        }

        if (sentence.getEndTime() == null || sentence.getEndTime() <= 0) {
            result.addError(prefix + "结束时间不能为空且必须大于0");
        }

        if (sentence.getStartTime() != null && sentence.getEndTime() != null) {
            if (sentence.getStartTime() >= sentence.getEndTime()) {
                result.addError(prefix + "开始时间必须小于结束时间");
            }

            if (songDuration != null && sentence.getEndTime() > songDuration) {
                result.addWarning(prefix + "结束时间超过了儿歌总时长");
            }
        }

        // 验证歌词文本
        if (sentence.getLyricsText() == null || sentence.getLyricsText().trim().isEmpty()) {
            result.addError(prefix + "歌词文本不能为空");
        } else if (sentence.getLyricsText().length() > 200) {
            result.addWarning(prefix + "歌词文本长度超过200个字符");
        }

        // 验证时长
        if (sentence.getDuration() != null && sentence.getStartTime() != null && sentence.getEndTime() != null) {
            int calculatedDuration = sentence.getEndTime() - sentence.getStartTime();
            if (Math.abs(sentence.getDuration() - calculatedDuration) > 100) { // 允许100ms误差
                result.addWarning(prefix + "设置的时长与计算的时长不匹配");
            }
        }

        return result;
    }

    /**
     * 验证时间轴连续性
     */
    private static void validateTimelineContinuity(List<NurserySongSentenceDO> sentences, ValidationResult result) {
        for (int i = 0; i < sentences.size() - 1; i++) {
            NurserySongSentenceDO current = sentences.get(i);
            NurserySongSentenceDO next = sentences.get(i + 1);

            if (current.getEndTime() != null && next.getStartTime() != null) {
                int gap = next.getStartTime() - current.getEndTime();
                
                if (gap < 0) {
                    result.addWarning("句子[" + i + "]和句子[" + (i + 1) + "]时间重叠");
                } else if (gap > 5000) { // 5秒间隔
                    result.addWarning("句子[" + i + "]和句子[" + (i + 1) + "]时间间隔过大(" + gap + "ms)");
                }
            }
        }
    }

    /**
     * 简单的URL格式验证
     */
    private static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        return url.startsWith("http://") || url.startsWith("https://") || url.startsWith("/");
    }

    /**
     * 验证结果类
     */
    @Data
    public static class ValidationResult {
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();

        public void addError(String error) {
            errors.add(error);
        }

        public void addWarning(String warning) {
            warnings.add(warning);
        }

        public boolean isValid() {
            return errors.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }

        public void merge(ValidationResult other) {
            if (other != null) {
                this.errors.addAll(other.getErrors());
                this.warnings.addAll(other.getWarnings());
            }
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            
            if (!errors.isEmpty()) {
                sb.append("错误: ").append(String.join("; ", errors));
            }
            
            if (!warnings.isEmpty()) {
                if (sb.length() > 0) {
                    sb.append(" | ");
                }
                sb.append("警告: ").append(String.join("; ", warnings));
            }
            
            return sb.toString();
        }
    }
}
