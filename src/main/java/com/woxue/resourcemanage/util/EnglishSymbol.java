package com.woxue.resourcemanage.util;


public class EnglishSymbol
{

	/**
	 * 替换英文音标
	 * 
	 * @param sSymbol
	 * @return
	 */
	public static String getEnglishSymbol(String sSymbol)
	{
		String[] aReplace = { "0", "1", "2", "3", "4", "5", "6", "t7", "7",
				"8", "9", "d=", "=", ";" };
		String[] aReplaceWith = { "θ", "ɑ", "ʌ", "ə", "ε", "æ", "ɔ", "ʧ", "ʃ",
				"ð", "ŋ", "ʤ", "ʒ", "ֽ" };
		String sEngSymbol = sSymbol;
		if (sEngSymbol != null && !(sEngSymbol.equals(""))
				&& !(sEngSymbol.equalsIgnoreCase("null")))
		{
			for (int i = 0; i < aReplace.length; i++)
			{
				sEngSymbol = sEngSymbol.replace(aReplace[i], aReplaceWith[i]);
			}
		}
		else
		{
			sEngSymbol = "";
		}
		return sEngSymbol;
	}
	
	/**
	 * 转换英文音标
	 * 
	 * @param sSymbol
	 * @return
	 */
	public static String getEnglishSymbolToDb(String sSymbol)
	{
		String[] aReplaceWith = { "0", "1", "2", "3", "4", "5", "6", "t7", "7",
				"8", "9", "d=", "=", ";" };
		String[] aReplace = { "θ", "ɑ", "ʌ", "ə", "ε", "æ", "ɔ", "ʧ", "ʃ",
				"ð", "ŋ", "ʤ", "ʒ", "ֽ" };
		String sEngSymbol = sSymbol;
		if (sEngSymbol != null && !(sEngSymbol.equals(""))
				&& !(sEngSymbol.equalsIgnoreCase("null")))
		{
			for (int i = 0; i < aReplace.length; i++)
			{
				sEngSymbol = sEngSymbol.replace(aReplace[i], aReplaceWith[i]);
			}
		}
		else
		{
			sEngSymbol = "";
		}
		return sEngSymbol;
	}
}
