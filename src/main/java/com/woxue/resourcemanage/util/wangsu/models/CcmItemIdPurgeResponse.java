// This file is auto-generated, don't edit it. Thanks.
package com.woxue.resourcemanage.util.wangsu.models;

import com.aliyun.tea.*;

public class CcmItemIdPurgeResponse extends TeaModel {
    // {"en":"The status code of the task creation result, 1 means success, 0 means failure.","zh_CN":"表示任务创建结果的状态码，1表示成功，0表示失败"}
    @NameInMap("Code")
    @Validation(required = true)
    public Integer Code;

    // {"en":"Content system response message after submitting the task.", "zh_CN":"表示任务提交后，系统的响应消息"}
    @NameInMap("Message")
    @Validation(required = true)
    public String Message;

    // {"en":"After calling the API once and submitting the task successfully, the content system will return an itemId. This ID is the unique identifier for each submission. You can use itemId to batch query the status (success/failure) of the task.", "zh_CN":"调用一次接口并提交任务成功后，将返回一个iteamId，是当次提交任务的唯一标识，通过itemId可批量查询任务的状态（成功/失败）。"}
    @NameInMap("itemId")
    @Validation(required = true)
    public String itemId;

    public static CcmItemIdPurgeResponse build(java.util.Map<String, ?> map) throws Exception {
        CcmItemIdPurgeResponse self = new CcmItemIdPurgeResponse();
        return TeaModel.build(map, self);
    }

    public CcmItemIdPurgeResponse setCode(Integer Code) {
        this.Code = Code;
        return this;
    }
    public Integer getCode() {
        return this.Code;
    }

    public CcmItemIdPurgeResponse setMessage(String Message) {
        this.Message = Message;
        return this;
    }
    public String getMessage() {
        return this.Message;
    }

    public CcmItemIdPurgeResponse setItemId(String itemId) {
        this.itemId = itemId;
        return this;
    }
    public String getItemId() {
        return this.itemId;
    }

}
