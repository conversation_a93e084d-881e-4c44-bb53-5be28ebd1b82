package com.woxue.resourcemanage.util.wangsu.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class CryptoUtils {
	private CryptoUtils(){}

	/**
	 * hmac+sha256+hex
	 */
	public static String sha256Hex(String s) throws NoSuchAlgorithmException {
		MessageDigest md = MessageDigest.getInstance("SHA-256");
		byte[] d = md.digest(s.getBytes(StandardCharsets.UTF_8));
		return DatatypeConverter.printHexBinary(d).toLowerCase();
	}

	/**
	 * hmac+sha256
	 */
	public static byte[] hmac256(byte[] key, String msg) throws NoSuchAlgorithmException, InvalidKeyException {
		Mac mac = Mac.getInstance("HmacSHA256");
		SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
		mac.init(secretKeySpec);
		return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
	}

}
