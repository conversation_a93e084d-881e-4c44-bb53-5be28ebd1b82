package com.woxue.resourcemanage.util.wangsu.model;

import com.alibaba.fastjson.JSON;
import com.woxue.resourcemanage.util.wangsu.common.Constant;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.HttpPost;

import java.util.*;

@Setter
@Getter
public class HttpRequestMsg {

    private String uri;
    private String url;
    private String host;
    private String method;
    private String protocol;
    private Map<String, String> params;
    private Map<String, String> headers;
    private String body;
    private String signedHeaders;
    private Object msg;

    public HttpRequestMsg() {
        params = new HashMap<>(4);
        headers = new HashMap<>(8);
        putHeader(HttpHeaders.CONTENT_TYPE, Constant.APPLICATION_JSON);
        putHeader(Constant.X_CNC_AUTH_METHOD, Constant.AUTH_METHOD);
    }

    public void putParam(String name, String value) {
        params.put(name, value);
    }

    public String getParam(String name) {
        String values = params.get(name);
        return StringUtils.isNotBlank(values) ? values : null;
    }

    public String getQueryString() {
        int index = uri.indexOf("?");
        if (HttpPost.METHOD_NAME.equals(method) || index == -1) {
            return "";
        }
        return uri.substring(index + 1);
    }

    public void putHeader(String name, String value) {
        headers.put(name, value);
    }

    public String getHeader(String name) {
        String value = null;
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            if (StringUtils.equalsIgnoreCase(entry.getKey(), name)) {
                value = entry.getValue();
                break;
            }
        }
        return value;
    }

    public Optional<String> getHeader(String... names) {
        return Arrays.stream(names).map(this::getHeader).filter(Objects::nonNull).findFirst();
    }

    public void removeHeader(String name) {
        headers.keySet().removeIf(s -> StringUtils.equalsIgnoreCase(s, name));
    }

    public void setJsonBody(Object object) {
        body = JSON.toJSONString(object);
    }

}
