package com.woxue.resourcemanage.util.wangsu.util;

import com.woxue.resourcemanage.util.wangsu.exception.ApiAuthException;
import com.woxue.resourcemanage.util.wangsu.model.HttpRequestMsg;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class HttpUtils {

    private HttpUtils() {}

    private static final Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    public static String call(HttpRequestMsg requestMsg) {
        CloseableHttpClient client = HttpClientBuilder.create().build();
        CloseableHttpResponse response = null;
        try {
            switch (requestMsg.getMethod()) {
                case HttpPost.METHOD_NAME:
                    HttpPost post = new HttpPost(requestMsg.getUrl());
                    setRequestHeaders(post, requestMsg);
                    if (StringUtils.isNotBlank(requestMsg.getBody())) {
                        post.setEntity(new StringEntity(requestMsg.getBody(), StandardCharsets.UTF_8));
                    }
                    response = client.execute(post);
                    break;
                case HttpGet.METHOD_NAME:
                    HttpGet get = new HttpGet(requestMsg.getUrl());
                    setRequestHeaders(get, requestMsg);
                    response = client.execute(get);
                    break;
                case HttpPut.METHOD_NAME:
                    HttpPut put = new HttpPut(requestMsg.getUrl());
                    if (StringUtils.isNotBlank(requestMsg.getBody())) {
                        put.setEntity(new StringEntity(requestMsg.getBody(), StandardCharsets.UTF_8));
                    }
                    setRequestHeaders(put, requestMsg);
                    response = client.execute(put);
                    break;
                case HttpDelete.METHOD_NAME:
                    HttpDelete delete = new HttpDelete(requestMsg.getUrl());
                    setRequestHeaders(delete, requestMsg);
                    response = client.execute(delete);
                    break;
                default:
                    logger.error("not support this http method : {}", requestMsg.getMethod());
                    break;
            }
            if (response == null) {
                throw new ApiAuthException("api invoke fail. response is null.");
            }
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("api invoke fail.", e);
        } finally {
            closeClient(client);
            closeResponse(response);
        }
        return null;
    }

    private static void closeClient(CloseableHttpClient client) {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                logger.error("client close fail.", e);
            }
        }
    }

    private static void closeResponse(CloseableHttpResponse response) {
        if (response != null) {
            try {
                response.close();
            } catch (IOException e) {
                logger.error("response close fail.", e);
            }
        }
    }

    private static void setRequestHeaders(HttpRequestBase request, HttpRequestMsg requestMsg) {
        if (!requestMsg.getHeaders().isEmpty()) {
            for (Map.Entry<String, String> header : requestMsg.getHeaders().entrySet()) {
                request.setHeader(header.getKey(), header.getValue());
            }
        }
    }
}
