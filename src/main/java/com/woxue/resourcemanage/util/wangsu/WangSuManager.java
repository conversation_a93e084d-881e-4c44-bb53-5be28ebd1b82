package com.woxue.resourcemanage.util.wangsu;

import com.alibaba.fastjson.JSON;
import com.woxue.common.util.GsonManager;
import com.woxue.resourcemanage.util.wangsu.auth.AkSkAuth;
import com.woxue.resourcemanage.util.wangsu.model.AkSkConfig;
import com.woxue.resourcemanage.util.wangsu.model.WangSuResponse;
import com.woxue.resourcemanage.util.wangsu.models.CcmItemIdPurgeRequest;

import java.util.Arrays;
import java.util.Collections;

/**
 * 网宿
 * <AUTHOR>
 * @date 2022/7/4 10:19
 */
public class WangSuManager {
    final static String accessKey = "Ncq1TUvdCTmyJrVf2zxH0Afq91yFUrVH7QnC";
    final static String accessKeySecret = "****************************************************************";


    public static WangSuResponse clearDir(String dir) {

        CcmItemIdPurgeRequest ccmItemIdPurgeRequest = new CcmItemIdPurgeRequest();
        ccmItemIdPurgeRequest.setDirs(Collections.singletonList("https://sound.hssenglish.com/" + dir));

        AkSkConfig akskConfig = new AkSkConfig();
        // start: you can edit the value
        akskConfig.setAccessKey(accessKey);
        akskConfig.setSecretKey(accessKeySecret);
        akskConfig.setEndPoint("{endPoint}");
        akskConfig.setUri("/ccm/purge/ItemIdReceiver");
        akskConfig.setMethod("POST");
        // end: you can edit the value

        // see com.cnc.wplus.auth.AkSkAuth for more methods, you can edit
        String responseStr = AkSkAuth.invoke(akskConfig, JSON.toJSONString(ccmItemIdPurgeRequest.toMap()));
        System.out.println(responseStr);
        return GsonManager.fromJson(responseStr, WangSuResponse.class);
    }

    public static WangSuResponse clearUrl(String url) {

        CcmItemIdPurgeRequest ccmItemIdPurgeRequest = new CcmItemIdPurgeRequest();
        ccmItemIdPurgeRequest.setUrls(Collections.singletonList("https://sound.hssenglish.com/" + url));

        AkSkConfig akskConfig = new AkSkConfig();
        // start: you can edit the value
        akskConfig.setAccessKey(accessKey);
        akskConfig.setSecretKey(accessKeySecret);
        akskConfig.setEndPoint("{endPoint}");
        akskConfig.setUri("/ccm/purge/ItemIdReceiver");
        akskConfig.setMethod("POST");
        // end: you can edit the value

        // see com.cnc.wplus.auth.AkSkAuth for more methods, you can edit
        String responseStr = AkSkAuth.invoke(akskConfig, JSON.toJSONString(ccmItemIdPurgeRequest.toMap()));
        System.out.println(responseStr);
        return GsonManager.fromJson(responseStr, WangSuResponse.class);
    }
}
