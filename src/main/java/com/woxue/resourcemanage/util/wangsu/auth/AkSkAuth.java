package com.woxue.resourcemanage.util.wangsu.auth;

import com.woxue.resourcemanage.util.wangsu.common.Constant;
import com.woxue.resourcemanage.util.wangsu.exception.ApiAuthException;
import com.woxue.resourcemanage.util.wangsu.model.AkSkConfig;
import com.woxue.resourcemanage.util.wangsu.model.HttpRequestMsg;
import com.woxue.resourcemanage.util.wangsu.util.CryptoUtils;
import com.woxue.resourcemanage.util.wangsu.util.HttpUtils;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeSet;

/**
 * <AUTHOR>
 */
public class AkSkAuth {
    private static final Logger logger = LoggerFactory.getLogger(AkSkAuth.class);

    public static String post(AkSkConfig akSkConfig, Object object) {
        HttpRequestMsg requestMsg = new HttpRequestMsg();
        requestMsg.setUri(akSkConfig.getUri());
        requestMsg.setMethod(HttpPost.METHOD_NAME);
        requestMsg.setSignedHeaders(getSignedHeaders(akSkConfig.getSignedHeaders()));
        requestMsg.setJsonBody(object);
        return invoke(requestMsg, akSkConfig.getAccessKey(), akSkConfig.getSecretKey());
    }

    public static String post(HttpRequestMsg requestMsg, String accessKey, String secretKey) {
        requestMsg.setMethod(HttpPost.METHOD_NAME);
        return invoke(requestMsg, accessKey, secretKey);
    }

    public static String get(AkSkConfig akSkConfig) {
        HttpRequestMsg requestMsg = new HttpRequestMsg();
        requestMsg.setUri(akSkConfig.getUri());
        requestMsg.setMethod(HttpGet.METHOD_NAME);
        return invoke(requestMsg, akSkConfig.getAccessKey(), akSkConfig.getSecretKey());
    }

    public static String get(HttpRequestMsg requestMsg, String accessKey, String secretKey) {
        requestMsg.setMethod(HttpGet.METHOD_NAME);
        return invoke(requestMsg, accessKey, secretKey);
    }

    public static String put(AkSkConfig akSkConfig, Object object) {
        HttpRequestMsg requestMsg = new HttpRequestMsg();
        requestMsg.setUri(akSkConfig.getUri());
        requestMsg.setMethod(HttpPut.METHOD_NAME);
        requestMsg.setSignedHeaders(getSignedHeaders(akSkConfig.getSignedHeaders()));
        requestMsg.setJsonBody(object);
        return invoke(requestMsg, akSkConfig.getAccessKey(), akSkConfig.getSecretKey());
    }

    public static String put(HttpRequestMsg requestMsg, String accessKey, String secretKey) {
        requestMsg.setMethod(HttpPut.METHOD_NAME);
        return invoke(requestMsg, accessKey, secretKey);
    }

    public static String delete(AkSkConfig akSkConfig) {
        HttpRequestMsg requestMsg = new HttpRequestMsg();
        requestMsg.setUri(akSkConfig.getUri());
        requestMsg.setMethod(HttpDelete.METHOD_NAME);
        return invoke(requestMsg, akSkConfig.getAccessKey(), akSkConfig.getSecretKey());
    }

    public static String delete(HttpRequestMsg requestMsg, String accessKey, String secretKey) {
        requestMsg.setMethod(HttpDelete.METHOD_NAME);
        return invoke(requestMsg, accessKey, secretKey);
    }

    public static String invoke(HttpRequestMsg requestMsg, String accessKey, String secretKey) {
        try {
            getAuthAndSetHeaders(requestMsg, accessKey, secretKey);
            return HttpUtils.call(requestMsg);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ApiAuthException("api invoke fail.", e);
        }
    }

    public static String invoke(AkSkConfig akSkConfig, String jsonBody) {
        try {
            HttpRequestMsg requestMsg = transferHttpRequestMsg(akSkConfig, jsonBody);
            getAuthAndSetHeaders(requestMsg, akSkConfig.getAccessKey(), akSkConfig.getSecretKey());
            return HttpUtils.call(requestMsg);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ApiAuthException("api invoke fail.", e);
        }
    }

    private static HttpRequestMsg transferHttpRequestMsg(AkSkConfig akSkConfig, String jsonBody) {
        HttpRequestMsg requestMsg = new HttpRequestMsg();
        requestMsg.setUri(akSkConfig.getUri());
        if (StringUtils.isNotBlank(akSkConfig.getEndPoint())  && !Constant.END_POINT.equals(akSkConfig.getEndPoint())) {
            requestMsg.setHost(akSkConfig.getEndPoint());
            requestMsg.setUrl(Constant.HTTPS_REQUEST_PREFIX + akSkConfig.getEndPoint() + requestMsg.getUri());
        } else {
            requestMsg.setHost(Constant.HTTP_DOMAIN);
            requestMsg.setUrl(Constant.HTTP_REQUEST_PREFIX + requestMsg.getUri());
        }
        requestMsg.setMethod(akSkConfig.getMethod());
        requestMsg.setSignedHeaders(getSignedHeaders(akSkConfig.getSignedHeaders()));
        String requestMethod = akSkConfig.getMethod();
        if (requestMethod.equals(HttpPost.METHOD_NAME) || requestMethod.equals(HttpPut.METHOD_NAME)) {
            requestMsg.setBody(jsonBody);
        }
        return requestMsg;
    }

    private static void getAuthAndSetHeaders(HttpRequestMsg requestMsg, String accessKey, String secretKey) {
        try {
            String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
            requestMsg.putHeader(HttpHeaders.HOST, requestMsg.getHost());
            requestMsg.putHeader(Constant.HEAD_SIGN_ACCESS_KEY, accessKey);
            requestMsg.putHeader(Constant.HEAD_SIGN_TIMESTAMP, timeStamp);
            String signature = getSignature(requestMsg, secretKey, timeStamp);
            requestMsg.putHeader(HttpHeaders.AUTHORIZATION, genAuthorization(accessKey, getSignedHeaders(requestMsg.getSignedHeaders()), signature));
        } catch (Exception e) {
            logger.error("aksk get authorization fail.", e);
        }
    }

    /**
     * Authorization =
     * Algorithm + ' ' +
     * 'Credential=' + AccessKey + ', ' +
     * 'SignedHeaders=' + SignedHeaders + ', ' +
     * 'Signature=' + Signature
     */
    private static String genAuthorization(String accessKey, String signedHeaders, String signature) {
        return Constant.HEAD_SIGN_ALGORITHM + " " +
                "Credential=" + accessKey + ", " +
                "SignedHeaders=" + signedHeaders + ", " +
                "Signature=" + signature;
    }

    /**
     * 1,Signature=HexEncode(HMAC_SHA256(SecretKey, StringToSign));
     * 2,Stringtosign=Algorithm+\n+Timestamp+\n+Hash.SHA256(CanonicalRequest)
     * 3,CanonicalRequest=
     * Method+’\n’+
     * RequestURI+’\n’+
     * QueryString+’\n’+
     * CanonicalHeaders+’\n’+
     * SignedHeaders+’\n’+
     * HashedRequestPayload
     * 4,HashedRequestPayload= Lowercase(HexEncode(Hash.SHA256(RequestPayload)))
     */
    private static String getSignature(HttpRequestMsg requestMsg, String secretKey, String timestamp) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        String bodyStr = requestMsg.getBody();
        if (HttpGet.METHOD_NAME.equals(requestMsg.getMethod()) || HttpDelete.METHOD_NAME.equals(requestMsg.getMethod()) || StringUtils.isBlank(bodyStr)) {
            bodyStr = "";
        }
        String hashedRequestPayload = CryptoUtils.sha256Hex(bodyStr);
        String canonicalRequest = requestMsg.getMethod() + "\n" +
                requestMsg.getUri().split("\\?")[0] + "\n" +
                URLDecoder.decode(requestMsg.getQueryString(), CharEncoding.UTF_8) + "\n" +
                getCanonicalHeaders(requestMsg.getHeaders(), getSignedHeaders(requestMsg.getSignedHeaders())) + "\n" +
                getSignedHeaders(requestMsg.getSignedHeaders()) + "\n" +
                hashedRequestPayload;
        String stringToSign = Constant.HEAD_SIGN_ALGORITHM + "\n" + timestamp + "\n" + CryptoUtils.sha256Hex(canonicalRequest);
        return DatatypeConverter.printHexBinary(CryptoUtils.hmac256(secretKey.getBytes(StandardCharsets.UTF_8), stringToSign)).toLowerCase();
    }

    private static String getCanonicalHeaders(Map<String, String> headers, String signedHeaders) {
        String[] headerNames = signedHeaders.split(";");
        StringBuilder canonicalHeaders = new StringBuilder();
        for (String headerName : headerNames) {
            canonicalHeaders.append(headerName).append(":")
                    .append(getValueByHeader(headerName, headers).toLowerCase()).append("\n");
        }
        return canonicalHeaders.toString();
    }

    private static String getSignedHeaders(String signedHeaders) {
        if (StringUtils.isBlank(signedHeaders)) {
            return "content-type;host";
        }
        String[] headers = signedHeaders.split(";");
        TreeSet<String> signedHeaderSet = new TreeSet<>();
        for (String header : headers) {
            signedHeaderSet.add(header.toLowerCase());
        }
        StringBuilder newSignedHeaders = new StringBuilder();
        for (String s : signedHeaderSet) {
            newSignedHeaders.append(s).append(";");
        }
        return newSignedHeaders.substring(0, newSignedHeaders.length() - 1);
    }

    private static String getValueByHeader(String name, Map<String, String> customHeaderMap) {
        String value = null;
        for (Map.Entry<String, String> entry : customHeaderMap.entrySet()) {
            if (StringUtils.equalsIgnoreCase(entry.getKey(), name)) {
                value = entry.getValue();
                break;
            }
        }
        return value;
    }
}
