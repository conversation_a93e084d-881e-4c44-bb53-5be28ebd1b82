package com.woxue.resourcemanage.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * @description: config.properties配置文件
 * @author: TT
 * @create: 2021-08-19
 */
public class PropertiesUtils {
    private static Properties prop=null;
    public static String getProperty(String key) {
        if (prop == null) {
            prop = new Properties();
            try {
                InputStream in = PropertiesUtils.class.getResourceAsStream("/config.properties");
                prop.load(in);
                in.close();
            } catch (
                    IOException e) {
                e.printStackTrace();
            }
        }
        return prop.getProperty(key);
    }
}
