package com.woxue.resourcemanage.util;


import com.woxue.resourcemanage.entity.grammar.GrammarUnitContentBean;

import java.util.ArrayList;
import java.util.List;

public class ContentStack {
	private final List<GrammarUnitContentBean> list = new ArrayList<GrammarUnitContentBean>();
	
	public int size(){
		return list.size();
	}
	
	public boolean push(GrammarUnitContentBean content){
		list.add(content);
		return true;
	}
	
	public GrammarUnitContentBean pop(){
		if(list.size()==0){
			return null;
		}
		return list.remove(list.size()-1);
	}
	
	public GrammarUnitContentBean getTop(){
		if(list.size()==0){
			return null;
		}
		return list.get(list.size()-1);
	}
	
	public boolean clear(){
		list.clear();
		return true;
	}
	
	private int index = 1;
	public int counter(){
		return index++;
	}
}
