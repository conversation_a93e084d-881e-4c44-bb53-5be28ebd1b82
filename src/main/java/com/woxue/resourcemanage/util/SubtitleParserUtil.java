package com.woxue.resourcemanage.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字幕解析工具类
 * 支持 SRT、VTT、JSON 等格式的字幕文件解析
 *
 * <AUTHOR>
 * @since 2025/01/24
 */
@Slf4j
public class SubtitleParserUtil {

    /**
     * SRT 格式正则表达式
     */
    private static final Pattern SRT_PATTERN = Pattern.compile(
            "(\\d+)\\s*\\n([\\d:,]+)\\s*-->\\s*([\\d:,]+)\\s*\\n([\\s\\S]*?)(?=\\n\\d+\\s*\\n|\\n*$)"
    );

    /**
     * VTT 时间格式正则表达式
     */
    private static final Pattern VTT_TIME_PATTERN = Pattern.compile(
            "([\\d:.,]+)\\s*-->\\s*([\\d:.,]+)"
    );

    /**
     * 解析字幕文件
     *
     * @param content 字幕文件内容
     * @param fileName 文件名（用于判断格式）
     * @return 句子时间轴列表
     */
    public static List<SentenceTimeline> parseSubtitle(String content, String fileName) {
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("字幕内容不能为空");
        }

        String extension = getFileExtension(fileName);
        
        switch (extension.toLowerCase()) {
            case "srt":
                return parseSRT(content);
            case "vtt":
                return parseVTT(content);
            case "json":
                return parseJSON(content);
            default:
                throw new UnsupportedOperationException("不支持的字幕格式: " + extension);
        }
    }

    /**
     * 解析 SRT 格式字幕
     */
    public static List<SentenceTimeline> parseSRT(String content) {
        List<SentenceTimeline> timelines = new ArrayList<>();
        
        Matcher matcher = SRT_PATTERN.matcher(content);
        int index = 0;
        
        while (matcher.find()) {
            try {
                String startTimeStr = matcher.group(2);
                String endTimeStr = matcher.group(3);
                String text = matcher.group(4).trim();
                
                // 清理文本中的HTML标签和特殊字符
                text = cleanText(text);
                
                if (text != null && !text.isEmpty()) {
                    int startTime = parseTimeToMillis(startTimeStr, true);
                    int endTime = parseTimeToMillis(endTimeStr, true);
                    
                    // 检查是否存在相同时间戳的字幕（双语字幕处理）
                    SentenceTimeline existingTimeline = findTimelineByTime(timelines, startTime, endTime);
                    
                    if (existingTimeline != null) {
                        // 合并双语字幕：将新文本添加到现有文本后面，用换行符分隔
                        String combinedText = existingTimeline.getText() + "\n" + text;
                        existingTimeline.setText(combinedText);
                        
                        // 设置双语字幕标记和分离的英中文本
                        existingTimeline.setIsBilingual(true);
                        String existingText = existingTimeline.getText().split("\n")[0];
                        
                        // 判断哪个是英文，哪个是中文（简单判断：包含中文字符的为中文）
                        if (containsChinese(existingText)) {
                            existingTimeline.setChineseText(existingText);
                            existingTimeline.setEnglishText(text);
                        } else {
                            existingTimeline.setEnglishText(existingText);
                            existingTimeline.setChineseText(text);
                        }
                        
                        log.debug("合并双语字幕: {} -> {}", text, combinedText);
                    } else {
                        // 创建新的时间轴条目
                        SentenceTimeline timeline = new SentenceTimeline();
                        timeline.setIndex(index++);
                        timeline.setStartTime(startTime);
                        timeline.setEndTime(endTime);
                        timeline.setText(text);
                        timeline.setDuration(endTime - startTime);
                        
                        timelines.add(timeline);
                    }
                }
            } catch (Exception e) {
                log.warn("解析SRT字幕条目失败: {}", e.getMessage());
            }
        }
        
        return timelines;
    }

    /**
     * 解析 VTT 格式字幕
     */
    public static List<SentenceTimeline> parseVTT(String content) {
        List<SentenceTimeline> timelines = new ArrayList<>();
        
        String[] lines = content.split("\n");
        int index = 0;
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            
            Matcher timeMatcher = VTT_TIME_PATTERN.matcher(line);
            if (timeMatcher.find()) {
                try {
                    String startTimeStr = timeMatcher.group(1);
                    String endTimeStr = timeMatcher.group(2);
                    
                    // 获取下一行的文本内容
                    StringBuilder textBuilder = new StringBuilder();
                    for (int j = i + 1; j < lines.length; j++) {
                        String textLine = lines[j].trim();
                        if (textLine.isEmpty()) {
                            break;
                        }
                        if (VTT_TIME_PATTERN.matcher(textLine).find()) {
                            break;
                        }
                        if (textBuilder.length() > 0) {
                            textBuilder.append(" ");
                        }
                        textBuilder.append(textLine);
                    }
                    
                    String text = cleanText(textBuilder.toString());
                    
                    if (text != null && !text.isEmpty()) {
                        int startTime = parseTimeToMillis(startTimeStr, false);
                        int endTime = parseTimeToMillis(endTimeStr, false);
                        
                        SentenceTimeline timeline = new SentenceTimeline();
                        timeline.setIndex(index++);
                        timeline.setStartTime(startTime);
                        timeline.setEndTime(endTime);
                        timeline.setText(text);
                        timeline.setDuration(endTime - startTime);
                        
                        timelines.add(timeline);
                    }
                } catch (Exception e) {
                    log.warn("解析VTT字幕条目失败: {}", e.getMessage());
                }
            }
        }
        
        return timelines;
    }

    /**
     * 解析 JSON 格式字幕（简单实现）
     */
    public static List<SentenceTimeline> parseJSON(String content) {
        // 简单的JSON解析实现
        // 实际项目中建议使用Jackson或Gson
        List<SentenceTimeline> timelines = new ArrayList<>();
        log.warn("JSON字幕解析功能待完善");
        return timelines;
    }

    /**
     * 将时间字符串转换为毫秒
     */
    private static int parseTimeToMillis(String timeStr, boolean isSRT) {
        try {
            // 统一格式：将逗号替换为点号
            if (isSRT) {
                timeStr = timeStr.replace(",", ".");
            }
            
            String[] parts = timeStr.split(":");
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            
            String[] secondsParts = parts[2].split("\\.");
            int seconds = Integer.parseInt(secondsParts[0]);
            int millis = 0;
            
            if (secondsParts.length > 1) {
                String millisStr = secondsParts[1];
                // 确保毫秒部分是3位数
                if (millisStr.length() == 1) {
                    millisStr += "00";
                } else if (millisStr.length() == 2) {
                    millisStr += "0";
                } else if (millisStr.length() > 3) {
                    millisStr = millisStr.substring(0, 3);
                }
                millis = Integer.parseInt(millisStr);
            }
            
            return hours * 3600000 + minutes * 60000 + seconds * 1000 + millis;
        } catch (Exception e) {
            throw new IllegalArgumentException("时间格式错误: " + timeStr);
        }
    }

    /**
     * 根据时间戳查找已存在的时间轴条目（用于双语字幕合并）
     *
     * @param timelines 已解析的时间轴列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 匹配的时间轴条目，如果没有找到则返回null
     */
    private static SentenceTimeline findTimelineByTime(List<SentenceTimeline> timelines, int startTime, int endTime) {
        for (SentenceTimeline timeline : timelines) {
            if (timeline.getStartTime().equals(startTime) && timeline.getEndTime().equals(endTime)) {
                return timeline;
            }
        }
        return null;
    }

    /**
     * 判断文本是否包含中文字符
     *
     * @param text 待检查的文本
     * @return 如果包含中文字符返回true，否则返回false
     */
    private static boolean containsChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        for (char c : text.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS ||
                Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT) {
                return true;
            }
        }
        return false;
    }

    /**
     * 清理文本内容
     */
    private static String cleanText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 移除HTML标签
        text = text.replaceAll("<[^>]+>", "");
        // 移除多余的空白字符
        text = text.replaceAll("\\s+", " ");
        // 移除首尾空白
        text = text.trim();
        
        return text;
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1);
        }
        
        return "";
    }

    /**
     * 句子时间轴数据类
     */
    @Data
    public static class SentenceTimeline {
        /**
         * 句子序号
         */
        private Integer index;

        /**
         * 开始时间(毫秒)
         */
        private Integer startTime;

        /**
         * 结束时间(毫秒)
         */
        private Integer endTime;

        /**
         * 歌词文本（双语字幕会用换行符分隔）
         */
        private String text;

        /**
         * 歌词拼音（可选）
         */
        private String pinyin;

        /**
         * 句子时长(毫秒)
         */
        private Integer duration;

        /**
         * 英文文本（从双语字幕中提取）
         */
        private String englishText;

        /**
         * 中文文本（从双语字幕中提取）
         */
        private String chineseText;

        /**
         * 是否为双语字幕
         */
        private Boolean isBilingual = false;
    }
}
