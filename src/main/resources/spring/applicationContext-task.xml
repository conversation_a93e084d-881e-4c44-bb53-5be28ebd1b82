<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

	<context:component-scan base-package="com.woxue.**" />
	<!-- 定时任务配置 -->
	<task:annotation-driven scheduler="myScheduler" />
	<task:scheduler id="myScheduler" pool-size="5"/>

</beans>
