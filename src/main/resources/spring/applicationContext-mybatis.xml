<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd">


	<!-- 数据库连接池 -->
	<bean id="writeDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName" ><value>java:comp/env/jdbc/REDBOOK_CONTENT_WRITE</value></property>
	</bean>
	<bean id="readDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName" ><value>java:comp/env/jdbc/REDBOOK_CONTENT_READ</value></property>
	</bean>


	<!-- 动态数据源，根据service接口上的注解来决定取哪个数据源 -->
	<bean id="dataSource" class="com.woxue.common.dataSource.DynamicDataSource">
		<property name="writeDataSource" ref="writeDataSource" />
		<property name="readDataSource" ref="readDataSource" />
	</bean>

	<!-- 配置sqlsessionFactory -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean" p:configLocation="classpath:mybatis/mybatis-config.xml">
		<property name="dataSource" ref="dataSource"/>
		<!-- 自动扫描entity目录，省略Configuration.xml里手工配置 -->
		<property name="mapperLocations"
			value="classpath*:com/woxue/**/mapper/*.xml" />
	</bean>

	<!-- 配置扫描包，加载mapper代理对象 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.woxue.**.dao"></property>
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
	</bean>


	<!-- 事务管理器 -->
	<bean id="transactionManager" class="com.woxue.common.dataSource.DynamicDataSourceTransactionManager"><!-- org.springframework.jdbc.datasource.DataSourceTransactionManager -->
		<!-- 数据源 -->
		<property name="dataSource" ref="dataSource" />
	</bean>

	<!-- 注解方式配置事物 -->
	<tx:annotation-driven transaction-manager="transactionManager" />

	<!-- 通知 -->
	<tx:advice id="transactionAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<!-- 传播行为 -->
			<tx:method name="save*" propagation="REQUIRED" />
			<tx:method name="insert*" propagation="REQUIRED" />
			<tx:method name="add*" propagation="REQUIRED" />
			<tx:method name="create*" propagation="REQUIRED" />
			<tx:method name="delete*" propagation="REQUIRED" />
			<tx:method name="update*" propagation="REQUIRED" />
			<tx:method name="display*" propagation="REQUIRED" />
			<tx:method name="do*" propagation="REQUIRED" />
			<tx:method name="query*" propagation="SUPPORTS" read-only="true" />
			<tx:method name="find*" propagation="SUPPORTS" read-only="true" />
			<tx:method name="select*" propagation="SUPPORTS" read-only="true" />
			<tx:method name="get*" propagation="SUPPORTS" read-only="true" />
		</tx:attributes>
	</tx:advice>

	<!-- 切面 -->
	<!-- <aop:config> <aop:advisor advice-ref="transactionAdvice" pointcut="execution(* 
		com.woxue.redbookresource.service..*.*(..))" /> </aop:config> -->


</beans>
