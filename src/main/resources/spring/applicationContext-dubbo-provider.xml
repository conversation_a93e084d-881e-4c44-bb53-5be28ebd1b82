<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
	   xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
	<context:property-placeholder location="classpath:*.properties" />
	<!-- 为当前服务提供者取个名字，并且提供给注册中心 -->
	<dubbo:application name="redbook-resource-dubbo"></dubbo:application>
	
	<!-- 注册中心的配置，使用zk暴露服务 -->
	<dubbo:registry protocol="zookeeper" address="${dubbo.registry.address}" timeout="50000"></dubbo:registry>
	
	<!-- 定义暴露服务的端口号 -->
	<dubbo:protocol name="dubbo" port="20844" threads="1000"></dubbo:protocol>
	
	<!-- 暴露具体的服务接口 -->
	<dubbo:service ref="dictionaryService" interface="com.woxue.redbookresource.service.IDictionaryService" retries="3" timeout="5000"></dubbo:service>
	<dubbo:service ref="otherResourceService" interface="com.woxue.redbookresource.service.IOtherResourceService" retries="3" timeout="5000"></dubbo:service>
	<dubbo:service ref="systemConfigService" interface="com.woxue.redbookresource.service.ISystemConfigService" retries="3" timeout="5000"></dubbo:service>

	<dubbo:service ref="redBookCourseService" interface="com.woxue.redbookresource.service.IRedBookCourseService" retries="3" timeout="5000"></dubbo:service>

	<dubbo:service ref="pictureBookService" interface="com.woxue.redbookresource.service.IPictureBookService" retries="3" timeout="5000"></dubbo:service>
	<dubbo:service ref="phoneticResourceService" interface="com.woxue.redbookresource.service.IPhoneticResourceService" retries="3" timeout="5000"></dubbo:service>
	<dubbo:service ref="readExpandResourceService" interface="com.woxue.redbookresource.service.IReadExpandResourceService" retries="3" timeout="5000"></dubbo:service>

	<dubbo:service ref="KidCourseService" interface="com.redbook.kid.resource.KidCourseService" retries="3" timeout="5000"></dubbo:service>
	<dubbo:service ref="NurserySongResourceService" interface="com.redbook.kid.resource.NurserySongResourceService" retries="3" timeout="5000"></dubbo:service>
	<dubbo:service ref="KidPhonicsResourceService" interface="com.redbook.kid.resource.KidPhonicsResourceService" retries="3" timeout="5000"></dubbo:service>
	<!-- 监控服务的注册 -->
	<!-- <dubbo:monitor protocol="registry"></dubbo:monitor> -->
</beans>
