<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd">

	<!-- controller包(自动注入) -->
	<context:component-scan base-package="com.woxue.resourcemanage.controller" />
	
	<!-- swagger -->
	<bean class="com.woxue.resourcemanage.config.SwaggerConfiguration" />
	<!--将静态资源交由默认的servlet处理-->
	<mvc:default-servlet-handler/>

	<mvc:annotation-driven>
		<mvc:message-converters>
			<bean class="com.woxue.resourcemanage.config.MmoJsonConverter"/>
			<bean class="com.woxue.resourcemanage.config.MmoStringConverter"/>
		</mvc:message-converters>
	</mvc:annotation-driven>

	
	<!-- 自动创建代理 对@AspectJ注解的支持 -->
	<!-- 通知spring使用cglib而不是jdk的来生成代理方法 AOP可以拦截到Controller --> 
	<aop:aspectj-autoproxy proxy-target-class="true"></aop:aspectj-autoproxy>

	<!-- 开启aop，对类代理 -->
	<aop:config proxy-target-class="true"></aop:config>

	<!-- 对模型视图名称的解析,即在模型视图名称添加前后缀 -->
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="/" />
		<property name="suffix" value=".jsp" />
	</bean>
	
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="defaultEncoding" value="UTF-8" />
		<property name="maxUploadSize" value="32505856" /><!-- 上传文件大小限制为31M，31*1024*1024 -->
		<property name="maxInMemorySize" value="4096" />
	</bean>
	
	<!-- 属性文件不能跨容器，要在controller中也能被访问到，也需要配置 -->
	<context:property-placeholder location="classpath:*.properties" />

</beans>
