<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.woxue</groupId>
		<artifactId>redbook-parent</artifactId>
		<version>7.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>redbook-resource</artifactId>
	<packaging>war</packaging>

	<dependencies>

		<dependency>
			<groupId>com.woxue</groupId>
			<artifactId>redbook-common</artifactId>
			<version>7.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.redbook</groupId>
			<artifactId>kid-common</artifactId>
			<version>1.0.2-SNAPSHOT</version>
		</dependency>

		<!-- JSP相关 -->
		<dependency>
			<groupId>jstl</groupId>
			<artifactId>jstl</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jsp-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

		<!--wangsu-->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.6</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>tea</artifactId>
			<version>[1.1.13, 2.0.0)</version>
		</dependency>
<!--
		<dependency>
			<groupId>edu.stanford.nlp</groupId>
			<artifactId>stanford-corenlp</artifactId>
			<version>4.5.1</version>
		</dependency>
		<dependency>
			<groupId>edu.stanford.nlp</groupId>
			<artifactId>stanford-corenlp</artifactId>
			<version>4.5.1</version>
			<classifier>models</classifier>
		</dependency>
-->

	</dependencies>
	<profiles>
		<profile>
			<id>dev</id><!--执行打包命令时将使用此处的id进行定位-->
			<properties>
				<env>conf-dev</env><!--此处为对应的环境放置配置文件的目录，上一步创建的为dev,这里设置为dev。下面两个目录配置参照此处-->
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault><!--此处将dev设置为默认环境-->
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env>conf-test</env>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<env>conf-prod</env>
			</properties>
		</profile>
		<!--小红本腾讯服务器-->
		<profile>
			<id>prod-redbook</id>
			<properties>
				<env>conf-prod-redbook</env>
			</properties>
		</profile>
	</profiles>
	<build>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>conf-dev/*</exclude>
					<exclude>conf-test/*</exclude>
					<exclude>conf-prod/*</exclude>
					<exclude>conf-prod-redbook/*</exclude>
				</excludes>
				<includes>
					<include>**/*.properties</include>
					<include>spring/*.xml</include>
					<include>mybatis/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource><!--此处设置是配置相应配置文件夹的路径-->
				<directory>src/main/resources/${env}</directory>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
				<configuration>
					<port>8083</port>
					<path>/redbook-resource</path>
					<useBodyEncodingForURI>true</useBodyEncodingForURI>
					<uriEncoding>UTF-8</uriEncoding>
					<mode>context</mode>
					<contextFile>src/main/resources/tomcat-context.xml</contextFile>
				</configuration>
			</plugin>
        </plugins>
	</build>
</project>