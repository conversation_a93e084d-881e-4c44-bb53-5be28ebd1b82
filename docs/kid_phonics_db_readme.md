# 少儿自然拼读资源管理系统数据库设计说明

## 一、概述

本文档描述了为少儿自然拼读资源管理系统设计的数据库结构。该系统将管理C1-C6共6个课程的自然拼读内容，包括音素、例词等资源。

## 二、表结构设计

### 1. 课程表 (kid_phonics_course)

管理C1-C6六个自然拼读课程的基本信息。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| course_code | VARCHAR(10) | 课程代码，如C1, C2等 |
| course_name_zh | VARCHAR(50) | 课程中文名称，如辅音泉洞 |
| course_name_en | VARCHAR(50) | 课程英文名称，如Consonants |
| description | TEXT | 课程描述 |
| cover_img | VARCHAR(255) | 封面图片URL |
| branch | INT | 分支号，累加更新 |
| publish_status | TINYINT | 发布状态：0-未发布，1-已发布 |
| publish_time | DATETIME | 发布时间 |
| sequence | INT | 显示顺序 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 2. 单元表 (kid_phonics_unit)

管理每个课程下的单元信息。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| course_id | INT | 关联课程ID |
| unit_code | VARCHAR(10) | 单元编码，如U1, U2等 |
| unit_name | VARCHAR(50) | 单元名称 |
| phoneme_type | VARCHAR(20) | 音素类型，三种类型：SINGLE_LETTER(单个字母)、VOWEL_COMBINATION(元音组合)、OTHER(其它) |
| description | TEXT | 单元描述 |
| sequence | INT | 显示顺序 |
| status | TINYINT | 状态：0-禁用，1-启用 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 3. 音素表 (kid_phonics_letter)

存储音素信息，包括单个字母音素和组合音素。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| unit_id | INT | 关联单元ID |
| letter | VARCHAR(10) | 音素文本，如b, am等 |
| ipa | VARCHAR(20) | 国际音标，如/b/, /æm/等 |
| is_combination | TINYINT | 是否组合音素：0-否，1-是 |
| sound_url | VARCHAR(255) | 基础发音音频URL |
| sequence | INT | 显示顺序 |
| status | TINYINT | 状态：0-禁用，1-启用 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 4. 音素组成部分表 (kid_phonics_letter_component)

存储组合音素的组成部分信息，如"am"由"a"和"m"组成。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| letter_id | INT | 关联音素ID |
| component | VARCHAR(5) | 组成部分字母，如a, m等 |
| sound_url | VARCHAR(255) | 组成部分音频URL |
| sequence | INT | 显示顺序 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 5. 例词表 (kid_phonics_word)

存储与音素关联的例词信息。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| letter_id | INT | 关联音素ID |
| word | VARCHAR(50) | 例词，如bag, dam等 |
| phonetic | VARCHAR(50) | 音标 |
| translation | VARCHAR(100) | 中文翻译，包含词性，如"n.垃圾箱" |
| full_sound_url | VARCHAR(255) | 完整发音音频URL |
| initial_sound_url | VARCHAR(255) | 首音发音音频URL |
| rhyme_sound_url | VARCHAR(255) | 韵尾发音音频URL |
| image_url | VARCHAR(255) | 图片URL |
| sequence | INT | 显示顺序 |
| status | TINYINT | 状态：0-禁用，1-启用 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 6. 儿歌表 (kid_phonics_rhyme)

存储每个单元的相关儿歌信息。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| unit_id | INT | 关联单元ID |
| title | VARCHAR(100) | 儿歌标题 |
| description | TEXT | 儿歌描述 |
| video_url | VARCHAR(255) | 视频URL |
| cover_img | VARCHAR(255) | 封面图片URL |
| duration | INT | 时长(秒) |
| sequence | INT | 显示顺序 |
| status | TINYINT | 状态：0-禁用，1-启用 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 7. 绘本表 (kid_phonics_picture_book)

存储每个单元的绘本资源。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| unit_id | INT | 关联单元ID |
| title | VARCHAR(100) | 绘本标题 |
| description | TEXT | 绘本描述 |
| book_url | VARCHAR(255) | 绘本文件URL |
| cover_img | VARCHAR(255) | 封面图片URL |
| page_count | INT | 页数 |
| sequence | INT | 显示顺序 |
| status | TINYINT | 状态：0-禁用，1-启用 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 8. 绘本内容表 (kid_phonics_picture_book_content)

存储绘本的每一页内容。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| picture_book_id | INT | 关联绘本ID |
| unit_id | INT | 关联单元ID |
| pic_url | VARCHAR(255) | 图片URL |
| sentence_num | INT | 句子数量 |
| sequence | INT | 显示顺序 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 9. 绘本句子表 (kid_phonics_picture_book_sentence)

存储绘本每一页的句子内容。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| content_id | INT | 关联绘本内容ID |
| picture_book_id | INT | 关联绘本ID |
| unit_id | INT | 关联单元ID |
| example_en_us | VARCHAR(500) | 英文例句 |
| example_en_us_element | VARCHAR(500) | 英文例句拆分 |
| example_zh_cn | VARCHAR(500) | 中文例句 |
| example_zh_py | VARCHAR(500) | 拼音 |
| speaker | VARCHAR(50) | 发言人 |
| sound_file | VARCHAR(255) | 英文音频URL |
| cn_sound_file | VARCHAR(255) | 中文音频URL |
| core_sentence | TINYINT | 是否核心句：0-否，1-是 |
| sequence | INT | 显示顺序 |
| create_time | DATETIME | 创建时间 |
| update_time | DATETIME | 更新时间 |

### 10. 发布记录表 (kid_phonics_publish_record)

记录课程发布历史。

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | INT | 主键ID |
| course_id | INT | 关联课程ID |
| branch | INT | 分支号 |
| publish_time | DATETIME | 发布时间 |
| publisher | VARCHAR(50) | 发布人 |
| remark | TEXT | 备注 |
| create_time | DATETIME | 创建时间 |

## 三、资源文件说明

### 1. 音素资源
* 单个字母音素(如"b"): 1个音频文件
  - 基础发音: `b.mp3`

* 组合音素(如"am"): 3个音频文件
  - 整体发音: `am.mp3`
  - 组成部分"a": `am_a.mp3`
  - 组成部分"m": `am_m.mp3`

### 2. 例词资源
每个例词3个音频文件:
  - 完整发音: `dam.mp3`
  - 首音发音: `dam_d.mp3`
  - 韵尾发音: `dam_am.mp3`

## 四、音素类型说明

数据库中使用简化的三种音素类型：

| 类型编码 | 类型名称 | 说明 |
| ------- | ------- | ---- |
| SINGLE_LETTER | 单个字母 | 包括单辅音(如b,c,d)和单元音(如a,e,i) |
| VOWEL_COMBINATION | 元音组合 | 短元音组合，如am,ad,en等 |
| OTHER | 其它 | 包括辅音连缀(bl,cl)、长元音(a_e,ai)、特殊组合等 |

通过这种简化的分类方式，可以灵活管理不同类型的音素资源，同时避免过度设计。具体的音素类型可以通过单元命名、课程划分等方式进一步区分。

## 五、数据关系说明

```
课程(kid_phonics_course) 1:n 单元(kid_phonics_unit)
单元(kid_phonics_unit) 1:n 音素(kid_phonics_letter)
音素(kid_phonics_letter) 1:n 音素组成部分(kid_phonics_letter_component) [仅组合音素]
音素(kid_phonics_letter) 1:n 例词(kid_phonics_word)
单元(kid_phonics_unit) 1:n 儿歌(kid_phonics_rhyme)
单元(kid_phonics_unit) 1:n 绘本(kid_phonics_picture_book)
绘本(kid_phonics_picture_book) 1:n 绘本内容(kid_phonics_picture_book_content)
绘本内容(kid_phonics_picture_book_content) 1:n 绘本句子(kid_phonics_picture_book_sentence)
课程(kid_phonics_course) 1:n 发布记录(kid_phonics_publish_record)
``` 