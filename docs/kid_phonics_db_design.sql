-- 少儿自然拼读数据库设计
-- 版本：1.0
-- 日期：2024年

-- 创建少儿自然拼读课程表
CREATE TABLE kid_phonics_course (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  course_code VARCHAR(10) NOT NULL COMMENT '课程代码，如C1, C2等',
  course_name_zh VARCHAR(50) NOT NULL COMMENT '课程中文名称，如辅音泉洞',
  course_name_en VARCHAR(50) NOT NULL COMMENT '课程英文名称，如Consonants',
  description TEXT COMMENT '课程描述',
  cover_img VARCHAR(255) COMMENT '封面图片URL',
  branch INT NOT NULL DEFAULT 1 COMMENT '分支号，累加更新',
  publish_status TINYINT NOT NULL DEFAULT 0 COMMENT '发布状态：0-未发布，1-已发布',
  publish_time DATETIME COMMENT '发布时间',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_course_code (course_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读课程表';

-- 创建单元表
CREATE TABLE kid_phonics_unit (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  course_id INT NOT NULL COMMENT '关联课程ID',
  unit_code VARCHAR(10) NOT NULL COMMENT '单元编码，如U1, U2等',
  unit_name VARCHAR(50) NOT NULL COMMENT '单元名称',
  phoneme_type VARCHAR(20) COMMENT '音素类型，三种类型：SINGLE_LETTER(单个字母)、VOWEL_COMBINATION(元音组合)、OTHER(其它)',
  description TEXT COMMENT '单元描述',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_course_unit (course_id, unit_code),
  KEY idx_course_id (course_id),
  CONSTRAINT fk_unit_course FOREIGN KEY (course_id) REFERENCES kid_phonics_course (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读单元表';

-- 创建音素表
CREATE TABLE kid_phonics_letter (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  unit_id INT NOT NULL COMMENT '关联单元ID',
  letter VARCHAR(10) NOT NULL COMMENT '音素文本，如b, am等',
  ipa VARCHAR(20) COMMENT '国际音标，如/b/, /æm/等',
  is_combination TINYINT NOT NULL DEFAULT 0 COMMENT '是否组合音素：0-否，1-是',
  sound_url VARCHAR(255) COMMENT '基础发音音频URL',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_unit_id (unit_id),
  CONSTRAINT fk_letter_unit FOREIGN KEY (unit_id) REFERENCES kid_phonics_unit (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读音素表';

-- 创建音素组成部分表（仅用于组合音素）
CREATE TABLE kid_phonics_letter_component (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  letter_id INT NOT NULL COMMENT '关联音素ID',
  component VARCHAR(5) NOT NULL COMMENT '组成部分字母，如a, m等',
  sound_url VARCHAR(255) COMMENT '组成部分音频URL',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_letter_id (letter_id),
  CONSTRAINT fk_component_letter FOREIGN KEY (letter_id) REFERENCES kid_phonics_letter (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读音素组成部分表';

-- 创建例词表
CREATE TABLE kid_phonics_word (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  letter_id INT NOT NULL COMMENT '关联音素ID',
  word VARCHAR(50) NOT NULL COMMENT '例词，如bag, dam等',
  phonetic VARCHAR(50) COMMENT '音标',
  translation VARCHAR(100) COMMENT '中文翻译，包含词性，如"n.垃圾箱"',
  full_sound_url VARCHAR(255) COMMENT '完整发音音频URL',
  initial_sound_url VARCHAR(255) COMMENT '首音发音音频URL',
  rhyme_sound_url VARCHAR(255) COMMENT '韵尾发音音频URL',
  image_url VARCHAR(255) COMMENT '图片URL',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_letter_id (letter_id),
  CONSTRAINT fk_word_letter FOREIGN KEY (letter_id) REFERENCES kid_phonics_letter (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读例词表';

-- 创建儿歌表
CREATE TABLE kid_phonics_rhyme (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  unit_id INT NOT NULL COMMENT '关联单元ID',
  title VARCHAR(100) NOT NULL COMMENT '儿歌标题',
  description TEXT COMMENT '儿歌描述',
  video_url VARCHAR(255) COMMENT '视频URL',
  cover_img VARCHAR(255) COMMENT '封面图片URL',
  duration INT COMMENT '时长(秒)',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_unit_id (unit_id),
  CONSTRAINT fk_rhyme_unit FOREIGN KEY (unit_id) REFERENCES kid_phonics_unit (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读儿歌表';

-- 创建绘本表
CREATE TABLE kid_phonics_picture_book (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  unit_id INT NOT NULL COMMENT '关联单元ID',
  title VARCHAR(100) NOT NULL COMMENT '绘本标题',
  description TEXT COMMENT '绘本描述',
  book_url VARCHAR(255) COMMENT '绘本文件URL',
  cover_img VARCHAR(255) COMMENT '封面图片URL',
  page_count INT COMMENT '页数',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_unit_id (unit_id),
  CONSTRAINT fk_picture_book_unit FOREIGN KEY (unit_id) REFERENCES kid_phonics_unit (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读绘本表';

-- 创建绘本内容表
CREATE TABLE kid_phonics_picture_book_content (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  picture_book_id INT NOT NULL COMMENT '关联绘本ID',
  unit_id INT NOT NULL COMMENT '关联单元ID',
  pic_url VARCHAR(255) NOT NULL COMMENT '图片URL',
  sentence_num INT NOT NULL DEFAULT 0 COMMENT '句子数量',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_picture_book_id (picture_book_id),
  KEY idx_unit_id (unit_id),
  CONSTRAINT fk_content_picture_book FOREIGN KEY (picture_book_id) REFERENCES kid_phonics_picture_book (id) ON DELETE CASCADE,
  CONSTRAINT fk_content_unit FOREIGN KEY (unit_id) REFERENCES kid_phonics_unit (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读绘本内容表';

-- 创建绘本句子表
CREATE TABLE kid_phonics_picture_book_sentence (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  content_id INT NOT NULL COMMENT '关联绘本内容ID',
  picture_book_id INT NOT NULL COMMENT '关联绘本ID',
  unit_id INT NOT NULL COMMENT '关联单元ID',
  example_en_us VARCHAR(500) NOT NULL COMMENT '英文例句',
  example_en_us_element VARCHAR(500) COMMENT '英文例句拆分',
  example_zh_cn VARCHAR(500) NOT NULL COMMENT '中文例句',
  example_zh_py VARCHAR(500) COMMENT '拼音',
  speaker VARCHAR(50) COMMENT '发言人',
  sound_file VARCHAR(255) COMMENT '英文音频URL',
  cn_sound_file VARCHAR(255) COMMENT '中文音频URL',
  core_sentence TINYINT NOT NULL DEFAULT 0 COMMENT '是否核心句：0-否，1-是',
  sequence INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_content_id (content_id),
  KEY idx_picture_book_id (picture_book_id),
  KEY idx_unit_id (unit_id),
  CONSTRAINT fk_sentence_content FOREIGN KEY (content_id) REFERENCES kid_phonics_picture_book_content (id) ON DELETE CASCADE,
  CONSTRAINT fk_sentence_picture_book FOREIGN KEY (picture_book_id) REFERENCES kid_phonics_picture_book (id) ON DELETE CASCADE,
  CONSTRAINT fk_sentence_unit FOREIGN KEY (unit_id) REFERENCES kid_phonics_unit (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读绘本句子表';

-- 创建发布记录表
CREATE TABLE kid_phonics_publish_record (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  course_id INT NOT NULL COMMENT '关联课程ID',
  branch INT NOT NULL COMMENT '分支号',
  publish_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  publisher VARCHAR(50) COMMENT '发布人',
  remark TEXT COMMENT '备注',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY idx_course_id (course_id),
  CONSTRAINT fk_publish_course FOREIGN KEY (course_id) REFERENCES kid_phonics_course (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='少儿自然拼读发布记录表'; 