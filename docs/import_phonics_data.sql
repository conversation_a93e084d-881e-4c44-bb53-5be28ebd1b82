-- Kid Phonics Database Data Import
-- Version: 2.0
-- Date: 2025-07-29
-- Description: This script includes phonetic transcriptions, translations, and parts of speech for all example words.

-- Clear existing data
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE kid_phonics_word;
TRUNCATE TABLE kid_phonics_letter;
TRUNCATE TABLE kid_phonics_unit;
TRUNCATE TABLE kid_phonics_course;
SET FOREIGN_KEY_CHECKS = 1;

-- Level 1: Consonants
INSERT INTO `kid_phonics_course` (`id`, `course_code`, `course_name_zh`, `course_name_en`, `sequence`) VALUES (1, 'C1', '辅音', 'Consonants', 1);

INSERT INTO `kid_phonics_unit` (`id`, `course_id`, `unit_code`, `unit_name`, `phoneme_type`, `sequence`) VALUES
(1, 1, 'U1', 'b', 'SINGLE_LETTER', 1),
(2, 1, 'U2', 'c', 'SINGLE_LETTER', 2),
(3, 1, 'U3', 'd', 'SINGLE_LETTER', 3),
(4, 1, 'U4', 'f', 'SINGLE_LETTER', 4),
(5, 1, 'U5', 'g', 'SINGLE_LETTER', 5),
(6, 1, 'U6', 'h', 'SINGLE_LETTER', 6),
(7, 1, 'U7', 'j', 'SINGLE_LETTER', 7),
(8, 1, 'U8', 'k', 'SINGLE_LETTER', 8),
(9, 1, 'U9', 'l', 'SINGLE_LETTER', 9),
(10, 1, 'U10', 'm', 'SINGLE_LETTER', 10),
(11, 1, 'U11', 'n', 'SINGLE_LETTER', 11),
(12, 1, 'U12', 'p', 'SINGLE_LETTER', 12),
(13, 1, 'U13', 'q', 'SINGLE_LETTER', 13),
(14, 1, 'U14', 'r', 'SINGLE_LETTER', 14),
(15, 1, 'U15', 's', 'SINGLE_LETTER', 15),
(16, 1, 'U16', 't', 'SINGLE_LETTER', 16),
(17, 1, 'U17', 'v', 'SINGLE_LETTER', 17),
(18, 1, 'U18', 'w', 'SINGLE_LETTER', 18),
(19, 1, 'U19', 'x', 'SINGLE_LETTER', 19),
(20, 1, 'U20', 'y', 'SINGLE_LETTER', 20),
(21, 1, 'U21', 'z', 'SINGLE_LETTER', 21);

INSERT INTO `kid_phonics_letter` (`id`, `unit_id`, `letter`, `ipa`, `is_combination`) VALUES
(1, 1, 'b', '/b/', 0),
(2, 2, 'c', '/k/', 0),
(3, 3, 'd', '/d/', 0),
(4, 4, 'f', '/f/', 0),
(5, 5, 'g', '/g/', 0),
(6, 6, 'h', '/h/', 0),
(7, 7, 'j', '/dʒ/', 0),
(8, 8, 'k', '/k/', 0),
(9, 9, 'l', '/l/', 0),
(10, 10, 'm', '/m/', 0),
(11, 11, 'n', '/n/', 0),
(12, 12, 'p', '/p/', 0),
(13, 13, 'q', '/k/', 0),
(14, 14, 'r', '/r/', 0),
(15, 15, 's', '/s/', 0),
(16, 16, 't', '/t/', 0),
(17, 17, 'v', '/v/', 0),
(18, 18, 'w', '/w/', 0),
(19, 19, 'x', '/ks/', 0),
(20, 20, 'y', '/j/', 0),
(21, 21, 'z', '/z/', 0);

INSERT INTO `kid_phonics_word` (`letter_id`, `word`, `phonetic`, `translation`, `sequence`) VALUES
(1, 'bag', '/bæɡ/', 'n.包', 1), (1, 'bed', '/bed/', 'n.床', 2), (1, 'bus', '/bʌs/', 'n.公共汽车', 3), (1, 'ball', '/bɔːl/', 'n.球', 4),
(2, 'cat', '/kæt/', 'n.猫', 1), (2, 'car', '/kɑːr/', 'n.汽车', 2), (2, 'cow', '/kaʊ/', 'n.奶牛', 3), (2, 'cup', '/kʌp/', 'n.杯子', 4),
(3, 'dog', '/dɒɡ/', 'n.狗', 1), (3, 'doll', '/dɒl/', 'n.洋娃娃', 2), (3, 'desk', '/desk/', 'n.书桌', 3), (3, 'duck', '/dʌk/', 'n.鸭子', 4),
(4, 'fan', '/fæn/', 'n.风扇', 1), (4, 'fox', '/fɒks/', 'n.狐狸', 2), (4, 'fish', '/fɪʃ/', 'n.鱼', 3), (4, 'fast', '/fɑːst/', 'adj.快的', 4),
(5, 'gift', '/ɡɪft/', 'n.礼物', 1), (5, 'girl', '/ɡɜːl/', 'n.女孩', 2), (5, 'goat', '/ɡəʊt/', 'n.山羊', 3), (5, 'good', '/ɡʊd/', 'adj.好的', 4),
(6, 'hen', '/hen/', 'n.母鸡', 1), (6, 'hat', '/hæt/', 'n.帽子', 2), (6, 'hot', '/hɒt/', 'adj.热的', 3), (6, 'house', '/haʊs/', 'n.房子', 4),
(7, 'jam', '/dʒæm/', 'n.果酱', 1), (7, 'jet', '/dʒet/', 'n.喷气式飞机', 2), (7, 'job', '/dʒɒb/', 'n.工作', 3), (7, 'jar', '/dʒɑːr/', 'n.罐子', 4),
(8, 'key', '/kiː/', 'n.钥匙', 1), (8, 'kid', '/kɪd/', 'n.小孩', 2), (8, 'kite', '/kaɪt/', 'n.风筝', 3), (8, 'king', '/kɪŋ/', 'n.国王', 4),
(9, 'lab', '/læb/', 'n.实验室', 1), (9, 'leg', '/leɡ/', 'n.腿', 2), (9, 'leaf', '/liːf/', 'n.叶子', 3), (9, 'lion', '/ˈlaɪən/', 'n.狮子', 4),
(10, 'man', '/mæn/', 'n.男人', 1), (10, 'milk', '/mɪlk/', 'n.牛奶', 2), (10, 'moon', '/muːn/', 'n.月亮', 3), (10, 'mouse', '/maʊs/', 'n.老鼠', 4),
(11, 'net', '/net/', 'n.网', 1), (11, 'nine', '/naɪn/', 'num.九', 2), (11, 'nest', '/nest/', 'n.鸟巢', 3), (11, 'nose', '/nəʊz/', 'n.鼻子', 4),
(12, 'pig', '/pɪɡ/', 'n.猪', 1), (12, 'pen', '/pen/', 'n.钢笔', 2), (12, 'pet', '/pet/', 'n.宠物', 3), (12, 'pear', '/peər/', 'n.梨', 4),
(13, 'quiet', '/ˈkwaɪət/', 'adj.安静的', 1), (13, 'quick', '/kwɪk/', 'adj.快的', 2), (13, 'quilt', '/kwɪlt/', 'n.被子', 3), (13, 'queen', '/kwiːn/', 'n.女王', 4),
(14, 'red', '/red/', 'adj.红色的', 1), (14, 'rat', '/ræt/', 'n.老鼠', 2), (14, 'rain', '/reɪn/', 'n.雨', 3), (14, 'ring', '/rɪŋ/', 'n.戒指', 4),
(15, 'sun', '/sʌn/', 'n.太阳', 1), (15, 'see', '/siː/', 'v.看见', 2), (15, 'six', '/sɪks/', 'num.六', 3), (15, 'seal', '/siːl/', 'n.海豹', 4),
(16, 'tea', '/tiː/', 'n.茶', 1), (16, 'ten', '/ten/', 'num.十', 2), (16, 'taxi', '/ˈtæksi/', 'n.出租车', 3), (16, 'time', '/taɪm/', 'n.时间', 4),
(17, 'van', '/væn/', 'n.货车', 1), (17, 'vet', '/vet/', 'n.兽医', 2), (17, 'vase', '/vɑːz/', 'n.花瓶', 3), (17, 'vest', '/vest/', 'n.背心', 4),
(18, 'wet', '/wet/', 'adj.湿的', 1), (18, 'win', '/wɪn/', 'v.赢', 2), (18, 'wall', '/wɔːl/', 'n.墙', 3), (18, 'wolf', '/wʊlf/', 'n.狼', 4),
(19, 'fox', '/fɒks/', 'n.狐狸', 1), (19, 'box', '/bɒks/', 'n.盒子', 2), (19, 'six', '/sɪks/', 'num.六', 3), (19, 'mix', '/mɪks/', 'v.混合', 4),
(20, 'yak', '/jæk/', 'n.牦牛', 1), (20, 'yam', '/jæm/', 'n.山药', 2), (20, 'yard', '/jɑːd/', 'n.院子', 3), (20, 'year', '/jɪər/', 'n.年', 4),
(21, 'zip', '/zɪp/', 'n.拉链', 1), (21, 'zoo', '/zuː/', 'n.动物园', 2), (21, 'zoom', '/zuːm/', 'v.变焦', 3), (21, 'zone', '/zəʊn/', 'n.区域', 4);

-- Level 2: Vowels 1
INSERT INTO `kid_phonics_course` (`id`, `course_code`, `course_name_zh`, `course_name_en`, `sequence`) VALUES (2, 'C2', '元音1', 'Vowels 1', 2);

INSERT INTO `kid_phonics_unit` (`id`, `course_id`, `unit_code`, `unit_name`, `phoneme_type`, `sequence`) VALUES
(22, 2, 'U1', 'a', 'SINGLE_LETTER', 1),
(23, 2, 'U2', 'ad', 'VOWEL_COMBINATION', 2),
(24, 2, 'U3', 'am', 'VOWEL_COMBINATION', 3),
(25, 2, 'U4', 'e', 'SINGLE_LETTER', 4),
(26, 2, 'U5', 'et', 'VOWEL_COMBINATION', 5),
(27, 2, 'U6', 'en', 'VOWEL_COMBINATION', 6),
(28, 2, 'U7', 'i', 'SINGLE_LETTER', 7),
(29, 2, 'U8', 'ig', 'VOWEL_COMBINATION', 8),
(30, 2, 'U9', 'it', 'VOWEL_COMBINATION', 9),
(31, 2, 'U10', 'o', 'SINGLE_LETTER', 10),
(32, 2, 'U11', 'op', 'VOWEL_COMBINATION', 11),
(33, 2, 'U12', 'og', 'VOWEL_COMBINATION', 12),
(34, 2, 'U13', 'u', 'SINGLE_LETTER', 13),
(35, 2, 'U14', 'ug', 'VOWEL_COMBINATION', 14),
(36, 2, 'U15', 'un', 'VOWEL_COMBINATION', 15);

INSERT INTO `kid_phonics_letter` (`id`, `unit_id`, `letter`, `ipa`, `is_combination`) VALUES
(22, 22, 'a', '/æ/', 0),
(23, 23, 'ad', '/æd/', 1),
(24, 24, 'am', '/æm/', 1),
(25, 25, 'e', '/e/', 0),
(26, 26, 'et', '/et/', 1),
(27, 27, 'en', '/en/', 1),
(28, 28, 'i', '/ɪ/', 0),
(29, 29, 'ig', '/ɪg/', 1),
(30, 30, 'it', '/ɪt/', 1),
(31, 31, 'o', '/ɒ/', 0),
(32, 32, 'op', '/ɒp/', 1),
(33, 33, 'og', '/ɒg/', 1),
(34, 34, 'u', '/ʌ/', 0),
(35, 35, 'ug', '/ʌg/', 1),
(36, 36, 'un', '/ʌn/', 1);

INSERT INTO `kid_phonics_word` (`letter_id`, `word`, `phonetic`, `translation`, `sequence`) VALUES
(22, 'ax', '/æks/', 'n.斧头', 1), (22, 'ant', '/ænt/', 'n.蚂蚁', 2), (22, 'and', '/ænd/', 'conj.和', 3), (22, 'act', '/ækt/', 'v.行动', 4),
(23, 'dad', '/dæd/', 'n.爸爸', 1), (23, 'mad', '/mæd/', 'adj.疯狂的', 2), (23, 'pad', '/pæd/', 'n.垫子', 3), (23, 'sad', '/sæd/', 'adj.悲伤的', 4),
(24, 'dam', '/dæm/', 'n.水坝', 1), (24, 'ham', '/hæm/', 'n.火腿', 2), (24, 'jam', '/dʒæm/', 'n.果酱', 3), (24, 'yam', '/jæm/', 'n.山药', 4),
(25, 'egg', '/eɡ/', 'n.鸡蛋', 1), (25, 'end', '/end/', 'n.结尾', 2), (25, 'elf', '/elf/', 'n.精灵', 3), (25, 'edge', '/edʒ/', 'n.边缘', 4),
(26, 'jet', '/dʒet/', 'n.喷气式飞机', 1), (26, 'net', '/net/', 'n.网', 2), (26, 'pet', '/pet/', 'n.宠物', 3), (26, 'wet', '/wet/', 'adj.湿的', 4),
(27, 'hen', '/hen/', 'n.母鸡', 1), (27, 'men', '/men/', 'n.男人(复数)', 2), (27, 'pen', '/pen/', 'n.钢笔', 3), (27, 'ten', '/ten/', 'num.十', 4),
(28, 'ill', '/ɪl/', 'adj.生病的', 1), (28, 'ink', '/ɪŋk/', 'n.墨水', 2), (28, 'inch', '/ɪntʃ/', 'n.英寸', 3), (28, 'igloo', '/ˈɪɡluː/', 'n.冰屋', 4),
(29, 'big', '/bɪɡ/', 'adj.大的', 1), (29, 'pig', '/pɪɡ/', 'n.猪', 2), (29, 'wig', '/wɪɡ/', 'n.假发', 3), (29, 'dig', '/dɪɡ/', 'v.挖', 4),
(30, 'fit', '/fɪt/', 'v.合身', 1), (30, 'hit', '/hɪt/', 'v.打', 2), (30, 'pit', '/pɪt/', 'n.坑', 3), (30, 'sit', '/sɪt/', 'v.坐', 4),
(31, 'ox', '/ɒks/', 'n.公牛', 1), (31, 'on', '/ɒn/', 'prep.在...上', 2), (31, 'off', '/ɒf/', 'adv.离开', 3), (31, 'oil', '/ɔɪl/', 'n.油', 4),
(32, 'hop', '/hɒp/', 'v.单脚跳', 1), (32, 'mop', '/mɒp/', 'n.拖把', 2), (32, 'pop', '/pɒp/', 'n.流行音乐', 3), (32, 'top', '/tɒp/', 'n.顶部', 4),
(33, 'dog', '/dɒɡ/', 'n.狗', 1), (33, 'fog', '/fɒɡ/', 'n.雾', 2), (33, 'jog', '/dʒɒɡ/', 'v.慢跑', 3), (33, 'log', '/lɒɡ/', 'n.原木', 4),
(34, 'up', '/ʌp/', 'prep.向上', 1), (34, 'us', '/ʌs/', 'pron.我们', 2), (34, 'under', '/ˈʌndər/', 'prep.在...下', 3), (34, 'uncle', '/ˈʌŋkl/', 'n.叔叔', 4),
(35, 'bug', '/bʌɡ/', 'n.虫子', 1), (35, 'hug', '/hʌɡ/', 'v.拥抱', 2), (35, 'mug', '/mʌɡ/', 'n.马克杯', 3), (35, 'rug', '/rʌɡ/', 'n.地毯', 4),
(36, 'bun', '/bʌn/', 'n.小圆面包', 1), (36, 'fun', '/fʌn/', 'n.乐趣', 2), (36, 'run', '/rʌn/', 'v.跑', 3), (36, 'sun', '/sʌn/', 'n.太阳', 4);

-- Level 3: Vowels 2
INSERT INTO `kid_phonics_course` (`id`, `course_code`, `course_name_zh`, `course_name_en`, `sequence`) VALUES (3, 'C3', '元音2', 'Vowels 2', 3);

INSERT INTO `kid_phonics_unit` (`id`, `course_id`, `unit_code`, `unit_name`, `phoneme_type`, `sequence`) VALUES
(37, 3, 'U1', 'a_e', 'VOWEL_COMBINATION', 1),
(38, 3, 'U2', 'ake', 'VOWEL_COMBINATION', 2),
(39, 3, 'U3', 'i_e', 'VOWEL_COMBINATION', 3),
(40, 3, 'U4', 'ike', 'VOWEL_COMBINATION', 4),
(41, 3, 'U5', 'o_e', 'VOWEL_COMBINATION', 5),
(42, 3, 'U6', 'ole', 'VOWEL_COMBINATION', 6),
(43, 3, 'U7', 'u_e', 'VOWEL_COMBINATION', 7),
(44, 3, 'U8', 'ai', 'VOWEL_COMBINATION', 8),
(45, 3, 'U9', 'ay', 'VOWEL_COMBINATION', 9),
(46, 3, 'U10', 'ie', 'VOWEL_COMBINATION', 10),
(47, 3, 'U11', 'y', 'SINGLE_LETTER', 11),
(48, 3, 'U12', 'oa', 'VOWEL_COMBINATION', 12),
(49, 3, 'U13', 'ow', 'VOWEL_COMBINATION', 13),
(50, 3, 'U14', 'ue', 'VOWEL_COMBINATION', 14),
(51, 3, 'U15', 'ew', 'VOWEL_COMBINATION', 15);

INSERT INTO `kid_phonics_letter` (`id`, `unit_id`, `letter`, `ipa`, `is_combination`) VALUES
(37, 37, 'a_e', '/eɪ/', 1),
(38, 38, 'ake', '/eɪk/', 1),
(39, 39, 'i_e', '/aɪ/', 1),
(40, 40, 'ike', '/aɪk/', 1),
(41, 41, 'o_e', '/əʊ/', 1),
(42, 42, 'ole', '/əʊl/', 1),
(43, 43, 'u_e', '/juː/', 1),
(44, 44, 'ai', '/eɪ/', 1),
(45, 45, 'ay', '/eɪ/', 1),
(46, 46, 'ie', '/aɪ/', 1),
(47, 47, 'y', '/aɪ/', 0),
(48, 48, 'oa', '/əʊ/', 1),
(49, 49, 'ow', '/əʊ/', 1),
(50, 50, 'ue', '/juː/', 1),
(51, 51, 'ew', '/juː/', 1);

INSERT INTO `kid_phonics_word` (`letter_id`, `word`, `phonetic`, `translation`, `sequence`) VALUES
(37, 'game', '/ɡeɪm/', 'n.游戏', 1), (37, 'lame', '/leɪm/', 'adj.瘸的', 2), (37, 'name', '/neɪm/', 'n.名字', 3), (37, 'same', '/seɪm/', 'adj.相同的', 4),
(38, 'bake', '/beɪk/', 'v.烘烤', 1), (38, 'cake', '/keɪk/', 'n.蛋糕', 2), (38, 'lake', '/leɪk/', 'n.湖', 3), (38, 'make', '/meɪk/', 'v.制作', 4),
(39, 'fine', '/faɪn/', 'adj.好的', 1), (39, 'nine', '/naɪn/', 'num.九', 2), (39, 'pine', '/paɪn/', 'n.松树', 3), (39, 'wine', '/waɪn/', 'n.葡萄酒', 4),
(40, 'bike', '/baɪk/', 'n.自行车', 1), (40, 'like', '/laɪk/', 'v.喜欢', 2), (40, 'hike', '/haɪk/', 'v.远足', 3), (40, 'mike', '/maɪk/', 'n.麦克风', 4),
(41, 'hose', '/həʊz/', 'n.软管', 1), (41, 'nose', '/nəʊz/', 'n.鼻子', 2), (41, 'pose', '/pəʊz/', 'v.摆姿势', 3), (41, 'rose', '/rəʊz/', 'n.玫瑰', 4),
(42, 'bole', '/bəʊl/', 'n.树干', 1), (42, 'hole', '/həʊl/', 'n.洞', 2), (42, 'role', '/rəʊl/', 'n.角色', 3), (42, 'mole', '/məʊl/', 'n.鼹鼠', 4),
(43, 'cube', '/kjuːb/', 'n.立方体', 1), (43, 'tube', '/tjuːb/', 'n.管子', 2), (43, 'cute', '/kjuːt/', 'adj.可爱的', 3), (43, 'mute', '/mjuːt/', 'adj.哑的', 4),
(44, 'rain', '/reɪn/', 'n.雨', 1), (44, 'sail', '/seɪl/', 'v.航行', 2), (44, 'tail', '/teɪl/', 'n.尾巴', 3), (44, 'wait', '/weɪt/', 'v.等待', 4),
(45, 'bay', '/beɪ/', 'n.海湾', 1), (45, 'day', '/deɪ/', 'n.天', 2), (45, 'pay', '/peɪ/', 'v.支付', 3), (45, 'say', '/seɪ/', 'v.说', 4),
(46, 'die', '/daɪ/', 'v.死', 1), (46, 'lie', '/laɪ/', 'v.躺', 2), (46, 'pie', '/paɪ/', 'n.派', 3), (46, 'tie', '/taɪ/', 'n.领带', 4),
(47, 'my', '/maɪ/', 'pron.我的', 1), (47, 'cry', '/kraɪ/', 'v.哭', 2), (47, 'fly', '/flaɪ/', 'v.飞', 3), (47, 'sky', '/skaɪ/', 'n.天空', 4),
(48, 'boat', '/bəʊt/', 'n.船', 1), (48, 'coat', '/kəʊt/', 'n.外套', 2), (48, 'goat', '/ɡəʊt/', 'n.山羊', 3), (48, 'road', '/rəʊd/', 'n.路', 4),
(49, 'bow', '/baʊ/', 'v.鞠躬', 1), (49, 'cow', '/kaʊ/', 'n.奶牛', 2), (49, 'row', '/rəʊ/', 'v.划船', 3), (49, 'low', '/ləʊ/', 'adj.低的', 4),
(50, 'cue', '/kjuː/', 'n.提示', 1), (50, 'due', '/djuː/', 'adj.到期的', 2), (50, 'hue', '/hjuː/', 'n.色调', 3), (50, 'sue', '/suː/', 'v.控告', 4),
(51, 'few', '/fjuː/', 'adj.很少的', 1), (51, 'hew', '/hjuː/', 'v.砍', 2), (51, 'new', '/njuː/', 'adj.新的', 3), (51, 'nephew', '/ˈnefjuː/', 'n.侄子', 4);

-- Level 4: Consonant Blends
INSERT INTO `kid_phonics_course` (`id`, `course_code`, `course_name_zh`, `course_name_en`, `sequence`) VALUES (4, 'C4', '辅音连缀', 'Consonant Blends', 4);

INSERT INTO `kid_phonics_unit` (`id`, `course_id`, `unit_code`, `unit_name`, `phoneme_type`, `sequence`) VALUES
(52, 4, 'U1', 'bl', 'OTHER', 1),
(53, 4, 'U2', 'cl', 'OTHER', 2),
(54, 4, 'U3', 'fl', 'OTHER', 3),
(55, 4, 'U4', 'br', 'OTHER', 4),
(56, 4, 'U5', 'cr', 'OTHER', 5),
(57, 4, 'U6', 'fr', 'OTHER', 6),
(58, 4, 'U7', 'sk', 'OTHER', 7),
(59, 4, 'U8', 'sp', 'OTHER', 8),
(60, 4, 'U9', 'st', 'OTHER', 9),
(61, 4, 'U10', 'th', 'OTHER', 10),
(62, 4, 'U11', 'th', 'OTHER', 11),
(63, 4, 'U12', 'ph', 'OTHER', 12),
(64, 4, 'U13', 'wh', 'OTHER', 13),
(65, 4, 'U14', 'ng', 'OTHER', 14),
(66, 4, 'U15', 'sh', 'OTHER', 15),
(67, 4, 'U16', 'ch', 'OTHER', 16),
(68, 4, 'U17', 'dr', 'OTHER', 17),
(69, 4, 'U18', 'tr', 'OTHER', 18),
(70, 4, 'U19', 'ts', 'OTHER', 19),
(71, 4, 'U20', 'ds', 'OTHER', 20);

INSERT INTO `kid_phonics_letter` (`id`, `unit_id`, `letter`, `ipa`, `is_combination`) VALUES
(52, 52, 'bl', '/bl/', 1),
(53, 53, 'cl', '/kl/', 1),
(54, 54, 'fl', '/fl/', 1),
(55, 55, 'br', '/br/', 1),
(56, 56, 'cr', '/kr/', 1),
(57, 57, 'fr', '/fr/', 1),
(58, 58, 'sk', '/sk/', 1),
(59, 59, 'sp', '/sp/', 1),
(60, 60, 'st', '/st/', 1),
(61, 61, 'th', '/ð/', 1),
(62, 62, 'th', '/θ/', 1),
(63, 63, 'ph', '/f/', 1),
(64, 64, 'wh', '/w/', 1),
(65, 65, 'ng', '/ŋ/', 1),
(66, 66, 'sh', '/ʃ/', 1),
(67, 67, 'ch', '/tʃ/', 1),
(68, 68, 'dr', '/dr/', 1),
(69, 69, 'tr', '/tr/', 1),
(70, 70, 'ts', '/ts/', 1),
(71, 71, 'ds', '/dz/', 1);

INSERT INTO `kid_phonics_word` (`letter_id`, `word`, `phonetic`, `translation`, `sequence`) VALUES
(52, 'blue', '/bluː/', 'adj.蓝色的', 1), (52, 'blow', '/bləʊ/', 'v.吹', 2), (52, 'black', '/blæk/', 'adj.黑色的', 3), (52, 'block', '/blɒk/', 'n.街区', 4),
(53, 'clap', '/klæp/', 'v.鼓掌', 1), (53, 'clam', '/klæm/', 'n.蛤', 2), (53, 'club', '/klʌb/', 'n.俱乐部', 3), (53, 'clean', '/kliːn/', 'adj.干净的', 4),
(54, 'fly', '/flaɪ/', 'v.飞', 1), (54, 'flea', '/fliː/', 'n.跳蚤', 2), (54, 'flag', '/flæɡ/', 'n.旗帜', 3), (54, 'flat', '/flæt/', 'n.公寓', 4),
(55, 'bride', '/braɪd/', 'n.新娘', 1), (55, 'brown', '/braʊn/', 'adj.棕色的', 2), (55, 'bread', '/bred/', 'n.面包', 3), (55, 'brush', '/brʌʃ/', 'n.刷子', 4),
(56, 'crab', '/kræb/', 'n.螃蟹', 1), (56, 'crag', '/kræɡ/', 'n.悬崖', 2), (56, 'crop', '/krɒp/', 'n.庄稼', 3), (56, 'crayon', '/ˈkreɪən/', 'n.蜡笔', 4),
(57, 'free', '/friː/', 'adj.自由的', 1), (57, 'frog', '/frɒɡ/', 'n.青蛙', 2), (57, 'fruit', '/fruːt/', 'n.水果', 3), (57, 'fridge', '/frɪdʒ/', 'n.冰箱', 4),
(58, 'sky', '/skaɪ/', 'n.天空', 1), (58, 'skill', '/skɪl/', 'n.技能', 2), (58, 'skate', '/skeɪt/', 'v.滑冰', 3), (58, 'skirt', '/skɜːt/', 'n.裙子', 4),
(59, 'spot', '/spɒt/', 'n.斑点', 1), (59, 'speak', '/spiːk/', 'v.说', 2), (59, 'spade', '/speɪd/', 'n.铲子', 3), (59, 'spoon', '/spuːn/', 'n.勺子', 4),
(60, 'star', '/stɑːr/', 'n.星星', 1), (60, 'stop', '/stɒp/', 'v.停止', 2), (60, 'stand', '/stænd/', 'v.站立', 3), (60, 'stamp', '/stæmp/', 'n.邮票', 4),
(61, 'this', '/ðɪs/', 'pron.这个', 1), (61, 'that', '/ðæt/', 'pron.那个', 2), (61, 'father', '/ˈfɑːðər/', 'n.父亲', 3), (61, 'mother', '/ˈmʌðər/', 'n.母亲', 4),
(62, 'think', '/θɪŋk/', 'v.思考', 1), (62, 'thick', '/θɪk/', 'adj.厚的', 2), (62, 'thin', '/θɪn/', 'adj.薄的', 3), (62, 'throw', '/θrəʊ/', 'v.扔', 4),
(63, 'phone', '/fəʊn/', 'n.电话', 1), (63, 'photo', '/ˈfəʊtəʊ/', 'n.照片', 2), (63, 'physic', '/ˈfɪzɪk/', 'n.医学', 3), (63, 'phrase', '/freɪz/', 'n.短语', 4),
(64, 'whale', '/weɪl/', 'n.鲸鱼', 1), (64, 'wheel', '/wiːl/', 'n.轮子', 2), (64, 'wheat', '/wiːt/', 'n.小麦', 3), (64, 'white', '/waɪt/', 'adj.白色的', 4),
(65, 'ring', '/rɪŋ/', 'n.戒指', 1), (65, 'sing', '/sɪŋ/', 'v.唱歌', 2), (65, 'wing', '/wɪŋ/', 'n.翅膀', 3), (65, 'song', '/sɒŋ/', 'n.歌曲', 4),
(66, 'ship', '/ʃɪp/', 'n.船', 1), (66, 'shark', '/ʃɑːk/', 'n.鲨鱼', 2), (66, 'shop', '/ʃɒp/', 'n.商店', 3), (66, 'shut', '/ʃʌt/', 'v.关闭', 4),
(67, 'chess', '/tʃes/', 'n.国际象棋', 1), (67, 'cherry', '/ˈtʃeri/', 'n.樱桃', 2), (67, 'change', '/tʃeɪndʒ/', 'v.改变', 3), (67, 'charge', '/tʃɑːdʒ/', 'v.收费', 4),
(68, 'draw', '/drɔː/', 'v.画', 1), (68, 'drum', '/drʌm/', 'n.鼓', 2), (68, 'drink', '/drɪŋk/', 'v.喝', 3), (68, 'dress', '/dres/', 'n.连衣裙', 4),
(69, 'true', '/truː/', 'adj.真的', 1), (69, 'trip', '/trɪp/', 'n.旅行', 2), (69, 'truck', '/trʌk/', 'n.卡车', 3), (69, 'tree', '/triː/', 'n.树', 4),
(70, 'bats', '/bæts/', 'n.蝙蝠(复数)', 1), (70, 'cats', '/kæts/', 'n.猫(复数)', 2), (70, 'hats', '/hæts/', 'n.帽子(复数)', 3), (70, 'pets', '/pets/', 'n.宠物(复数)', 4),
(71, 'beds', '/bedz/', 'n.床(复数)', 1), (71, 'birds', '/bɜːdz/', 'n.鸟(复数)', 2), (71, 'cards', '/kɑːdz/', 'n.卡片(复数)', 3), (71, 'hands', '/hændz/', 'n.手(复数)', 4);

-- Level 5: Letter Combinations 1
INSERT INTO `kid_phonics_course` (`id`, `course_code`, `course_name_zh`, `course_name_en`, `sequence`) VALUES (5, 'C5', '字母组合1', 'Letter Combinations 1', 5);

INSERT INTO `kid_phonics_unit` (`id`, `course_id`, `unit_code`, `unit_name`, `phoneme_type`, `sequence`) VALUES
(72, 5, 'U1', 'ar', 'VOWEL_COMBINATION', 1),
(73, 5, 'U2', 'ee', 'VOWEL_COMBINATION', 2),
(74, 5, 'U3', 'ea', 'VOWEL_COMBINATION', 3),
(75, 5, 'U4', 'ir', 'VOWEL_COMBINATION', 4),
(76, 5, 'U5', 'ur', 'VOWEL_COMBINATION', 5),
(77, 5, 'U6', 'or', 'VOWEL_COMBINATION', 6),
(78, 5, 'U7', 'au', 'VOWEL_COMBINATION', 7),
(79, 5, 'U8', 'aw', 'VOWEL_COMBINATION', 8),
(80, 5, 'U9', 'ue', 'VOWEL_COMBINATION', 9),
(81, 5, 'U10', 'ui', 'VOWEL_COMBINATION', 10),
(82, 5, 'U11', 'oo', 'VOWEL_COMBINATION', 11),
(83, 5, 'U12', 'oo', 'VOWEL_COMBINATION', 12),
(84, 5, 'U13', 'ea', 'VOWEL_COMBINATION', 13),
(85, 5, 'U14', 'er', 'VOWEL_COMBINATION', 14),
(86, 5, 'U15', 'or', 'VOWEL_COMBINATION', 15),
(87, 5, 'U16', 'ou', 'VOWEL_COMBINATION', 16),
(88, 5, 'U17', 'ow', 'VOWEL_COMBINATION', 17),
(89, 5, 'U18', 'oi', 'VOWEL_COMBINATION', 18),
(90, 5, 'U19', 'oy', 'VOWEL_COMBINATION', 19),
(91, 5, 'U20', 'ear', 'VOWEL_COMBINATION', 20),
(92, 5, 'U21', 'eer', 'VOWEL_COMBINATION', 21),
(93, 5, 'U22', 'ear', 'VOWEL_COMBINATION', 22),
(94, 5, 'U23', 'are', 'VOWEL_COMBINATION', 23),
(95, 5, 'U24', 'air', 'VOWEL_COMBINATION', 24),
(96, 5, 'U25', 'oor', 'VOWEL_COMBINATION', 25);

INSERT INTO `kid_phonics_letter` (`id`, `unit_id`, `letter`, `ipa`, `is_combination`) VALUES
(72, 72, 'ar', '/ɑː/', 1),
(73, 73, 'ee', '/iː/', 1),
(74, 74, 'ea', '/iː/', 1),
(75, 75, 'ir', '/ɜː/', 1),
(76, 76, 'ur', '/ɜː/', 1),
(77, 77, 'or', '/ɔː/', 1),
(78, 78, 'au', '/ɔː/', 1),
(79, 79, 'aw', '/ɔː/', 1),
(80, 80, 'ue', '/uː/', 1),
(81, 81, 'ui', '/uː/', 1),
(82, 82, 'oo', '/uː/', 1),
(83, 83, 'oo', '/ʊ/', 1),
(84, 84, 'ea', '/e/', 1),
(85, 85, 'er', '/ə/', 1),
(86, 86, 'or', '/ə/', 1),
(87, 87, 'ou', '/aʊ/', 1),
(88, 88, 'ow', '/aʊ/', 1),
(89, 89, 'oi', '/ɔɪ/', 1),
(90, 90, 'oy', '/ɔɪ/', 1),
(91, 91, 'ear', '/ɪə/', 1),
(92, 92, 'eer', '/ɪə/', 1),
(93, 93, 'ear', '/eə/', 1),
(94, 94, 'are', '/eə/', 1),
(95, 95, 'air', '/eə/', 1),
(96, 96, 'oor', '/ʊə/', 1);

INSERT INTO `kid_phonics_word` (`letter_id`, `word`, `phonetic`, `translation`, `sequence`) VALUES
(72, 'car', '/kɑːr/', 'n.汽车', 1), (72, 'jar', '/dʒɑːr/', 'n.罐子', 2), (72, 'farm', '/fɑːrm/', 'n.农场', 3), (72, 'park', '/pɑːrk/', 'n.公园', 4),
(73, 'bee', '/biː/', 'n.蜜蜂', 1), (73, 'feet', '/fiːt/', 'n.脚(复数)', 2), (73, 'meet', '/miːt/', 'v.遇见', 3), (73, 'jeep', '/dʒiːp/', 'n.吉普车', 4),
(74, 'sea', '/siː/', 'n.大海', 1), (74, 'seat', '/siːt/', 'n.座位', 2), (74, 'meat', '/miːt/', 'n.肉', 3), (74, 'leaf', '/liːf/', 'n.叶子', 4),
(75, 'sir', '/sɜːr/', 'n.先生', 1), (75, 'bird', '/bɜːrd/', 'n.鸟', 2), (75, 'girl', '/ɡɜːrl/', 'n.女孩', 3), (75, 'shirt', '/ʃɜːrt/', 'n.衬衫', 4),
(76, 'fur', '/fɜːr/', 'n.毛皮', 1), (76, 'hurt', '/hɜːrt/', 'v.伤害', 2), (76, 'nurse', '/nɜːrs/', 'n.护士', 3), (76, 'purple', '/ˈpɜːrpl/', 'adj.紫色的', 4),
(77, 'corn', '/kɔːrn/', 'n.玉米', 1), (77, 'fork', '/fɔːrk/', 'n.叉子', 2), (77, 'pork', '/pɔːrk/', 'n.猪肉', 3), (77, 'horse', '/hɔːrs/', 'n.马', 4),
(78, 'cause', '/kɔːz/', 'n.原因', 1), (78, 'pause', '/pɔːz/', 'v.暂停', 2), (78, 'sauce', '/sɔːs/', 'n.酱汁', 3), (78, 'autumn', '/ˈɔːtəm/', 'n.秋天', 4),
(79, 'law', '/lɔː/', 'n.法律', 1), (79, 'paw', '/pɔː/', 'n.爪子', 2), (79, 'saw', '/sɔː/', 'n.锯子', 3), (79, 'draw', '/drɔː/', 'v.画', 4),
(80, 'blue', '/bluː/', 'adj.蓝色的', 1), (80, 'clue', '/kluː/', 'n.线索', 2), (80, 'glue', '/ɡluː/', 'n.胶水', 3), (80, 'true', '/truː/', 'adj.真的', 4),
(81, 'suit', '/suːt/', 'n.西装', 1), (81, 'fruit', '/fruːt/', 'n.水果', 2), (81, 'juice', '/dʒuːs/', 'n.果汁', 3), (81, 'cruise', '/kruːz/', 'n.巡航', 4),
(82, 'zoo', '/zuː/', 'n.动物园', 1), (82, 'food', '/fuːd/', 'n.食物', 2), (82, 'moon', '/muːn/', 'n.月亮', 3), (82, 'tooth', '/tuːθ/', 'n.牙齿', 4),
(83, 'wood', '/wʊd/', 'n.木头', 1), (83, 'book', '/bʊk/', 'n.书', 2), (83, 'cook', '/kʊk/', 'v.烹饪', 3), (83, 'foot', '/fʊt/', 'n.脚', 4),
(84, 'head', '/hed/', 'n.头', 1), (84, 'bread', '/bred/', 'n.面包', 2), (84, 'health', '/helθ/', 'n.健康', 3), (84, 'wealth', '/welθ/', 'n.财富', 4),
(85, 'corner', '/ˈkɔːrnər/', 'n.角落', 1), (85, 'driver', '/ˈdraɪvər/', 'n.司机', 2), (85, 'teacher', '/ˈtiːtʃər/', 'n.老师', 3), (85, 'sister', '/ˈsɪstər/', 'n.姐妹', 4),
(86, 'actor', '/ˈæktər/', 'n.演员', 1), (86, 'doctor', '/ˈdɒktər/', 'n.医生', 2), (86, 'editor', '/ˈedɪtər/', 'n.编辑', 3), (86, 'visitor', '/ˈvɪzɪtər/', 'n.访客', 4),
(87, 'loud', '/laʊd/', 'adj.大声的', 1), (87, 'cloud', '/klaʊd/', 'n.云', 2), (87, 'mouse', '/maʊs/', 'n.老鼠', 3), (87, 'house', '/haʊs/', 'n.房子', 4),
(88, 'cow', '/kaʊ/', 'n.奶牛', 1), (88, 'how', '/haʊ/', 'adv.如何', 2), (88, 'now', '/naʊ/', 'adv.现在', 3), (88, 'owl', '/aʊl/', 'n.猫头鹰', 4),
(89, 'oil', '/ɔɪl/', 'n.油', 1), (89, 'boil', '/bɔɪl/', 'v.煮', 2), (89, 'coil', '/kɔɪl/', 'n.线圈', 3), (89, 'soil', '/sɔɪl/', 'n.土壤', 4),
(90, 'boy', '/bɔɪ/', 'n.男孩', 1), (90, 'joy', '/dʒɔɪ/', 'n.喜悦', 2), (90, 'toy', '/tɔɪ/', 'n.玩具', 3), (90, 'ploy', '/plɔɪ/', 'n.计策', 4),
(91, 'ear', '/ɪər/', 'n.耳朵', 1), (91, 'hear', '/hɪər/', 'v.听见', 2), (91, 'near', '/nɪər/', 'adv.附近', 3), (91, 'year', '/jɪər/', 'n.年', 4),
(92, 'beer', '/bɪər/', 'n.啤酒', 1), (92, 'deer', '/dɪər/', 'n.鹿', 2), (92, 'jeer', '/dʒɪər/', 'v.嘲笑', 3), (92, 'peer', '/pɪər/', 'v.凝视', 4),
(93, 'bear', '/beər/', 'n.熊', 1), (93, 'pear', '/peər/', 'n.梨', 2), (93, 'wear', '/weər/', 'v.穿', 3), (93, 'swear', '/sweər/', 'v.发誓', 4),
(94, 'care', '/keər/', 'v.关心', 1), (94, 'dare', '/deər/', 'v.敢', 2), (94, 'rare', '/reər/', 'adj.稀有的', 3), (94, 'share', '/ʃeər/', 'v.分享', 4),
(95, 'fair', '/feər/', 'adj.公平的', 1), (95, 'hair', '/heər/', 'n.头发', 2), (95, 'stair', '/steər/', 'n.楼梯', 3), (95, 'chair', '/tʃeər/', 'n.椅子', 4),
(96, 'boor', '/bʊər/', 'n.粗鲁的人', 1), (96, 'poor', '/pʊər/', 'adj.贫穷的', 2), (96, 'moor', '/mʊər/', 'n.沼泽', 3), (96, 'floor', '/flɔːr/', 'n.地板', 4);

-- Level 6: Letter Combinations 2
INSERT INTO `kid_phonics_course` (`id`, `course_code`, `course_name_zh`, `course_name_en`, `sequence`) VALUES (6, 'C6', '字母组合2', 'Letter Combinations 2', 6);

INSERT INTO `kid_phonics_unit` (`id`, `course_id`, `unit_code`, `unit_name`, `phoneme_type`, `sequence`) VALUES
(97, 6, 'U1', 'c', 'SINGLE_LETTER', 1),
(98, 6, 'U2', 's', 'SINGLE_LETTER', 2),
(99, 6, 'U3', 'g', 'SINGLE_LETTER', 3),
(100, 6, 'U4', 'wr', 'OTHER', 4),
(101, 6, 'U5', 'kn', 'OTHER', 5),
(102, 6, 'U6', 'mb', 'OTHER', 6),
(103, 6, 'U7', 'ck', 'OTHER', 7),
(104, 6, 'U8', 'sm', 'OTHER', 8),
(105, 6, 'U9', 'squ', 'OTHER', 9),
(106, 6, 'U10', 'igh', 'VOWEL_COMBINATION', 10),
(107, 6, 'U11', 'ire', 'VOWEL_COMBINATION', 11),
(108, 6, 'U12', 'all', 'VOWEL_COMBINATION', 12),
(109, 6, 'U13', 'ful', 'OTHER', 13),
(110, 6, 'U14', 'ous', 'OTHER', 14),
(111, 6, 'U15', 'ment', 'OTHER', 15),
(112, 6, 'U16', 'sion', 'OTHER', 16),
(113, 6, 'U17', 'tion', 'OTHER', 17),
(114, 6, 'U18', 'ture', 'OTHER', 18);

INSERT INTO `kid_phonics_letter` (`id`, `unit_id`, `letter`, `ipa`, `is_combination`) VALUES
(97, 97, 'c', '/s/', 0),
(98, 98, 's', '/z/', 0),
(99, 99, 'g', '/dʒ/', 0),
(100, 100, 'wr', '/r/', 1),
(101, 101, 'kn', '/n/', 1),
(102, 102, 'mb', '/m/', 1),
(103, 103, 'ck', '/k/', 1),
(104, 104, 'sm', '/sm/', 1),
(105, 105, 'squ', '/skw/', 1),
(106, 106, 'igh', '/aɪ/', 1),
(107, 107, 'ire', '/aɪər/', 1),
(108, 108, 'all', '/ɔːl/', 1),
(109, 109, 'ful', '/fəl/', 1),
(110, 110, 'ous', '/əs/', 1),
(111, 111, 'ment', '/mənt/', 1),
(112, 112, 'sion', '/ʒən/', 1),
(113, 113, 'tion', '/ʃən/', 1),
(114, 114, 'ture', '/tʃər/', 1);

INSERT INTO `kid_phonics_word` (`letter_id`, `word`, `phonetic`, `translation`, `sequence`) VALUES
(97, 'city', '/ˈsɪti/', 'n.城市', 1), (97, 'circle', '/ˈsɜːrkl/', 'n.圆圈', 2), (97, 'rice', '/raɪs/', 'n.米饭', 3), (97, 'dice', '/daɪs/', 'n.骰子', 4),
(98, 'rose', '/rəʊz/', 'n.玫瑰', 1), (98, 'jeans', '/dʒiːnz/', 'n.牛仔裤', 2), (98, 'cheese', '/tʃiːz/', 'n.奶酪', 3), (98, 'choose', '/tʃuːz/', 'v.选择', 4),
(99, 'gym', '/dʒɪm/', 'n.体育馆', 1), (99, 'giant', '/ˈdʒaɪənt/', 'n.巨人', 2), (99, 'cage', '/keɪdʒ/', 'n.笼子', 3), (99, 'orange', '/ˈɒrɪndʒ/', 'n.橙子', 4),
(100, 'write', '/raɪt/', 'v.写', 1), (100, 'wrist', '/rɪst/', 'n.手腕', 2), (100, 'wrap', '/ræp/', 'v.包裹', 3), (100, 'wrong', '/rɒŋ/', 'adj.错误的', 4),
(101, 'know', '/nəʊ/', 'v.知道', 1), (101, 'knee', '/niː/', 'n.膝盖', 2), (101, 'knife', '/naɪf/', 'n.刀', 3), (101, 'knock', '/nɒk/', 'v.敲', 4),
(102, 'bomb', '/bɒm/', 'n.炸弹', 1), (102, 'comb', '/kəʊm/', 'n.梳子', 2), (102, 'lamb', '/læm/', 'n.羔羊', 3), (102, 'climb', '/klaɪm/', 'v.爬', 4),
(103, 'back', '/bæk/', 'adv.向后', 1), (103, 'sick', '/sɪk/', 'adj.生病的', 2), (103, 'neck', '/nek/', 'n.脖子', 3), (103, 'lock', '/lɒk/', 'n.锁', 4),
(104, 'small', '/smɔːl/', 'adj.小的', 1), (104, 'smart', '/smɑːrt/', 'adj.聪明的', 2), (104, 'smile', '/smaɪl/', 'v.微笑', 3), (104, 'smell', '/smel/', 'v.闻', 4),
(105, 'square', '/skweər/', 'n.正方形', 1), (105, 'squid', '/skwɪd/', 'n.鱿鱼', 2), (105, 'squash', '/skwɒʃ/', 'n.壁球', 3), (105, 'squeeze', '/skwiːz/', 'v.挤', 4),
(106, 'high', '/haɪ/', 'adj.高的', 1), (106, 'light', '/laɪt/', 'n.光', 2), (106, 'night', '/naɪt/', 'n.夜晚', 3), (106, 'right', '/raɪt/', 'adj.正确的', 4),
(107, 'fire', '/faɪər/', 'n.火', 1), (107, 'hire', '/haɪər/', 'v.雇佣', 2), (107, 'tire', '/taɪər/', 'n.轮胎', 3), (107, 'wire', '/waɪər/', 'n.电线', 4),
(108, 'call', '/kɔːl/', 'v.打电话', 1), (108, 'fall', '/fɔːl/', 'v.落下', 2), (108, 'tall', '/tɔːl/', 'adj.高的', 3), (108, 'wall', '/wɔːl/', 'n.墙', 4),
(109, 'careful', '/ˈkeəfəl/', 'adj.小心的', 1), (109, 'helpful', '/ˈhelpfəl/', 'adj.有帮助的', 2), (109, 'useful', '/ˈjuːsfəl/', 'adj.有用的', 3), (109, 'wonderful', '/ˈwʌndəfəl/', 'adj.精彩的', 4),
(110, 'famous', '/ˈfeɪməs/', 'adj.著名的', 1), (110, 'obvious', '/ˈɒbviəs/', 'adj.明显的', 2), (110, 'nervous', '/ˈnɜːvəs/', 'adj.紧张的', 3), (110, 'dangerous', '/ˈdeɪndʒərəs/', 'adj.危险的', 4),
(111, 'agreement', '/əˈɡriːmənt/', 'n.协议', 1), (111, 'movement', '/ˈmuːvmənt/', 'n.运动', 2), (111, 'government', '/ˈɡʌvənmənt/', 'n.政府', 3), (111, 'instrument', '/ˈɪnstrəmənt/', 'n.仪器', 4),
(112, 'vision', '/ˈvɪʒən/', 'n.视力', 1), (112, 'decision', '/dɪˈsɪʒən/', 'n.决定', 2), (112, 'television', '/ˈtelɪvɪʒən/', 'n.电视', 3), (112, 'devision', '/dɪˈvɪʒən/', 'n.分配', 4),
(113, 'attention', '/əˈtenʃən/', 'n.注意', 1), (113, 'education', '/ˌedʒuˈkeɪʃən/', 'n.教育', 2), (113, 'pollution', '/pəˈluːʃən/', 'n.污染', 3), (113, 'station', '/ˈsteɪʃən/', 'n.车站', 4),
(114, 'culture', '/ˈkʌltʃər/', 'n.文化', 1), (114, 'future', '/ˈfjuːtʃər/', 'n.未来', 2), (114, 'nature', '/ˈneɪtʃər/', 'n.自然', 3), (114, 'picture', '/ˈpɪktʃər/', 'n.图片', 4);